"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CloudbaseOAuth = exports.authModels = exports.Auth = void 0;
var oauth2client_1 = require("./oauth2client/oauth2client");
var apis_1 = require("./auth/apis");
var apis_2 = require("./auth/apis");
Object.defineProperty(exports, "Auth", { enumerable: true, get: function () { return apis_2.Auth; } });
exports.authModels = __importStar(require("./auth/models"));
var CloudbaseOAuth = (function () {
    function CloudbaseOAuth(authOptions) {
        var apiOrigin = authOptions.apiOrigin, clientId = authOptions.clientId, env = authOptions.env, storage = authOptions.storage, request = authOptions.request, baseRequest = authOptions.baseRequest, anonymousSignInFunc = authOptions.anonymousSignInFunc, wxCloud = authOptions.wxCloud;
        this.oauth2client = new oauth2client_1.OAuth2Client({
            apiOrigin: apiOrigin,
            clientId: clientId,
            env: env,
            storage: storage,
            baseRequest: baseRequest || request,
            anonymousSignInFunc: anonymousSignInFunc,
            wxCloud: wxCloud,
        });
        this.authApi = new apis_1.Auth(__assign(__assign({ credentialsClient: this.oauth2client }, authOptions), { request: request ? this.oauth2client.request.bind(this.oauth2client) : undefined }));
    }
    return CloudbaseOAuth;
}());
exports.CloudbaseOAuth = CloudbaseOAuth;
//# sourceMappingURL=data:application/json;base64,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