<template>
	<view class="main-container" @click="closeUserDropdown">
		<!-- 侧边栏导航 -->
		<view class="sidebar">
			<view class="logo-area">
				<text class="logo-text">锐视视觉</text>
				<text class="expand-btn">≡</text>
			</view>
			
			<view class="nav-items">
				<view class="nav-item" :class="{ 'active': currentView === 'mainView' }" @click="currentView = 'mainView'">
					<view class="nav-icon">
						<text class="iconfont icon-main"></text>
					</view>
					<text class="nav-text">仪表盘</text>
				</view>
				
				<view class="nav-item nav-parent" :class="{ 'open': openMenus.includes('reports') }">
					<view class="nav-item-main" @click="toggleSubMenu('reports')">
						<view class="nav-icon">
							<text class="iconfont icon-report"></text>
						</view>
						<text class="nav-text">报表</text>
						<text class="nav-arrow">▼</text>
					</view>
					<view class="nav-sub-items" v-if="openMenus.includes('reports')">
						<view class="nav-sub-item" :class="{ 'active': currentView === 'reports_income-expense_index' }" 
							@click="currentView = 'reports_income-expense_index'">
							<text class="nav-text">收支统计</text>
						</view>
						<view class="nav-sub-item" :class="{ 'active': currentView === 'reports_receivable_index' }" 
							@click="currentView = 'reports_receivable_index'">
							<text class="nav-text">应收统计</text>
						</view>
						<view class="nav-sub-item" :class="{ 'active': currentView === 'reports_payable_index' }" 
							@click="currentView = 'reports_payable_index'">
							<text class="nav-text">应付统计</text>
						</view>
						<view class="nav-sub-item" :class="{ 'active': currentView === 'reports_project_index' }" 
							@click="currentView = 'reports_project_index'">
							<text class="nav-text">项目统计</text>
						</view>
						<view class="nav-sub-item" :class="{ 'active': currentView === 'reports_client_index' }" 
							@click="currentView = 'reports_client_index'">
							<text class="nav-text">客户统计</text>
						</view>
						<view class="nav-sub-item" :class="{ 'active': currentView === 'reports_supplier_index' }" 
							@click="currentView = 'reports_supplier_index'">
							<text class="nav-text">供货商统计</text>
						</view>
						<view class="nav-sub-item" @click="goToExportData">
							<text class="nav-text">数据导出</text>
						</view>
					</view>
				</view>
				
				<view class="nav-item" :class="{ 'active': currentView === 'approval_index' }" @click="currentView = 'approval_index'">
					<view class="nav-icon">
						<text class="iconfont icon-approval"></text>
					</view>
					<text class="nav-text">审批管理</text>
				</view>
				
				<view class="nav-item nav-parent" :class="{ 'open': openMenus.includes('income-expense') }">
					<view class="nav-item-main" @click="toggleSubMenu('income-expense')">
						<view class="nav-icon">
							<text class="iconfont icon-category"></text>
						</view>
						<text class="nav-text">收支类型</text>
						<text class="nav-arrow">▼</text>
					</view>
					<view class="nav-sub-items" v-if="openMenus.includes('income-expense')">
						<view class="nav-sub-item" :class="{ 'active': currentView === 'category_income' }" 
							@click="currentView = 'category_income'">
							<text class="nav-text">收入类型</text>
						</view>
						<view class="nav-sub-item" :class="{ 'active': currentView === 'category_expense' }" 
							@click="currentView = 'category_expense'">
							<text class="nav-text">支出类型</text>
						</view>
					</view>
				</view>
				
				<view class="nav-item" :class="{ 'active': currentView === 'project' }" @click="currentView = 'project'">
					<view class="nav-icon">
						<text class="iconfont icon-project"></text>
					</view>
					<text class="nav-text">项目管理</text>
				</view>
				
				<view class="nav-item" :class="{ 'active': currentView === 'client' }" @click="currentView = 'client'">
					<view class="nav-icon">
						<text class="iconfont icon-client"></text>
					</view>
					<text class="nav-text">客户管理</text>
				</view>
				
				<view class="nav-item" :class="{ 'active': currentView === 'supplier' }" @click="currentView = 'supplier'">
					<view class="nav-icon">
						<text class="iconfont icon-supplier"></text>
					</view>
					<text class="nav-text">供货商管理</text>
				</view>
				
				<view class="nav-item" :class="{ 'active': currentView === 'role' }" @click="currentView = 'role'">
					<view class="nav-icon">
						<text class="iconfont icon-role"></text>
					</view>
					<text class="nav-text">角色管理</text>
				</view>
				
				<view class="nav-item" :class="{ 'active': currentView === 'userSettings' }" @click="currentView = 'userSettings'">
					<view class="nav-icon">
						<text class="iconfont icon-user"></text>
					</view>
					<text class="nav-text">个人信息设置</text>
				</view>
				
				<view class="nav-item settings" :class="{ 'active': currentView === 'systemSettings' }" @click="currentView = 'systemSettings'">
					<view class="nav-icon">
						<text class="iconfont icon-settings"></text>
					</view>
					<text class="nav-text">系统设置</text>
				</view>
				
				<view class="nav-item logout" @click="handleLogout">
					<view class="nav-icon">
						<text class="iconfont icon-logout"></text>
					</view>
					<text class="nav-text">退出登录</text>
				</view>
			</view>
		</view>
		
		<!-- 主内容区域 -->
		<view class="main-content">
			<!-- 顶部搜索栏 -->
			<view class="header">
				<view class="search-section">
					<view class="category-dropdown">
						<text>所有类别</text>
						<text class="dropdown-icon">▼</text>
					</view>
					
					<view class="search-box">
						<input type="text" placeholder="搜索..." />
						<text class="search-icon">🔍</text>
					</view>
				</view>
				
				<view class="user-section">
					<view class="user-info-container" @click.stop="handleUserInfoClick">
						<view class="user-avatar">
							<image :src="userInfo.avatar || '/static/avatar/admin.png'" :key="avatarKey" mode="aspectFill"></image>
						</view>
						<text class="user-name">{{userInfo.nickname || '用户名'}}</text>
						<text class="dropdown-icon">▼</text>
					</view>
					
					
						<!-- 用户下拉菜单 -->
						<view class="user-dropdown" v-if="showUserDropdown" @click.stop>
							<view class="dropdown-header">
								<view class="dropdown-avatar">
									<image :src="userInfo.avatar || '/static/avatar/admin.png'" mode="aspectFill"></image>
								</view>
								<view class="dropdown-user-info">
									<text class="dropdown-name">{{userInfo.nickname || userInfo.username || '用户'}}</text>
									<text class="dropdown-role">{{userInfo.role || '普通用户'}}</text>
								</view>
							</view>
							<view class="dropdown-divider"></view>
							<view class="dropdown-item" @click="currentView = 'userSettings'">
								<text class="dropdown-icon">👤</text>
								<text>个人信息</text>
							</view>
							<view class="dropdown-item" @click="showUserInfoDebug">
								<text class="dropdown-icon">🔍</text>
								<text>调试信息</text>
							</view>
							<view class="dropdown-divider"></view>
							<view class="dropdown-item logout-item" @click="handleLogout">
								<text class="dropdown-icon">🚪</text>
								<text>退出登录</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 动态内容区域 -->
			<view v-if="currentView === 'mainView'">
				<!-- 仪表盘内容 -->
				<!-- 数据概览 -->
				<view class="analytic-overview">
					<view class="section-header">
						<text class="section-title">财务数据概览 {{ currentYear }}年{{ currentMonth }}月</text>
						<view class="period-selector" @click="refreshFinancialData">
							<text>刷新数据</text>
							<text class="dropdown-icon">🔄</text>
						</view>
					</view>
					
					<view class="stat-cards">
						<view class="stat-card income">
							<view class="card-info">
								<text class="card-title">本月收入</text>
								<text class="card-value">¥{{ formatMoney(financialStats.income) }}</text>
								<text class="card-period" :style="{ color: getGrowthDisplay(financialStats.incomeGrowth).color }">
									{{ getGrowthDisplay(financialStats.incomeGrowth).text }}
								</text>
							</view>
							<view class="card-icon income-icon">💰</view>
						</view>

						<view class="stat-card expense">
							<view class="card-info">
								<text class="card-title">本月支出</text>
								<text class="card-value">¥{{ formatMoney(financialStats.expense) }}</text>
								<text class="card-period" :style="{ color: getGrowthDisplay(financialStats.expenseGrowth).color }">
									{{ getGrowthDisplay(financialStats.expenseGrowth).text }}
								</text>
							</view>
							<view class="card-icon expense-icon">💸</view>
						</view>

						<view class="stat-card cash-in">
							<view class="card-info">
								<text class="card-title">资金流入</text>
								<text class="card-value">¥{{ formatMoney(financialStats.cashIn) }}</text>
								<text class="card-period" :style="{ color: getGrowthDisplay(financialStats.cashInGrowth).color }">
									{{ getGrowthDisplay(financialStats.cashInGrowth).text }}
								</text>
							</view>
							<view class="card-icon cash-in-icon">📈</view>
						</view>

						<view class="stat-card cash-out">
							<view class="card-info">
								<text class="card-title">资金流出</text>
								<text class="card-value">¥{{ formatMoney(financialStats.cashOut) }}</text>
								<text class="card-period" :style="{ color: getGrowthDisplay(financialStats.cashOutGrowth).color }">
									{{ getGrowthDisplay(financialStats.cashOutGrowth).text }}
								</text>
							</view>
							<view class="card-icon cash-out-icon">📉</view>
						</view>
					</view>
				</view>
				
				<!-- 收入图表和状态 -->
				<view class="main-row">
					<view class="revenue-chart">
						<view class="section-header">
							<text class="section-title">财务概况</text>
							<view class="period-selector">
								<text>{{ currentYear }}年{{ currentMonth }}月</text>
								<text class="dropdown-icon">📊</text>
							</view>
						</view>
						<view class="chart-container">
							<view class="finance-summary">
								<view class="summary-item">
									<text class="summary-label">本月利润</text>
									<text class="summary-value profit" :style="{ color: (financialStats.income - financialStats.expense) >= 0 ? '#67C23A' : '#F56C6C' }">
										¥{{ formatMoney(financialStats.income - financialStats.expense) }}
									</text>
								</view>
								<view class="summary-item">
									<text class="summary-label">净现金流</text>
									<text class="summary-value cash-flow" :style="{ color: (financialStats.cashIn - financialStats.cashOut) >= 0 ? '#67C23A' : '#F56C6C' }">
										¥{{ formatMoney(financialStats.cashIn - financialStats.cashOut) }}
									</text>
								</view>
								<view class="summary-item">
									<text class="summary-label">收支比</text>
									<text class="summary-value ratio">
										{{ financialStats.expense > 0 ? (financialStats.income / financialStats.expense).toFixed(2) : '∞' }}
									</text>
								</view>
							</view>
						</view>
					</view>
					
					<view class="status-section">
						<view class="section-header">
							<text class="section-title">快捷操作</text>
							<view class="period-selector">
								<text>财务管理</text>
								<text class="dropdown-icon">⚡</text>
							</view>
						</view>
						<view class="status-content">
							<view class="quick-actions">
								<view class="action-item" @click="currentView = 'reports_income-expense_index'">
									<view class="action-icon">📊</view>
									<text class="action-label">收支统计</text>
								</view>

								<view class="action-item" @click="currentView = 'approval_index'">
									<view class="action-icon">✅</view>
									<text class="action-label">审批管理</text>
								</view>

								<view class="action-item" @click="currentView = 'reports_receivable_index'">
									<view class="action-icon">💰</view>
									<text class="action-label">应收管理</text>
								</view>

								<view class="action-item" @click="currentView = 'reports_payable_index'">
									<view class="action-icon">💸</view>
									<text class="action-label">应付管理</text>
								</view>

								<view class="action-item" @click="goToExportData">
									<view class="action-icon">📤</view>
									<text class="action-label">数据导出</text>
								</view>

								<view class="action-item" @click="refreshFinancialData">
									<view class="action-icon">🔄</view>
									<text class="action-label">刷新数据</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 最近订单和跟踪 -->
				<view class="main-row">
					<view class="recent-transactions">
						<view class="section-header">
							<text class="section-title">财务功能导航</text>
							<view class="period-selector">
								<text>快速访问</text>
								<text class="dropdown-icon">🚀</text>
							</view>
						</view>

						<view class="navigation-grid">
							<view class="nav-card" @click="currentView = 'reports_income-expense_index'">
								<view class="nav-icon">📊</view>
								<view class="nav-content">
									<text class="nav-title">收支统计</text>
									<text class="nav-desc">查看详细的收支明细和趋势分析</text>
								</view>
							</view>

							<view class="nav-card" @click="currentView = 'reports_receivable_index'">
								<view class="nav-icon">💰</view>
								<view class="nav-content">
									<text class="nav-title">应收管理</text>
									<text class="nav-desc">管理客户应收账款和回款情况</text>
								</view>
							</view>

							<view class="nav-card" @click="currentView = 'reports_payable_index'">
								<view class="nav-icon">💸</view>
								<view class="nav-content">
									<text class="nav-title">应付管理</text>
									<text class="nav-desc">管理供应商应付账款和付款计划</text>
								</view>
							</view>

							<view class="nav-card" @click="currentView = 'approval_index'">
								<view class="nav-icon">✅</view>
								<view class="nav-content">
									<text class="nav-title">审批管理</text>
									<text class="nav-desc">处理报销审批和费用审核</text>
								</view>
							</view>

							<view class="nav-card" @click="currentView = 'reports_project_index'">
								<view class="nav-icon">📈</view>
								<view class="nav-content">
									<text class="nav-title">项目统计</text>
									<text class="nav-desc">查看项目盈亏和绩效分析</text>
								</view>
							</view>

							<view class="nav-card" @click="goToExportData">
								<view class="nav-icon">📤</view>
								<view class="nav-content">
									<text class="nav-title">数据导出</text>
									<text class="nav-desc">导出财务报表和统计数据</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 收支统计报表 -->
			<view v-if="currentView === 'reports_income-expense_index'">
				<IncomeExpenseReport />
			</view>

			<!-- 审批管理 -->
			<view v-if="currentView === 'approval_index'">
				<ApprovalManagement />
			</view>

			<!-- 应收管理 -->
			<view v-if="currentView === 'reports_receivable_index'">
				<view class="page-title">
					<text>应收管理</text>
				</view>
				<view class="coming-soon">
					<text>应收管理功能开发中...</text>
				</view>
			</view>

			<!-- 应付管理 -->
			<view v-if="currentView === 'reports_payable_index'">
				<view class="page-title">
					<text>应付管理</text>
				</view>
				<view class="coming-soon">
					<text>应付管理功能开发中...</text>
				</view>
			</view>

			<!-- 项目统计 -->
			<view v-if="currentView === 'reports_project_index'">
				<view class="page-title">
					<text>项目统计</text>
				</view>
				<view class="coming-soon">
					<text>项目统计功能开发中...</text>
				</view>
			</view>

			<!-- 项目管理 -->
			<view v-if="currentView === 'project'">
				<view class="page-title">
					<text>项目管理</text>
				</view>
				
				<view class="project-content">
					<!-- 项目管理内容 -->
					<view class="project-filters">
						<view class="filter-item">
							<text>项目状态：</text>
							<view class="filter-select">
								<text>全部</text>
								<text class="dropdown-icon">▼</text>
							</view>
						</view>
						
						<view class="filter-item">
							<text>时间范围：</text>
							<view class="filter-select">
								<text>本季度</text>
								<text class="dropdown-icon">▼</text>
							</view>
						</view>
						
						<view class="add-btn">
							<text>+ 新增项目</text>
						</view>
					</view>
					
					<view class="project-table">
						<view class="table-header">
							<view class="table-cell project-name">
								<text>项目名称</text>
							</view>
							<view class="table-cell client">
								<text>客户</text>
							</view>
							<view class="table-cell start-date">
								<text>开始日期</text>
							</view>
							<view class="table-cell end-date">
								<text>结束日期</text>
							</view>
							<view class="table-cell status">
								<text>状态</text>
							</view>
							<view class="table-cell budget">
								<text>预算</text>
							</view>
							<view class="table-cell operations">
								<text>操作</text>
							</view>
						</view>
						
						<view class="table-row">
							<view class="table-cell project-name">
								<text>2023年度品牌升级</text>
							</view>
							<view class="table-cell client">
								<text>张三公司</text>
							</view>
							<view class="table-cell start-date">
								<text>2023-03-01</text>
							</view>
							<view class="table-cell end-date">
								<text>2023-09-30</text>
							</view>
							<view class="table-cell status">
								<text class="status-badge in-progress">进行中</text>
							</view>
							<view class="table-cell budget">
								<text>¥850,000</text>
							</view>
							<view class="table-cell operations">
								<text class="operation-btn edit">编辑</text>
								<text class="operation-btn delete">删除</text>
							</view>
						</view>
						
						<view class="table-row">
							<view class="table-cell project-name">
								<text>新产品线视觉设计</text>
							</view>
							<view class="table-cell client">
								<text>李四实业</text>
							</view>
							<view class="table-cell start-date">
								<text>2023-05-15</text>
							</view>
							<view class="table-cell end-date">
								<text>2023-08-15</text>
							</view>
							<view class="table-cell status">
								<text class="status-badge completed">已完成</text>
							</view>
							<view class="table-cell budget">
								<text>¥560,000</text>
							</view>
							<view class="table-cell operations">
								<text class="operation-btn edit">编辑</text>
								<text class="operation-btn delete">删除</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 报表 - 收支统计 -->
			<view v-if="currentView === 'reports_income-expense_index'">
				<view class="page-title">
					<text>收支统计报表</text>
				</view>
				
				<view class="report-filters">
					<view class="filter-item">
						<text>时间范围：</text>
						<view class="filter-select">
							<text>本年度</text>
							<text class="dropdown-icon">▼</text>
						</view>
					</view>
					
					<view class="filter-item">
						<text>类型：</text>
						<view class="filter-select">
							<text>全部</text>
							<text class="dropdown-icon">▼</text>
						</view>
					</view>
					
					<view class="export-btn">
						<text>导出报表</text>
					</view>
				</view>
				
				<view class="report-summary">
					<view class="summary-card income">
						<view class="summary-title">总收入</view>
						<view class="summary-value">¥3,249,850</view>
					</view>
					
					<view class="summary-card expense">
						<view class="summary-title">总支出</view>
						<view class="summary-value">¥1,856,420</view>
					</view>
					
					<view class="summary-card profit">
						<view class="summary-title">利润</view>
						<view class="summary-value">¥1,393,430</view>
					</view>
				</view>
				
				<view class="chart-section">
					<view class="section-header">
						<text class="section-title">收支趋势</text>
					</view>
					
					<view class="chart-container">
						<!-- 这里实际开发中会使用图表组件 -->
						<view class="chart-placeholder">
							<text>收支趋势图表</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 其他页面内容区域 -->
			<view v-if="currentView !== 'dashboard' && currentView !== 'project' && currentView !== 'reports_income-expense_index'" class="coming-soon">
				<view class="coming-soon-content">
					<text class="coming-soon-icon">🚧</text>
					<text class="coming-soon-title">该功能正在开发中</text>
					<text class="coming-soon-text">我们正在努力开发这个功能，请稍后再来查看。</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import appConfig from '@/utils/appConfig.js'
	import authHelper from '@/utils/authHelper.js'
	import avatarHelper from '@/utils/avatarHelper.js'
	import AvatarService from '@/utils/avatarService.js'
	import pcFinanceService from '@/utils/pcFinanceService.js'
	import { formatMoney, getGrowthDisplay, getCurrentFinanceDate } from '@/utils/pcUtils.js'
	import IncomeExpenseReport from './components/IncomeExpenseReport.vue'
	import ApprovalManagement from './components/ApprovalManagement.vue'

	export default {
		components: {
			IncomeExpenseReport,
			ApprovalManagement
		},
		data() {
			return {
				openMenus: [],
				currentView: 'mainView', // 默认显示仪表盘
				currentViewParams: {}, // 用于传递给组件的参数
				showUserDropdown: false, // 控制用户下拉菜单的显示
				userInfo: {}, // 用户信息对象，将通过loadUserInfo()方法加载
				avatarKey: Date.now(), // 用于强制刷新头像

				// 财务数据
				financialStats: {
					income: 0,
					expense: 0,
					cashIn: 0,
					cashOut: 0,
					incomeGrowth: 0,
					expenseGrowth: 0,
					cashInGrowth: 0,
					cashOutGrowth: 0
				},
				currentYear: new Date().getFullYear(),
				currentMonth: new Date().getMonth() + 1,
				loading: false
			}
		},
		onLoad() {
			uni.setNavigationBarTitle({
				title: '财务数据仪表盘 - PC端'
			});
			// 加载用户信息
			this.loadUserInfo();
			// 加载财务数据
			this.loadFinancialData();
		},
		onReady() {
			// 在页面加载完成后，获取页面元素
		},
		methods: {
			// 从本地存储或服务器获取用户信息
			loadUserInfo() {
				try {
					// 从本地存储获取用户信息
					let userInfoData = uni.getStorageSync('userInfo');
					console.log('PC页面 - 原始用户信息:', typeof userInfoData, userInfoData);
					
					// 处理不同格式的用户信息（字符串JSON或对象）
					let userInfo;
					if (typeof userInfoData === 'string') {
						// PC端存储的是JSON字符串
						try {
							userInfo = JSON.parse(userInfoData);
						} catch(jsonErr) {
							console.error('JSON解析错误:', jsonErr);
							userInfo = null;
						}
					} else if (userInfoData && typeof userInfoData === 'object') {
						// 小程序存储的是对象
						userInfo = userInfoData;
					}
					
					if (userInfo) {
						this.userInfo = userInfo;
						
						// 确保显示昵称而不是用户名
						if (!this.userInfo.nickname && this.userInfo.name) {
							this.userInfo.nickname = this.userInfo.name;
						}
						
						// 使用全局头像服务处理头像
						this.userInfo.avatar = AvatarService.getUserAvatar(userInfo);
						console.log('PC页面 - 处理后头像URL:', this.userInfo.avatar);
						
						// 强制刷新头像
						this.refreshAvatar();
						
						console.log('PC页面 - 加载用户信息成功:', this.userInfo);
					} else {
						// 如果没有本地存储，设置默认用户信息
						this.userInfo = {
							nickname: '默认用户',
							avatar: avatarHelper.getUserAvatarUrl(null)
						};
						console.log('PC页面 - 使用默认用户信息');
					}
				} catch (e) {
					console.error('PC页面 - 加载用户信息失败:', e);
					// 加载失败时设置默认值
					this.userInfo = {
						nickname: '默认用户',
						avatar: avatarHelper.getUserAvatarUrl(null)
					};
				}
			},
			
			// 强制刷新头像
			refreshAvatar() {
				this.avatarKey = Date.now();
				console.log('PC页面 - 强制刷新头像，新key:', this.avatarKey);
			},
			
			// 处理用户信息点击
			handleUserInfoClick(e) {
				// 阻止冒泡，避免触发外部点击事件
				e.stopPropagation();
				this.showUserDropdown = !this.showUserDropdown;
			},
			
			// 关闭用户下拉菜单
			closeUserDropdown() {
				this.showUserDropdown = false;
			},
			
			toggleSubMenu(menuName) {
				if (this.openMenus.includes(menuName)) {
					this.openMenus = this.openMenus.filter(item => item !== menuName);
				} else {
					this.openMenus.push(menuName);
				}
			},
			
			// 修改导航方法，在当前页面切换视图
			navigateTo(path) {
				try {
					console.log('导航到:', path);
					
					// 解析路径以确定要显示的组件
					const parts = path.split('/');
					const viewName = parts[parts.length - 2] === 'PC' 
						? parts[parts.length - 1] // 例如 /pages/PC/project
						: `${parts[parts.length - 2]}_${parts[parts.length - 1]}`; // 例如 /pages/PC/reports/income-expense/index
					
					// 更新当前视图
					this.currentView = viewName;
					
					// 更新页面标题
					let title = this.getViewTitle(viewName);
					uni.setNavigationBarTitle({
						title: title + ' - PC端'
					});
					
					// 关闭用户下拉菜单
					this.closeUserDropdown();
				} catch(e) {
					console.error('导航异常:', e);
					uni.showToast({
						title: '导航异常，请稍后再试',
						icon: 'none'
					});
				}
			},
			
			// 获取视图对应的标题
			getViewTitle(viewName) {
				const titleMap = {
					'mainView': '财务数据仪表盘',
					'project': '项目管理',
					'client': '客户管理',
					'supplier': '供货商管理',
					'role': '角色管理',
					'userSettings': '个人信息设置',
					'systemSettings': '系统设置',
					'approval_index': '审批管理',
					'reports_income-expense_index': '收支统计',
					'reports_receivable_index': '应收统计',
					'reports_payable_index': '应付统计', 
					'reports_project_index': '项目统计',
					'reports_client_index': '客户统计',
					'reports_supplier_index': '供货商统计',
					'category_income': '收入类型',
					'category_expense': '支出类型'
				};
				
				return titleMap[viewName] || '财务管理系统';
			},

			// 处理退出登录
			handleLogout() {
				uni.showModal({
					title: '提示',
					content: '确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							try {
								// 保存记住密码的信息
								let loginInfo = null;
								try {
									const loginInfoStr = uni.getStorageSync('loginInfo');
									if (loginInfoStr) {
										loginInfo = JSON.parse(loginInfoStr);
									}
								} catch(e) {
									console.error('获取登录信息失败:', e);
								}
								
								// 只清除用户信息，保留记住密码信息
								uni.removeStorageSync('userInfo');
								
								// 如果有记住密码信息，重新保存
								if (loginInfo && loginInfo.rememberMe) {
									uni.setStorageSync('loginInfo', JSON.stringify(loginInfo));
								}
								
								console.log('退出登录，保留记住密码信息:', loginInfo);
								
								// 跳转到登录页面
								uni.reLaunch({
									url: '/pages/PC/login'
								});
							} catch(e) {
								console.error('退出登录异常:', e);
								uni.showToast({
									title: '退出异常，请重试',
									icon: 'none'
								});
							}
						}
					}
				});
				
				// 关闭用户下拉菜单
				this.closeUserDropdown();
			},
			
			// 更新导航项的激活状态
			isActive(viewName) {
				return this.currentView === viewName;
			},
			
			// 显示用户信息调试
			showUserInfoDebug() {
				// 格式化用户信息以便查看
				const infoStr = JSON.stringify(this.userInfo, null, 2);
				uni.showModal({
					title: '用户信息调试',
					content: infoStr,
					showCancel: false,
					confirmText: '确定'
				});
				
				// 打印到控制台以便更好地查看
				console.log('当前用户信息:', this.userInfo);
				console.log('原始存储:', uni.getStorageSync('userInfo'));
			},

			// 跳转到数据导出页面
			goToExportData() {
				uni.navigateTo({
					url: '/pages/PC/exportData'
				});
			},

			// 加载财务数据
			async loadFinancialData() {
				this.loading = true;

				try {
					console.log('PC端 - 开始加载财务数据');

					// 使用PC端财务数据服务获取仪表板数据
					const dashboardData = await pcFinanceService.getDashboardData(
						this.currentYear,
						this.currentMonth,
						false // 不强制刷新，使用缓存
					);

					console.log('PC端 - 获取到仪表板数据:', dashboardData);

					if (dashboardData) {
						// 处理批量云函数返回的嵌套数据结构
						const cashFlowData = dashboardData.cashFlow || {};
						const incomeExpenseData = dashboardData.incomeExpense || {};

						this.financialStats = {
							// 从cashFlow对象中获取资金流数据
							cashIn: cashFlowData.cashIn || 0,
							cashOut: cashFlowData.cashOut || 0,
							cashInGrowth: cashFlowData.cashInGrowth || 0,
							cashOutGrowth: cashFlowData.cashOutGrowth || 0,

							// 从incomeExpense对象中获取收支数据
							income: incomeExpenseData.income || 0,
							expense: incomeExpenseData.expense || 0,
							incomeGrowth: incomeExpenseData.incomeGrowth || 0,
							expenseGrowth: incomeExpenseData.expenseGrowth || 0
						};

						console.log('PC端 - 财务数据更新完成:', this.financialStats);
					}
				} catch (error) {
					console.error('PC端 - 加载财务数据失败:', error);
					uni.showToast({
						title: '财务数据加载失败',
						icon: 'none'
					});
				} finally {
					this.loading = false;
				}
			},

			// 刷新财务数据
			async refreshFinancialData() {
				try {
					// 强制刷新数据
					const dashboardData = await pcFinanceService.getDashboardData(
						this.currentYear,
						this.currentMonth,
						true // 强制刷新
					);

					if (dashboardData) {
						const cashFlowData = dashboardData.cashFlow || {};
						const incomeExpenseData = dashboardData.incomeExpense || {};

						this.financialStats = {
							cashIn: cashFlowData.cashIn || 0,
							cashOut: cashFlowData.cashOut || 0,
							cashInGrowth: cashFlowData.cashInGrowth || 0,
							cashOutGrowth: cashFlowData.cashOutGrowth || 0,
							income: incomeExpenseData.income || 0,
							expense: incomeExpenseData.expense || 0,
							incomeGrowth: incomeExpenseData.incomeGrowth || 0,
							expenseGrowth: incomeExpenseData.expenseGrowth || 0
						};
					}

					uni.showToast({
						title: '数据刷新成功',
						icon: 'success'
					});
				} catch (error) {
					console.error('PC端 - 刷新财务数据失败:', error);
					uni.showToast({
						title: '数据刷新失败',
						icon: 'none'
					});
				}
			},

			// 格式化金额
			formatMoney(amount) {
				return formatMoney(amount);
			},

			// 获取增长率显示
			getGrowthDisplay(growthRate) {
				return getGrowthDisplay(growthRate);
			}
		}
	}
</script>

<style lang="scss">
	.main-container {
		display: flex;
		min-height: 100vh;
		background-color: #f6f6f9;
	}
	
	/* 侧边栏样式 */
	.sidebar {
		width: 220px;
		background: linear-gradient(180deg, #6549D5 0%, #5932E6 100%);
		color: #fff;
		display: flex;
		flex-direction: column;
		flex-shrink: 0;
		box-shadow: 2px 0 8px rgba(0,0,0,0.1);
	}
	
	.logo-area {
		padding: 20px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 60px;
		
		.logo-text {
			font-size: 18px;
			font-weight: bold;
			letter-spacing: 0.5px;
		}
		
		.expand-btn {
			font-size: 18px;
			cursor: pointer;
		}
	}
	
	.nav-items {
		display: flex;
		flex-direction: column;
		padding: 20px 0;
	}
	
	.nav-item {
		display: flex;
		flex-direction: column;
		position: relative;
		transition: all 0.2s;
		
		&:hover {
			background-color: rgba(255,255,255,0.2);
			
			&:before {
				content: "";
				position: absolute;
				left: 0;
				top: 0;
				height: 100%;
				width: 4px;
				background-color: #fff;
			}
		}
		
		&.active {
			background-color: rgba(255,255,255,0.2);
			
			&:before {
				content: "";
				position: absolute;
				left: 0;
				top: 0;
				height: 100%;
				width: 4px;
				background-color: #fff;
			}
		}
		
		&:not(.nav-parent) {
			padding: 12px 20px;
			cursor: pointer;
			display: flex;
			flex-direction: row;
			align-items: center;
		}
		
		.nav-icon {
			width: 20px;
			height: 20px;
			margin-right: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.nav-text {
			font-size: 14px;
		}
		
		.nav-arrow {
			margin-left: auto;
			font-size: 10px;
			transition: transform 0.3s;
		}
		
		.badge {
			background-color: #FF6B6B;
			color: #fff;
			font-size: 12px;
			height: 18px;
			min-width: 18px;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 9px;
			margin-left: auto;
			padding: 0 6px;
		}
		
		&.settings {
			margin-top: auto;
		}
		
		&.logout {
			margin-top: 10px;
		}
		
		&.nav-parent {
			.nav-item-main {
				display: flex;
				flex-direction: row;
				align-items: center;
				padding: 12px 20px;
				cursor: pointer;
			}
			
			.nav-sub-items {
				display: flex;
				flex-direction: column;
				overflow: hidden;
				
				.nav-sub-item {
					padding: 12px 20px 12px 70px;
					cursor: pointer;
					position: relative;
					
					&:hover, &.active {
						background-color: rgba(255,255,255,0.05);
						
						&:before {
							content: "";
							position: absolute;
							left: 0;
							top: 0;
							height: 100%;
							width: 4px;
							background-color: #fff;
						}
					}
				}
			}
			
			&.open {
				.nav-arrow {
					transform: rotate(180deg);
				}
			}
		}
	}
	
	/* 主内容区域样式 */
	.main-content {
		flex: 1;
		padding: 20px;
		overflow-y: auto;
	}
	
	/* 顶部搜索栏样式 */
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30px;
	}
	
	.search-section {
		display: flex;
		align-items: center;
	}
	
	.category-dropdown {
		display: flex;
		align-items: center;
		padding: 8px 16px;
		background-color: #fff;
		border-radius: 6px;
		margin-right: 15px;
		cursor: pointer;
		box-shadow: 0 2px 8px rgba(0,0,0,0.05);
		
		.dropdown-icon {
			margin-left: 10px;
			font-size: 12px;
			color: #999;
		}
	}
	
	.search-box {
		position: relative;
		
		input {
			padding: 8px 16px;
			padding-right: 40px;
			width: 300px;
			border: none;
			border-radius: 6px;
			background-color: #fff;
			box-shadow: 0 2px 8px rgba(0,0,0,0.05);
		}
		
		.search-icon {
			position: absolute;
			right: 12px;
			top: 50%;
			transform: translateY(-50%);
			color: #999;
		}
	}
	
	.user-section {
		display: flex;
		align-items: center;
		
		.user-info-container {
			display: flex;
			align-items: center;
			cursor: pointer;
			position: relative;
			background-color: rgba(255, 255, 255, 0.1);
			padding: 6px 12px;
			border-radius: 20px;
			transition: all 0.3s;
			
			&:hover {
				background-color: rgba(255, 255, 255, 0.2);
			}
		}
		
		.user-avatar {
			width: 32px;
			height: 32px;
			border-radius: 16px;
			overflow: hidden;
			background-color: #ddd;
			margin-right: 10px;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
		
		.user-name {
			font-size: 14px;
			color: #333;
			font-weight: 500;
			margin-right: 6px;
			max-width: 120px;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
		
		.dropdown-icon {
			font-size: 10px;
			color: #999;
		}
		
		.user-dropdown {
			position: absolute;
			top: 100%;
			right: 0;
			background-color: #fff;
			border-radius: 6px;
			box-shadow: 0 4px 15px rgba(0,0,0,0.1);
			padding: 10px 0;
			z-index: 10;
			min-width: 150px;
			border: 1px solid #eee;
			margin-top: 8px;
		}
		
		.dropdown-header {
			display: flex;
			align-items: center;
			padding: 10px 15px;
			border-bottom: 1px solid #eee;
		}
		
		.dropdown-avatar {
			width: 40px;
			height: 40px;
			border-radius: 20px;
			overflow: hidden;
			margin-right: 10px;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
		
		.dropdown-user-info {
			display: flex;
			flex-direction: column;
		}
		
		.dropdown-name {
			font-size: 14px;
			font-weight: 500;
			color: #333;
		}
		
		.dropdown-role {
			font-size: 12px;
			color: #666;
			margin-top: 2px;
		}
		
		.dropdown-divider {
			height: 1px;
			background-color: #eee;
			margin: 5px 0;
		}
		
		.dropdown-item {
			display: flex;
			align-items: center;
			padding: 10px 15px;
			cursor: pointer;
			color: #333;
			font-size: 14px;
			
			&:hover {
				background-color: #f5f5f5;
			}
			
			.dropdown-icon {
				margin-right: 10px;
				font-size: 16px;
				color: #666;
			}
		}
		
		.logout-item {
			color: #E53935;
			font-weight: 500;
			
			&:hover {
				background-color: #FFEBEE;
			}
		}
	}
	
	/* 数据概览卡片样式 */
	.analytic-overview {
		margin-bottom: 30px;
	}
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;
		
		.section-title {
			font-size: 18px;
			font-weight: bold;
			color: #333;
		}
		
		.period-selector {
			display: flex;
			align-items: center;
			padding: 6px 12px;
			background-color: #fff;
			border-radius: 4px;
			cursor: pointer;
			
			.dropdown-icon {
				margin-left: 8px;
				font-size: 12px;
				color: #999;
			}
		}
	}
	
	.stat-cards {
		display: flex;
		gap: 20px;
	}
	
	.stat-card {
		flex: 1;
		padding: 20px;
		border-radius: 12px;
		display: flex;
		justify-content: space-between;
		box-shadow: 0 4px 15px rgba(0,0,0,0.05);
		
		.card-info {
			display: flex;
			flex-direction: column;
		}
		
		.card-title {
			font-size: 14px;
			color: rgba(255,255,255,0.8);
			margin-bottom: 8px;
		}
		
		.card-value {
			font-size: 24px;
			font-weight: bold;
			color: #fff;
			margin-bottom: 5px;
		}
		
		.card-period {
			font-size: 12px;
			color: rgba(255,255,255,0.7);
		}
		
		&.income {
			background: linear-gradient(135deg, #67C23A, #85CE61);
		}

		&.expense {
			background: linear-gradient(135deg, #F56C6C, #F78989);
		}

		&.cash-in {
			background: linear-gradient(135deg, #409EFF, #66B1FF);
		}

		&.cash-out {
			background: linear-gradient(135deg, #E6A23C, #EEBE77);
		}
	}

	.card-icon {
		width: 60px;
		height: 60px;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32px;
		opacity: 0.8;
	}

	/* 财务概况样式 */
	.finance-summary {
		display: flex;
		flex-direction: column;
		gap: 20px;
		padding: 20px;
	}

	.summary-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 15px;
		background-color: #f8f9fa;
		border-radius: 8px;
		border-left: 4px solid #409EFF;
	}

	.summary-label {
		font-size: 14px;
		color: #666;
		font-weight: 500;
	}

	.summary-value {
		font-size: 18px;
		font-weight: bold;

		&.profit {
			font-size: 20px;
		}

		&.cash-flow {
			font-size: 18px;
		}

		&.ratio {
			font-size: 16px;
			color: #409EFF;
		}
	}

	/* 快捷操作样式 */
	.quick-actions {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 15px;
		padding: 20px;
	}

	.action-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20px 15px;
		background-color: #f8f9fa;
		border-radius: 8px;
		cursor: pointer;
		transition: all 0.3s;
		border: 1px solid #e9ecef;

		&:hover {
			background-color: #e9ecef;
			transform: translateY(-2px);
			box-shadow: 0 4px 12px rgba(0,0,0,0.1);
		}
	}

	.action-icon {
		font-size: 24px;
		margin-bottom: 8px;
	}

	.action-label {
		font-size: 12px;
		color: #666;
		text-align: center;
		font-weight: 500;
	}

	/* 导航网格样式 */
	.navigation-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20px;
		padding: 20px;
	}

	.nav-card {
		display: flex;
		align-items: center;
		padding: 20px;
		background-color: #fff;
		border-radius: 12px;
		border: 1px solid #e9ecef;
		cursor: pointer;
		transition: all 0.3s;

		&:hover {
			transform: translateY(-3px);
			box-shadow: 0 8px 25px rgba(0,0,0,0.1);
			border-color: #409EFF;
		}
	}

	.nav-icon {
		font-size: 32px;
		margin-right: 15px;
		flex-shrink: 0;
	}

	.nav-content {
		flex: 1;
	}

	.nav-title {
		display: block;
		font-size: 16px;
		font-weight: 600;
		color: #333;
		margin-bottom: 5px;
	}

	.nav-desc {
		display: block;
		font-size: 12px;
		color: #666;
		line-height: 1.4;
	}

	/* 开发中页面样式 */
	.coming-soon {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 400px;
		background-color: #fff;
		border-radius: 12px;
		margin: 20px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);

		text {
			font-size: 18px;
			color: #999;
		}
	}

	.page-title {
		padding: 20px;

		text {
			font-size: 24px;
			font-weight: bold;
			color: #333;
		}
	}
	
	/* 图表和状态区域样式 */
	.main-row {
		display: flex;
		gap: 20px;
		margin-bottom: 30px;
	}
	
	.revenue-chart {
		flex: 2;
		background-color: #fff;
		border-radius: 12px;
		padding: 20px;
		box-shadow: 0 4px 15px rgba(0,0,0,0.05);
	}
	
	.chart-container {
		height: 300px;
		
		.chart-placeholder {
			width: 100%;
			height: 100%;
			background-color: #f9f9f9;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #999;
		}
	}
	
	.status-section {
		flex: 1;
		background-color: #fff;
		border-radius: 12px;
		padding: 20px;
		box-shadow: 0 4px 15px rgba(0,0,0,0.05);
	}
	
	.status-content {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.progress-circle {
		width: 120px;
		height: 120px;
		border-radius: 60px;
		border: 10px solid #f2f2f2;
		border-right-color: #4CD4A9;
		border-bottom-color: #4CD4A9;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-bottom: 20px;
		
		.progress-percentage {
			font-size: 24px;
			font-weight: bold;
			color: #333;
		}
		
		.progress-label {
			font-size: 12px;
			color: #999;
		}
	}
	
	.status-stats {
		display: flex;
		justify-content: space-around;
		width: 100%;
	}
	
	.status-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		
		.status-count {
			font-size: 20px;
			font-weight: bold;
			color: #333;
			margin-bottom: 5px;
		}
		
		.status-label {
			font-size: 12px;
			color: #999;
		}
	}
	
	/* 表格样式 */
	.recent-orders, .project-table {
		background-color: #fff;
		border-radius: 12px;
		padding: 20px;
		box-shadow: 0 4px 15px rgba(0,0,0,0.05);
	}
	
	.table-container {
		width: 100%;
		overflow-x: auto;
	}
	
	.table-header {
		display: flex;
		padding: 15px 0;
		border-bottom: 1px solid #eee;
		font-weight: 500;
	}
	
	.table-row {
		display: flex;
		padding: 15px 0;
		border-bottom: 1px solid #eee;
		
		&:last-child {
			border-bottom: none;
		}
	}
	
	.table-cell {
		flex: 1;
		min-width: 120px;
		
		&.invoice {
			flex: 0.8;
		}
		
		&.customer, &.project-name {
			flex: 1.5;
		}
		
		&.date, &.start-date, &.end-date {
			flex: 0.8;
		}
		
		&.amount, &.budget {
			flex: 1;
			text-align: right;
		}
		
		&.status {
			flex: 0.8;
			
			.status-badge {
				display: inline-block;
				padding: 5px 10px;
				border-radius: 15px;
				font-size: 12px;
				
				&.processing, &.in-progress {
					background-color: #E3F2FD;
					color: #1976D2;
				}
				
				&.completed {
					background-color: #E8F5E9;
					color: #43A047;
				}
				
				&.cancelled {
					background-color: #FFEBEE;
					color: #E53935;
				}
			}
		}
		
		&.tracking {
			flex: 1;
			text-align: right;
			
			.tracking-id {
				color: #999;
				font-size: 12px;
			}
		}
		
		&.operations {
			flex: 1;
			display: flex;
			gap: 8px;
			
			.operation-btn {
				padding: 5px 10px;
				border-radius: 4px;
				font-size: 12px;
				cursor: pointer;
				
				&.edit {
					background-color: #E3F2FD;
					color: #1976D2;
				}
				
				&.delete {
					background-color: #FFEBEE;
					color: #E53935;
				}
			}
		}
	}
	
	/* 页面标题样式 */
	.page-title {
		font-size: 24px;
		font-weight: bold;
		color: #333;
		margin-bottom: 20px;
		padding-bottom: 10px;
		border-bottom: 1px solid #eee;
	}
	
	/* 项目和报表页面样式 */
	.project-filters, .report-filters {
		display: flex;
		align-items: center;
		margin-bottom: 20px;
		gap: 15px;
		
		.filter-item {
			display: flex;
			align-items: center;
			
			.filter-select {
				margin-left: 10px;
				padding: 8px 12px;
				background-color: #fff;
				border-radius: 6px;
				cursor: pointer;
				box-shadow: 0 2px 5px rgba(0,0,0,0.05);
				display: flex;
				align-items: center;
				
				.dropdown-icon {
					margin-left: 8px;
					font-size: 10px;
					color: #999;
				}
			}
		}
		
		.add-btn, .export-btn {
			margin-left: auto;
			padding: 8px 15px;
			background-color: #6549D5;
			color: #fff;
			border-radius: 6px;
			cursor: pointer;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
	
	/* 报表页面样式 */
	.report-summary {
		display: flex;
		gap: 20px;
		margin-bottom: 30px;
		
		.summary-card {
			flex: 1;
			padding: 20px;
			border-radius: 12px;
			background-color: #fff;
			box-shadow: 0 4px 15px rgba(0,0,0,0.05);
			
			.summary-title {
				font-size: 16px;
				color: #666;
				margin-bottom: 10px;
			}
			
			.summary-value {
				font-size: 24px;
				font-weight: bold;
				color: #333;
			}
			
			&.income .summary-value {
				color: #4CD4A9;
			}
			
			&.expense .summary-value {
				color: #FF6B8A;
			}
			
			&.profit .summary-value {
				color: #6549D5;
			}
		}
	}
	
	.chart-section {
		background-color: #fff;
		border-radius: 12px;
		padding: 20px;
		box-shadow: 0 4px 15px rgba(0,0,0,0.05);
		margin-bottom: 30px;
	}
	
	/* 正在开发中提示 */
	.coming-soon {
		height: 70vh;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.coming-soon-content {
		text-align: center;
		
		.coming-soon-icon {
			font-size: 60px;
			margin-bottom: 20px;
			display: block;
		}
		
		.coming-soon-title {
			font-size: 24px;
			font-weight: bold;
			color: #333;
			margin-bottom: 10px;
		}
		
		.coming-soon-text {
			font-size: 16px;
			color: #666;
		}
	}
</style> 