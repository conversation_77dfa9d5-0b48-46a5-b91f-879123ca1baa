"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var MyURLSearchParams = (function () {
    function MyURLSearchParams(init) {
        this.params = {};
        if (typeof init === 'string') {
            this.parse(init);
        }
        else if (init && typeof init === 'object') {
            for (var key in init) {
                if (Object.prototype.hasOwnProperty.call(init, key)) {
                    this.append(key, init[key]);
                }
            }
        }
    }
    MyURLSearchParams.prototype.parse = function (str) {
        var _this = this;
        var params = str.split('&');
        params.forEach(function (param) {
            var _a = param.split('=').map(decodeURIComponent), key = _a[0], value = _a[1];
            _this.append(key, value);
        });
    };
    MyURLSearchParams.prototype.append = function (key, value) {
        if (this.params[key]) {
            this.params[key] = this.params[key].concat([value]);
        }
        else {
            this.params[key] = [value];
        }
    };
    MyURLSearchParams.prototype.get = function (key) {
        return this.params[key] ? this.params[key][0] : null;
    };
    MyURLSearchParams.prototype.getAll = function (key) {
        return this.params[key] || [];
    };
    MyURLSearchParams.prototype.delete = function (key) {
        delete this.params[key];
    };
    MyURLSearchParams.prototype.has = function (key) {
        return Object.prototype.hasOwnProperty.call(this.params, key);
    };
    MyURLSearchParams.prototype.set = function (key, value) {
        this.params[key] = [value];
    };
    MyURLSearchParams.prototype.toString = function () {
        var items = [];
        var _loop_1 = function (key) {
            if (Object.prototype.hasOwnProperty.call(this_1.params, key)) {
                this_1.params[key].forEach(function (value) {
                    items.push("".concat(encodeURIComponent(key), "=").concat(encodeURIComponent(value)));
                });
            }
        };
        var this_1 = this;
        for (var key in this.params) {
            _loop_1(key);
        }
        return items.join('&');
    };
    return MyURLSearchParams;
}());
exports.default = MyURLSearchParams;
//# sourceMappingURL=data:application/json;base64,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