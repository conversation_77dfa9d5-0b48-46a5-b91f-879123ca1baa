{"name": "@cloudbase/js-sdk", "version": "2.17.6", "description": "cloudbase javascript sdk", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "miniprogram": "miniprogram_dist", "typings": "./index.d.ts", "scripts": {"lint": "eslint --fix \"./src/**/*.ts\"", "build": "rm -rf dist/ && gulp build", "build:cdn": "gulp cdn", "build:e2e": "rm -rf dist/ && NODE_ENV=e2e gulp e2e", "precommit": "npm run lint"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/TencentCloudBase/cloudbase-js-sdk"}, "keywords": ["tcb", "cloudbase", "Cloudbase", "serverless", "Serverless", "javascript", "JavaScript"], "files": ["miniprogram_dist", "**/dist/", "/index.d.ts", "**/package.json"], "components": ["app", "auth", "database", "functions", "storage"], "author": "", "license": "Apache-2.0", "dependencies": {"@cloudbase/adapter-interface": "^0.7.0", "@cloudbase/ai": "^2.17.6", "@cloudbase/analytics": "^2.17.6", "@cloudbase/app": "^2.17.6", "@cloudbase/auth": "^2.17.6", "@cloudbase/cloudrun": "^2.17.6", "@cloudbase/database": "0.9.22", "@cloudbase/functions": "^2.17.6", "@cloudbase/model": "^2.17.6", "@cloudbase/realtime": "^2.17.6", "@cloudbase/storage": "^2.17.6", "@cloudbase/types": "^2.17.6", "@cloudbase/utilities": "^2.17.6"}, "browserslist": ["last 2 version", "> 1%", "chrome 53"], "gitHead": "ab6b2e1eb2c4ccfe19e7ad1a92ac4c94e8f72332"}