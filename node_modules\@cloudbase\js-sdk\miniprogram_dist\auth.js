/*! For license information please see auth.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("cloudbase_auth",[],t):"object"==typeof exports?exports.cloudbase_auth=t():e.cloudbase_auth=t()}("undefined"!=typeof window?window:this,(()=>(()=>{"use strict";var e={325:(e,t,r)=>{var n;r.r(t),r.d(t,{AbstractSDKRequest:()=>o,AbstractStorage:()=>i,StorageType:()=>n,formatUrl:()=>s}),function(e){e.local="local",e.none="none",e.session="session"}(n||(n={}));var o=function(){},i=function(){};function s(e,t,r){void 0===r&&(r={});var n=/\?/.test(t),o="";for(var i in r)""===o?!n&&(t+="?"):o+="&",o+=i+"="+encodeURIComponent(r[i]);return/^http(s)?\:\/\//.test(t+=o)?t:""+e+t}},232:(e,t,r)=>{var n,o,i,s,a,u;function c(){return"xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g,(e=>{var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}r.r(t),r.d(t,{Auth:()=>Wo,LoginState:()=>Bo,generateAuthInstance:()=>Fo,registerAuth:()=>Ho}),function(e){e.AUTH_SIGN_UP_URL="/auth/v1/signup",e.AUTH_TOKEN_URL="/auth/v1/token",e.AUTH_REVOKE_URL="/auth/v1/revoke",e.WEDA_USER_URL="/auth/v1/plugin/weda/userinfo",e.AUTH_RESET_PASSWORD="/auth/v1/reset",e.AUTH_GET_DEVICE_CODE="/auth/v1/device/code",e.CHECK_USERNAME="/auth/v1/checkUsername",e.CHECK_IF_USER_EXIST="/auth/v1/checkIfUserExist",e.GET_PROVIDER_TYPE="/auth/v1/mgr/provider/providerSubType",e.AUTH_SIGN_IN_URL="/auth/v1/signin",e.AUTH_SIGN_IN_ANONYMOUSLY_URL="/auth/v1/signin/anonymously",e.AUTH_SIGN_IN_WITH_PROVIDER_URL="/auth/v1/signin/with/provider",e.AUTH_SIGN_IN_WITH_WECHAT_URL="/auth/v1/signin/wechat/noauth",e.AUTH_SIGN_IN_CUSTOM="/auth/v1/signin/custom",e.PROVIDER_TOKEN_URL="/auth/v1/provider/token",e.PROVIDER_URI_URL="/auth/v1/provider/uri",e.USER_ME_URL="/auth/v1/user/me",e.AUTH_SIGNOUT_URL="/auth/v1/user/signout",e.USER_QUERY_URL="/auth/v1/user/query",e.USER_PRIFILE_URL="/auth/v1/user/profile",e.USER_BASIC_EDIT_URL="/auth/v1/user/basic/edit",e.USER_TRANS_BY_PROVIDER_URL="/auth/v1/user/trans/by/provider",e.PROVIDER_LIST="/auth/v1/user/provider",e.PROVIDER_BIND_URL="/auth/v1/user/provider/bind",e.PROVIDER_UNBIND_URL="/auth/v1/user/provider",e.CHECK_PWD_URL="/auth/v1/user/sudo",e.SUDO_URL="/auth/v1/user/sudo",e.BIND_CONTACT_URL="/auth/v1/user/contact",e.AUTH_SET_PASSWORD="/auth/v1/user/password",e.AUTHORIZE_DEVICE_URL="/auth/v1/user/device/authorize",e.AUTHORIZE_URL="/auth/v1/user/authorize",e.AUTHORIZE_INFO_URL="/auth/v1/user/authorize/info",e.AUTHORIZED_DEVICES_DELETE_URL="/auth/v1/user/authorized/devices/",e.AUTH_REVOKE_ALL_URL="/auth/v1/user/revoke/all",e.GET_USER_BEHAVIOR_LOG="/auth/v1/user/security/history",e.VERIFICATION_URL="/auth/v1/verification",e.VERIFY_URL="/auth/v1/verification/verify",e.CAPTCHA_DATA_URL="/auth/v1/captcha/data",e.VERIFY_CAPTCHA_DATA_URL="/auth/v1/captcha/data/verify",e.GET_CAPTCHA_URL="/auth/v1/captcha/init",e.GET_MINIPROGRAM_QRCODE="/auth/v1/qrcode/generate",e.GET_MINIPROGRAM_QRCODE_STATUS="/auth/v1/qrcode/get/status"}(n||(n={})),function(e){e.AUTH_SIGN_IN_URL="/auth/v2/signin/username",e.AUTH_TOKEN_URL="/auth/v2/token",e.USER_ME_URL="/auth/v2/user/me",e.VERIFY_URL="/auth/v2/signin/verificationcode",e.AUTH_SIGN_IN_WITH_PROVIDER_URL="/auth/v2/signin/with/provider",e.AUTH_PUBLIC_KEY="/auth/v2/signin/publichkey",e.AUTH_RESET_PASSWORD="/auth/v2/signin/password/update"}(o||(o={})),function(e){e.REGISTER="REGISTER",e.SIGN_IN="SIGN_IN",e.PASSWORD_RESET="PASSWORD_RESET",e.EMAIL_ADDRESS_CHANGE="EMAIL_ADDRESS_CHANGE",e.PHONE_NUMBER_CHANGE="PHONE_NUMBER_CHANGE"}(i||(i={})),(u=s||(s={})).UNREACHABLE="unreachable",u.LOCAL="local",u.CANCELLED="cancelled",u.UNKNOWN="unknown",u.UNAUTHENTICATED="unauthenticated",u.RESOURCE_EXHAUSTED="resource_exhausted",u.FAILED_PRECONDITION="failed_precondition",u.INVALID_ARGUMENT="invalid_argument",u.DEADLINE_EXCEEDED="deadline_exceeded",u.NOT_FOUND="not_found",u.ALREADY_EXISTS="already_exists",u.PERMISSION_DENIED="permission_denied",u.ABORTED="aborted",u.OUT_OF_RANGE="out_of_range",u.UNIMPLEMENTED="unimplemented",u.INTERNAL="internal",u.UNAVAILABLE="unavailable",u.DATA_LOSS="data_loss",u.INVALID_PASSWORD="invalid_password",u.PASSWORD_NOT_SET="password_not_set",u.INVALID_STATUS="invalid_status",u.USER_PENDING="user_pending",u.USER_BLOCKED="user_blocked",u.INVALID_VERIFICATION_CODE="invalid_verification_code",u.TWO_FACTOR_REQUIRED="two_factor_required",u.INVALID_TWO_FACTOR="invalid_two_factor",u.INVALID_TWO_FACTOR_RECOVERY="invalid_two_factor_recovery",u.UNDER_REVIEW="under_review",u.INVALID_REQUEST="invalid_request",u.UNAUTHORIZED_CLIENT="unauthorized_client",u.ACCESS_DENIED="access_denied",u.UNSUPPORTED_RESPONSE_TYPE="unsupported_response_type",u.INVALID_SCOPE="invalid_scope",u.INVALID_GRANT="invalid_grant",u.SERVER_ERROR="server_error",u.TEMPORARILY_UNAVAILABLE="temporarily_unavailable",u.INTERACTION_REQUIRED="interaction_required",u.LOGIN_REQUIRED="login_required",u.ACCOUNT_SELECTION_REQUIRED="account_selection_required",u.CONSENT_REQUIRED="consent_required",u.INVALID_REQUEST_URI="invalid_request_uri",u.INVALID_REQUEST_OBJECT="invalid_request_object",u.REQUEST_NOT_SUPPORTED="request_not_supported",u.REQUEST_URI_NOT_SUPPORTED="request_uri_not_supported",u.REGISTRATION_NOT_SUPPORTED="registration_not_supported",u.CAPTCHA_REQUIRED="captcha_required",u.CAPTCHA_INVALID="captcha_invalid",function(e){e.CLIENT_ID="client_id",e.CLIENT_SECRET="client_secret",e.RESPONSE_TYPE="response_type",e.SCOPE="scope",e.STATE="state",e.REDIRECT_URI="redirect_uri",e.ERROR="error",e.ERROR_DESCRIPTION="error_description",e.ERROR_URI="error_uri",e.GRANT_TYPE="grant_type",e.CODE="code",e.ACCESS_TOKEN="access_token",e.TOKEN_TYPE="token_type",e.EXPIRES_IN="expires_in",e.USERNAME="username",e.PASSWORD="password",e.REFRESH_TOKEN="refresh_token"}(a||(a={}));var l=r(655);function h(e,t,r,n,o,i,s){try{var a=e[i](s),u=a.value}catch(e){return void r(e)}a.done?t(u):Promise.resolve(u).then(n,o)}function d(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function s(e){h(i,n,o,s,a,"next",e)}function a(e){h(i,n,o,s,a,"throw",e)}s(void 0)}))}}class f{constructor(e){this.clientId=(null==e?void 0:e.clientId)||"",globalThis.jsSdkFnPromiseMap=globalThis.jsSdkFnPromiseMap||new Map}run(e,t){var r=this;return d((function*(){e="".concat(r.clientId,"_").concat(e);var n=globalThis.jsSdkFnPromiseMap.get(e);return n||(n=new Promise(((n,o)=>{d((function*(){try{yield r.runIdlePromise();var i=t();n(yield i)}catch(e){o(e)}finally{globalThis.jsSdkFnPromiseMap.delete(e)}}))()})),globalThis.jsSdkFnPromiseMap.set(e,n)),n}))()}runIdlePromise(){return Promise.resolve()}}var p="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach((function(t){g(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function g(e,t,r){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(e,t,r,n,o,i,s){try{var a=e[i](s),u=a.value}catch(e){return void r(e)}a.done?t(u):Promise.resolve(u).then(n,o)}function _(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function s(e){y(i,n,o,s,a,"next",e)}function a(e){y(i,n,o,s,a,"throw",e)}s(void 0)}))}}var b="x-request-id",w="x-device-id",S="device_id",T=function(){var e=_((function*(e,t){var r=null,n=null;try{var o=Object.assign({},t);o.method||(o.method="GET"),o.body&&"string"!=typeof o.body&&(o.body=JSON.stringify(o.body));var i=yield fetch(e,o),a=yield i.json();null!=a&&a.error?(n=a).error_uri=new URL(e).pathname:r=a}catch(t){n={error:s.UNREACHABLE,error_description:t.message,error_uri:new URL(e).pathname}}if(n)throw n;return r}));return function(t,r){return e.apply(this,arguments)}}(),I=new class{constructor(e){this._env=(null==e?void 0:e.env)||""}getItem(e){var t=this;return _((function*(){return window.localStorage.getItem("".concat(e).concat(t._env))}))()}removeItem(e){var t=this;return _((function*(){window.localStorage.removeItem("".concat(e).concat(t._env))}))()}setItem(e,t){var r=this;return _((function*(){window.localStorage.setItem("".concat(e).concat(r._env),t)}))()}getItemSync(e){return window.localStorage.getItem("".concat(e).concat(this._env))}removeItemSync(e){window.localStorage.removeItem("".concat(e).concat(this._env))}setItemSync(e,t){window.localStorage.setItem("".concat(e).concat(this._env),t)}};function E(e){var t=!0;return null!=e&&e.expires_at&&null!=e&&e.access_token&&(t=e.expires_at<new Date),t}class R{constructor(e){this.credentials=null,this.singlePromise=null,this.tokenSectionName=e.tokenSectionName,this.storage=e.storage,this.clientId=e.clientId,this.singlePromise=new f({clientId:this.clientId})}getStorageCredentialsSync(){var e=null,t=this.storage.getItemSync(this.tokenSectionName);if(null!=t)try{var r;null!==(r=e=JSON.parse(t))&&void 0!==r&&r.expires_at&&(e.expires_at=new Date(e.expires_at))}catch(t){this.storage.removeItem(this.tokenSectionName),e=null}return e}setCredentials(e){var t=this;return _((function*(){if(null!=e&&e.expires_in){if(null!=e&&e.expires_at||(e.expires_at=new Date(Date.now()+1e3*(e.expires_in-30))),t.storage){var r=JSON.stringify(e);yield t.storage.setItem(t.tokenSectionName,r)}t.credentials=e}else t.storage&&(yield t.storage.removeItem(t.tokenSectionName)),t.credentials=null}))()}getCredentials(){var e=this;return _((function*(){return e.singlePromise.run("getCredentials",_((function*(){return E(e.credentials)&&(e.credentials=yield e.getStorageCredentials()),e.credentials})))}))()}getStorageCredentials(){var e=this;return _((function*(){return e.singlePromise.run("_getStorageCredentials",_((function*(){var t=null,r=yield e.storage.getItem(e.tokenSectionName);if(null!=r)try{var n;null!==(n=t=JSON.parse(r))&&void 0!==n&&n.expires_at&&(t.expires_at=new Date(t.expires_at))}catch(r){yield e.storage.removeItem(e.tokenSectionName),t=null}return t})))}))()}}class O{constructor(e){this.singlePromise=null,e.clientSecret||(e.clientSecret=""),!e.clientId&&e.env&&(e.clientId=e.env),this.apiOrigin=e.apiOrigin,this.clientId=e.clientId,this.singlePromise=new f({clientId:this.clientId}),this.retry=this.formatRetry(e.retry,O.defaultRetry),e.baseRequest?this.baseRequest=e.baseRequest:this.baseRequest=T,this.tokenInURL=e.tokenInURL,this.headers=e.headers,this.storage=e.storage||I,this.localCredentials=new R({tokenSectionName:"credentials_".concat(e.clientId),storage:this.storage,clientId:e.clientId}),this.clientSecret=e.clientSecret,e.clientId&&(this.basicAuth="Basic ".concat(function(e){for(var t,r,n,o,i="",s=0,a=(e=String(e)).length%3;s<e.length;){if((r=e.charCodeAt(s++))>255||(n=e.charCodeAt(s++))>255||(o=e.charCodeAt(s++))>255)throw new TypeError("Failed to execute 'btoa' on 'Window': The string to be encoded contains characters outside of the Latin1 range.");i+=p.charAt((t=r<<16|n<<8|o)>>18&63)+p.charAt(t>>12&63)+p.charAt(t>>6&63)+p.charAt(63&t)}return a?i.slice(0,a-3)+"===".substring(a):i}("".concat(e.clientId,":").concat(this.clientSecret)))),this.wxCloud=e.wxCloud;try{(function(){if("undefined"==typeof wx)return!1;if("undefined"==typeof Page)return!1;if(!wx.getSystemInfoSync)return!1;if(!wx.getStorageSync)return!1;if(!wx.setStorageSync)return!1;if(!wx.connectSocket)return!1;if(!wx.request)return!1;try{if(!wx.getSystemInfoSync())return!1;if("qq"===wx.getSystemInfoSync().AppPlatform)return!1}catch(e){return!1}return!0})()&&void 0===this.wxCloud&&e.env&&(wx.cloud.init({env:e.env}),this.wxCloud=wx.cloud)}catch(e){}this.refreshTokenFunc=e.refreshTokenFunc||this.defaultRefreshTokenFunc,this.anonymousSignInFunc=e.anonymousSignInFunc}setCredentials(e){return this.localCredentials.setCredentials(e)}getAccessToken(){var e=this;return _((function*(){var t=yield e.getCredentials();if(null!=t&&t.access_token)return Promise.resolve(t.access_token);var r={error:s.UNAUTHENTICATED};return Promise.reject(r)}))()}request(e,t){var r=this;return _((function*(){var n,o;t||(t={});var i=r.formatRetry(t.retry,r.retry);if(t.headers=t.headers||{},r.headers&&(t.headers=m(m({},r.headers),t.headers)),t.headers[b]||(t.headers[b]=c()),!t.headers[w]){var a=yield r.getDeviceId();t.headers[w]=a}if(null!==(n=t)&&void 0!==n&&n.withBasicAuth&&r.basicAuth&&(t.headers.Authorization=r.basicAuth),null!==(o=t)&&void 0!==o&&o.withCredentials){var u=yield r.getCredentials();u&&(r.tokenInURL?(e.indexOf("?")<0&&(e+="?"),e+="access_token=".concat(u.access_token)):t.headers.Authorization="".concat(u.token_type," ").concat(u.access_token))}else r.clientId&&e.indexOf("client_id")<0&&(e+=e.indexOf("?")<0?"?":"&",e+="client_id=".concat(r.clientId));e.startsWith("/")&&(e=r.apiOrigin+e);for(var l=null,h=i+1,d=0;d<h;d++){try{l=t.useWxCloud?yield r.wxCloudCallFunction(e,t):yield r.baseRequest(e,t);break}catch(e){if(t.withCredentials&&e&&e.error===s.UNAUTHENTICATED)return yield r.setCredentials(null),Promise.reject(e);if(d===i||!e||"unreachable"!==e.error)return Promise.reject(e)}yield r.sleep(O.retryInterval)}return l}))()}wxCloudCallFunction(e,t){var r=this;return _((function*(){var n=null,o=null;try{var i,a="";try{a=yield wx.getRendererUserAgent()}catch(e){}var{result:u}=yield r.wxCloud.callFunction({name:"httpOverCallFunction",data:{url:e,method:t.method,headers:m({origin:"https://servicewechat.com","User-Agent":a},t.headers),body:t.body}});null!=u&&null!==(i=u.body)&&void 0!==i&&i.error_code?(o=null==u?void 0:u.body).error_uri=(0,l.y)(e):n=null==u?void 0:u.body}catch(t){o={error:s.UNREACHABLE,error_description:t.message,error_uri:(0,l.y)(e)}}if(o)throw o;return n}))()}getCredentials(){var e=this;return _((function*(){var t=yield e.localCredentials.getCredentials();return t?(E(t)&&(t=t&&"anonymous"===t.scope?e.anonymousSignInFunc?(yield e.anonymousSignInFunc(t))||(yield e.localCredentials.getCredentials()):yield e.anonymousSignIn(t):yield e.refreshToken(t)),t):e.unAuthenticatedError("credentials not found")}))()}getCredentialsSync(){return this.localCredentials.getStorageCredentialsSync()}getCredentialsAsync(){return this.getCredentials()}getScope(){var e=this;return _((function*(){var t=yield e.localCredentials.getCredentials();return t?t.scope:e.unAuthenticatedError("credentials not found")}))()}getGroups(){var e=this;return _((function*(){var t=yield e.localCredentials.getCredentials();return t?t.groups:e.unAuthenticatedError("credentials not found")}))()}refreshToken(e){var t=this;return _((function*(){return t.singlePromise.run("_refreshToken",_((function*(){if(!e||!e.refresh_token)return t.unAuthenticatedError("no refresh token found in credentials");try{var r=yield t.refreshTokenFunc(e.refresh_token,e);return yield t.localCredentials.setCredentials(r),r}catch(e){return e.error===s.INVALID_GRANT?(yield t.localCredentials.setCredentials(null),t.unAuthenticatedError(e.error_description)):Promise.reject(e)}})))}))()}checkRetry(e){var t=null;if(("number"!=typeof e||e<O.minRetry||e>O.maxRetry)&&(t={error:s.UNREACHABLE,error_description:"wrong options param: retry"}),t)throw t;return e}formatRetry(e,t){return void 0===e?t:this.checkRetry(e)}sleep(e){return _((function*(){return new Promise((t=>{setTimeout((()=>{t()}),e)}))}))()}anonymousSignIn(e){var t=this;return _((function*(){return t.singlePromise.run("_anonymous",_((function*(){if(!e||"anonymous"!==e.scope)return t.unAuthenticatedError("no anonymous in credentials");try{var r=yield t.request(n.AUTH_SIGN_IN_ANONYMOUSLY_URL,{method:"POST",withBasicAuth:!0,body:{}});return yield t.localCredentials.setCredentials(r),r}catch(e){return e.error===s.INVALID_GRANT?(yield t.localCredentials.setCredentials(null),t.unAuthenticatedError(e.error_description)):Promise.reject(e)}})))}))()}defaultRefreshTokenFunc(e,t){var r=this;return _((function*(){if(void 0===e||""===e)return r.unAuthenticatedError("refresh token not found");var i=n.AUTH_TOKEN_URL;return"v2"===(null==t?void 0:t.version)&&(i=o.AUTH_TOKEN_URL),m(m({},yield r.request(i,{method:"POST",body:{client_id:r.clientId,client_secret:r.clientSecret,grant_type:"refresh_token",refresh_token:e}})),{},{version:(null==t?void 0:t.version)||"v1"})}))()}getDeviceId(){var e=this;return _((function*(){if(e.deviceID)return e.deviceID;var t=yield e.storage.getItem(S);return"string"==typeof t&&t.length>=16&&t.length<=48||(t=c(),yield e.storage.setItem(S,t)),e.deviceID=t,t}))()}unAuthenticatedError(e){var t={error:s.UNAUTHENTICATED,error_description:e};return Promise.reject(t)}}function P(){var{wx:e}=globalThis;if(void 0===e)return!1;if(void 0===globalThis.Page)return!1;if(!e.getSystemInfoSync)return!1;if(!e.getStorageSync)return!1;if(!e.setStorageSync)return!1;if(!e.connectSocket)return!1;if(!e.request)return!1;try{if(!e.getSystemInfoSync())return!1;if("qq"===e.getSystemInfoSync().AppPlatform)return!1}catch(e){return!1}return!0}O.defaultRetry=2,O.minRetry=0,O.maxRetry=5,O.retryInterval=1e3;var C=!1;function A(){C=C||void 0!==typeof window&&"miniprogram"===window.__wxjs_environment}try{P()||(C=C||!!navigator.userAgent.match(/miniprogram/i)||"miniprogram"===window.__wxjs_environment,window&&window.WeixinJSBridge&&window.WeixinJSBridge.invoke?A():"undefined"!=typeof document&&document.addEventListener("WeixinJSBridgeReady",A,!1))}catch(ee){}function U(){return C}const L=class{constructor(e){if(this.params={},"string"==typeof e)this.parse(e);else if(e&&"object"==typeof e)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&this.append(t,e[t])}parse(e){e.split("&").forEach((e=>{var[t,r]=e.split("=").map(decodeURIComponent);this.append(t,r)}))}append(e,t){this.params[e]?this.params[e]=this.params[e].concat([t]):this.params[e]=[t]}get(e){return this.params[e]?this.params[e][0]:null}getAll(e){return this.params[e]||[]}delete(e){delete this.params[e]}has(e){return Object.prototype.hasOwnProperty.call(this.params,e)}set(e,t){this.params[e]=[t]}toString(){var e=this,t=[],r=function(r){Object.prototype.hasOwnProperty.call(e.params,r)&&e.params[r].forEach((e=>{t.push("".concat(encodeURIComponent(r),"=").concat(encodeURIComponent(e)))}))};for(var n in this.params)r(n);return t.join("&")}};function k(e,t,r,n,o,i,s){try{var a=e[i](s),u=a.value}catch(e){return void r(e)}a.done?t(u):Promise.resolve(u).then(n,o)}function N(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function s(e){k(i,n,o,s,a,"next",e)}function a(e){k(i,n,o,s,a,"throw",e)}s(void 0)}))}}class q{constructor(e){e.openURIWithCallback||(e.openURIWithCallback=this.getDefaultOpenURIWithCallback()),e.storage||(e.storage=I),this.config=e,this.tokenSectionName="captcha_".concat(e.clientId)}request(e,t){var r=this;return N((function*(){t||(t={}),t.method||(t.method="GET");var n,o="".concat(t.method,":").concat(e),i=e;t.withCaptcha&&(i=yield r.appendCaptchaTokenToURL(e,o,!1));try{n=yield r.config.request(i,t)}catch(n){return n.error===s.CAPTCHA_REQUIRED||n.error===s.CAPTCHA_INVALID?(e=yield r.appendCaptchaTokenToURL(e,o,n.error===s.CAPTCHA_INVALID),r.config.request(e,t)):Promise.reject(n)}return n}))()}getDefaultOpenURIWithCallback(){if(!P()&&!U()&&(window.location.search.indexOf("__captcha")>0&&(document.body.style.display="none"),null===document.getElementById("captcha_panel_wrap"))){var e=document.createElement("div");e.style.cssText="background-color: rgba(0, 0, 0, 0.7);position: fixed;left: 0px;right: 0px;top: 0px;bottom: 0px;padding: 9vw 0 0 0;display: none;z-index:100;",e.setAttribute("id","captcha_panel_wrap"),setTimeout((()=>{document.body.appendChild(e)}),0)}return this.defaultOpenURIWithCallback}defaultOpenURIWithCallback(e,t){return N((function*(){var{width:r="355px",height:n="355px"}=t||{};if(e.match(/^(data:.*)$/))return Promise.reject({error:s.UNIMPLEMENTED,error_description:"need to impl captcha data"});var o=document.getElementById("captcha_panel_wrap"),i=document.createElement("iframe");return o.innerHTML="",i.setAttribute("src",e),i.setAttribute("id","review-panel-iframe"),i.style.cssText="min-width:".concat(r,";display:block;height:").concat(n,";margin:0 auto;background-color: rgb(255, 255, 255);border: none;"),o.appendChild(i),o.style.display="block",new Promise(((e,t)=>{i.onload=function(){try{var r=window.location,n=i.contentWindow.location;if(n.host+n.pathname===r.host+r.pathname){o.style.display="none";var s=new L(n.search),a=s.get("captcha_token");return a?e({captcha_token:a,expires_in:Number(s.get("expires_in"))}):t({error:s.get("error"),error_description:s.get("error_description")})}o.style.display="block"}catch(e){o.style.display="block"}}}))}))()}getCaptchaToken(e,t){var r=this;return N((function*(){if(!e){var o=yield r.findCaptchaToken();if(o)return o}var i;if(P()||U()){var s=yield r.config.request(n.CAPTCHA_DATA_URL,{method:"POST",body:{state:t,redirect_uri:""},withCredentials:!1});i={url:"".concat(s.data,"?state=").concat(encodeURIComponent(t),"&token=").concat(encodeURIComponent(s.token))}}else{var a="".concat(window.location.origin+window.location.pathname,"?__captcha=on");if((i=yield r.config.request(n.GET_CAPTCHA_URL,{method:"POST",body:{client_id:r.config.clientId,redirect_uri:a,state:t},withCredentials:!1})).captcha_token){var u={captcha_token:i.captcha_token,expires_in:i.expires_in};return r.saveCaptchaToken(u),i.captcha_token}}var c=yield r.config.openURIWithCallback(i.url);return r.saveCaptchaToken(c),c.captcha_token}))()}appendCaptchaTokenToURL(e,t,r){var n=this;return N((function*(){var o=yield n.getCaptchaToken(r,t);return e.indexOf("?")>0?e+="&captcha_token=".concat(o):e+="?captcha_token=".concat(o),e}))()}saveCaptchaToken(e){var t=this;return N((function*(){e.expires_at=new Date(Date.now()+1e3*(e.expires_in-10));var r=JSON.stringify(e);yield t.config.storage.setItem(t.tokenSectionName,r)}))()}findCaptchaToken(){var e=this;return N((function*(){var t=yield e.config.storage.getItem(e.tokenSectionName);if(null!=t)try{var r=JSON.parse(t);return null!=r&&r.expires_at&&(r.expires_at=new Date(r.expires_at)),r.expires_at<new Date?null:r.captcha_token}catch(t){return yield e.config.storage.removeItem(e.tokenSectionName),null}return null}))()}}var D=["provider_redirect_uri","other_params"];function x(e,t,r,n,o,i,s){try{var a=e[i](s),u=a.value}catch(e){return void r(e)}a.done?t(u):Promise.resolve(u).then(n,o)}function j(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function s(e){x(i,n,o,s,a,"next",e)}function a(e){x(i,n,o,s,a,"throw",e)}s(void 0)}))}}function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function B(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?M(Object(r),!0).forEach((function(t){W(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function W(e,t,r){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function F(e){if(!globalThis.IS_MP_BUILD&&e)return r(171)}class V{static parseParamsToSearch(e){return Object.keys(e).forEach((t=>{e[t]||delete e[t]})),new L(e).toString()}constructor(e){var{request:t}=e,r=e.credentialsClient;if(!r){var n={apiOrigin:e.apiOrigin,clientId:e.clientId,storage:e.storage,env:e.env,baseRequest:e.baseRequest,anonymousSignInFunc:e.anonymousSignInFunc,wxCloud:e.wxCloud};r=new O(n)}if(!t){var o=r.request.bind(r),i=new q(B({clientId:e.clientId,request:o,storage:e.storage},e.captchaOptions));t=i.request.bind(i)}this.config={env:e.env,apiOrigin:e.apiOrigin,clientId:e.clientId,request:t,credentialsClient:r,storage:e.storage||I}}getParamsByVersion(e,t){var r,i=(0,l.I)(e),s=(null===(r={v2:o}[null==i?void 0:i.version])||void 0===r?void 0:r[t])||n[t];return i&&delete i.version,{params:i,url:s}}signIn(e){var t=this;return j((function*(){var r=e.version||"v1",n=t.getParamsByVersion(e,"AUTH_SIGN_IN_URL");n.params.query&&delete n.params.query;var o=yield t.getEncryptParams(n.params),i=yield t.config.request(n.url,{method:"POST",body:o});return yield t.config.credentialsClient.setCredentials(B(B({},i),{},{version:r})),Promise.resolve(i)}))()}signInAnonymously(){var e=arguments,t=this;return j((function*(){var r=e.length>0&&void 0!==e[0]?e[0]:{},o=e.length>1&&void 0!==e[1]&&e[1],i=yield t.config.request(n.AUTH_SIGN_IN_ANONYMOUSLY_URL,{method:"POST",body:r,useWxCloud:o});return yield t.config.credentialsClient.setCredentials(i),Promise.resolve(i)}))()}signUp(e){var t=this;return j((function*(){var r=yield t.config.request(n.AUTH_SIGN_UP_URL,{method:"POST",body:e});return yield t.config.credentialsClient.setCredentials(r),Promise.resolve(r)}))()}signOut(e){var t=this;return j((function*(){var r={};if(e){try{r=yield t.config.request(n.AUTH_SIGNOUT_URL,{method:"POST",withCredentials:!0,body:e})}catch(e){e.error!==s.UNAUTHENTICATED&&console.log("sign_out_error",e)}return yield t.config.credentialsClient.setCredentials(),r}var o=yield t.config.credentialsClient.getAccessToken(),i=yield t.config.request(n.AUTH_REVOKE_URL,{method:"POST",body:{token:o}});return yield t.config.credentialsClient.setCredentials(),Promise.resolve(i)}))()}revokeAllDevices(){var e=this;return j((function*(){yield e.config.request(n.AUTH_REVOKE_ALL_URL,{method:"DELETE",withCredentials:!0})}))()}revokeDevice(e){var t=this;return j((function*(){yield t.config.request(n.AUTHORIZED_DEVICES_DELETE_URL+e.device_id,{method:"DELETE",withCredentials:!0})}))()}getVerification(e,t){var r=this;return j((function*(){var o=!1;return("CUR_USER"===e.target||(yield r.hasLoginState()))&&(o=!0),r.config.request(n.VERIFICATION_URL,{method:"POST",body:e,withCaptcha:(null==t?void 0:t.withCaptcha)||!1,withCredentials:o})}))()}verify(e){var t=this;return j((function*(){var r=t.getParamsByVersion(e,"VERIFY_URL"),n=yield t.config.request(r.url,{method:"POST",body:r.params});return"v2"===(null==e?void 0:e.version)&&(yield t.config.credentialsClient.setCredentials(B(B({},n),{},{version:"v2"}))),n}))()}genProviderRedirectUri(e){var t=this;return j((function*(){var{provider_redirect_uri:r,other_params:o={}}=e,i=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.includes(r)||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,D);r&&!i.redirect_uri&&(i.redirect_uri=r);var s="".concat(n.PROVIDER_URI_URL,"?").concat(V.parseParamsToSearch(i));return Object.keys(o).forEach((e=>{var t=o[e];("sign_out_uri"!==e||(null==t?void 0:t.length)>0)&&(s+="&other_params[".concat(e,"]=").concat(encodeURIComponent(t)))})),t.config.request(s,{method:"GET"})}))()}grantProviderToken(e){var t=arguments,r=this;return j((function*(){var o=t.length>1&&void 0!==t[1]&&t[1];return r.config.request(n.PROVIDER_TOKEN_URL,{method:"POST",body:e,useWxCloud:o})}))()}patchProviderToken(e){var t=this;return j((function*(){return t.config.request(n.PROVIDER_TOKEN_URL,{method:"PATCH",body:e})}))()}signInWithProvider(e){var t=arguments,r=this;return j((function*(){var n=t.length>1&&void 0!==t[1]&&t[1],o=r.getParamsByVersion(e,"AUTH_SIGN_IN_WITH_PROVIDER_URL"),i=yield r.config.request(o.url,{method:"POST",body:o.params,useWxCloud:n});return yield r.config.credentialsClient.setCredentials(B(B({},i),{},{version:(null==e?void 0:e.version)||"v1"})),Promise.resolve(i)}))()}signInCustom(e){var t=this;return j((function*(){var r=yield t.config.request(n.AUTH_SIGN_IN_CUSTOM,{method:"POST",body:e});return yield t.config.credentialsClient.setCredentials(r),Promise.resolve(r)}))()}signInWithWechat(){var e=arguments,t=this;return j((function*(){var r=e.length>0&&void 0!==e[0]?e[0]:{},o=yield t.config.request(n.AUTH_SIGN_IN_WITH_WECHAT_URL,{method:"POST",body:r});return yield t.config.credentialsClient.setCredentials(o),Promise.resolve(o)}))()}bindWithProvider(e){var t=this;return j((function*(){return t.config.request(n.PROVIDER_BIND_URL,{method:"POST",body:e,withCredentials:!0})}))()}getUserProfile(e){var t=this;return j((function*(){return t.getUserInfo(e)}))()}getUserInfo(){var e=arguments,t=this;return j((function*(){var r,n=e.length>0&&void 0!==e[0]?e[0]:{},o=t.getParamsByVersion(n,"USER_ME_URL");if(null!==(r=o.params)&&void 0!==r&&r.query){var i=new L(o.params.query);o.url+="?".concat(i.toString())}var s=yield t.config.request(o.url,{method:"GET",withCredentials:!0});return s.sub&&(s.uid=s.sub),s}))()}getWedaUserInfo(){var e=this;return j((function*(){return yield e.config.request(n.WEDA_USER_URL,{method:"GET",withCredentials:!0})}))()}deleteMe(e){var t=this;return j((function*(){var r=t.getParamsByVersion(e,"USER_ME_URL"),n="".concat(r.url,"?").concat(V.parseParamsToSearch(r.params));return t.config.request(n,{method:"DELETE",withCredentials:!0})}))()}hasLoginState(){var e=this;return j((function*(){try{return yield e.config.credentialsClient.getAccessToken(),!0}catch(e){return!1}}))()}hasLoginStateSync(){return this.config.credentialsClient.getCredentialsSync()}getLoginState(){var e=this;return j((function*(){return e.config.credentialsClient.getCredentialsAsync()}))()}transByProvider(e){var t=this;return j((function*(){return t.config.request(n.USER_TRANS_BY_PROVIDER_URL,{method:"PATCH",body:e,withCredentials:!0})}))()}grantToken(e){var t=this;return j((function*(){var r=t.getParamsByVersion(e,"AUTH_TOKEN_URL");return t.config.request(r.url,{method:"POST",body:r.params})}))()}getProviders(){var e=this;return j((function*(){return e.config.request(n.PROVIDER_LIST,{method:"GET",withCredentials:!0})}))()}unbindProvider(e){var t=this;return j((function*(){return t.config.request("".concat(n.PROVIDER_UNBIND_URL,"/").concat(e.provider_id),{method:"DELETE",withCredentials:!0})}))()}checkPassword(e){var t=this;return j((function*(){return t.config.request("".concat(n.CHECK_PWD_URL),{method:"POST",withCredentials:!0,body:e})}))()}editContact(e){var t=this;return j((function*(){return t.config.request("".concat(n.BIND_CONTACT_URL),{method:"PATCH",withCredentials:!0,body:e})}))()}setPassword(e){var t=this;return j((function*(){return t.config.request("".concat(n.AUTH_SET_PASSWORD),{method:"PATCH",withCredentials:!0,body:e})}))()}updatePasswordByOld(e){var t=this;return j((function*(){var r=yield t.sudo({password:e.old_password});return t.setPassword({sudo_token:r.sudo_token,new_password:e.new_password})}))()}sudo(e){var t=this;return j((function*(){return t.config.request("".concat(n.SUDO_URL),{method:"POST",withCredentials:!0,body:e})}))()}sendVerificationCodeToCurrentUser(e){var t=this;return j((function*(){return e.target="CUR_USER",t.config.request(n.VERIFICATION_URL,{method:"POST",body:e,withCredentials:!0,withCaptcha:!0})}))()}changeBoundProvider(e){var t=this;return j((function*(){return t.config.request("".concat(n.PROVIDER_LIST,"/").concat(e.provider_id,"/trans"),{method:"POST",body:{provider_trans_token:e.trans_token},withCredentials:!0})}))()}setUserProfile(e){var t=this;return j((function*(){return t.config.request(n.USER_PRIFILE_URL,{method:"PATCH",body:e,withCredentials:!0})}))()}updateUserBasicInfo(e){var t=this;return j((function*(){return t.config.request(n.USER_BASIC_EDIT_URL,{method:"POST",withCredentials:!0,body:e})}))()}queryUserProfile(e){var t=this;return j((function*(){var r=new L(e);return t.config.request("".concat(n.USER_QUERY_URL,"?").concat(r.toString()),{method:"GET",withCredentials:!0})}))()}setCustomSignFunc(e){this.getCustomSignTicketFn=e}signInWithCustomTicket(){var e=this;return j((function*(){var t=e.getCustomSignTicketFn;if(!t)return Promise.reject({error:"failed_precondition",error_description:"please use setCustomSignFunc to set custom sign function"});var r=yield t();return e.signInCustom({provider_id:"custom",ticket:r})}))()}resetPassword(e){var t=this;return j((function*(){return t.config.request(n.AUTH_RESET_PASSWORD,{method:"POST",body:e})}))()}authorize(e){var t=this;return j((function*(){return t.config.request(n.AUTHORIZE_URL,{method:"POST",withCredentials:!0,body:e})}))()}authorizeDevice(e){var t=this;return j((function*(){return t.config.request(n.AUTHORIZE_DEVICE_URL,{method:"POST",withCredentials:!0,body:e})}))()}deviceAuthorize(e){var t=this;return j((function*(){return t.config.request(n.AUTH_GET_DEVICE_CODE,{method:"POST",body:e,withCredentials:!0})}))()}authorizeInfo(e){var t=this;return j((function*(){var r="".concat(n.AUTHORIZE_INFO_URL,"?").concat(V.parseParamsToSearch(e)),o=!0,i=!1;return(yield t.hasLoginState())&&(i=!0,o=!1),t.config.request(r,{method:"GET",withBasicAuth:o,withCredentials:i})}))()}checkUsername(e){var t=this;return j((function*(){return t.config.request(n.CHECK_USERNAME,{method:"GET",body:e,withCredentials:!0})}))()}checkIfUserExist(e){var t=this;return j((function*(){var r=new L(e);return t.config.request("".concat(n.CHECK_IF_USER_EXIST,"?").concat(r.toString()),{method:"GET"})}))()}loginScope(){var e=this;return j((function*(){return e.config.credentialsClient.getScope()}))()}loginGroups(){var e=this;return j((function*(){return e.config.credentialsClient.getGroups()}))()}refreshTokenForce(e){var t=this;return j((function*(){var r=yield t.config.credentialsClient.getCredentials();return yield t.config.credentialsClient.refreshToken(B(B({},r),{},{version:(null==e?void 0:e.version)||"v1"}))}))()}getCredentials(){var e=this;return j((function*(){return e.config.credentialsClient.getCredentials()}))()}getPublicKey(){var e=this;return j((function*(){return e.config.request(o.AUTH_PUBLIC_KEY,{method:"POST",body:{}})}))()}getEncryptParams(e){var t=this;return j((function*(){var{isEncrypt:r}=e;delete e.isEncrypt;var n=(0,l.I)(e),o=F(r);if(!o)return e;var i="",s="";try{var a=yield t.getPublicKey();if(i=a.public_key,s=a.public_key_thumbprint,!i||!s)throw a}catch(e){throw e}return{params:o.getEncryptInfo({publicKey:i,payload:n}),public_key_thumbprint:s}}))()}getProviderSubType(){var e=this;return j((function*(){return e.config.request(n.GET_PROVIDER_TYPE,{method:"POST",body:{provider_id:"weda"}})}))()}verifyCaptchaData(e){var t=this;return j((function*(){var{token:r,key:o}=e;return t.config.request(n.VERIFY_CAPTCHA_DATA_URL,{method:"POST",body:{token:r,key:o},withCredentials:!1})}))()}createCaptchaData(e){var t=this;return j((function*(){var{state:r,redirect_uri:o}=e;return t.config.request(n.CAPTCHA_DATA_URL,{method:"POST",body:{state:r,redirect_uri:o},withCredentials:!1})}))()}getMiniProgramCode(e){var t=this;return j((function*(){return t.config.request(n.GET_MINIPROGRAM_QRCODE,{method:"POST",body:e})}))()}getMiniProgramQrCodeStatus(e){var t=this;return j((function*(){return t.config.request(n.GET_MINIPROGRAM_QRCODE_STATUS,{method:"POST",body:e})}))()}getUserBehaviorLog(e){var t=this;return j((function*(){var r="".concat(n.GET_USER_BEHAVIOR_LOG,"?").concat({LOGIN:"query[action]=USER_LOGIN",MODIFY:"ne_query[action]=USER_LOGIN"}[e.type],"&limit=").concat(e.limit).concat(e.page_token?"&page_token=".concat(e.page_token):"");return t.config.request(r,{method:"GET",withCredentials:!0})}))()}modifyPassword(e){var t=this;return j((function*(){var r="",o="",i=F(!0);if(!i)throw new Error("do not support encrypt, a encrypt util required.");try{var s=yield t.getPublicKey();if(r=s.public_key,o=s.public_key_thumbprint,!r||!o)throw s}catch(e){throw e}var a=e.password?i.getEncryptInfo({publicKey:r,payload:e.password}):"",u=i.getEncryptInfo({publicKey:r,payload:e.new_password});return t.config.request(n.USER_BASIC_EDIT_URL,{method:"POST",withCredentials:!0,body:{user_id:e.user_id,encrypt_password:a,encrypt_new_password:u,public_key_thumbprint:o}})}))()}modifyPasswordWithoutLogin(e){var t=this;return j((function*(){var r="",n="",i=F(!0);if(!i)throw new Error("do not support encrypt, a encrypt util required.");try{var s=yield t.getPublicKey();if(r=s.public_key,n=s.public_key_thumbprint,!r||!n)throw s}catch(e){throw e}var a=e.password?i.getEncryptInfo({publicKey:r,payload:e.password}):"",u=i.getEncryptInfo({publicKey:r,payload:e.new_password});return t.config.request(o.AUTH_RESET_PASSWORD,{method:"POST",body:{username:e.username,password:a,new_password:u,public_key_thumbprint:n}})}))()}}function H(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function z(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?H(Object(r),!0).forEach((function(t){G(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):H(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function G(e,t,r){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class Y{constructor(e){var{apiOrigin:t,clientId:r,env:n,storage:o,request:i,baseRequest:s,anonymousSignInFunc:a,wxCloud:u}=e;this.oauth2client=new O({apiOrigin:t,clientId:r,env:n,storage:o,baseRequest:s||i,anonymousSignInFunc:a,wxCloud:u}),this.authApi=new V(z(z({credentialsClient:this.oauth2client},e),{},{request:i?this.oauth2client.request.bind(this.oauth2client):void 0}))}}var $=r(616),K=r(794),Q=r(141),Z=r(689),J=r(197),X=r(325);function ee(){}function te(e){return"object"==typeof e&&null!==e||"function"==typeof e}const re=ee;function ne(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0})}catch(e){}}const oe=Promise,ie=Promise.resolve.bind(oe),se=Promise.prototype.then,ae=Promise.reject.bind(oe),ue=ie;function ce(e){return new oe(e)}function le(e){return ce((t=>t(e)))}function he(e){return ae(e)}function de(e,t,r){return se.call(e,t,r)}function fe(e,t,r){de(de(e,t,r),void 0,re)}function pe(e,t){fe(e,t)}function ve(e,t){fe(e,void 0,t)}function me(e,t,r){return de(e,t,r)}function ge(e){de(e,void 0,re)}let ye=e=>{if("function"==typeof queueMicrotask)ye=queueMicrotask;else{const e=le(void 0);ye=t=>de(e,t)}return ye(e)};function _e(e,t,r){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function be(e,t,r){try{return le(_e(e,t,r))}catch(e){return he(e)}}class we{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){const t=this._back;let r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){const e=this._front;let t=e;const r=this._cursor;let n=r+1;const o=e._elements,i=o[r];return 16384===n&&(t=e._next,n=0),--this._size,this._cursor=n,e!==t&&(this._front=t),o[r]=void 0,i}forEach(e){let t=this._cursor,r=this._front,n=r._elements;for(;!(t===n.length&&void 0===r._next||t===n.length&&(r=r._next,n=r._elements,t=0,0===n.length));)e(n[t]),++t}peek(){const e=this._front,t=this._cursor;return e._elements[t]}}const Se=Symbol("[[AbortSteps]]"),Te=Symbol("[[ErrorSteps]]"),Ie=Symbol("[[CancelSteps]]"),Ee=Symbol("[[PullSteps]]"),Re=Symbol("[[ReleaseSteps]]");function Oe(e,t){e._ownerReadableStream=t,t._reader=e,"readable"===t._state?Ue(e):"closed"===t._state?function(e){Ue(e),Ne(e)}(e):Le(e,t._storedError)}function Pe(e,t){return xn(e._ownerReadableStream,t)}function Ce(e){const t=e._ownerReadableStream;"readable"===t._state?ke(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e){Le(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness"))}(e),t._readableStreamController[Re](),t._reader=void 0,e._ownerReadableStream=void 0}function Ae(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function Ue(e){e._closedPromise=ce(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r}))}function Le(e,t){Ue(e),ke(e,t)}function ke(e,t){void 0!==e._closedPromise_reject&&(ge(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function Ne(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}const qe=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},De=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function xe(e,t){if(void 0!==e&&"object"!=typeof(r=e)&&"function"!=typeof r)throw new TypeError(`${t} is not an object.`);var r}function je(e,t){if("function"!=typeof e)throw new TypeError(`${t} is not a function.`)}function Me(e,t){if(!function(e){return"object"==typeof e&&null!==e||"function"==typeof e}(e))throw new TypeError(`${t} is not an object.`)}function Be(e,t,r){if(void 0===e)throw new TypeError(`Parameter ${t} is required in '${r}'.`)}function We(e,t,r){if(void 0===e)throw new TypeError(`${t} is required in '${r}'.`)}function Fe(e){return Number(e)}function Ve(e){return 0===e?0:e}function He(e,t){const r=Number.MAX_SAFE_INTEGER;let n=Number(e);if(n=Ve(n),!qe(n))throw new TypeError(`${t} is not a finite number`);if(n=function(e){return Ve(De(e))}(n),n<0||n>r)throw new TypeError(`${t} is outside the accepted range of 0 to ${r}, inclusive`);return qe(n)&&0!==n?n:0}function ze(e,t){if(!qn(e))throw new TypeError(`${t} is not a ReadableStream.`)}function Ge(e){return new Ze(e)}function Ye(e,t){e._reader._readRequests.push(t)}function $e(e,t,r){const n=e._reader._readRequests.shift();r?n._closeSteps():n._chunkSteps(t)}function Ke(e){return e._reader._readRequests.length}function Qe(e){const t=e._reader;return void 0!==t&&!!Je(t)}class Ze{constructor(e){if(Be(e,1,"ReadableStreamDefaultReader"),ze(e,"First parameter"),Dn(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");Oe(this,e),this._readRequests=new we}get closed(){return Je(this)?this._closedPromise:he(tt("closed"))}cancel(e=void 0){return Je(this)?void 0===this._ownerReadableStream?he(Ae("cancel")):Pe(this,e):he(tt("cancel"))}read(){if(!Je(this))return he(tt("read"));if(void 0===this._ownerReadableStream)return he(Ae("read from"));let e,t;const r=ce(((r,n)=>{e=r,t=n}));return Xe(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!Je(this))throw tt("releaseLock");void 0!==this._ownerReadableStream&&function(e){Ce(e),et(e,new TypeError("Reader was released"))}(this)}}function Je(e){return!!te(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof Ze}function Xe(e,t){const r=e._ownerReadableStream;r._disturbed=!0,"closed"===r._state?t._closeSteps():"errored"===r._state?t._errorSteps(r._storedError):r._readableStreamController[Ee](t)}function et(e,t){const r=e._readRequests;e._readRequests=new we,r.forEach((e=>{e._errorSteps(t)}))}function tt(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}var rt,nt,ot;function it(e){return e.slice()}function st(e,t,r,n,o){new Uint8Array(e).set(new Uint8Array(r,n,o),t)}Object.defineProperties(Ze.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),ne(Ze.prototype.cancel,"cancel"),ne(Ze.prototype.read,"read"),ne(Ze.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Ze.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});let at=e=>(at="function"==typeof e.transfer?e=>e.transfer():"function"==typeof structuredClone?e=>structuredClone(e,{transfer:[e]}):e=>e,at(e)),ut=e=>(ut="boolean"==typeof e.detached?e=>e.detached:e=>0===e.byteLength,ut(e));function ct(e,t,r){if(e.slice)return e.slice(t,r);const n=r-t,o=new ArrayBuffer(n);return st(o,0,e,t,n),o}function lt(e,t){const r=e[t];if(null!=r){if("function"!=typeof r)throw new TypeError(`${String(t)} is not a function`);return r}}function ht(e){try{const t=e.done,r=e.value;return de(ue(r),(e=>({done:t,value:e})))}catch(e){return he(e)}}const dt=null!==(ot=null!==(rt=Symbol.asyncIterator)&&void 0!==rt?rt:null===(nt=Symbol.for)||void 0===nt?void 0:nt.call(Symbol,"Symbol.asyncIterator"))&&void 0!==ot?ot:"@@asyncIterator";function ft(e,t="sync",r){if(void 0===r)if("async"===t){if(void 0===(r=lt(e,dt)))return function(e){const t={next(){let t;try{t=pt(e)}catch(e){return he(e)}return ht(t)},return(t){let r;try{const n=lt(e.iterator,"return");if(void 0===n)return le({done:!0,value:t});r=_e(n,e.iterator,[t])}catch(e){return he(e)}return te(r)?ht(r):he(new TypeError("The iterator.return() method must return an object"))}};return{iterator:t,nextMethod:t.next,done:!1}}(ft(e,"sync",lt(e,Symbol.iterator)))}else r=lt(e,Symbol.iterator);if(void 0===r)throw new TypeError("The object is not iterable");const n=_e(r,e,[]);if(!te(n))throw new TypeError("The iterator method must return an object");return{iterator:n,nextMethod:n.next,done:!1}}function pt(e){const t=_e(e.nextMethod,e.iterator,[]);if(!te(t))throw new TypeError("The iterator.next() method must return an object");return t}class vt{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){const e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?me(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){const t=()=>this._returnSteps(e);return this._ongoingPromise?me(this._ongoingPromise,t,t):t()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});const e=this._reader;let t,r;const n=ce(((e,n)=>{t=e,r=n}));return Xe(e,{_chunkSteps:e=>{this._ongoingPromise=void 0,ye((()=>t({value:e,done:!1})))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,Ce(e),t({value:void 0,done:!0})},_errorSteps:t=>{this._ongoingPromise=void 0,this._isFinished=!0,Ce(e),r(t)}}),n}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;const t=this._reader;if(!this._preventCancel){const r=Pe(t,e);return Ce(t),me(r,(()=>({value:e,done:!0})))}return Ce(t),le({value:e,done:!0})}}const mt={next(){return gt(this)?this._asyncIteratorImpl.next():he(yt("next"))},return(e){return gt(this)?this._asyncIteratorImpl.return(e):he(yt("return"))},[dt](){return this}};function gt(e){if(!te(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof vt}catch(e){return!1}}function yt(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}Object.defineProperty(mt,dt,{enumerable:!1});const _t=Number.isNaN||function(e){return e!=e};function bt(e){const t=ct(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function wt(e){const t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function St(e,t,r){if("number"!=typeof(n=r)||_t(n)||n<0||r===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");var n;e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function Tt(e){e._queue=new we,e._queueTotalSize=0}function It(e){return e===DataView}class Et{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!Pt(this))throw Xt("view");return this._view}respond(e){if(!Pt(this))throw Xt("respond");if(Be(e,1,"respond"),e=He(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(ut(this._view.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");Qt(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!Pt(this))throw Xt("respondWithNewView");if(Be(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(ut(e.buffer))throw new TypeError("The given view's buffer has been detached and so cannot be used as a response");Zt(this._associatedReadableByteStreamController,e)}}Object.defineProperties(Et.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),ne(Et.prototype.respond,"respond"),ne(Et.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Et.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class Rt{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!Ot(this))throw er("byobRequest");return $t(this)}get desiredSize(){if(!Ot(this))throw er("desiredSize");return Kt(this)}close(){if(!Ot(this))throw er("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");const e=this._controlledReadableByteStream._state;if("readable"!==e)throw new TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);Ht(this)}enqueue(e){if(!Ot(this))throw er("enqueue");if(Be(e,1,"enqueue"),!ArrayBuffer.isView(e))throw new TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");const t=this._controlledReadableByteStream._state;if("readable"!==t)throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);zt(this,e)}error(e=void 0){if(!Ot(this))throw er("error");Gt(this,e)}[Ie](e){At(this),Tt(this);const t=this._cancelAlgorithm(e);return Vt(this),t}[Ee](e){const t=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void Yt(this,e);const r=this._autoAllocateChunkSize;if(void 0!==r){let n;try{n=new ArrayBuffer(r)}catch(t){return void e._errorSteps(t)}const o={buffer:n,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}Ye(t,e),Ct(this)}[Re](){if(this._pendingPullIntos.length>0){const e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new we,this._pendingPullIntos.push(e)}}}function Ot(e){return!!te(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof Rt}function Pt(e){return!!te(e)&&!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof Et}function Ct(e){const t=function(e){const t=e._controlledReadableByteStream;return"readable"===t._state&&(!e._closeRequested&&(!!e._started&&(!!(Qe(t)&&Ke(t)>0)||(!!(ir(t)&&or(t)>0)||Kt(e)>0))))}(e);t&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,fe(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,Ct(e)),null)),(t=>(Gt(e,t),null)))))}function At(e){Mt(e),e._pendingPullIntos=new we}function Ut(e,t){let r=!1;"closed"===e._state&&(r=!0);const n=Lt(t);"default"===t.readerType?$e(e,n,r):function(e,t,r){const n=e._reader._readIntoRequests.shift();r?n._closeSteps(t):n._chunkSteps(t)}(e,n,r)}function Lt(e){const t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function kt(e,t,r,n){e._queue.push({buffer:t,byteOffset:r,byteLength:n}),e._queueTotalSize+=n}function Nt(e,t,r,n){let o;try{o=ct(t,r,r+n)}catch(t){throw Gt(e,t),t}kt(e,o,0,n)}function qt(e,t){t.bytesFilled>0&&Nt(e,t.buffer,t.byteOffset,t.bytesFilled),Ft(e)}function Dt(e,t){const r=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),n=t.bytesFilled+r;let o=r,i=!1;const s=n-n%t.elementSize;s>=t.minimumFill&&(o=s-t.bytesFilled,i=!0);const a=e._queue;for(;o>0;){const r=a.peek(),n=Math.min(o,r.byteLength),i=t.byteOffset+t.bytesFilled;st(t.buffer,i,r.buffer,r.byteOffset,n),r.byteLength===n?a.shift():(r.byteOffset+=n,r.byteLength-=n),e._queueTotalSize-=n,xt(0,n,t),o-=n}return i}function xt(e,t,r){r.bytesFilled+=t}function jt(e){0===e._queueTotalSize&&e._closeRequested?(Vt(e),jn(e._controlledReadableByteStream)):Ct(e)}function Mt(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function Bt(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;const t=e._pendingPullIntos.peek();Dt(e,t)&&(Ft(e),Ut(e._controlledReadableByteStream,t))}}function Wt(e,t){const r=e._pendingPullIntos.peek();Mt(e),"closed"===e._controlledReadableByteStream._state?function(e,t){"none"===t.readerType&&Ft(e);const r=e._controlledReadableByteStream;if(ir(r))for(;or(r)>0;)Ut(r,Ft(e))}(e,r):function(e,t,r){if(xt(0,t,r),"none"===r.readerType)return qt(e,r),void Bt(e);if(r.bytesFilled<r.minimumFill)return;Ft(e);const n=r.bytesFilled%r.elementSize;if(n>0){const t=r.byteOffset+r.bytesFilled;Nt(e,r.buffer,t-n,n)}r.bytesFilled-=n,Ut(e._controlledReadableByteStream,r),Bt(e)}(e,t,r),Ct(e)}function Ft(e){return e._pendingPullIntos.shift()}function Vt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function Ht(e){const t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state)if(e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();if(t.bytesFilled%t.elementSize!=0){const t=new TypeError("Insufficient bytes to fill elements in the given buffer");throw Gt(e,t),t}}Vt(e),jn(t)}}function zt(e,t){const r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;const{buffer:n,byteOffset:o,byteLength:i}=t;if(ut(n))throw new TypeError("chunk's buffer is detached and so cannot be enqueued");const s=at(n);if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();if(ut(t.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");Mt(e),t.buffer=at(t.buffer),"none"===t.readerType&&qt(e,t)}Qe(r)?(function(e){const t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;Yt(e,t._readRequests.shift())}}(e),0===Ke(r)?kt(e,s,o,i):(e._pendingPullIntos.length>0&&Ft(e),$e(r,new Uint8Array(s,o,i),!1))):ir(r)?(kt(e,s,o,i),Bt(e)):kt(e,s,o,i),Ct(e)}function Gt(e,t){const r=e._controlledReadableByteStream;"readable"===r._state&&(At(e),Tt(e),Vt(e),Mn(r,t))}function Yt(e,t){const r=e._queue.shift();e._queueTotalSize-=r.byteLength,jt(e);const n=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(n)}function $t(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),n=Object.create(Et.prototype);!function(e,t,r){e._associatedReadableByteStreamController=t,e._view=r}(n,e,r),e._byobRequest=n}return e._byobRequest}function Kt(e){const t=e._controlledReadableByteStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function Qt(e,t){const r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===t)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw new RangeError("bytesWritten out of range")}r.buffer=at(r.buffer),Wt(e,t)}function Zt(e,t){const r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===t.byteLength)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");const n=t.byteLength;r.buffer=at(t.buffer),Wt(e,n)}function Jt(e,t,r,n,o,i,s){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,Tt(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=i,t._pullAlgorithm=n,t._cancelAlgorithm=o,t._autoAllocateChunkSize=s,t._pendingPullIntos=new we,e._readableStreamController=t,fe(le(r()),(()=>(t._started=!0,Ct(t),null)),(e=>(Gt(t,e),null)))}function Xt(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function er(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function tr(e,t){if("byob"!=(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function rr(e){return new sr(e)}function nr(e,t){e._reader._readIntoRequests.push(t)}function or(e){return e._reader._readIntoRequests.length}function ir(e){const t=e._reader;return void 0!==t&&!!ar(t)}Object.defineProperties(Rt.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),ne(Rt.prototype.close,"close"),ne(Rt.prototype.enqueue,"enqueue"),ne(Rt.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Rt.prototype,Symbol.toStringTag,{value:"ReadableByteStreamController",configurable:!0});class sr{constructor(e){if(Be(e,1,"ReadableStreamBYOBReader"),ze(e,"First parameter"),Dn(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!Ot(e._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");Oe(this,e),this._readIntoRequests=new we}get closed(){return ar(this)?this._closedPromise:he(lr("closed"))}cancel(e=void 0){return ar(this)?void 0===this._ownerReadableStream?he(Ae("cancel")):Pe(this,e):he(lr("cancel"))}read(e,t={}){if(!ar(this))return he(lr("read"));if(!ArrayBuffer.isView(e))return he(new TypeError("view must be an array buffer view"));if(0===e.byteLength)return he(new TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return he(new TypeError("view's buffer must have non-zero byteLength"));if(ut(e.buffer))return he(new TypeError("view's buffer has been detached"));let r;try{r=function(e,t){var r;return xe(e,t),{min:He(null!==(r=null==e?void 0:e.min)&&void 0!==r?r:1,`${t} has member 'min' that`)}}(t,"options")}catch(e){return he(e)}const n=r.min;if(0===n)return he(new TypeError("options.min must be greater than 0"));if(function(e){return It(e.constructor)}(e)){if(n>e.byteLength)return he(new RangeError("options.min must be less than or equal to view's byteLength"))}else if(n>e.length)return he(new RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return he(Ae("read from"));let o,i;const s=ce(((e,t)=>{o=e,i=t}));return ur(this,e,n,{_chunkSteps:e=>o({value:e,done:!1}),_closeSteps:e=>o({value:e,done:!0}),_errorSteps:e=>i(e)}),s}releaseLock(){if(!ar(this))throw lr("releaseLock");void 0!==this._ownerReadableStream&&function(e){Ce(e),cr(e,new TypeError("Reader was released"))}(this)}}function ar(e){return!!te(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof sr}function ur(e,t,r,n){const o=e._ownerReadableStream;o._disturbed=!0,"errored"===o._state?n._errorSteps(o._storedError):function(e,t,r,n){const o=e._controlledReadableByteStream,i=t.constructor,s=function(e){return It(e)?1:e.BYTES_PER_ELEMENT}(i),{byteOffset:a,byteLength:u}=t,c=r*s;let l;try{l=at(t.buffer)}catch(e){return void n._errorSteps(e)}const h={buffer:l,bufferByteLength:l.byteLength,byteOffset:a,byteLength:u,bytesFilled:0,minimumFill:c,elementSize:s,viewConstructor:i,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(h),void nr(o,n);if("closed"!==o._state){if(e._queueTotalSize>0){if(Dt(e,h)){const t=Lt(h);return jt(e),void n._chunkSteps(t)}if(e._closeRequested){const t=new TypeError("Insufficient bytes to fill elements in the given buffer");return Gt(e,t),void n._errorSteps(t)}}e._pendingPullIntos.push(h),nr(o,n),Ct(e)}else{const e=new i(h.buffer,h.byteOffset,0);n._closeSteps(e)}}(o._readableStreamController,t,r,n)}function cr(e,t){const r=e._readIntoRequests;e._readIntoRequests=new we,r.forEach((e=>{e._errorSteps(t)}))}function lr(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function hr(e,t){const{highWaterMark:r}=e;if(void 0===r)return t;if(_t(r)||r<0)throw new RangeError("Invalid highWaterMark");return r}function dr(e){const{size:t}=e;return t||(()=>1)}function fr(e,t){xe(e,t);const r=null==e?void 0:e.highWaterMark,n=null==e?void 0:e.size;return{highWaterMark:void 0===r?void 0:Fe(r),size:void 0===n?void 0:pr(n,`${t} has member 'size' that`)}}function pr(e,t){return je(e,t),t=>Fe(e(t))}function vr(e,t,r){return je(e,r),r=>be(e,t,[r])}function mr(e,t,r){return je(e,r),()=>be(e,t,[])}function gr(e,t,r){return je(e,r),r=>_e(e,t,[r])}function yr(e,t,r){return je(e,r),(r,n)=>be(e,t,[r,n])}function _r(e,t){if(!Ir(e))throw new TypeError(`${t} is not a WritableStream.`)}Object.defineProperties(sr.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),ne(sr.prototype.cancel,"cancel"),ne(sr.prototype.read,"read"),ne(sr.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(sr.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});const br="function"==typeof AbortController;class wr{constructor(e={},t={}){void 0===e?e=null:Me(e,"First parameter");const r=fr(t,"Second parameter"),n=function(e,t){xe(e,t);const r=null==e?void 0:e.abort,n=null==e?void 0:e.close,o=null==e?void 0:e.start,i=null==e?void 0:e.type,s=null==e?void 0:e.write;return{abort:void 0===r?void 0:vr(r,e,`${t} has member 'abort' that`),close:void 0===n?void 0:mr(n,e,`${t} has member 'close' that`),start:void 0===o?void 0:gr(o,e,`${t} has member 'start' that`),write:void 0===s?void 0:yr(s,e,`${t} has member 'write' that`),type:i}}(e,"First parameter");if(Tr(this),void 0!==n.type)throw new RangeError("Invalid type is specified");const o=dr(r);!function(e,t,r,n){const o=Object.create(Wr.prototype);let i,s,a,u;i=void 0!==t.start?()=>t.start(o):()=>{},s=void 0!==t.write?e=>t.write(e,o):()=>le(void 0),a=void 0!==t.close?()=>t.close():()=>le(void 0),u=void 0!==t.abort?e=>t.abort(e):()=>le(void 0),Vr(e,o,i,s,a,u,r,n)}(this,n,hr(r,1),o)}get locked(){if(!Ir(this))throw Qr("locked");return Er(this)}abort(e=void 0){return Ir(this)?Er(this)?he(new TypeError("Cannot abort a stream that already has a writer")):Rr(this,e):he(Qr("abort"))}close(){return Ir(this)?Er(this)?he(new TypeError("Cannot close a stream that already has a writer")):Ur(this)?he(new TypeError("Cannot close an already-closing stream")):Or(this):he(Qr("close"))}getWriter(){if(!Ir(this))throw Qr("getWriter");return Sr(this)}}function Sr(e){return new Nr(e)}function Tr(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new we,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function Ir(e){return!!te(e)&&!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof wr}function Er(e){return void 0!==e._writer}function Rr(e,t){var r;if("closed"===e._state||"errored"===e._state)return le(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort(t);const n=e._state;if("closed"===n||"errored"===n)return le(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let o=!1;"erroring"===n&&(o=!0,t=void 0);const i=ce(((r,n)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:n,_reason:t,_wasAlreadyErroring:o}}));return e._pendingAbortRequest._promise=i,o||Cr(e,t),i}function Or(e){const t=e._state;if("closed"===t||"errored"===t)return he(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));const r=ce(((t,r)=>{const n={_resolve:t,_reject:r};e._closeRequest=n})),n=e._writer;var o;return void 0!==n&&e._backpressure&&"writable"===t&&cn(n),St(o=e._writableStreamController,Br,0),Gr(o),r}function Pr(e,t){"writable"!==e._state?Ar(e):Cr(e,t)}function Cr(e,t){const r=e._writableStreamController;e._state="erroring",e._storedError=t;const n=e._writer;void 0!==n&&xr(n,t),!function(e){return void 0!==e._inFlightWriteRequest||void 0!==e._inFlightCloseRequest}(e)&&r._started&&Ar(e)}function Ar(e){e._state="errored",e._writableStreamController[Te]();const t=e._storedError;if(e._writeRequests.forEach((e=>{e._reject(t)})),e._writeRequests=new we,void 0===e._pendingAbortRequest)return void Lr(e);const r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring)return r._reject(t),void Lr(e);fe(e._writableStreamController[Se](r._reason),(()=>(r._resolve(),Lr(e),null)),(t=>(r._reject(t),Lr(e),null)))}function Ur(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function Lr(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);const t=e._writer;void 0!==t&&rn(t,e._storedError)}function kr(e,t){const r=e._writer;void 0!==r&&t!==e._backpressure&&(t?function(e){on(e)}(r):cn(r)),e._backpressure=t}Object.defineProperties(wr.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),ne(wr.prototype.abort,"abort"),ne(wr.prototype.close,"close"),ne(wr.prototype.getWriter,"getWriter"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(wr.prototype,Symbol.toStringTag,{value:"WritableStream",configurable:!0});class Nr{constructor(e){if(Be(e,1,"WritableStreamDefaultWriter"),_r(e,"First parameter"),Er(e))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;const t=e._state;if("writable"===t)!Ur(e)&&e._backpressure?on(this):an(this),en(this);else if("erroring"===t)sn(this,e._storedError),en(this);else if("closed"===t)an(this),en(this),nn(this);else{const t=e._storedError;sn(this,t),tn(this,t)}}get closed(){return qr(this)?this._closedPromise:he(Jr("closed"))}get desiredSize(){if(!qr(this))throw Jr("desiredSize");if(void 0===this._ownerWritableStream)throw Xr("desiredSize");return function(e){const t=e._ownerWritableStream,r=t._state;return"errored"===r||"erroring"===r?null:"closed"===r?0:zr(t._writableStreamController)}(this)}get ready(){return qr(this)?this._readyPromise:he(Jr("ready"))}abort(e=void 0){return qr(this)?void 0===this._ownerWritableStream?he(Xr("abort")):function(e,t){return Rr(e._ownerWritableStream,t)}(this,e):he(Jr("abort"))}close(){if(!qr(this))return he(Jr("close"));const e=this._ownerWritableStream;return void 0===e?he(Xr("close")):Ur(e)?he(new TypeError("Cannot close an already-closing stream")):Dr(this)}releaseLock(){if(!qr(this))throw Jr("releaseLock");void 0!==this._ownerWritableStream&&jr(this)}write(e=void 0){return qr(this)?void 0===this._ownerWritableStream?he(Xr("write to")):Mr(this,e):he(Jr("write"))}}function qr(e){return!!te(e)&&!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof Nr}function Dr(e){return Or(e._ownerWritableStream)}function xr(e,t){"pending"===e._readyPromiseState?un(e,t):function(e,t){sn(e,t)}(e,t)}function jr(e){const t=e._ownerWritableStream,r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");xr(e,r),function(e,t){"pending"===e._closedPromiseState?rn(e,t):function(e,t){tn(e,t)}(e,t)}(e,r),t._writer=void 0,e._ownerWritableStream=void 0}function Mr(e,t){const r=e._ownerWritableStream,n=r._writableStreamController,o=function(e,t){try{return e._strategySizeAlgorithm(t)}catch(t){return Yr(e,t),1}}(n,t);if(r!==e._ownerWritableStream)return he(Xr("write to"));const i=r._state;if("errored"===i)return he(r._storedError);if(Ur(r)||"closed"===i)return he(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===i)return he(r._storedError);const s=function(e){return ce(((t,r)=>{const n={_resolve:t,_reject:r};e._writeRequests.push(n)}))}(r);return function(e,t,r){try{St(e,t,r)}catch(t){return void Yr(e,t)}const n=e._controlledWritableStream;Ur(n)||"writable"!==n._state||kr(n,$r(e)),Gr(e)}(n,t,o),s}Object.defineProperties(Nr.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),ne(Nr.prototype.abort,"abort"),ne(Nr.prototype.close,"close"),ne(Nr.prototype.releaseLock,"releaseLock"),ne(Nr.prototype.write,"write"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Nr.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});const Br={};class Wr{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!Fr(this))throw Zr("abortReason");return this._abortReason}get signal(){if(!Fr(this))throw Zr("signal");if(void 0===this._abortController)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(e=void 0){if(!Fr(this))throw Zr("error");"writable"===this._controlledWritableStream._state&&Kr(this,e)}[Se](e){const t=this._abortAlgorithm(e);return Hr(this),t}[Te](){Tt(this)}}function Fr(e){return!!te(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof Wr}function Vr(e,t,r,n,o,i,s,a){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,Tt(t),t._abortReason=void 0,t._abortController=function(){if(br)return new AbortController}(),t._started=!1,t._strategySizeAlgorithm=a,t._strategyHWM=s,t._writeAlgorithm=n,t._closeAlgorithm=o,t._abortAlgorithm=i;const u=$r(t);kr(e,u),fe(le(r()),(()=>(t._started=!0,Gr(t),null)),(r=>(t._started=!0,Pr(e,r),null)))}function Hr(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function zr(e){return e._strategyHWM-e._queueTotalSize}function Gr(e){const t=e._controlledWritableStream;if(!e._started)return;if(void 0!==t._inFlightWriteRequest)return;if("erroring"===t._state)return void Ar(t);if(0===e._queue.length)return;const r=e._queue.peek().value;r===Br?function(e){const t=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(t),wt(e);const r=e._closeAlgorithm();Hr(e),fe(r,(()=>(function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";const t=e._writer;void 0!==t&&nn(t)}(t),null)),(e=>(function(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),Pr(e,t)}(t,e),null)))}(e):function(e,t){const r=e._controlledWritableStream;!function(e){e._inFlightWriteRequest=e._writeRequests.shift()}(r),fe(e._writeAlgorithm(t),(()=>{!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(r);const t=r._state;if(wt(e),!Ur(r)&&"writable"===t){const t=$r(e);kr(r,t)}return Gr(e),null}),(t=>("writable"===r._state&&Hr(e),function(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,Pr(e,t)}(r,t),null)))}(e,r)}function Yr(e,t){"writable"===e._controlledWritableStream._state&&Kr(e,t)}function $r(e){return zr(e)<=0}function Kr(e,t){const r=e._controlledWritableStream;Hr(e),Cr(r,t)}function Qr(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function Zr(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function Jr(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function Xr(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function en(e){e._closedPromise=ce(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"}))}function tn(e,t){en(e),rn(e,t)}function rn(e,t){void 0!==e._closedPromise_reject&&(ge(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function nn(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function on(e){e._readyPromise=ce(((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r})),e._readyPromiseState="pending"}function sn(e,t){on(e),un(e,t)}function an(e){on(e),cn(e)}function un(e,t){void 0!==e._readyPromise_reject&&(ge(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function cn(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(Wr.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Wr.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});const ln="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0,hn=function(){const e=null==ln?void 0:ln.DOMException;return function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;if("DOMException"!==e.name)return!1;try{return new e,!0}catch(e){return!1}}(e)?e:void 0}()||function(){const e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return ne(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}();function dn(e,t,r,n,o,i){const s=Ge(e),a=Sr(t);e._disturbed=!0;let u=!1,c=le(void 0);return ce(((l,h)=>{let d;if(void 0!==i){if(d=()=>{const r=void 0!==i.reason?i.reason:new hn("Aborted","AbortError"),s=[];n||s.push((()=>"writable"===t._state?Rr(t,r):le(void 0))),o||s.push((()=>"readable"===e._state?xn(e,r):le(void 0))),y((()=>Promise.all(s.map((e=>e())))),!0,r)},i.aborted)return void d();i.addEventListener("abort",d)}var f,p,v;if(g(e,s._closedPromise,(e=>(n?_(!0,e):y((()=>Rr(t,e)),!0,e),null))),g(t,a._closedPromise,(t=>(o?_(!0,t):y((()=>xn(e,t)),!0,t),null))),f=e,p=s._closedPromise,v=()=>(r?_():y((()=>function(e){const t=e._ownerWritableStream,r=t._state;return Ur(t)||"closed"===r?le(void 0):"errored"===r?he(t._storedError):Dr(e)}(a))),null),"closed"===f._state?v():pe(p,v),Ur(t)||"closed"===t._state){const t=new TypeError("the destination writable stream closed before all data could be piped to it");o?_(!0,t):y((()=>xn(e,t)),!0,t)}function m(){const e=c;return de(c,(()=>e!==c?m():void 0))}function g(e,t,r){"errored"===e._state?r(e._storedError):ve(t,r)}function y(e,r,n){function o(){return fe(e(),(()=>b(r,n)),(e=>b(!0,e))),null}u||(u=!0,"writable"!==t._state||Ur(t)?o():pe(m(),o))}function _(e,r){u||(u=!0,"writable"!==t._state||Ur(t)?b(e,r):pe(m(),(()=>b(e,r))))}function b(e,t){return jr(a),Ce(s),void 0!==i&&i.removeEventListener("abort",d),e?h(t):l(void 0),null}ge(ce(((e,t)=>{!function r(n){n?e():de(u?le(!0):de(a._readyPromise,(()=>ce(((e,t)=>{Xe(s,{_chunkSteps:t=>{c=de(Mr(a,t),void 0,ee),e(!1)},_closeSteps:()=>e(!0),_errorSteps:t})})))),r,t)}(!1)})))}))}class fn{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!pn(this))throw In("desiredSize");return wn(this)}close(){if(!pn(this))throw In("close");if(!Sn(this))throw new TypeError("The stream is not in a state that permits close");yn(this)}enqueue(e=void 0){if(!pn(this))throw In("enqueue");if(!Sn(this))throw new TypeError("The stream is not in a state that permits enqueue");return _n(this,e)}error(e=void 0){if(!pn(this))throw In("error");bn(this,e)}[Ie](e){Tt(this);const t=this._cancelAlgorithm(e);return gn(this),t}[Ee](e){const t=this._controlledReadableStream;if(this._queue.length>0){const r=wt(this);this._closeRequested&&0===this._queue.length?(gn(this),jn(t)):vn(this),e._chunkSteps(r)}else Ye(t,e),vn(this)}[Re](){}}function pn(e){return!!te(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof fn}function vn(e){mn(e)&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,fe(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,vn(e)),null)),(t=>(bn(e,t),null)))))}function mn(e){const t=e._controlledReadableStream;return!!Sn(e)&&!!e._started&&(!!(Dn(t)&&Ke(t)>0)||wn(e)>0)}function gn(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function yn(e){if(!Sn(e))return;const t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(gn(e),jn(t))}function _n(e,t){if(!Sn(e))return;const r=e._controlledReadableStream;if(Dn(r)&&Ke(r)>0)$e(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw bn(e,t),t}try{St(e,t,r)}catch(t){throw bn(e,t),t}}vn(e)}function bn(e,t){const r=e._controlledReadableStream;"readable"===r._state&&(Tt(e),gn(e),Mn(r,t))}function wn(e){const t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function Sn(e){const t=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===t}function Tn(e,t,r,n,o,i,s){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,Tt(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=s,t._strategyHWM=i,t._pullAlgorithm=n,t._cancelAlgorithm=o,e._readableStreamController=t,fe(le(r()),(()=>(t._started=!0,vn(t),null)),(e=>(bn(t,e),null)))}function In(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function En(e){return te(t=e)&&void 0!==t.getReader?function(e){let t;return t=Ln(ee,(function(){let r;try{r=e.read()}catch(r){return he(r)}return me(r,(e=>{if(!te(e))throw new TypeError("The promise returned by the reader.read() method must fulfill with an object");if(e.done)yn(t._readableStreamController);else{const r=e.value;_n(t._readableStreamController,r)}}))}),(function(t){try{return le(e.cancel(t))}catch(t){return he(t)}}),0),t}(e.getReader()):function(e){let t;const r=ft(e,"async");return t=Ln(ee,(function(){let e;try{e=pt(r)}catch(e){return he(e)}return me(le(e),(e=>{if(!te(e))throw new TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(e.done)yn(t._readableStreamController);else{const r=e.value;_n(t._readableStreamController,r)}}))}),(function(e){const t=r.iterator;let n;try{n=lt(t,"return")}catch(e){return he(e)}return void 0===n?le(void 0):me(be(n,t,[e]),(e=>{if(!te(e))throw new TypeError("The promise returned by the iterator.return() method must fulfill with an object")}))}),0),t}(e);var t}function Rn(e,t,r){return je(e,r),r=>be(e,t,[r])}function On(e,t,r){return je(e,r),r=>be(e,t,[r])}function Pn(e,t,r){return je(e,r),r=>_e(e,t,[r])}function Cn(e,t){if("bytes"!=(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function An(e,t){xe(e,t);const r=null==e?void 0:e.preventAbort,n=null==e?void 0:e.preventCancel,o=null==e?void 0:e.preventClose,i=null==e?void 0:e.signal;return void 0!==i&&function(e,t){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError(`${t} is not an AbortSignal.`)}(i,`${t} has member 'signal' that`),{preventAbort:Boolean(r),preventCancel:Boolean(n),preventClose:Boolean(o),signal:i}}Object.defineProperties(fn.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),ne(fn.prototype.close,"close"),ne(fn.prototype.enqueue,"enqueue"),ne(fn.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(fn.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class Un{constructor(e={},t={}){void 0===e?e=null:Me(e,"First parameter");const r=fr(t,"Second parameter"),n=function(e,t){xe(e,t);const r=e,n=null==r?void 0:r.autoAllocateChunkSize,o=null==r?void 0:r.cancel,i=null==r?void 0:r.pull,s=null==r?void 0:r.start,a=null==r?void 0:r.type;return{autoAllocateChunkSize:void 0===n?void 0:He(n,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===o?void 0:Rn(o,r,`${t} has member 'cancel' that`),pull:void 0===i?void 0:On(i,r,`${t} has member 'pull' that`),start:void 0===s?void 0:Pn(s,r,`${t} has member 'start' that`),type:void 0===a?void 0:Cn(a,`${t} has member 'type' that`)}}(e,"First parameter");if(Nn(this),"bytes"===n.type){if(void 0!==r.size)throw new RangeError("The strategy for a byte stream cannot have a size function");!function(e,t,r){const n=Object.create(Rt.prototype);let o,i,s;o=void 0!==t.start?()=>t.start(n):()=>{},i=void 0!==t.pull?()=>t.pull(n):()=>le(void 0),s=void 0!==t.cancel?e=>t.cancel(e):()=>le(void 0);const a=t.autoAllocateChunkSize;if(0===a)throw new TypeError("autoAllocateChunkSize must be greater than 0");Jt(e,n,o,i,s,r,a)}(this,n,hr(r,0))}else{const e=dr(r);!function(e,t,r,n){const o=Object.create(fn.prototype);let i,s,a;i=void 0!==t.start?()=>t.start(o):()=>{},s=void 0!==t.pull?()=>t.pull(o):()=>le(void 0),a=void 0!==t.cancel?e=>t.cancel(e):()=>le(void 0),Tn(e,o,i,s,a,r,n)}(this,n,hr(r,1),e)}}get locked(){if(!qn(this))throw Bn("locked");return Dn(this)}cancel(e=void 0){return qn(this)?Dn(this)?he(new TypeError("Cannot cancel a stream that already has a reader")):xn(this,e):he(Bn("cancel"))}getReader(e=void 0){if(!qn(this))throw Bn("getReader");return void 0===function(e,t){xe(e,t);const r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:tr(r,`${t} has member 'mode' that`)}}(e,"First parameter").mode?Ge(this):rr(this)}pipeThrough(e,t={}){if(!qn(this))throw Bn("pipeThrough");Be(e,1,"pipeThrough");const r=function(e,t){xe(e,t);const r=null==e?void 0:e.readable;We(r,"readable","ReadableWritablePair"),ze(r,`${t} has member 'readable' that`);const n=null==e?void 0:e.writable;return We(n,"writable","ReadableWritablePair"),_r(n,`${t} has member 'writable' that`),{readable:r,writable:n}}(e,"First parameter"),n=An(t,"Second parameter");if(Dn(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(Er(r.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return ge(dn(this,r.writable,n.preventClose,n.preventAbort,n.preventCancel,n.signal)),r.readable}pipeTo(e,t={}){if(!qn(this))return he(Bn("pipeTo"));if(void 0===e)return he("Parameter 1 is required in 'pipeTo'.");if(!Ir(e))return he(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let r;try{r=An(t,"Second parameter")}catch(e){return he(e)}return Dn(this)?he(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):Er(e)?he(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):dn(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!qn(this))throw Bn("tee");return it(function(e){return Ot(e._readableStreamController)?function(e){let t,r,n,o,i,s=Ge(e),a=!1,u=!1,c=!1,l=!1,h=!1;const d=ce((e=>{i=e}));function f(e){ve(e._closedPromise,(t=>(e!==s||(Gt(n._readableStreamController,t),Gt(o._readableStreamController,t),l&&h||i(void 0)),null)))}function p(){ar(s)&&(Ce(s),s=Ge(e),f(s)),Xe(s,{_chunkSteps:t=>{ye((()=>{u=!1,c=!1;const r=t;let s=t;if(!l&&!h)try{s=bt(t)}catch(t){return Gt(n._readableStreamController,t),Gt(o._readableStreamController,t),void i(xn(e,t))}l||zt(n._readableStreamController,r),h||zt(o._readableStreamController,s),a=!1,u?m():c&&g()}))},_closeSteps:()=>{a=!1,l||Ht(n._readableStreamController),h||Ht(o._readableStreamController),n._readableStreamController._pendingPullIntos.length>0&&Qt(n._readableStreamController,0),o._readableStreamController._pendingPullIntos.length>0&&Qt(o._readableStreamController,0),l&&h||i(void 0)},_errorSteps:()=>{a=!1}})}function v(t,r){Je(s)&&(Ce(s),s=rr(e),f(s));const d=r?o:n,p=r?n:o;ur(s,t,1,{_chunkSteps:t=>{ye((()=>{u=!1,c=!1;const n=r?h:l;if(r?l:h)n||Zt(d._readableStreamController,t);else{let r;try{r=bt(t)}catch(t){return Gt(d._readableStreamController,t),Gt(p._readableStreamController,t),void i(xn(e,t))}n||Zt(d._readableStreamController,t),zt(p._readableStreamController,r)}a=!1,u?m():c&&g()}))},_closeSteps:e=>{a=!1;const t=r?h:l,n=r?l:h;t||Ht(d._readableStreamController),n||Ht(p._readableStreamController),void 0!==e&&(t||Zt(d._readableStreamController,e),!n&&p._readableStreamController._pendingPullIntos.length>0&&Qt(p._readableStreamController,0)),t&&n||i(void 0)},_errorSteps:()=>{a=!1}})}function m(){if(a)return u=!0,le(void 0);a=!0;const e=$t(n._readableStreamController);return null===e?p():v(e._view,!1),le(void 0)}function g(){if(a)return c=!0,le(void 0);a=!0;const e=$t(o._readableStreamController);return null===e?p():v(e._view,!0),le(void 0)}function y(){}return n=kn(y,m,(function(n){if(l=!0,t=n,h){const n=it([t,r]),o=xn(e,n);i(o)}return d})),o=kn(y,g,(function(n){if(h=!0,r=n,l){const n=it([t,r]),o=xn(e,n);i(o)}return d})),f(s),[n,o]}(e):function(e){const t=Ge(e);let r,n,o,i,s,a=!1,u=!1,c=!1,l=!1;const h=ce((e=>{s=e}));function d(){return a?(u=!0,le(void 0)):(a=!0,Xe(t,{_chunkSteps:e=>{ye((()=>{u=!1;const t=e,r=e;c||_n(o._readableStreamController,t),l||_n(i._readableStreamController,r),a=!1,u&&d()}))},_closeSteps:()=>{a=!1,c||yn(o._readableStreamController),l||yn(i._readableStreamController),c&&l||s(void 0)},_errorSteps:()=>{a=!1}}),le(void 0))}function f(){}return o=Ln(f,d,(function(t){if(c=!0,r=t,l){const t=it([r,n]),o=xn(e,t);s(o)}return h})),i=Ln(f,d,(function(t){if(l=!0,n=t,c){const t=it([r,n]),o=xn(e,t);s(o)}return h})),ve(t._closedPromise,(e=>(bn(o._readableStreamController,e),bn(i._readableStreamController,e),c&&l||s(void 0),null))),[o,i]}(e)}(this))}values(e=void 0){if(!qn(this))throw Bn("values");return function(e,t){const r=Ge(e),n=new vt(r,t),o=Object.create(mt);return o._asyncIteratorImpl=n,o}(this,function(e){xe(e,"First parameter");const t=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(t)}}(e).preventCancel)}[dt](e){return this.values(e)}static from(e){return En(e)}}function Ln(e,t,r,n=1,o=()=>1){const i=Object.create(Un.prototype);return Nn(i),Tn(i,Object.create(fn.prototype),e,t,r,n,o),i}function kn(e,t,r){const n=Object.create(Un.prototype);return Nn(n),Jt(n,Object.create(Rt.prototype),e,t,r,0,void 0),n}function Nn(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function qn(e){return!!te(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof Un}function Dn(e){return void 0!==e._reader}function xn(e,t){if(e._disturbed=!0,"closed"===e._state)return le(void 0);if("errored"===e._state)return he(e._storedError);jn(e);const r=e._reader;if(void 0!==r&&ar(r)){const e=r._readIntoRequests;r._readIntoRequests=new we,e.forEach((e=>{e._closeSteps(void 0)}))}return me(e._readableStreamController[Ie](t),ee)}function jn(e){e._state="closed";const t=e._reader;if(void 0!==t&&(Ne(t),Je(t))){const e=t._readRequests;t._readRequests=new we,e.forEach((e=>{e._closeSteps()}))}}function Mn(e,t){e._state="errored",e._storedError=t;const r=e._reader;void 0!==r&&(ke(r,t),Je(r)?et(r,t):cr(r,t))}function Bn(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function Wn(e,t){xe(e,t);const r=null==e?void 0:e.highWaterMark;return We(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:Fe(r)}}Object.defineProperties(Un,{from:{enumerable:!0}}),Object.defineProperties(Un.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),ne(Un.from,"from"),ne(Un.prototype.cancel,"cancel"),ne(Un.prototype.getReader,"getReader"),ne(Un.prototype.pipeThrough,"pipeThrough"),ne(Un.prototype.pipeTo,"pipeTo"),ne(Un.prototype.tee,"tee"),ne(Un.prototype.values,"values"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Un.prototype,Symbol.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(Un.prototype,dt,{value:Un.prototype.values,writable:!0,configurable:!0});const Fn=e=>e.byteLength;ne(Fn,"size");class Vn{constructor(e){Be(e,1,"ByteLengthQueuingStrategy"),e=Wn(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!zn(this))throw Hn("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!zn(this))throw Hn("size");return Fn}}function Hn(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function zn(e){return!!te(e)&&!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof Vn}Object.defineProperties(Vn.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Vn.prototype,Symbol.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});const Gn=()=>1;ne(Gn,"size");class Yn{constructor(e){Be(e,1,"CountQueuingStrategy"),e=Wn(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!Kn(this))throw $n("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!Kn(this))throw $n("size");return Gn}}function $n(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function Kn(e){return!!te(e)&&!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof Yn}function Qn(e,t,r){return je(e,r),r=>be(e,t,[r])}function Zn(e,t,r){return je(e,r),r=>_e(e,t,[r])}function Jn(e,t,r){return je(e,r),(r,n)=>be(e,t,[r,n])}function Xn(e,t,r){return je(e,r),r=>be(e,t,[r])}Object.defineProperties(Yn.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Yn.prototype,Symbol.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class eo{constructor(e={},t={},r={}){void 0===e&&(e=null);const n=fr(t,"Second parameter"),o=fr(r,"Third parameter"),i=function(e,t){xe(e,t);const r=null==e?void 0:e.cancel,n=null==e?void 0:e.flush,o=null==e?void 0:e.readableType,i=null==e?void 0:e.start,s=null==e?void 0:e.transform,a=null==e?void 0:e.writableType;return{cancel:void 0===r?void 0:Xn(r,e,`${t} has member 'cancel' that`),flush:void 0===n?void 0:Qn(n,e,`${t} has member 'flush' that`),readableType:o,start:void 0===i?void 0:Zn(i,e,`${t} has member 'start' that`),transform:void 0===s?void 0:Jn(s,e,`${t} has member 'transform' that`),writableType:a}}(e,"First parameter");if(void 0!==i.readableType)throw new RangeError("Invalid readableType specified");if(void 0!==i.writableType)throw new RangeError("Invalid writableType specified");const s=hr(o,0),a=dr(o),u=hr(n,1),c=dr(n);let l;!function(e,t,r,n,o,i){function s(){return t}e._writable=function(e,t,r,n,o=1,i=()=>1){const s=Object.create(wr.prototype);return Tr(s),Vr(s,Object.create(Wr.prototype),e,t,r,n,o,i),s}(s,(function(t){return function(e,t){const r=e._transformStreamController;return e._backpressure?me(e._backpressureChangePromise,(()=>{const n=e._writable;if("erroring"===n._state)throw n._storedError;return lo(r,t)})):lo(r,t)}(e,t)}),(function(){return function(e){const t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;const r=e._readable;t._finishPromise=ce(((e,r)=>{t._finishPromise_resolve=e,t._finishPromise_reject=r}));const n=t._flushAlgorithm();return uo(t),fe(n,(()=>("errored"===r._state?po(t,r._storedError):(yn(r._readableStreamController),fo(t)),null)),(e=>(bn(r._readableStreamController,e),po(t,e),null))),t._finishPromise}(e)}),(function(t){return function(e,t){const r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;const n=e._readable;r._finishPromise=ce(((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t}));const o=r._cancelAlgorithm(t);return uo(r),fe(o,(()=>("errored"===n._state?po(r,n._storedError):(bn(n._readableStreamController,t),fo(r)),null)),(e=>(bn(n._readableStreamController,e),po(r,e),null))),r._finishPromise}(e,t)}),r,n),e._readable=Ln(s,(function(){return function(e){return io(e,!1),e._backpressureChangePromise}(e)}),(function(t){return function(e,t){const r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;const n=e._writable;r._finishPromise=ce(((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t}));const o=r._cancelAlgorithm(t);return uo(r),fe(o,(()=>("errored"===n._state?po(r,n._storedError):(Yr(n._writableStreamController,t),oo(e),fo(r)),null)),(t=>(Yr(n._writableStreamController,t),oo(e),po(r,t),null))),r._finishPromise}(e,t)}),o,i),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,io(e,!0),e._transformStreamController=void 0}(this,ce((e=>{l=e})),u,c,s,a),function(e,t){const r=Object.create(so.prototype);let n,o,i;n=void 0!==t.transform?e=>t.transform(e,r):e=>{try{return co(r,e),le(void 0)}catch(e){return he(e)}},o=void 0!==t.flush?()=>t.flush(r):()=>le(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>le(void 0),function(e,t,r,n,o){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=r,t._flushAlgorithm=n,t._cancelAlgorithm=o,t._finishPromise=void 0,t._finishPromise_resolve=void 0,t._finishPromise_reject=void 0}(e,r,n,o,i)}(this,i),void 0!==i.start?l(i.start(this._transformStreamController)):l(void 0)}get readable(){if(!to(this))throw vo("readable");return this._readable}get writable(){if(!to(this))throw vo("writable");return this._writable}}function to(e){return!!te(e)&&!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof eo}function ro(e,t){bn(e._readable._readableStreamController,t),no(e,t)}function no(e,t){uo(e._transformStreamController),Yr(e._writable._writableStreamController,t),oo(e)}function oo(e){e._backpressure&&io(e,!1)}function io(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=ce((t=>{e._backpressureChangePromise_resolve=t})),e._backpressure=t}Object.defineProperties(eo.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(eo.prototype,Symbol.toStringTag,{value:"TransformStream",configurable:!0});class so{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!ao(this))throw ho("desiredSize");return wn(this._controlledTransformStream._readable._readableStreamController)}enqueue(e=void 0){if(!ao(this))throw ho("enqueue");co(this,e)}error(e=void 0){if(!ao(this))throw ho("error");var t;t=e,ro(this._controlledTransformStream,t)}terminate(){if(!ao(this))throw ho("terminate");!function(e){const t=e._controlledTransformStream;yn(t._readable._readableStreamController),no(t,new TypeError("TransformStream terminated"))}(this)}}function ao(e){return!!te(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof so}function uo(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}function co(e,t){const r=e._controlledTransformStream,n=r._readable._readableStreamController;if(!Sn(n))throw new TypeError("Readable side is not in a state that permits enqueue");try{_n(n,t)}catch(e){throw no(r,e),r._readable._storedError}const o=function(e){return!mn(e)}(n);o!==r._backpressure&&io(r,!0)}function lo(e,t){return me(e._transformAlgorithm(t),void 0,(t=>{throw ro(e._controlledTransformStream,t),t}))}function ho(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function fo(e){void 0!==e._finishPromise_resolve&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function po(e,t){void 0!==e._finishPromise_reject&&(ge(e._finishPromise),e._finishPromise_reject(t),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function vo(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}Object.defineProperties(so.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),ne(so.prototype.enqueue,"enqueue"),ne(so.prototype.error,"error"),ne(so.prototype.terminate,"terminate"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(so.prototype,Symbol.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});var mo,go=(mo=function(e,t){return mo=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},mo(e,t)},function(e,t){function r(){this.constructor=e}mo(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),yo=function(){return yo=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},yo.apply(this,arguments)},_o="Error when aborting requestTask",bo=function(e){function t(t){void 0===t&&(t={});var r=e.call(this)||this,n=t.timeout,o=t.timeoutMsg,i=t.restrictedMethods;return r._timeout=n||0,r._timeoutMsg=o||"请求超时",r._restrictedMethods=i||["get","post","upload","download"],r}return go(t,e),t.prototype.post=function(e){var t=this;return new Promise((function(r,n){var o=e.url,i=e.data,s=e.headers,a=wx.request({url:(0,X.formatUrl)("https:",o),data:i,timeout:t._timeout,method:"POST",header:s,success:function(e){r(e)},fail:function(e){n(e)},complete:function(e){if(e&&e.errMsg&&t._timeout&&-1!==t._restrictedMethods.indexOf("post")&&"request:fail timeout"===e.errMsg){console.warn(t._timeoutMsg);try{a.abort()}catch(e){}}}})}))},t.prototype.upload=function(e){var t=this,r=this;return new Promise((function(n){return function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}u((n=n.apply(e,t||[])).next())}))}(t,void 0,void 0,(function(){var t,o,i,s,a,u;return function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,n=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!((o=(o=s.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}}(this,(function(c){return t=e.url,o=e.file,i=e.data,s=e.headers,a=e.onUploadProgress,u=wx.uploadFile({url:t,filePath:o,name:"file",formData:yo({},i),header:s,timeout:this._timeout,success:function(e){var t={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&i.success_action_status&&(t.statusCode=parseInt(i.success_action_status,10)),n(t)},fail:function(e){n(e)},complete:function(e){if(e&&e.errMsg&&r._timeout&&-1!==r._restrictedMethods.indexOf("upload")&&"request:fail timeout"===e.errMsg){console.warn(r._timeoutMsg);try{u.abort()}catch(e){}}}}),a&&u.onProgressUpdate((function(e){a(e)})),[2]}))}))}))},t.prototype.download=function(e){var t=this,r=this;return new Promise((function(n,o){var i=e.url,s=e.headers,a=wx.downloadFile({url:(0,X.formatUrl)("https:",i),header:s,timeout:t._timeout,success:function(e){200===e.statusCode&&e.tempFilePath?n({statusCode:200,tempFilePath:e.tempFilePath}):n(e)},fail:function(e){o(e)},complete:function(e){if(e&&e.errMsg&&r._timeout&&-1!==r._restrictedMethods.indexOf("download")&&"request:fail timeout"===e.errMsg){console.warn(r._timeoutMsg);try{a.abort()}catch(e){}}}})}))},t.prototype.fetch=function(e){var t=e.url,r=e.body,n=e.enableAbort,o=e.headers,i=e.method,s=e.stream,a=void 0!==s&&s,u=e.signal,c=e.timeout,l=this,h=null!=c?c:this._timeout,d=null,f=new Un({start:function(e){d=e},cancel:function(){d=null}});return new Promise((function(e,s){a&&e({data:f});var c=wx.request({url:(0,X.formatUrl)("https:",t),data:r,timeout:h,method:i.toUpperCase(),header:o,success:function(t){var r;null===(r=d)||void 0===r||r.close(),!a&&e(t)},fail:function(e){var t;if(null===(t=d)||void 0===t||t.close(),s(e),a)throw e},complete:function(e){if(e&&e.errMsg&&h&&-1!==l._restrictedMethods.indexOf("post")&&n&&"request:fail timeout"===e.errMsg){console.warn(l._timeoutMsg);try{c.abort()}catch(e){console.warn(_o,e)}}},enableChunked:a});if(c.onChunkReceived((function(e){var t;null===(t=d)||void 0===t||t.enqueue(new Uint8Array(e.data))})),u){var p=function(){try{c.abort()}catch(e){console.warn(_o,e)}};u.aborted?p():u.addEventListener("abort",(function(){return p()}))}}))},t}(X.AbstractSDKRequest),wo={setItem:function(e,t){wx.setStorageSync(e,t)},getItem:function(e){return wx.getStorageSync(e)},removeItem:function(e){wx.removeStorageSync(e)},clear:function(){wx.clearStorageSync()}},So=function(e,t){void 0===t&&(t={});var r=wx.connectSocket(yo({url:e},t));return{set onopen(e){r.onOpen(e)},set onmessage(e){r.onMessage(e)},set onclose(e){r.onClose(e)},set onerror(e){r.onError(e)},send:function(e){return r.send({data:e})},close:function(e,t){return r.close({code:e,reason:t})},get readyState(){return r.readyState},CONNECTING:0,OPEN:1,CLOSING:2,CLOSED:3}};const To=function(){return{root:{},reqClass:bo,wsClass:So,localStorage:wo,primaryStorage:X.StorageType.local,getAppSign:function(){var e=wx.getAccountInfoSync();return"undefined"!=typeof App||"undefined"!=typeof getApp||wx.onAppHide||wx.offAppHide||wx.onAppShow||wx.offAppShow?e&&e.miniProgram?e.miniProgram.appId:"":e&&e.plugin?e.plugin.appId:""}}},Io=function(){if("undefined"==typeof wx)return!1;if("undefined"==typeof Page)return!1;if(!wx.getSystemInfoSync)return!1;if(!wx.getStorageSync)return!1;if(!wx.setStorageSync)return!1;if(!wx.connectSocket)return!1;if(!wx.request)return!1;try{if(!wx.getSystemInfoSync())return!1;if("qq"===wx.getSystemInfoSync().AppPlatform)return!1}catch(e){return!1}return!0};var Eo=r(182),Ro=function(){return Ro=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},Ro.apply(this,arguments)},Oo=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}u((n=n.apply(e,t||[])).next())}))},Po=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((o=(o=s.trys).length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},Co=function(){function e(e){this.localStorage=(null==e?void 0:e.localStorage)||globalThis.localStorage}return e.prototype.getItem=function(e){return Oo(this,void 0,void 0,(function(){return Po(this,(function(t){return[2,this.localStorage.getItem(e)]}))}))},e.prototype.removeItem=function(e){return Oo(this,void 0,void 0,(function(){return Po(this,(function(t){return this.localStorage.removeItem(e),[2]}))}))},e.prototype.setItem=function(e,t){return Oo(this,void 0,void 0,(function(){return Po(this,(function(r){return this.localStorage.setItem(e,t),[2]}))}))},e.prototype.getItemSync=function(e){return this.localStorage.getItem(e)},e.prototype.setItemSync=function(e,t){this.localStorage.setItem(e,t)},e.prototype.removeItemSync=function(e){this.localStorage.removeItem(e)},e}(),Ao=/^[^:]+:\/\/[^/]+(\/[^?#]+)/;var Uo=function(e){if(!Io()||e.storage&&e.baseRequest)return e;var t={};try{var r=To(),n=r.localStorage,o=r.reqClass;if(n&&(t.storage=new Co({localStorage:n})),o){var i=new o({timeout:1e4,restrictedMethods:["get","post","upload","download","request"]});t.request=function(e){return function(t,r){var n;return Oo(this,void 0,void 0,(function(){var o;return Po(this,(function(i){return o=null===(n=null==r?void 0:r.headers)||void 0===n?void 0:n["x-request-id"],[2,new Promise((function(n,i){var s=Object.assign({},r);s.body&&"string"!=typeof s.body&&(s.body=JSON.stringify(s.body));var a=wx.request({url:t,data:s.body,timeout:e._timeout,method:s.method||"GET",header:s.headers,success:function(e){var r,s;(e.code||(null===(r=e.data)||void 0===r?void 0:r.error_code))&&(e={error:e.code||e.data.error,error_description:e.data.error_description||e.message||e.code||e.data.error_code,request_id:e.requestId,error_code:null===(s=e.data)||void 0===s?void 0:s.error_code}),e.request_id||(e.request_id=e.request_id||o),e.error&&(e.error_uri=function(e){return Ao.test(e)?RegExp.$1:e}(t),i(e)),n(e.data||{})},fail:function(e){i({error:"unreachable",error_description:e.message})},complete:function(t){if(t&&t.errMsg&&e._timeout&&-1!==e._restrictedMethods.indexOf("request")&&"request:fail timeout"===t.errMsg){console.warn(e._timeoutMsg);try{a.abort()}catch(e){}}}})}))]}))}))}}(i)}return e.captchaOptions&&(t.baseRequest=t.request,t.request=void 0),Ro(Ro({},e),t)}catch(e){console.error("adapter generate fail:",e)}return e},Lo=function(){return Lo=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},Lo.apply(this,arguments)},ko=function(e,t,r,n){var o,i=arguments.length,s=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,r,n);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,r,s):o(t,r))||s);return i>3&&s&&Object.defineProperty(t,r,s),s},No=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},qo=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}u((n=n.apply(e,t||[])).next())}))},Do=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((o=(o=s.trys).length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}},xo="loginStateChanged",jo=new Z.CloudbaseEventEmitter,Mo=function(){function e(e){var t=e.cache,r=e.oauthInstance;this.cache=t,this.oauthInstance=r,this.setUserInfo()}return e.prototype.checkLocalInfo=function(){return qo(this,void 0,void 0,(function(){return Do(this,(function(e){return this.uid=this.getLocalUserInfo("uid"),this.gender=this.getLocalUserInfo("gender"),this.picture=this.getLocalUserInfo("picture"),this.email=this.getLocalUserInfo("email"),this.emailVerified=this.getLocalUserInfo("email_verified"),this.phoneNumber=this.getLocalUserInfo("phone_number"),this.username=this.getLocalUserInfo("username"),this.name=this.getLocalUserInfo("name"),this.birthdate=this.getLocalUserInfo("birthdate"),this.zoneinfo=this.getLocalUserInfo("zoneinfo"),this.locale=this.getLocalUserInfo("locale"),this.sub=this.getLocalUserInfo("sub"),this.createdFrom=this.getLocalUserInfo("created_from"),this.providers=this.getLocalUserInfo("providers"),[2]}))}))},e.prototype.checkLocalInfoAsync=function(){return qo(this,void 0,void 0,(function(){var e,t,r,n;return Do(this,(function(o){switch(o.label){case 0:return e=this,[4,this.getLocalUserInfoAsync("uid")];case 1:return e.uid=o.sent(),t=this,[4,this.getLocalUserInfoAsync("gender")];case 2:return t.gender=o.sent(),this.picture=this.getLocalUserInfo("picture"),r=this,[4,this.getLocalUserInfoAsync("email")];case 3:return r.email=o.sent(),this.emailVerified=this.getLocalUserInfo("email_verified"),this.phoneNumber=this.getLocalUserInfo("phone_number"),n=this,[4,this.getLocalUserInfoAsync("username")];case 4:return n.username=o.sent(),this.name=this.getLocalUserInfo("name"),this.birthdate=this.getLocalUserInfo("birthdate"),this.zoneinfo=this.getLocalUserInfo("zoneinfo"),this.locale=this.getLocalUserInfo("locale"),this.sub=this.getLocalUserInfo("sub"),this.createdFrom=this.getLocalUserInfo("created_from"),this.providers=this.getLocalUserInfo("providers"),[2]}}))}))},e.prototype.update=function(e){return qo(this,void 0,void 0,(function(){var t;return Do(this,(function(r){switch(r.label){case 0:return[4,this.oauthInstance.authApi.setUserProfile(Lo({},e))];case 1:return t=r.sent(),this.setLocalUserInfo(t),[2]}}))}))},e.prototype.updateUserBasicInfo=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){switch(t.label){case 0:return[4,this.oauthInstance.authApi.updateUserBasicInfo(Lo({},e))];case 1:return t.sent(),this.setLocalUserInfo({username:e.username}),[2]}}))}))},e.prototype.updatePassword=function(e,t){return this.oauthInstance.authApi.updatePasswordByOld({old_password:t,new_password:e})},e.prototype.updateUsername=function(e){return"string"!=typeof e&&(0,$.throwError)(K.ERRORS.INVALID_PARAMS,"username must be a string"),this.update({username:e})},e.prototype.refresh=function(e){return qo(this,void 0,void 0,(function(){var t;return Do(this,(function(r){switch(r.label){case 0:return[4,this.oauthInstance.authApi.getUserInfo(e)];case 1:return t=r.sent(),this.setLocalUserInfo(t),[2,t]}}))}))},e.prototype.getLocalUserInfo=function(e){var t=this.cache.keys.userInfoKey;return this.cache.getStore(t)[e]},e.prototype.getLocalUserInfoAsync=function(e){return qo(this,void 0,void 0,(function(){var t;return Do(this,(function(r){switch(r.label){case 0:return t=this.cache.keys.userInfoKey,[4,this.cache.getStoreAsync(t)];case 1:return[2,r.sent()[e]]}}))}))},e.prototype.setUserInfo=function(){var e=this,t=this.cache.keys.userInfoKey,r=this.cache.getStore(t);["uid","email","name","gender","picture","email_verified","phone_number","birthdate","zoneinfo","locale","sub","created_from","providers","username"].forEach((function(t){e[t]=r[t]}))},e.prototype.setLocalUserInfo=function(e){var t=this.cache.keys.userInfoKey;this.cache.setStore(t,e),this.setUserInfo()},ko([(0,Q.catchErrorsDecorator)({title:"更新用户信息失败",messages:["请确认以下各项：","  1 - 调用 User.update() 的语法或参数是否正确","  2 - 用户信息中是否包含非法值","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object]),No("design:returntype",Promise)],e.prototype,"update",null),ko([(0,Q.catchErrorsDecorator)({title:"更新密码失败",messages:["请确认以下各项：","  1 - 调用 User.updatePassword() 的语法或参数是否正确","  3 - 新密码中是否包含非法字符","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[String,String]),No("design:returntype",void 0)],e.prototype,"updatePassword",null),ko([(0,Q.catchErrorsDecorator)({title:"更新用户名失败",messages:["请确认以下各项：","  1 - 调用 User.updateUsername() 的语法或参数是否正确","  2 - 当前环境是否开通了用户名密码登录","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[String]),No("design:returntype",void 0)],e.prototype,"updateUsername",null),ko([(0,Q.catchErrorsDecorator)({title:"刷新本地用户信息失败",messages:["请确认以下各项：","  1 - 调用 User.refresh() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object]),No("design:returntype",Promise)],e.prototype,"refresh",null),e}(),Bo=function(){function e(e){var t=e.envId,r=e.cache,n=e.oauthInstance;t||(0,$.throwError)(K.ERRORS.INVALID_PARAMS,"envId is not defined"),this.cache=r,this.oauthInstance=n,this.user=new Mo({cache:this.cache,oauthInstance:n})}return e.prototype.checkLocalState=function(){var e;this.oauthLoginState=null===(e=this.oauthInstance)||void 0===e?void 0:e.authApi.hasLoginStateSync(),this.user.checkLocalInfo()},e.prototype.checkLocalStateAsync=function(){var e;return qo(this,void 0,void 0,(function(){return Do(this,(function(t){switch(t.label){case 0:return[4,null===(e=this.oauthInstance)||void 0===e?void 0:e.authApi.getLoginState()];case 1:return t.sent(),[4,this.user.checkLocalInfoAsync()];case 2:return t.sent(),[2]}}))}))},e}(),Wo=function(){function e(e){this.config=e,this.cache=e.cache,this.oauthInstance=e.oauthInstance}return e.prototype.bindPhoneNumber=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.editContact(e)]}))}))},e.prototype.unbindProvider=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.unbindProvider(e)]}))}))},e.prototype.bindEmail=function(e){return this.oauthInstance.authApi.editContact(e)},e.prototype.verify=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.verify(e)]}))}))},e.prototype.getVerification=function(e,t){return qo(this,void 0,void 0,(function(){return Do(this,(function(r){return[2,this.oauthInstance.authApi.getVerification(e,t)]}))}))},Object.defineProperty(e.prototype,"currentUser",{get:function(){if("async"!==this.cache.mode){var e=this.hasLoginState();return e&&e.user||null}(0,$.printWarn)(K.ERRORS.INVALID_OPERATION,"current platform's storage is asynchronous, please use getCurrentUser insteed")},enumerable:!1,configurable:!0}),e.prototype.getCurrentUser=function(){return qo(this,void 0,void 0,(function(){var e;return Do(this,(function(t){switch(t.label){case 0:return[4,this.getLoginState()];case 1:return(e=t.sent())?[4,e.user.checkLocalInfoAsync()]:[3,3];case 2:return t.sent(),[2,e.user||null];case 3:return[2,null]}}))}))},e.prototype.signInAnonymously=function(e){return void 0===e&&(e={}),qo(this,void 0,void 0,(function(){return Do(this,(function(t){switch(t.label){case 0:return[4,this.oauthInstance.authApi.signInAnonymously(e)];case 1:return t.sent(),[2,this.createLoginState()]}}))}))},e.prototype.signInAnonymouslyInWx=function(e){var t=(void 0===e?{}:e).useWxCloud;return qo(this,void 0,void 0,(function(){var e,r,n=this;return Do(this,(function(o){switch(o.label){case 0:if(!Io())throw Error("wx api undefined");return e=wx.getAccountInfoSync().miniProgram,r=function(r){return qo(n,void 0,void 0,(function(){var n,o;return Do(this,(function(i){switch(i.label){case 0:n=void 0,o=void 0,i.label=1;case 1:return i.trys.push([1,4,,5]),[4,this.oauthInstance.authApi.grantProviderToken({provider_id:null==e?void 0:e.appId,provider_code:r,provider_params:{provider_code_type:"open_id",appid:null==e?void 0:e.appId}},t)];case 2:if((null==(n=i.sent())?void 0:n.error_code)||!n.provider_token)throw n;return[4,this.oauthInstance.authApi.signInAnonymously({provider_token:n.provider_token},t)];case 3:if(null==(o=i.sent())?void 0:o.error_code)throw o;return[3,5];case 4:throw i.sent();case 5:return[2]}}))}))},[4,new Promise((function(e,t){wx.login({success:function(o){return qo(n,void 0,void 0,(function(){var n;return Do(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,r(o.code)];case 1:return i.sent(),e(!0),[3,3];case 2:return n=i.sent(),t(n),[3,3];case 3:return[2]}}))}))},fail:function(e){var r=new Error(null==e?void 0:e.errMsg);r.code=null==e?void 0:e.errno,t(r)}})}))];case 1:return o.sent(),[2,this.createLoginState(void 0,{asyncRefreshUser:!0})]}}))}))},e.prototype.bindOpenId=function(){return qo(this,void 0,void 0,(function(){var e,t,r=this;return Do(this,(function(n){switch(n.label){case 0:if(!Io())throw Error("wx api undefined");return e=wx.getAccountInfoSync().miniProgram,t=function(t){return qo(r,void 0,void 0,(function(){var r;return Do(this,(function(n){switch(n.label){case 0:r=void 0,n.label=1;case 1:return n.trys.push([1,4,,5]),[4,this.oauthInstance.authApi.grantProviderToken({provider_id:null==e?void 0:e.appId,provider_code:t,provider_params:{provider_code_type:"open_id",appid:null==e?void 0:e.appId}})];case 2:if((null==(r=n.sent())?void 0:r.error_code)||!r.provider_token)throw r;return[4,this.oauthInstance.authApi.bindWithProvider({provider_token:r.provider_token})];case 3:return n.sent(),[3,5];case 4:throw n.sent();case 5:return[2]}}))}))},[4,new Promise((function(e,n){wx.login({success:function(o){return qo(r,void 0,void 0,(function(){var r;return Do(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,t(o.code)];case 1:return i.sent(),e(!0),[3,3];case 2:return r=i.sent(),n(r),[3,3];case 3:return[2]}}))}))},fail:function(e){var t=new Error(null==e?void 0:e.errMsg);t.code=null==e?void 0:e.errno,n(t)}})}))];case 1:return n.sent(),[2]}}))}))},e.prototype.signInWithOpenId=function(e){var t=(void 0===e?{}:e).useWxCloud,r=void 0===t||t;return qo(this,void 0,void 0,(function(){var e,t,n=this;return Do(this,(function(o){switch(o.label){case 0:if(!Io())throw Error("wx api undefined");return e=wx.getAccountInfoSync().miniProgram,t=function(t){return qo(n,void 0,void 0,(function(){var n,o;return Do(this,(function(i){switch(i.label){case 0:n=void 0,o=void 0,i.label=1;case 1:return i.trys.push([1,4,,5]),[4,this.oauthInstance.authApi.grantProviderToken({provider_id:null==e?void 0:e.appId,provider_code:t,provider_params:{provider_code_type:"open_id",appid:null==e?void 0:e.appId}},r)];case 2:if((null==(n=i.sent())?void 0:n.error_code)||!n.provider_token)throw n;return[4,this.oauthInstance.authApi.signInWithProvider({provider_token:n.provider_token},r)];case 3:if(null==(o=i.sent())?void 0:o.error_code)throw o;return[3,5];case 4:throw i.sent();case 5:return[4,this.oauthInstance.oauth2client.setCredentials(o)];case 6:return i.sent(),[2]}}))}))},[4,new Promise((function(e,r){wx.login({success:function(o){return qo(n,void 0,void 0,(function(){var n;return Do(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,t(o.code)];case 1:return i.sent(),e(!0),[3,3];case 2:return n=i.sent(),r(n),[3,3];case 3:return[2]}}))}))},fail:function(e){var t=new Error(null==e?void 0:e.errMsg);t.code=null==e?void 0:e.errno,r(t)}})}))];case 1:return o.sent(),[2,this.createLoginState()]}}))}))},e.prototype.signInWithUnionId=function(){return qo(this,void 0,void 0,(function(){var e=this;return Do(this,(function(t){switch(t.label){case 0:if(!Io())throw Error("wx api undefined");t.label=1;case 1:return t.trys.push([1,3,,4]),[4,new Promise((function(t,r){var n=wx.getAccountInfoSync().miniProgram;wx.login({success:function(o){return qo(e,void 0,void 0,(function(){var e,i,s,a,u;return Do(this,(function(c){switch(c.label){case 0:e=null==n?void 0:n.appId,c.label=1;case 1:return c.trys.push([1,4,,5]),[4,this.oauthInstance.authApi.grantProviderToken({provider_code:o.code,provider_id:e,provider_params:{provider_code_type:"union_id",appid:null==n?void 0:n.appId}})];case 2:return i=c.sent(),(s=i.provider_token)?[4,this.oauthInstance.authApi.signInWithProvider({provider_id:e,provider_token:s})]:(r(i),[2]);case 3:return(null==(a=c.sent())?void 0:a.error_code)?(r(a),[2]):(t(!0),[3,5]);case 4:return u=c.sent(),r(u),[3,5];case 5:return[2]}}))}))},fail:function(e){var t=new Error(null==e?void 0:e.errMsg);t.code=null==e?void 0:e.errno,r(t)}})}))];case 2:return t.sent(),[3,4];case 3:throw t.sent();case 4:return[2,this.createLoginState()]}}))}))},e.prototype.signInWithPhoneAuth=function(e){var t=e.phoneCode,r=void 0===t?"":t;return qo(this,void 0,void 0,(function(){var e,t,n,o,i;return Do(this,(function(s){switch(s.label){case 0:if(!Io())throw Error("wx api undefined");return e=wx.getAccountInfoSync().miniProgram,t={provider_params:{provider_code_type:"phone"},provider_id:e.appId},[4,wx.login()];case 1:n=s.sent().code,t.provider_code=n,s.label=2;case 2:return s.trys.push([2,6,,7]),[4,this.oauthInstance.authApi.grantProviderToken(t)];case 3:if((o=s.sent()).error_code)throw o;return[4,this.oauthInstance.authApi.patchProviderToken({provider_token:o.provider_token,provider_id:e.appId,provider_params:{code:r,provider_code_type:"phone"}})];case 4:if((o=s.sent()).error_code)throw o;return[4,this.oauthInstance.authApi.signInWithProvider({provider_token:o.provider_token})];case 5:if(null==(i=s.sent())?void 0:i.error_code)throw i;return[3,7];case 6:throw s.sent();case 7:return[2,this.createLoginState()]}}))}))},e.prototype.signInWithSms=function(e){var t=e.verificationInfo,r=void 0===t?{verification_id:"",is_user:!1}:t,n=e.verificationCode,o=void 0===n?"":n,i=e.phoneNum,s=void 0===i?"":i,a=e.bindInfo,u=void 0===a?void 0:a;return qo(this,void 0,void 0,(function(){return Do(this,(function(e){try{return[2,this.signInWithUsername({verificationInfo:r,verificationCode:o,bindInfo:u,username:s,loginType:"sms"})]}catch(e){throw e}return[2]}))}))},e.prototype.signInWithEmail=function(e){var t=e.verificationInfo,r=void 0===t?{verification_id:"",is_user:!1}:t,n=e.verificationCode,o=void 0===n?"":n,i=e.bindInfo,s=void 0===i?void 0:i,a=e.email,u=void 0===a?"":a;return qo(this,void 0,void 0,(function(){return Do(this,(function(e){try{return[2,this.signInWithUsername({verificationInfo:r,verificationCode:o,bindInfo:s,username:u,loginType:"email"})]}catch(e){throw e}return[2]}))}))},e.prototype.setCustomSignFunc=function(e){this.oauthInstance.authApi.setCustomSignFunc(e)},e.prototype.signInWithCustomTicket=function(){return qo(this,void 0,void 0,(function(){return Do(this,(function(e){switch(e.label){case 0:return[4,this.oauthInstance.authApi.signInWithCustomTicket()];case 1:return e.sent(),[2,this.createLoginState()]}}))}))},e.prototype.signIn=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){switch(t.label){case 0:return[4,this.oauthInstance.authApi.signIn(e)];case 1:return t.sent(),[2,this.createLoginState(e)]}}))}))},e.prototype.signUp=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){switch(t.label){case 0:return[4,this.oauthInstance.authApi.signUp(e)];case 1:return t.sent(),[2,this.createLoginState()]}}))}))},e.prototype.setPassword=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.setPassword(e)]}))}))},e.prototype.isUsernameRegistered=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){switch(t.label){case 0:return"string"!=typeof e&&(0,$.throwError)(K.ERRORS.INVALID_PARAMS,"username must be a string"),[4,this.oauthInstance.authApi.checkIfUserExist({username:e})];case 1:return[2,t.sent().exist]}}))}))},e.prototype.signOut=function(e){return qo(this,void 0,void 0,(function(){var t,r;return Do(this,(function(n){switch(n.label){case 0:return t=this.cache.keys.userInfoKey,[4,this.oauthInstance.authApi.signOut(e)];case 1:return r=n.sent(),[4,this.cache.removeStoreAsync(t)];case 2:return n.sent(),jo.fire(xo),[2,r]}}))}))},e.prototype.hasLoginState=function(){var e;if("async"!==this.cache.mode)return(null===(e=this.oauthInstance)||void 0===e?void 0:e.authApi.hasLoginStateSync())?new Bo({envId:this.config.env,cache:this.cache,oauthInstance:this.oauthInstance}):null;(0,$.printWarn)(K.ERRORS.INVALID_OPERATION,"current platform's storage is asynchronous, please use getLoginState insteed")},e.prototype.getLoginState=function(){return qo(this,void 0,void 0,(function(){var e;return Do(this,(function(t){switch(t.label){case 0:e=null,t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.oauthInstance.authApi.getLoginState()];case 2:return e=t.sent(),[3,4];case 3:return t.sent(),[2,null];case 4:return e?[2,new Bo({envId:this.config.env,cache:this.cache,oauthInstance:this.oauthInstance})]:[2,null]}}))}))},e.prototype.getUserInfo=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.getUserInfo(e)]}))}))},e.prototype.getWedaUserInfo=function(){return qo(this,void 0,void 0,(function(){return Do(this,(function(e){return[2,this.oauthInstance.authApi.getWedaUserInfo()]}))}))},e.prototype.updateUserBasicInfo=function(e){return qo(this,void 0,void 0,(function(){var t;return Do(this,(function(r){switch(r.label){case 0:return[4,this.getLoginState()];case 1:return(t=r.sent())?[4,t.user.updateUserBasicInfo(e)]:[3,3];case 2:r.sent(),r.label=3;case 3:return[2]}}))}))},e.prototype.getAuthHeader=function(){return console.error("Auth.getAuthHeader API 已废弃"),{}},e.prototype.bindWithProvider=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.bindWithProvider(e)]}))}))},e.prototype.queryUser=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.queryUserProfile(e)]}))}))},e.prototype.getAccessToken=function(){return qo(this,void 0,void 0,(function(){return Do(this,(function(e){switch(e.label){case 0:return[4,this.oauthInstance.oauth2client.getAccessToken()];case 1:return[2,{accessToken:e.sent(),env:this.config.env}]}}))}))},e.prototype.grantProviderToken=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.grantProviderToken(e)]}))}))},e.prototype.patchProviderToken=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.patchProviderToken(e)]}))}))},e.prototype.signInWithProvider=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){switch(t.label){case 0:return[4,this.oauthInstance.authApi.signInWithProvider(e)];case 1:return t.sent(),[2,this.createLoginState(e)]}}))}))},e.prototype.signInWithWechat=function(e){return void 0===e&&(e={}),qo(this,void 0,void 0,(function(){return Do(this,(function(t){switch(t.label){case 0:return[4,this.oauthInstance.authApi.signInWithWechat(e)];case 1:return t.sent(),[2,this.createLoginState(e)]}}))}))},e.prototype.grantToken=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){switch(t.label){case 0:return[4,this.oauthInstance.authApi.grantToken(e)];case 1:return t.sent(),[2,this.createLoginState()]}}))}))},e.prototype.genProviderRedirectUri=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.genProviderRedirectUri(e)]}))}))},e.prototype.resetPassword=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.resetPassword(e)]}))}))},e.prototype.deviceAuthorize=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.deviceAuthorize(e)]}))}))},e.prototype.sudo=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.sudo(e)]}))}))},e.prototype.deleteMe=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.deleteMe(e)]}))}))},e.prototype.getProviders=function(){return qo(this,void 0,void 0,(function(){return Do(this,(function(e){return[2,this.oauthInstance.authApi.getProviders()]}))}))},e.prototype.loginScope=function(){return qo(this,void 0,void 0,(function(){return Do(this,(function(e){return[2,this.oauthInstance.authApi.loginScope()]}))}))},e.prototype.loginGroups=function(){return qo(this,void 0,void 0,(function(){return Do(this,(function(e){return[2,this.oauthInstance.authApi.loginGroups()]}))}))},e.prototype.onLoginStateChanged=function(e){return qo(this,void 0,void 0,(function(){var t,r=this;return Do(this,(function(n){switch(n.label){case 0:return jo.on(xo,(function(){return qo(r,void 0,void 0,(function(){var t;return Do(this,(function(r){switch(r.label){case 0:return[4,this.getLoginState()];case 1:return t=r.sent(),e.call(this,t),[2]}}))}))})),[4,this.getLoginState()];case 1:return t=n.sent(),e.call(this,t),[2]}}))}))},e.prototype.refreshTokenForce=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.refreshTokenForce(e)]}))}))},e.prototype.getCredentials=function(){return qo(this,void 0,void 0,(function(){return Do(this,(function(e){return[2,this.oauthInstance.authApi.getCredentials()]}))}))},e.prototype.setCredentials=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){switch(t.label){case 0:return[4,this.oauthInstance.oauth2client.setCredentials(e)];case 1:return t.sent(),[2]}}))}))},e.prototype.getProviderSubType=function(){return qo(this,void 0,void 0,(function(){return Do(this,(function(e){return[2,this.oauthInstance.authApi.getProviderSubType()]}))}))},e.prototype.createCaptchaData=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.createCaptchaData(e)]}))}))},e.prototype.verifyCaptchaData=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.verifyCaptchaData(e)]}))}))},e.prototype.getMiniProgramQrCode=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.getMiniProgramCode(e)]}))}))},e.prototype.getMiniProgramQrCodeStatus=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.getMiniProgramQrCodeStatus(e)]}))}))},e.prototype.modifyPassword=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.modifyPassword(e)]}))}))},e.prototype.modifyPasswordWithoutLogin=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.modifyPasswordWithoutLogin(e)]}))}))},e.prototype.getUserBehaviorLog=function(e){return qo(this,void 0,void 0,(function(){return Do(this,(function(t){return[2,this.oauthInstance.authApi.getUserBehaviorLog(e)]}))}))},e.prototype.toDefaultLoginPage=function(e){return void 0===e&&(e={}),qo(this,void 0,void 0,(function(){var t,r,n,o;return Do(this,(function(i){return t=e.config_version||"env",Io()?wx.navigateTo({url:"/packages/$wd_system/pages/login/index"}):(r=e.redirect_uri||window.location.href,n=new URL(r),o="".concat(n.origin,"/__auth/?app_id=").concat(e.app_id||"","&env_id=").concat(this.config.env,"&client_id=").concat(this.config.clientId,"&config_version=").concat(t,"&redirect_uri=").concat(encodeURIComponent(r)),window.location.href=o),[2]}))}))},e.prototype.createLoginState=function(e,t){return qo(this,void 0,void 0,(function(){var r;return Do(this,(function(n){switch(n.label){case 0:return[4,(r=new Bo({envId:this.config.env,cache:this.cache,oauthInstance:this.oauthInstance})).checkLocalStateAsync()];case 1:return n.sent(),(null==t?void 0:t.asyncRefreshUser)?(r.user.refresh(e),[3,4]):[3,2];case 2:return[4,r.user.refresh(e)];case 3:n.sent(),n.label=4;case 4:return jo.fire(xo),[2,r]}}))}))},e.prototype.signInWithUsername=function(e){var t=e.verificationInfo,r=void 0===t?{verification_id:"",is_user:!1}:t,n=e.verificationCode,o=void 0===n?"":n,i=e.username,s=void 0===i?"":i,a=e.bindInfo,u=void 0===a?void 0:a,c=e.loginType,l=void 0===c?"":c;return qo(this,void 0,void 0,(function(){var e,t,n,i,a,c,h;return Do(this,(function(d){switch(d.label){case 0:return d.trys.push([0,8,,9]),[4,this.oauthInstance.authApi.verify({verification_id:r.verification_id,verification_code:o})];case 1:if(null==(e=d.sent())?void 0:e.error_code)throw e;return t=e.verification_token,n="+86 ".concat(s),i={phone_number:n},"email"===l&&(i={email:n=s}),r.is_user?[4,this.oauthInstance.authApi.signIn({username:n,verification_token:t})]:[3,5];case 2:if(null==(a=d.sent())?void 0:a.error_code)throw a;return u?[4,this.oauthInstance.authApi.bindWithProvider({provider_token:null==u?void 0:u.providerToken})]:[3,4];case 3:if(null==(c=d.sent())?void 0:c.error_code)throw c;d.label=4;case 4:return[3,7];case 5:return[4,this.oauthInstance.authApi.signUp(Lo(Lo({},i),{verification_token:t,provider_token:null==u?void 0:u.providerId}))];case 6:if(null==(h=d.sent())?void 0:h.error_code)throw h;d.label=7;case 7:return[2,this.createLoginState()];case 8:throw d.sent();case 9:return[2]}}))}))},ko([(0,Q.catchErrorsDecorator)({title:"绑定手机号失败",messages:["请确认以下各项：","  1 - 调用 auth().bindPhoneNumber() 的语法或参数是否正确","  2 - 当前环境是否开通了短信验证码登录","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object]),No("design:returntype",Promise)],e.prototype,"bindPhoneNumber",null),ko([(0,Q.catchErrorsDecorator)({title:"解除三方绑定失败",messages:["请确认以下各项：","  1 - 调用 auth().unbindProvider() 的语法或参数是否正确","  2 - 当前账户是否已经与此登录方式解绑","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object]),No("design:returntype",Promise)],e.prototype,"unbindProvider",null),ko([(0,Q.catchErrorsDecorator)({title:"绑定邮箱地址失败",messages:["请确认以下各项：","  1 - 调用 auth().bindEmail() 的语法或参数是否正确","  2 - 当前环境是否开通了邮箱密码登录","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object]),No("design:returntype",void 0)],e.prototype,"bindEmail",null),ko([(0,Q.catchErrorsDecorator)({title:"验证码验证失败",messages:["请确认以下各项：","  1 - 调用 auth().verify() 的语法或参数是否正确","  2 - 当前环境是否开通了手机验证码/邮箱登录","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object]),No("design:returntype",Promise)],e.prototype,"verify",null),ko([(0,Q.catchErrorsDecorator)({title:"获取验证码失败",messages:["请确认以下各项：","  1 - 调用 auth().getVerification() 的语法或参数是否正确","  2 - 当前环境是否开通了手机验证码/邮箱登录","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object,Object]),No("design:returntype",Promise)],e.prototype,"getVerification",null),ko([(0,Q.catchErrorsDecorator)({title:"获取用户信息失败",messages:["请确认以下各项：","  1 - 调用 auth().getCurrentUser() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[]),No("design:returntype",Promise)],e.prototype,"getCurrentUser",null),ko([(0,Q.catchErrorsDecorator)({title:"匿名登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了匿名登录","  2 - 调用 auth().signInAnonymously() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object]),No("design:returntype",Promise)],e.prototype,"signInAnonymously",null),ko([(0,Q.catchErrorsDecorator)({title:"小程序匿名登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了匿名登录","  2 - 调用 auth().signInAnonymouslyInWx() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object]),No("design:returntype",Promise)],e.prototype,"signInAnonymouslyInWx",null),ko([(0,Q.catchErrorsDecorator)({title:"小程序绑定OpenID失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了小程序openId静默登录","  2 - 调用 auth().bindOpenId() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[]),No("design:returntype",Promise)],e.prototype,"bindOpenId",null),ko([(0,Q.catchErrorsDecorator)({title:"小程序openId静默登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了小程序openId静默登录","  2 - 调用 auth().signInWithOpenId() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object]),No("design:returntype",Promise)],e.prototype,"signInWithOpenId",null),ko([(0,Q.catchErrorsDecorator)({title:"小程序unionId静默登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了小程序unionId静默登录","  2 - 调用 auth().signInWithUnionId() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[]),No("design:returntype",Promise)],e.prototype,"signInWithUnionId",null),ko([(0,Q.catchErrorsDecorator)({title:"小程序手机号授权登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了小程序手机号授权登录","  2 - 调用 auth().signInWithPhoneAuth() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object]),No("design:returntype",Promise)],e.prototype,"signInWithPhoneAuth",null),ko([(0,Q.catchErrorsDecorator)({title:"短信验证码登陆",messages:["请确认以下各项：","  1 - 当前环境是否开启了小程序短信验证码登陆","  2 - 调用 auth().signInWithSms() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object]),No("design:returntype",Promise)],e.prototype,"signInWithSms",null),ko([(0,Q.catchErrorsDecorator)({title:"邮箱验证码登陆",messages:["请确认以下各项：","  1 - 当前环境是否开启了邮箱登陆","  2 - 调用 auth().signInWithEmail() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object]),No("design:returntype",Promise)],e.prototype,"signInWithEmail",null),ko([(0,Q.catchErrorsDecorator)({title:"自定义登录失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了自定义登录","  2 - 调用 auth().signInWithCustomTicket() 的语法或参数是否正确","  3 - ticket 是否归属于当前环境","  4 - 创建 ticket 的自定义登录私钥是否过期","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[]),No("design:returntype",Promise)],e.prototype,"signInWithCustomTicket",null),ko([(0,Q.catchErrorsDecorator)({title:"注册失败",messages:["请确认以下各项：","  1 - 当前环境是否开启了指定登录方式","  2 - 调用 auth().signUp() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object]),No("design:returntype",Promise)],e.prototype,"signUp",null),ko([(0,Q.catchErrorsDecorator)({title:"获取用户是否被占用失败",messages:["请确认以下各项：","  1 - 调用 auth().isUsernameRegistered() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[String]),No("design:returntype",Promise)],e.prototype,"isUsernameRegistered",null),ko([(0,Q.catchErrorsDecorator)({title:"用户登出失败",messages:["请确认以下各项：","  1 - 调用 auth().signOut() 的语法或参数是否正确","  2 - 当前用户是否为匿名登录（匿名登录不支持signOut）","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object]),No("design:returntype",Promise)],e.prototype,"signOut",null),ko([(0,Q.catchErrorsDecorator)({title:"获取本地登录态失败",messages:["请确认以下各项：","  1 - 调用 auth().getLoginState() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[]),No("design:returntype",Promise)],e.prototype,"getLoginState",null),ko([(0,Q.catchErrorsDecorator)({title:"获取用户信息失败",messages:["请确认以下各项：","  1 - 是否已登录","  2 - 调用 auth().getUserInfo() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object]),No("design:returntype",Promise)],e.prototype,"getUserInfo",null),ko([(0,Q.catchErrorsDecorator)({title:"获取微搭插件用户信息失败",messages:["请确认以下各项：","  1 - 是否已登录","  2 - 调用 auth().getWedaUserInfo() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[]),No("design:returntype",Promise)],e.prototype,"getWedaUserInfo",null),ko([(0,Q.catchErrorsDecorator)({title:"绑定第三方登录方式失败",messages:["请确认以下各项：","  1 - 调用 auth().bindWithProvider() 的语法或参数是否正确","  2 - 此账户是否已经绑定此第三方","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[Object]),No("design:returntype",Promise)],e.prototype,"bindWithProvider",null),ko([(0,Q.catchErrorsDecorator)({title:"获取身份源类型",messages:["请确认以下各项：","  1 - 调用 auth().getProviderSubType() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(K.COMMUNITY_SITE_URL)]}),No("design:type",Function),No("design:paramtypes",[]),No("design:returntype",Promise)],e.prototype,"getProviderSubType",null),e}();function Fo(e,t){var r=e.region,n=void 0===r?"ap-shanghai":r,o=(null==t?void 0:t.platform)||(0,Eo.useDefaultAdapter)(),i=o.runtime,s=t||{},a=s.env,u=s.clientId,c=s.debug,l=s.cache,h=s.app,d=(t||{}).apiOrigin;d||(d="https://".concat(a,".").concat(n,".tcb-api.tencentcloudapi.com"));var f=new Y(Uo({env:a,clientId:u,apiOrigin:d,storage:null==e?void 0:e.storage,baseRequest:null==e?void 0:e.baseRequest,request:null==e?void 0:e.request,anonymousSignInFunc:null==e?void 0:e.anonymousSignInFunc,captchaOptions:null==e?void 0:e.captchaOptions,wxCloud:null==e?void 0:e.wxCloud}));return{authInstance:new Wo({env:a,clientId:u,region:n,persistence:e.persistence,debug:c,cache:l||new J.CloudbaseCache({persistence:e.persistence,keys:{userInfoKey:"user_info_".concat(a)},platformInfo:o}),runtime:i||"web",_fromApp:h,oauthInstance:f}),oauthInstance:f}}var Vo={name:"auth",namespace:"auth",entity:function(e){if(void 0===e&&(e={region:"",persistence:"local"}),this.authInstance)return(0,$.printWarn)(K.ERRORS.INVALID_OPERATION,"every cloudbase instance should has only one auth object"),this.authInstance;var t=this.platform.adapter,r=e.persistence||t.primaryStorage;r&&r!==this.config.persistence&&this.updateConfig({persistence:r});var n=Fo(Lo(Lo({wxCloud:this.config.wxCloud,storage:this.config.storage},e),{persistence:this.config.persistence}),{env:this.config.env,clientId:this.config.clientId,apiOrigin:this.request.getBaseEndPoint(),platform:this.platform,cache:this.cache,app:this,debug:this.config.debug}),o=n.authInstance,i=n.oauthInstance;return this.oauthInstance=i,this.authInstance=o,this.authInstance}};try{cloudbase.registerComponent(Vo)}catch(ee){}function Ho(e){try{e.registerComponent(Vo)}catch(e){console.warn(e)}}},171:(e,t,r)=>{var n;if(r.r(t),r.d(t,{getEncryptInfo:()=>M}),!globalThis.IS_MP_BUILD){var o="undefined"!=typeof globalThis?globalThis.navigator:window.globalThis,i="0123456789abcdefghijklmnopqrstuvwxyz";function B(e){return i.charAt(e)}function W(e,t){return e&t}function F(e,t){return e|t}function V(e,t){return e^t}function H(e,t){return e&~t}function z(e){if(0==e)return-1;var t=0;return 65535&e||(e>>=16,t+=16),255&e||(e>>=8,t+=8),15&e||(e>>=4,t+=4),3&e||(e>>=2,t+=2),1&e||++t,t}function G(e){for(var t=0;0!=e;)e&=e-1,++t;return t}var s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function Y(e){var t,r,n="";for(t=0;t+3<=e.length;t+=3)r=parseInt(e.substring(t,t+3),16),n+=s.charAt(r>>6)+s.charAt(63&r);for(t+1==e.length?(r=parseInt(e.substring(t,t+1),16),n+=s.charAt(r<<2)):t+2==e.length&&(r=parseInt(e.substring(t,t+2),16),n+=s.charAt(r>>2)+s.charAt((3&r)<<4));(3&n.length)>0;)n+="=";return n}function $(e){var t,r="",n=0,o=0;for(t=0;t<e.length&&"="!=e.charAt(t);++t){var i=s.indexOf(e.charAt(t));i<0||(0==n?(r+=B(i>>2),o=3&i,n=1):1==n?(r+=B(o<<2|i>>4),o=15&i,n=2):2==n?(r+=B(o),r+=B(i>>2),o=3&i,n=3):(r+=B(o<<2|i>>4),r+=B(15&i),n=0))}return 1==n&&(r+=B(o<<2)),r}var a,u,c={decode:function(e){var t;if(void 0===a){var r="0123456789ABCDEF";for(a={},t=0;t<16;++t)a[r.charAt(t)]=t;for(r=r.toLowerCase(),t=10;t<16;++t)a[r.charAt(t)]=t;for(t=0;t<8;++t)a[" \f\n\r\t \u2028\u2029".charAt(t)]=-1}var n=[],o=0,i=0;for(t=0;t<e.length;++t){var s=e.charAt(t);if("="==s)break;if(-1!=(s=a[s])){if(void 0===s)throw new Error("Illegal character at offset "+t);o|=s,++i>=2?(n[n.length]=o,o=0,i=0):o<<=4}}if(i)throw new Error("Hex encoding incomplete: 4 bits missing");return n}},l={decode:function(e){var t;if(void 0===u){for(u=Object.create(null),t=0;t<64;++t)u["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t)]=t;for(t=0;t<9;++t)u["= \f\n\r\t \u2028\u2029".charAt(t)]=-1}var r=[],n=0,o=0;for(t=0;t<e.length;++t){var i=e.charAt(t);if("="==i)break;if(-1!=(i=u[i])){if(void 0===i)throw new Error("Illegal character at offset "+t);n|=i,++o>=4?(r[r.length]=n>>16,r[r.length]=n>>8&255,r[r.length]=255&n,n=0,o=0):n<<=6}}switch(o){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:r[r.length]=n>>10;break;case 3:r[r.length]=n>>16,r[r.length]=n>>8&255}return r},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(e){var t=l.re.exec(e);if(t)if(t[1])e=t[1];else{if(!t[2])throw new Error("RegExp out of sync");e=t[2]}return l.decode(e)}},h=1e13;class K{constructor(e){this.buf=[+e||0]}mulAdd(e,t){var r,n,o=this.buf,i=o.length;for(r=0;r<i;++r)(n=o[r]*e+t)<h?t=0:n-=(t=0|n/h)*h,o[r]=n;t>0&&(o[r]=t)}sub(e){var t,r,n=this.buf,o=n.length;for(t=0;t<o;++t)(r=n[t]-e)<0?(r+=h,e=1):e=0,n[t]=r;for(;0===n[n.length-1];)n.pop()}toString(e){if(10!=(e||10))throw new Error("only base 10 is supported");for(var t=this.buf,r=t[t.length-1].toString(),n=t.length-2;n>=0;--n)r+=(h+t[n]).toString().substring(1);return r}valueOf(){for(var e=this.buf,t=0,r=e.length-1;r>=0;--r)t=t*h+e[r];return t}simplify(){var e=this.buf;return 1==e.length?e[0]:this}}var d,f=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,p=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function Q(e,t){return e.length>t&&(e=e.substring(0,t)+"…"),e}class Z{constructor(e,t){this.hexDigits="0123456789ABCDEF",e instanceof Z?(this.enc=e.enc,this.pos=e.pos):(this.enc=e,this.pos=t)}get(e){if(void 0===e&&(e=this.pos++),e>=this.enc.length)throw new Error("Requesting byte offset "+e+" on a stream of length "+this.enc.length);return"string"==typeof this.enc?this.enc.charCodeAt(e):this.enc[e]}hexByte(e){return this.hexDigits.charAt(e>>4&15)+this.hexDigits.charAt(15&e)}hexDump(e,t,r){for(var n="",o=e;o<t;++o)if(n+=this.hexByte(this.get(o)),!0!==r)switch(15&o){case 7:n+="  ";break;case 15:n+="\n";break;default:n+=" "}return n}isASCII(e,t){for(var r=e;r<t;++r){var n=this.get(r);if(n<32||n>176)return!1}return!0}parseStringISO(e,t){for(var r="",n=e;n<t;++n)r+=String.fromCharCode(this.get(n));return r}parseStringUTF(e,t){for(var r="",n=e;n<t;){var o=this.get(n++);r+=o<128?String.fromCharCode(o):o>191&&o<224?String.fromCharCode((31&o)<<6|63&this.get(n++)):String.fromCharCode((15&o)<<12|(63&this.get(n++))<<6|63&this.get(n++))}return r}parseStringBMP(e,t){for(var r,n,o="",i=e;i<t;)r=this.get(i++),n=this.get(i++),o+=String.fromCharCode(r<<8|n);return o}parseTime(e,t,r){var n=this.parseStringISO(e,t),o=(r?f:p).exec(n);return o?(r&&(o[1]=+o[1],o[1]+=+o[1]<70?2e3:1900),n=o[1]+"-"+o[2]+"-"+o[3]+" "+o[4],o[5]&&(n+=":"+o[5],o[6]&&(n+=":"+o[6],o[7]&&(n+="."+o[7]))),o[8]&&(n+=" UTC","Z"!=o[8]&&(n+=o[8],o[9]&&(n+=":"+o[9]))),n):"Unrecognized time: "+n}parseInteger(e,t){for(var r,n=this.get(e),o=n>127,i=o?255:0,s="";n==i&&++e<t;)n=this.get(e);if(0==(r=t-e))return o?-1:0;if(r>4){for(s=n,r<<=3;!(128&(+s^i));)s=+s<<1,--r;s="("+r+" bit)\n"}o&&(n-=256);for(var a=new K(n),u=e+1;u<t;++u)a.mulAdd(256,this.get(u));return s+a.toString()}parseBitString(e,t,r){for(var n=this.get(e),o="("+((t-e-1<<3)-n)+" bit)\n",i="",s=e+1;s<t;++s){for(var a=this.get(s),u=s==t-1?n:0,c=7;c>=u;--c)i+=a>>c&1?"1":"0";if(i.length>r)return o+Q(i,r)}return o+i}parseOctetString(e,t,r){if(this.isASCII(e,t))return Q(this.parseStringISO(e,t),r);var n=t-e,o="("+n+" byte)\n";n>(r/=2)&&(t=e+r);for(var i=e;i<t;++i)o+=this.hexByte(this.get(i));return n>r&&(o+="…"),o}parseOID(e,t,r){for(var n="",o=new K,i=0,s=e;s<t;++s){var a=this.get(s);if(o.mulAdd(128,127&a),i+=7,!(128&a)){if(""===n)if((o=o.simplify())instanceof K)o.sub(80),n="2."+o.toString();else{var u=o<80?o<40?0:1:2;n=u+"."+(o-40*u)}else n+="."+o.toString();if(n.length>r)return Q(n,r);o=new K,i=0}}return i>0&&(n+=".incomplete"),n}}class J{constructor(e,t,r,n,o){if(!(n instanceof X))throw new Error("Invalid tag value.");this.stream=e,this.header=t,this.length=r,this.tag=n,this.sub=o}typeName(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}}content(e){if(void 0===this.tag)return null;void 0===e&&(e=1/0);var t=this.posContent(),r=Math.abs(this.length);if(!this.tag.isUniversal())return null!==this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(t,t+r,e);switch(this.tag.tagNumber){case 1:return 0===this.stream.get(t)?"false":"true";case 2:return this.stream.parseInteger(t,t+r);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(t,t+r,e);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(t,t+r,e);case 6:return this.stream.parseOID(t,t+r,e);case 16:case 17:return null!==this.sub?"("+this.sub.length+" elem)":"(no elem)";case 12:return Q(this.stream.parseStringUTF(t,t+r),e);case 18:case 19:case 20:case 21:case 22:case 26:return Q(this.stream.parseStringISO(t,t+r),e);case 30:return Q(this.stream.parseStringBMP(t,t+r),e);case 23:case 24:return this.stream.parseTime(t,t+r,23==this.tag.tagNumber)}return null}toString(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(null===this.sub?"null":this.sub.length)+"]"}toPrettyString(e){void 0===e&&(e="");var t=e+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(t+="+"),t+=this.length,this.tag.tagConstructed?t+=" (constructed)":!this.tag.isUniversal()||3!=this.tag.tagNumber&&4!=this.tag.tagNumber||null===this.sub||(t+=" (encapsulates)"),t+="\n",null!==this.sub){e+="  ";for(var r=0,n=this.sub.length;r<n;++r)t+=this.sub[r].toPrettyString(e)}return t}posStart(){return this.stream.pos}posContent(){return this.stream.pos+this.header}posEnd(){return this.stream.pos+this.header+Math.abs(this.length)}toHexString(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)}static decodeLength(e){var t=e.get(),r=127&t;if(r==t)return r;if(r>6)throw new Error("Length over 48 bits not supported at position "+(e.pos-1));if(0===r)return null;t=0;for(var n=0;n<r;++n)t=256*t+e.get();return t}getHexStringValue(){var e=this.toHexString(),t=2*this.header,r=2*this.length;return e.substr(t,r)}static decode(e){var t;t=e instanceof Z?e:new Z(e,0);var r=new Z(t),n=new X(t),o=J.decodeLength(t),i=t.pos,s=i-r.pos,a=null,u=function(){var e=[];if(null!==o){for(var r=i+o;t.pos<r;)e[e.length]=J.decode(t);if(t.pos!=r)throw new Error("Content size is not correct for container starting at offset "+i)}else try{for(;;){var n=J.decode(t);if(n.tag.isEOC())break;e[e.length]=n}o=i-t.pos}catch(e){throw new Error("Exception while decoding undefined length content: "+e)}return e};if(n.tagConstructed)a=u();else if(n.isUniversal()&&(3==n.tagNumber||4==n.tagNumber))try{if(3==n.tagNumber&&0!=t.get())throw new Error("BIT STRINGs with unused bits cannot encapsulate.");a=u();for(var c=0;c<a.length;++c)if(a[c].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch(e){a=null}if(null===a){if(null===o)throw new Error("We can't skip over an invalid tag with undefined length at offset "+i);t.pos=i+Math.abs(o)}return new J(r,s,o,n,a)}}class X{constructor(e){var t=e.get();if(this.tagClass=t>>6,this.tagConstructed=!!(32&t),this.tagNumber=31&t,31==this.tagNumber){var r=new K;do{t=e.get(),r.mulAdd(128,127&t)}while(128&t);this.tagNumber=r.simplify()}}isUniversal(){return 0===this.tagClass}isEOC(){return 0===this.tagClass&&0===this.tagNumber}}var v=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],m=(1<<26)/v[v.length-1];class ee{constructor(e,t,r){null!=e&&("number"==typeof e?this.fromNumber(e,t,r):null==t&&"string"!=typeof e?this.fromString(e,256):this.fromString(e,t))}toString(e){if(this.s<0)return"-"+this.negate().toString(e);var t;if(16==e)t=4;else if(8==e)t=3;else if(2==e)t=1;else if(32==e)t=5;else{if(4!=e)return this.toRadix(e);t=2}var r,n=(1<<t)-1,o=!1,i="",s=this.t,a=this.DB-s*this.DB%t;if(s-- >0)for(a<this.DB&&(r=this[s]>>a)>0&&(o=!0,i=B(r));s>=0;)a<t?(r=(this[s]&(1<<a)-1)<<t-a,r|=this[--s]>>(a+=this.DB-t)):(r=this[s]>>(a-=t)&n,a<=0&&(a+=this.DB,--s)),r>0&&(o=!0),o&&(i+=B(r));return o?i:"0"}negate(){var e=te();return ee.ZERO.subTo(this,e),e}abs(){return this.s<0?this.negate():this}compareTo(e){var t=this.s-e.s;if(0!=t)return t;var r=this.t;if(0!=(t=r-e.t))return this.s<0?-t:t;for(;--r>=0;)if(0!=(t=this[r]-e[r]))return t;return 0}bitLength(){return this.t<=0?0:this.DB*(this.t-1)+ue(this[this.t-1]^this.s&this.DM)}mod(e){var t=te();return this.abs().divRemTo(e,null,t),this.s<0&&t.compareTo(ee.ZERO)>0&&e.subTo(t,t),t}modPowInt(e,t){var r;return r=e<256||t.isEven()?new y(t):new _(t),this.exp(e,r)}clone(){var e=te();return this.copyTo(e),e}intValue(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}byteValue(){return 0==this.t?this.s:this[0]<<24>>24}shortValue(){return 0==this.t?this.s:this[0]<<16>>16}signum(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1}toByteArray(){var e=this.t,t=[];t[0]=this.s;var r,n=this.DB-e*this.DB%8,o=0;if(e-- >0)for(n<this.DB&&(r=this[e]>>n)!=(this.s&this.DM)>>n&&(t[o++]=r|this.s<<this.DB-n);e>=0;)n<8?(r=(this[e]&(1<<n)-1)<<8-n,r|=this[--e]>>(n+=this.DB-8)):(r=this[e]>>(n-=8)&255,n<=0&&(n+=this.DB,--e)),128&r&&(r|=-256),0==o&&(128&this.s)!=(128&r)&&++o,(o>0||r!=this.s)&&(t[o++]=r);return t}equals(e){return 0==this.compareTo(e)}min(e){return this.compareTo(e)<0?this:e}max(e){return this.compareTo(e)>0?this:e}and(e){var t=te();return this.bitwiseTo(e,W,t),t}or(e){var t=te();return this.bitwiseTo(e,F,t),t}xor(e){var t=te();return this.bitwiseTo(e,V,t),t}andNot(e){var t=te();return this.bitwiseTo(e,H,t),t}not(){for(var e=te(),t=0;t<this.t;++t)e[t]=this.DM&~this[t];return e.t=this.t,e.s=~this.s,e}shiftLeft(e){var t=te();return e<0?this.rShiftTo(-e,t):this.lShiftTo(e,t),t}shiftRight(e){var t=te();return e<0?this.lShiftTo(-e,t):this.rShiftTo(e,t),t}getLowestSetBit(){for(var e=0;e<this.t;++e)if(0!=this[e])return e*this.DB+z(this[e]);return this.s<0?this.t*this.DB:-1}bitCount(){for(var e=0,t=this.s&this.DM,r=0;r<this.t;++r)e+=G(this[r]^t);return e}testBit(e){var t=Math.floor(e/this.DB);return t>=this.t?0!=this.s:!!(this[t]&1<<e%this.DB)}setBit(e){return this.changeBit(e,F)}clearBit(e){return this.changeBit(e,H)}flipBit(e){return this.changeBit(e,V)}add(e){var t=te();return this.addTo(e,t),t}subtract(e){var t=te();return this.subTo(e,t),t}multiply(e){var t=te();return this.multiplyTo(e,t),t}divide(e){var t=te();return this.divRemTo(e,t,null),t}remainder(e){var t=te();return this.divRemTo(e,null,t),t}divideAndRemainder(e){var t=te(),r=te();return this.divRemTo(e,t,r),[t,r]}modPow(e,t){var r,n,o=e.bitLength(),i=ae(1);if(o<=0)return i;r=o<18?1:o<48?3:o<144?4:o<768?5:6,n=o<8?new y(t):t.isEven()?new b(t):new _(t);var s=[],a=3,u=r-1,c=(1<<r)-1;if(s[1]=n.convert(this),r>1){var l=te();for(n.sqrTo(s[1],l);a<=c;)s[a]=te(),n.mulTo(l,s[a-2],s[a]),a+=2}var h,d,f=e.t-1,p=!0,v=te();for(o=ue(e[f])-1;f>=0;){for(o>=u?h=e[f]>>o-u&c:(h=(e[f]&(1<<o+1)-1)<<u-o,f>0&&(h|=e[f-1]>>this.DB+o-u)),a=r;!(1&h);)h>>=1,--a;if((o-=a)<0&&(o+=this.DB,--f),p)s[h].copyTo(i),p=!1;else{for(;a>1;)n.sqrTo(i,v),n.sqrTo(v,i),a-=2;a>0?n.sqrTo(i,v):(d=i,i=v,v=d),n.mulTo(v,s[h],i)}for(;f>=0&&!(e[f]&1<<o);)n.sqrTo(i,v),d=i,i=v,v=d,--o<0&&(o=this.DB-1,--f)}return n.revert(i)}modInverse(e){var t=e.isEven();if(this.isEven()&&t||0==e.signum())return ee.ZERO;for(var r=e.clone(),n=this.clone(),o=ae(1),i=ae(0),s=ae(0),a=ae(1);0!=r.signum();){for(;r.isEven();)r.rShiftTo(1,r),t?(o.isEven()&&i.isEven()||(o.addTo(this,o),i.subTo(e,i)),o.rShiftTo(1,o)):i.isEven()||i.subTo(e,i),i.rShiftTo(1,i);for(;n.isEven();)n.rShiftTo(1,n),t?(s.isEven()&&a.isEven()||(s.addTo(this,s),a.subTo(e,a)),s.rShiftTo(1,s)):a.isEven()||a.subTo(e,a),a.rShiftTo(1,a);r.compareTo(n)>=0?(r.subTo(n,r),t&&o.subTo(s,o),i.subTo(a,i)):(n.subTo(r,n),t&&s.subTo(o,s),a.subTo(i,a))}return 0!=n.compareTo(ee.ONE)?ee.ZERO:a.compareTo(e)>=0?a.subtract(e):a.signum()<0?(a.addTo(e,a),a.signum()<0?a.add(e):a):a}pow(e){return this.exp(e,new g)}gcd(e){var t=this.s<0?this.negate():this.clone(),r=e.s<0?e.negate():e.clone();if(t.compareTo(r)<0){var n=t;t=r,r=n}var o=t.getLowestSetBit(),i=r.getLowestSetBit();if(i<0)return t;for(o<i&&(i=o),i>0&&(t.rShiftTo(i,t),r.rShiftTo(i,r));t.signum()>0;)(o=t.getLowestSetBit())>0&&t.rShiftTo(o,t),(o=r.getLowestSetBit())>0&&r.rShiftTo(o,r),t.compareTo(r)>=0?(t.subTo(r,t),t.rShiftTo(1,t)):(r.subTo(t,r),r.rShiftTo(1,r));return i>0&&r.lShiftTo(i,r),r}isProbablePrime(e){var t,r=this.abs();if(1==r.t&&r[0]<=v[v.length-1]){for(t=0;t<v.length;++t)if(r[0]==v[t])return!0;return!1}if(r.isEven())return!1;for(t=1;t<v.length;){for(var n=v[t],o=t+1;o<v.length&&n<m;)n*=v[o++];for(n=r.modInt(n);t<o;)if(n%v[t++]==0)return!1}return r.millerRabin(e)}copyTo(e){for(var t=this.t-1;t>=0;--t)e[t]=this[t];e.t=this.t,e.s=this.s}fromInt(e){this.t=1,this.s=e<0?-1:0,e>0?this[0]=e:e<-1?this[0]=e+this.DV:this.t=0}fromString(e,t){var r;if(16==t)r=4;else if(8==t)r=3;else if(256==t)r=8;else if(2==t)r=1;else if(32==t)r=5;else{if(4!=t)return void this.fromRadix(e,t);r=2}this.t=0,this.s=0;for(var n=e.length,o=!1,i=0;--n>=0;){var s=8==r?255&+e[n]:se(e,n);s<0?"-"==e.charAt(n)&&(o=!0):(o=!1,0==i?this[this.t++]=s:i+r>this.DB?(this[this.t-1]|=(s&(1<<this.DB-i)-1)<<i,this[this.t++]=s>>this.DB-i):this[this.t-1]|=s<<i,(i+=r)>=this.DB&&(i-=this.DB))}8==r&&128&+e[0]&&(this.s=-1,i>0&&(this[this.t-1]|=(1<<this.DB-i)-1<<i)),this.clamp(),o&&ee.ZERO.subTo(this,this)}clamp(){for(var e=this.s&this.DM;this.t>0&&this[this.t-1]==e;)--this.t}dlShiftTo(e,t){var r;for(r=this.t-1;r>=0;--r)t[r+e]=this[r];for(r=e-1;r>=0;--r)t[r]=0;t.t=this.t+e,t.s=this.s}drShiftTo(e,t){for(var r=e;r<this.t;++r)t[r-e]=this[r];t.t=Math.max(this.t-e,0),t.s=this.s}lShiftTo(e,t){for(var r=e%this.DB,n=this.DB-r,o=(1<<n)-1,i=Math.floor(e/this.DB),s=this.s<<r&this.DM,a=this.t-1;a>=0;--a)t[a+i+1]=this[a]>>n|s,s=(this[a]&o)<<r;for(a=i-1;a>=0;--a)t[a]=0;t[i]=s,t.t=this.t+i+1,t.s=this.s,t.clamp()}rShiftTo(e,t){t.s=this.s;var r=Math.floor(e/this.DB);if(r>=this.t)t.t=0;else{var n=e%this.DB,o=this.DB-n,i=(1<<n)-1;t[0]=this[r]>>n;for(var s=r+1;s<this.t;++s)t[s-r-1]|=(this[s]&i)<<o,t[s-r]=this[s]>>n;n>0&&(t[this.t-r-1]|=(this.s&i)<<o),t.t=this.t-r,t.clamp()}}subTo(e,t){for(var r=0,n=0,o=Math.min(e.t,this.t);r<o;)n+=this[r]-e[r],t[r++]=n&this.DM,n>>=this.DB;if(e.t<this.t){for(n-=e.s;r<this.t;)n+=this[r],t[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<e.t;)n-=e[r],t[r++]=n&this.DM,n>>=this.DB;n-=e.s}t.s=n<0?-1:0,n<-1?t[r++]=this.DV+n:n>0&&(t[r++]=n),t.t=r,t.clamp()}multiplyTo(e,t){var r=this.abs(),n=e.abs(),o=r.t;for(t.t=o+n.t;--o>=0;)t[o]=0;for(o=0;o<n.t;++o)t[o+r.t]=r.am(0,n[o],t,o,0,r.t);t.s=0,t.clamp(),this.s!=e.s&&ee.ZERO.subTo(t,t)}squareTo(e){for(var t=this.abs(),r=e.t=2*t.t;--r>=0;)e[r]=0;for(r=0;r<t.t-1;++r){var n=t.am(r,t[r],e,2*r,0,1);(e[r+t.t]+=t.am(r+1,2*t[r],e,2*r+1,n,t.t-r-1))>=t.DV&&(e[r+t.t]-=t.DV,e[r+t.t+1]=1)}e.t>0&&(e[e.t-1]+=t.am(r,t[r],e,2*r,0,1)),e.s=0,e.clamp()}divRemTo(e,t,r){var n=e.abs();if(!(n.t<=0)){var o=this.abs();if(o.t<n.t)return null!=t&&t.fromInt(0),void(null!=r&&this.copyTo(r));null==r&&(r=te());var i=te(),s=this.s,a=e.s,u=this.DB-ue(n[n.t-1]);u>0?(n.lShiftTo(u,i),o.lShiftTo(u,r)):(n.copyTo(i),o.copyTo(r));var c=i.t,l=i[c-1];if(0!=l){var h=l*(1<<this.F1)+(c>1?i[c-2]>>this.F2:0),d=this.FV/h,f=(1<<this.F1)/h,p=1<<this.F2,v=r.t,m=v-c,g=null==t?te():t;for(i.dlShiftTo(m,g),r.compareTo(g)>=0&&(r[r.t++]=1,r.subTo(g,r)),ee.ONE.dlShiftTo(c,g),g.subTo(i,i);i.t<c;)i[i.t++]=0;for(;--m>=0;){var y=r[--v]==l?this.DM:Math.floor(r[v]*d+(r[v-1]+p)*f);if((r[v]+=i.am(0,y,r,m,0,c))<y)for(i.dlShiftTo(m,g),r.subTo(g,r);r[v]<--y;)r.subTo(g,r)}null!=t&&(r.drShiftTo(c,t),s!=a&&ee.ZERO.subTo(t,t)),r.t=c,r.clamp(),u>0&&r.rShiftTo(u,r),s<0&&ee.ZERO.subTo(r,r)}}}invDigit(){if(this.t<1)return 0;var e=this[0];if(!(1&e))return 0;var t=3&e;return(t=(t=(t=(t=t*(2-(15&e)*t)&15)*(2-(255&e)*t)&255)*(2-((65535&e)*t&65535))&65535)*(2-e*t%this.DV)%this.DV)>0?this.DV-t:-t}isEven(){return 0==(this.t>0?1&this[0]:this.s)}exp(e,t){if(e>4294967295||e<1)return ee.ONE;var r=te(),n=te(),o=t.convert(this),i=ue(e)-1;for(o.copyTo(r);--i>=0;)if(t.sqrTo(r,n),(e&1<<i)>0)t.mulTo(n,o,r);else{var s=r;r=n,n=s}return t.revert(r)}chunkSize(e){return Math.floor(Math.LN2*this.DB/Math.log(e))}toRadix(e){if(null==e&&(e=10),0==this.signum()||e<2||e>36)return"0";var t=this.chunkSize(e),r=Math.pow(e,t),n=ae(r),o=te(),i=te(),s="";for(this.divRemTo(n,o,i);o.signum()>0;)s=(r+i.intValue()).toString(e).substr(1)+s,o.divRemTo(n,o,i);return i.intValue().toString(e)+s}fromRadix(e,t){this.fromInt(0),null==t&&(t=10);for(var r=this.chunkSize(t),n=Math.pow(t,r),o=!1,i=0,s=0,a=0;a<e.length;++a){var u=se(e,a);u<0?"-"==e.charAt(a)&&0==this.signum()&&(o=!0):(s=t*s+u,++i>=r&&(this.dMultiply(n),this.dAddOffset(s,0),i=0,s=0))}i>0&&(this.dMultiply(Math.pow(t,i)),this.dAddOffset(s,0)),o&&ee.ZERO.subTo(this,this)}fromNumber(e,t,r){if("number"==typeof t)if(e<2)this.fromInt(1);else for(this.fromNumber(e,r),this.testBit(e-1)||this.bitwiseTo(ee.ONE.shiftLeft(e-1),F,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(t);)this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(ee.ONE.shiftLeft(e-1),this);else{var n=[],o=7&e;n.length=1+(e>>3),t.nextBytes(n),o>0?n[0]&=(1<<o)-1:n[0]=0,this.fromString(n,256)}}bitwiseTo(e,t,r){var n,o,i=Math.min(e.t,this.t);for(n=0;n<i;++n)r[n]=t(this[n],e[n]);if(e.t<this.t){for(o=e.s&this.DM,n=i;n<this.t;++n)r[n]=t(this[n],o);r.t=this.t}else{for(o=this.s&this.DM,n=i;n<e.t;++n)r[n]=t(o,e[n]);r.t=e.t}r.s=t(this.s,e.s),r.clamp()}changeBit(e,t){var r=ee.ONE.shiftLeft(e);return this.bitwiseTo(r,t,r),r}addTo(e,t){for(var r=0,n=0,o=Math.min(e.t,this.t);r<o;)n+=this[r]+e[r],t[r++]=n&this.DM,n>>=this.DB;if(e.t<this.t){for(n+=e.s;r<this.t;)n+=this[r],t[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<e.t;)n+=e[r],t[r++]=n&this.DM,n>>=this.DB;n+=e.s}t.s=n<0?-1:0,n>0?t[r++]=n:n<-1&&(t[r++]=this.DV+n),t.t=r,t.clamp()}dMultiply(e){this[this.t]=this.am(0,e-1,this,0,0,this.t),++this.t,this.clamp()}dAddOffset(e,t){if(0!=e){for(;this.t<=t;)this[this.t++]=0;for(this[t]+=e;this[t]>=this.DV;)this[t]-=this.DV,++t>=this.t&&(this[this.t++]=0),++this[t]}}multiplyLowerTo(e,t,r){var n=Math.min(this.t+e.t,t);for(r.s=0,r.t=n;n>0;)r[--n]=0;for(var o=r.t-this.t;n<o;++n)r[n+this.t]=this.am(0,e[n],r,n,0,this.t);for(o=Math.min(e.t,t);n<o;++n)this.am(0,e[n],r,n,0,t-n);r.clamp()}multiplyUpperTo(e,t,r){--t;var n=r.t=this.t+e.t-t;for(r.s=0;--n>=0;)r[n]=0;for(n=Math.max(t-this.t,0);n<e.t;++n)r[this.t+n-t]=this.am(t-n,e[n],r,0,0,this.t+n-t);r.clamp(),r.drShiftTo(1,r)}modInt(e){if(e<=0)return 0;var t=this.DV%e,r=this.s<0?e-1:0;if(this.t>0)if(0==t)r=this[0]%e;else for(var n=this.t-1;n>=0;--n)r=(t*r+this[n])%e;return r}millerRabin(e){var t=this.subtract(ee.ONE),r=t.getLowestSetBit();if(r<=0)return!1;var n=t.shiftRight(r);(e=e+1>>1)>v.length&&(e=v.length);for(var o=te(),i=0;i<e;++i){o.fromInt(v[Math.floor(Math.random()*v.length)]);var s=o.modPow(n,this);if(0!=s.compareTo(ee.ONE)&&0!=s.compareTo(t)){for(var a=1;a++<r&&0!=s.compareTo(t);)if(0==(s=s.modPowInt(2,this)).compareTo(ee.ONE))return!1;if(0!=s.compareTo(t))return!1}}return!0}square(){var e=te();return this.squareTo(e),e}gcda(e,t){var r=this.s<0?this.negate():this.clone(),n=e.s<0?e.negate():e.clone();if(r.compareTo(n)<0){var o=r;r=n,n=o}var i=r.getLowestSetBit(),s=n.getLowestSetBit();if(s<0)t(r);else{i<s&&(s=i),s>0&&(r.rShiftTo(s,r),n.rShiftTo(s,n));var a=()=>{(i=r.getLowestSetBit())>0&&r.rShiftTo(i,r),(i=n.getLowestSetBit())>0&&n.rShiftTo(i,n),r.compareTo(n)>=0?(r.subTo(n,r),r.rShiftTo(1,r)):(n.subTo(r,n),n.rShiftTo(1,n)),r.signum()>0?setTimeout(a,0):(s>0&&n.lShiftTo(s,n),setTimeout((function(){t(n)}),0))};setTimeout(a,10)}}fromNumberAsync(e,t,r,n){if("number"==typeof t)if(e<2)this.fromInt(1);else{this.fromNumber(e,r),this.testBit(e-1)||this.bitwiseTo(ee.ONE.shiftLeft(e-1),F,this),this.isEven()&&this.dAddOffset(1,0);var o=this,i=function(){o.dAddOffset(2,0),o.bitLength()>e&&o.subTo(ee.ONE.shiftLeft(e-1),o),o.isProbablePrime(t)?setTimeout((function(){n()}),0):setTimeout(i,0)};setTimeout(i,0)}else{var s=[],a=7&e;s.length=1+(e>>3),t.nextBytes(s),a>0?s[0]&=(1<<a)-1:s[0]=0,this.fromString(s,256)}}}var g=function(){function e(){}return e.prototype.convert=function(e){return e},e.prototype.revert=function(e){return e},e.prototype.mulTo=function(e,t,r){e.multiplyTo(t,r)},e.prototype.sqrTo=function(e,t){e.squareTo(t)},e}(),y=function(){function e(e){this.m=e}return e.prototype.convert=function(e){return e.s<0||e.compareTo(this.m)>=0?e.mod(this.m):e},e.prototype.revert=function(e){return e},e.prototype.reduce=function(e){e.divRemTo(this.m,null,e)},e.prototype.mulTo=function(e,t,r){e.multiplyTo(t,r),this.reduce(r)},e.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)},e}(),_=function(){function e(e){this.m=e,this.mp=e.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<e.DB-15)-1,this.mt2=2*e.t}return e.prototype.convert=function(e){var t=te();return e.abs().dlShiftTo(this.m.t,t),t.divRemTo(this.m,null,t),e.s<0&&t.compareTo(ee.ZERO)>0&&this.m.subTo(t,t),t},e.prototype.revert=function(e){var t=te();return e.copyTo(t),this.reduce(t),t},e.prototype.reduce=function(e){for(;e.t<=this.mt2;)e[e.t++]=0;for(var t=0;t<this.m.t;++t){var r=32767&e[t],n=r*this.mpl+((r*this.mph+(e[t]>>15)*this.mpl&this.um)<<15)&e.DM;for(e[r=t+this.m.t]+=this.m.am(0,n,e,t,0,this.m.t);e[r]>=e.DV;)e[r]-=e.DV,e[++r]++}e.clamp(),e.drShiftTo(this.m.t,e),e.compareTo(this.m)>=0&&e.subTo(this.m,e)},e.prototype.mulTo=function(e,t,r){e.multiplyTo(t,r),this.reduce(r)},e.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)},e}(),b=function(){function e(e){this.m=e,this.r2=te(),this.q3=te(),ee.ONE.dlShiftTo(2*e.t,this.r2),this.mu=this.r2.divide(e)}return e.prototype.convert=function(e){if(e.s<0||e.t>2*this.m.t)return e.mod(this.m);if(e.compareTo(this.m)<0)return e;var t=te();return e.copyTo(t),this.reduce(t),t},e.prototype.revert=function(e){return e},e.prototype.reduce=function(e){for(e.drShiftTo(this.m.t-1,this.r2),e.t>this.m.t+1&&(e.t=this.m.t+1,e.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);e.compareTo(this.r2)<0;)e.dAddOffset(1,this.m.t+1);for(e.subTo(this.r2,e);e.compareTo(this.m)>=0;)e.subTo(this.m,e)},e.prototype.mulTo=function(e,t,r){e.multiplyTo(t,r),this.reduce(r)},e.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)},e}();function te(){return new ee(null)}function re(e,t){return new ee(e,t)}function ne(e,t,r,n,o,i){for(;--i>=0;){var s=t*this[e++]+r[n]+o;o=Math.floor(s/67108864),r[n++]=67108863&s}return o}function oe(e,t,r,n,o,i){for(var s=32767&t,a=t>>15;--i>=0;){var u=32767&this[e],c=this[e++]>>15,l=a*u+c*s;o=((u=s*u+((32767&l)<<15)+r[n]+(1073741823&o))>>>30)+(l>>>15)+a*c+(o>>>30),r[n++]=1073741823&u}return o}function ie(e,t,r,n,o,i){for(var s=16383&t,a=t>>14;--i>=0;){var u=16383&this[e],c=this[e++]>>14,l=a*u+c*s;o=((u=s*u+((16383&l)<<14)+r[n]+o)>>28)+(l>>14)+a*c,r[n++]=268435455&u}return o}void 0!==o&&"Microsoft Internet Explorer"==(null==o?void 0:o.appName)?(ee.prototype.am=oe,d=30):"Netscape"!=(null==o?void 0:o.appName)?(ee.prototype.am=ne,d=26):(ee.prototype.am=ie,d=28),ee.prototype.DB=d,ee.prototype.DM=(1<<d)-1,ee.prototype.DV=1<<d,ee.prototype.FV=Math.pow(2,52),ee.prototype.F1=52-d,ee.prototype.F2=2*d-52;var w,S,T=[];for(w="0".charCodeAt(0),S=0;S<=9;++S)T[w++]=S;for(w="a".charCodeAt(0),S=10;S<36;++S)T[w++]=S;for(w="A".charCodeAt(0),S=10;S<36;++S)T[w++]=S;function se(e,t){var r=T[e.charCodeAt(t)];return null==r?-1:r}function ae(e){var t=te();return t.fromInt(e),t}function ue(e){var t,r=1;return 0!=(t=e>>>16)&&(e=t,r+=16),0!=(t=e>>8)&&(e=t,r+=8),0!=(t=e>>4)&&(e=t,r+=4),0!=(t=e>>2)&&(e=t,r+=2),0!=(t=e>>1)&&(e=t,r+=1),r}ee.ZERO=ae(0),ee.ONE=ae(1);class ce{constructor(){this.i=0,this.j=0,this.S=[]}init(e){var t,r,n;for(t=0;t<256;++t)this.S[t]=t;for(r=0,t=0;t<256;++t)r=r+this.S[t]+e[t%e.length]&255,n=this.S[t],this.S[t]=this.S[r],this.S[r]=n;this.i=0,this.j=0}next(){var e;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,e=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=e,this.S[e+this.S[this.i]&255]}}var I,E,R=null;if(null==R){var O,P,C,A;R=[],E=0;var U=void 0;if(null!==(O=window)&&void 0!==O&&O.crypto&&null!==(P=window)&&void 0!==P&&P.crypto.getRandomValues){var L,k=new Uint32Array(256);for(null===(L=window)||void 0===L||L.crypto.getRandomValues(k),U=0;U<k.length;++U)R[E++]=255&k[U]}var N,q=function(e){var t,r,n;if(this.count=this.count||0,this.count>=256||E>=256){if(null!==(t=window)&&void 0!==t&&t.removeEventListener)null===(n=window)||void 0===n||n.removeEventListener("mousemove",q,!1);else if(null!==(r=window)&&void 0!==r&&r.detachEvent){var o;null===(o=window)||void 0===o||o.detachEvent("onmousemove",q)}}else try{var i=e.x+e.y;R[E++]=255&i,this.count+=1}catch(e){}};if(null!==(C=window)&&void 0!==C&&C.addEventListener)null===(N=window)||void 0===N||N.addEventListener("mousemove",q,!1);else if(null!==(A=window)&&void 0!==A&&A.attachEvent){var D;null===(D=window)||void 0===D||D.attachEvent("onmousemove",q)}}function le(){if(null==I){for(I=new ce;E<256;){var e=Math.floor(65536*Math.random());R[E++]=255&e}for(I.init(R),E=0;E<R.length;++E)R[E]=0;E=0}return I.next()}class he{constructor(){}nextBytes(e){for(var t=0;t<e.length;++t)e[t]=le()}}function de(e,t){if(t<e.length+11)return console.error("Message too long for RSA"),null;for(var r=[],n=e.length-1;n>=0&&t>0;){var o=e.charCodeAt(n--);o<128?r[--t]=o:o>127&&o<2048?(r[--t]=63&o|128,r[--t]=o>>6|192):(r[--t]=63&o|128,r[--t]=o>>6&63|128,r[--t]=o>>12|224)}r[--t]=0;for(var i=new he,s=[];t>2;){for(s[0]=0;0==s[0];)i.nextBytes(s);r[--t]=s[0]}return r[--t]=2,r[--t]=0,new ee(r)}class fe{constructor(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}doPublic(e){return e.modPowInt(this.e,this.n)}doPrivate(e){if(null==this.p||null==this.q)return e.modPow(this.d,this.n);for(var t=e.mod(this.p).modPow(this.dmp1,this.p),r=e.mod(this.q).modPow(this.dmq1,this.q);t.compareTo(r)<0;)t=t.add(this.p);return t.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)}setPublic(e,t){null!=e&&null!=t&&e.length>0&&t.length>0?(this.n=re(e,16),this.e=parseInt(t,16)):console.error("Invalid RSA public key")}encrypt(e){var t=de(e,this.n.bitLength()+7>>3);if(null==t)return null;var r=this.doPublic(t);if(null==r)return null;var n=r.toString(16);return 1&n.length?"0"+n:n}encryptLong(e){var t=this,r=(this.n.bitLength()+7>>3)-11;try{var n="";return e.length>r?(e.match(/.{1,117}/g).forEach((function(e){var r=t.encrypt(e);n+=r})),Y(n)):Y(this.encrypt(e))}catch(e){return!1}}decryptLong(e){var t=this,r=this.n.bitLength()+7>>3;e=$(e);try{if(e.length>r){var n="";return e.match(/.{1,256}/g).forEach((function(e){var r=t.decrypt(e);n+=r})),n}return this.decrypt(e)}catch(e){return!1}}setPrivate(e,t,r){null!=e&&null!=t&&e.length>0&&t.length>0?(this.n=re(e,16),this.e=parseInt(t,16),this.d=re(r,16)):console.error("Invalid RSA private key")}setPrivateEx(e,t,r,n,o,i,s,a){null!=e&&null!=t&&e.length>0&&t.length>0?(this.n=re(e,16),this.e=parseInt(t,16),this.d=re(r,16),this.p=re(n,16),this.q=re(o,16),this.dmp1=re(i,16),this.dmq1=re(s,16),this.coeff=re(a,16)):console.error("Invalid RSA private key")}generate(e,t){var r=new he,n=e>>1;this.e=parseInt(t,16);for(var o=new ee(t,16);;){for(;this.p=new ee(e-n,1,r),0!=this.p.subtract(ee.ONE).gcd(o).compareTo(ee.ONE)||!this.p.isProbablePrime(10););for(;this.q=new ee(n,1,r),0!=this.q.subtract(ee.ONE).gcd(o).compareTo(ee.ONE)||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var i=this.p;this.p=this.q,this.q=i}var s=this.p.subtract(ee.ONE),a=this.q.subtract(ee.ONE),u=s.multiply(a);if(0==u.gcd(o).compareTo(ee.ONE)){this.n=this.p.multiply(this.q),this.d=o.modInverse(u),this.dmp1=this.d.mod(s),this.dmq1=this.d.mod(a),this.coeff=this.q.modInverse(this.p);break}}}decrypt(e){var t=re(e,16),r=this.doPrivate(t);return null==r?null:pe(r,this.n.bitLength()+7>>3)}generateAsync(e,t,r){var n=new he,o=e>>1;this.e=parseInt(t,16);var i=new ee(t,16),s=this,a=()=>{var t=()=>{if(s.p.compareTo(s.q)<=0){var e=s.p;s.p=s.q,s.q=e}var t=s.p.subtract(ee.ONE),n=s.q.subtract(ee.ONE),o=t.multiply(n);0==o.gcd(i).compareTo(ee.ONE)?(s.n=s.p.multiply(s.q),s.d=i.modInverse(o),s.dmp1=s.d.mod(t),s.dmq1=s.d.mod(n),s.coeff=s.q.modInverse(s.p),setTimeout((function(){r()}),0)):setTimeout(a,0)},u=()=>{s.q=te(),s.q.fromNumberAsync(o,1,n,(function(){s.q.subtract(ee.ONE).gcda(i,(function(e){0==e.compareTo(ee.ONE)&&s.q.isProbablePrime(10)?setTimeout(t,0):setTimeout(u,0)}))}))},c=()=>{s.p=te(),s.p.fromNumberAsync(e-o,1,n,(function(){s.p.subtract(ee.ONE).gcda(i,(function(e){0==e.compareTo(ee.ONE)&&s.p.isProbablePrime(10)?setTimeout(u,0):setTimeout(c,0)}))}))};setTimeout(c,0)};setTimeout(a,0)}}function pe(e,t){for(var r=e.toByteArray(),n=0;n<r.length&&0==r[n];)++n;if(r.length-n!=t-1||2!=r[n])return null;for(++n;0!=r[n];)if(++n>=r.length)return null;for(var o="";++n<r.length;){var i=255&r[n];i<128?o+=String.fromCharCode(i):i>191&&i<224?(o+=String.fromCharCode((31&i)<<6|63&r[n+1]),++n):(o+=String.fromCharCode((15&i)<<12|(63&r[n+1])<<6|63&r[n+2]),n+=2)}return o}class ve extends fe{constructor(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";super(),e&&("string"==typeof e?this.parseKey(e):(this.hasPrivateKeyProperty(e)||this.hasPublicKeyProperty(e))&&this.parsePropertiesFrom(e))}parseKey(e){try{var t=0,r=0,n=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(e)?c.decode(e):l.unarmor(e),o=J.decode(n);if(3===o.sub.length&&(o=o.sub[2].sub[0]),9===o.sub.length){t=o.sub[1].getHexStringValue(),this.n=re(t,16),r=o.sub[2].getHexStringValue(),this.e=parseInt(r,16);var i=o.sub[3].getHexStringValue();this.d=re(i,16);var s=o.sub[4].getHexStringValue();this.p=re(s,16);var a=o.sub[5].getHexStringValue();this.q=re(a,16);var u=o.sub[6].getHexStringValue();this.dmp1=re(u,16);var h=o.sub[7].getHexStringValue();this.dmq1=re(h,16);var d=o.sub[8].getHexStringValue();this.coeff=re(d,16)}else{if(2!==o.sub.length)return!1;var f=o.sub[1].sub[0];t=f.sub[0].getHexStringValue(),this.n=re(t,16),r=f.sub[1].getHexStringValue(),this.e=parseInt(r,16)}return!0}catch(e){return!1}}hasPublicKeyProperty(e){return(e=e||{}).hasOwnProperty("n")&&e.hasOwnProperty("e")}hasPrivateKeyProperty(e){return(e=e||{}).hasOwnProperty("n")&&e.hasOwnProperty("e")&&e.hasOwnProperty("d")&&e.hasOwnProperty("p")&&e.hasOwnProperty("q")&&e.hasOwnProperty("dmp1")&&e.hasOwnProperty("dmq1")&&e.hasOwnProperty("coeff")}parsePropertiesFrom(e){this.n=e.n,this.e=e.e,e.hasOwnProperty("d")&&(this.d=e.d,this.p=e.p,this.q=e.q,this.dmp1=e.dmp1,this.dmq1=e.dmq1,this.coeff=e.coeff)}}(n=function(e){e=e||{},this.default_key_size=parseInt(e.default_key_size,10)||1024,this.default_public_exponent=e.default_public_exponent||"010001",this.log=e.log||!1,this.key=null}).prototype.setKey=function(e){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new ve(e)},n.prototype.setPrivateKey=function(e){this.setKey(e)},n.prototype.setPublicKey=function(e){this.setKey(e)},n.prototype.decrypt=function(e){try{return this.getKey().decrypt($(e))}catch(e){return!1}},n.prototype.encrypt=function(e){try{return Y(this.getKey().encrypt(e))}catch(e){return!1}},n.prototype.encryptLong=function(e){try{for(var t=this.getKey().encryptLong(e)||"",r=this.getKey().decryptLong(t)||"",n=0,o=/null$/g;o.test(r)&&(n++,t=this.getKey().encryptLong(e)||"",r=this.getKey().decryptLong(t)||"",!(n>10)););return t}catch(e){return!1}},n.prototype.getKey=function(e){if(!this.key){if(this.key=new ve,e&&"[object Function]"==={}.toString.call(e))return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,e);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},n.version="3.1.4"}const x=n;var j=r(655),M=function(){var{publicKey:e="",payload:t={}}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!e)return"";try{var r=(0,j.I)(t),n=new x;return n.setPublicKey(e),n.encryptLong("object"==typeof r?JSON.stringify(r):r)}catch(e){console.error("encrypt error:",e)}return""}},655:(e,t,r)=>{r.d(t,{I:()=>n,y:()=>o});var n=e=>{var t=t=>{for(var r in e)e.hasOwnProperty(r)&&(t[r]=n(e[r]));return t},r=null==e?"NullOrUndefined":Object.prototype.toString.call(e).slice(8,-1);if(["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array","BigInt64Array","BigUint64Array"].includes(r))return e.slice();switch(r){case"Object":return t(Object.create(Object.getPrototypeOf(e)));case"Array":return t([]);case"Date":return new Date(e.valueOf());case"RegExp":return new RegExp(e.source,(e.global?"g":"")+(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.sticky?"y":"")+(e.unicode?"u":""));default:return e}},o=e=>{var t=e.match(/^(?:http(s)?:\/\/[^\/]+)?(\/[^\?#]*)/);return t&&t[2]||""}},182:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.useDefaultAdapter=t.useAdapters=t.RUNTIME=void 0;var s,a=i(r(274)),u=r(616);!function(e){e.WEB="web",e.WX_MP="wx_mp"}(s=t.RUNTIME||(t.RUNTIME={})),t.useAdapters=function(e){for(var t=0,r=(0,u.isArray)(e)?e:[e];t<r.length;t++){var n=r[t],o=n.isMatch,i=n.genAdapter,s=n.runtime;if(o())return{adapter:i(),runtime:s}}},t.useDefaultAdapter=function(){return{adapter:a.genAdapter(),runtime:s.WEB}}},274:function(e,t,r){var n,o=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},i.apply(this,arguments)},s=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}u((n=n.apply(e,t||[])).next())}))},a=this&&this.__generator||function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((o=(o=s.trys).length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.WebRequest=t.genAdapter=void 0;var u=r(325),c=r(616),l=r(946),h=function(e){function t(t){var r=e.call(this)||this,n=t.timeout,o=t.timeoutMsg,i=t.restrictedMethods;return r.timeout=n||0,r.timeoutMsg=o||"请求超时",r.restrictedMethods=i||["get","post","upload","download"],r}return o(t,e),t.prototype.get=function(e){return this.request(i(i({},e),{method:"get"}),this.restrictedMethods.includes("get"))},t.prototype.post=function(e){return this.request(i(i({},e),{method:"post"}),this.restrictedMethods.includes("post"))},t.prototype.put=function(e){return this.request(i(i({},e),{method:"put"}))},t.prototype.upload=function(e){var t=e.data,r=e.file,n=e.name,o=e.method,s=e.headers,a=void 0===s?{}:s,u={post:"post",put:"put"}[null==o?void 0:o.toLowerCase()]||"put",c=new FormData;return"post"===u?(Object.keys(t).forEach((function(e){c.append(e,t[e])})),c.append("key",n),c.append("file",r),this.request(i(i({},e),{data:c,method:u}),this.restrictedMethods.includes("upload"))):this.request(i(i({},e),{method:"put",headers:a,body:r}),this.restrictedMethods.includes("upload"))},t.prototype.download=function(e){return s(this,void 0,void 0,(function(){var t,r,n,o;return a(this,(function(s){switch(s.label){case 0:return s.trys.push([0,2,,3]),[4,this.get(i(i({},e),{headers:{},responseType:"blob"}))];case 1:return t=s.sent().data,r=window.URL.createObjectURL(new Blob([t])),n=decodeURIComponent(new URL(e.url).pathname.split("/").pop()||""),(o=document.createElement("a")).href=r,o.setAttribute("download",n),o.style.display="none",document.body.appendChild(o),o.click(),window.URL.revokeObjectURL(r),document.body.removeChild(o),[3,3];case 2:return s.sent(),[3,3];case 3:return[2,new Promise((function(t){t({statusCode:200,tempFilePath:e.url})}))]}}))}))},t.prototype.fetch=function(e){var t;return s(this,void 0,void 0,(function(){var r,n,o,u,c,l,h,d,f,p,v,m=this;return a(this,(function(g){switch(g.label){case 0:return r=new AbortController,n=e.url,o=e.enableAbort,u=void 0!==o&&o,c=e.stream,l=void 0!==c&&c,h=e.signal,d=e.timeout,f=null!=d?d:this.timeout,h&&(h.aborted&&r.abort(),h.addEventListener("abort",(function(){return r.abort()}))),p=null,u&&f&&(p=setTimeout((function(){console.warn(m.timeoutMsg),r.abort(new Error(m.timeoutMsg))}),f)),[4,fetch(n,i(i({},e),{signal:r.signal})).then((function(e){return s(m,void 0,void 0,(function(){var t,r,n;return a(this,(function(o){switch(o.label){case 0:return clearTimeout(p),e.ok?(t=e,[3,3]):[3,1];case 1:return n=(r=Promise).reject,[4,e.json()];case 2:t=n.apply(r,[o.sent()]),o.label=3;case 3:return[2,t]}}))}))})).catch((function(e){return clearTimeout(p),Promise.reject(e)}))];case 1:return v=g.sent(),[2,{data:l?v.body:(null===(t=v.headers.get("content-type"))||void 0===t?void 0:t.includes("application/json"))?v.json():v.text(),statusCode:v.status,header:v.headers}]}}))}))},t.prototype.request=function(e,t){var r=this;void 0===t&&(t=!1);var n=String(e.method).toLowerCase()||"get";return new Promise((function(o){var i,s,a=e.url,u=e.headers,h=void 0===u?{}:u,d=e.data,f=e.responseType,p=e.withCredentials,v=e.body,m=e.onUploadProgress,g=(0,c.formatUrl)((0,l.getProtocol)(),a,"get"===n?d:{}),y=new XMLHttpRequest;y.open(n,g),f&&(y.responseType=f),Object.keys(h).forEach((function(e){y.setRequestHeader(e,h[e])})),m&&y.upload.addEventListener("progress",m),y.onreadystatechange=function(){var e={};if(4===y.readyState){var t=y.getAllResponseHeaders().trim().split(/[\r\n]+/),r={};t.forEach((function(e){var t=e.split(": "),n=t.shift().toLowerCase(),o=t.join(": ");r[n]=o})),e.header=r,e.statusCode=y.status;try{e.data="blob"===f?y.response:JSON.parse(y.responseText)}catch(t){e.data="blob"===f?y.response:y.responseText}clearTimeout(i),o(e)}},t&&r.timeout&&(i=setTimeout((function(){console.warn(r.timeoutMsg),y.abort()}),r.timeout)),s=(0,c.isFormData)(d)?d:"application/x-www-form-urlencoded"===h["content-type"]?(0,c.toQueryString)(d):v||(d?JSON.stringify(d):void 0),p&&(y.withCredentials=!0),y.send(s)}))},t}(u.AbstractSDKRequest);t.WebRequest=h,t.genAdapter=function(){return{root:window,reqClass:h,wsClass:WebSocket,localStorage}}},946:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.COMMUNITY_SITE_URL=t.IS_DEBUG_MODE=t.getProtocol=t.setProtocol=t.getSdkName=t.setSdkName=void 0;var r="@cloudbase/js-sdk";t.setSdkName=function(e){r=e},t.getSdkName=function(){return r};var n="https:";t.setProtocol=function(e){n=e},t.getProtocol=function(){return n},t.IS_DEBUG_MODE=!1,t.COMMUNITY_SITE_URL="https://support.qq.com/products/148793"},205:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ERRORS=void 0,t.ERRORS={INVALID_PARAMS:"INVALID_PARAMS",INVALID_SYNTAX:"INVALID_SYNTAX",INVALID_OPERATION:"INVALID_OPERATION",OPERATION_FAIL:"OPERATION_FAIL",NETWORK_ERROR:"NETWORK_ERROR",UNKOWN_ERROR:"UNKOWN_ERROR"}},794:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.OATUH_LOGINTYPE=void 0,o(r(946),t),o(r(205),t),t.OATUH_LOGINTYPE="constants"},718:function(e,t,r){var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}u((n=n.apply(e,t||[])).next())}))},o=this&&this.__generator||function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((o=(o=s.trys).length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.catchErrorsDecorator=void 0;var i=r(616),s=r(794),a=!1;"undefined"!=typeof navigator&&navigator.userAgent&&(a=-1!==navigator.userAgent.indexOf("Firefox"));var u=a?/(\.js\/)?__decorate(\$\d+)?<@.*\d$/:/(\/\w+\.js\.)?__decorate(\$\d+)?\s*\(.*\)$/,c=/https?:\/\/.+:\d*\/.*\.js:\d+:\d+/;function l(e){var t=e.err,r=e.className,n=e.methodName,o=e.sourceLink;if(!o)return null;var i,s=t.stack.split("\n"),u=a?/^catchErrorsDecorator\/<\/descriptor.value@.*\d$/:new RegExp("".concat(r,"\\.descriptor.value\\s*\\[as\\s").concat(n,"\\]\\s*\\(.*\\)$")),l=a?/^catchErrorsDecorator\/<\/descriptor.value/:new RegExp("".concat(r,"\\.descriptor.value\\s*\\[as\\s").concat(n,"\\]")),h=s.findIndex((function(e){return u.test(e)}));if(-1!==h){var d=s.filter((function(e,t){return t>h}));d.unshift(s[h].replace(l,"".concat(r,".").concat(n)).replace(c,o)),(i=new Error).stack="".concat(a?"@debugger":"Error","\n").concat(d.join("\n"))}return i}t.catchErrorsDecorator=function(e){var t=e.mode,r=void 0===t?"async":t,a=e.customInfo,h=void 0===a?{}:a,d=e.title,f=e.messages,p=void 0===f?[]:f;return function(e,t,a){if(s.IS_DEBUG_MODE){var f=h.className||e.constructor.name,v=h.methodName||t,m=a.value,g=function(){var e="",t=(new Error).stack.split("\n"),r=t.findIndex((function(e){return u.test(e)}));if(-1!==r){var n=c.exec(t[r+1]||"");e=n?n[0]:""}return e}();a.value="sync"===r?function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=l({err:new Error,className:f,methodName:v,sourceLink:g});try{return m.apply(this,e)}catch(e){var n=e,o=e.message,s=e.error,a=e.error_description,u={title:d||"".concat(f,".").concat(v," failed"),content:[{type:"error",body:e}]};if(o&&/^\{.*\}$/.test(o)){var c=JSON.parse(o);u.subtitle=o,c.code&&(r?(r.code=c.code,r.msg=c.msg):(e.code=c.code,e.message=c.msg),n=r||e,u.content=p.map((function(e){return{type:"info",body:e}})))}throw s&&a&&(u.subtitle=a,r?(r.code=s,r.msg=a):(e.code=s,e.message=a),n=r||e,u.content=p.map((function(e){return{type:"info",body:e}}))),(0,i.printGroupLog)(u),n}}:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n(this,void 0,void 0,(function(){var t,r,n,s,a,u,c,h;return o(this,(function(o){switch(o.label){case 0:t=l({err:new Error,className:f,methodName:v,sourceLink:g}),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,m.apply(this,e)];case 2:return[2,o.sent()];case 3:throw r=o.sent(),n=r,s=r.message,a=r.error,u=r.error_description,c={title:d||"".concat(f,".").concat(v," failed"),content:[{type:"error",body:r}]},s&&/^\{.*\}$/.test(s)&&(h=JSON.parse(s),c.subtitle=h,h.code&&(t?(t.code=h.code,t.message=h.msg):(r.code=h.code,r.message=h.msg),n=t||r,c.content=p.map((function(e){return{type:"info",body:e}})))),a&&u&&(c.subtitle=u,t?(t.code=a,t.msg=u):(r.code=a,r.message=u),n=t||r,c.content=p.map((function(e){return{type:"info",body:e}}))),(0,i.printGroupLog)(c),n;case 4:return[2]}}))}))}}}}},141:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),o(r(718),t)},197:function(e,t,r){var n,o=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),i=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function s(e){try{u(n.next(e))}catch(e){i(e)}}function a(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}u((n=n.apply(e,t||[])).next())}))},s=this&&this.__generator||function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((o=(o=s.trys).length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.CloudbaseCache=void 0;var a=r(325),u=r(616),c=r(794),l=function(e){function t(t){var r=e.call(this)||this;return r.root=t,t.tcbCacheObject||(t.tcbCacheObject={}),r}return o(t,e),t.prototype.setItem=function(e,t){this.root.tcbCacheObject[e]=t},t.prototype.getItem=function(e){return this.root.tcbCacheObject[e]},t.prototype.removeItem=function(e){delete this.root.tcbCacheObject[e]},t.prototype.clear=function(){delete this.root.tcbCacheObject},t}(a.AbstractStorage),h=function(){function e(e){this.keys={};var t=e.persistence,r=e.platformInfo,n=void 0===r?{}:r,o=e.keys,i=void 0===o?{}:o;this.platformInfo=n,this.storage||(this.persistenceTag=this.platformInfo.adapter.primaryStorage||t,this.storage=function(e,t){switch(e){case"local":default:return t.localStorage?t.localStorage:((0,u.printWarn)(c.ERRORS.INVALID_PARAMS,"localStorage is not supported on current platform"),new l(t.root));case"none":return new l(t.root)}}(this.persistenceTag,this.platformInfo.adapter),this.keys=i)}return Object.defineProperty(e.prototype,"mode",{get:function(){return this.storage.mode||"sync"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"persistence",{get:function(){return this.persistenceTag},enumerable:!1,configurable:!0}),e.prototype.setStore=function(e,t,r){if("async"!==this.mode){if(this.storage)try{var n={version:r||"localCachev1",content:t};this.storage.setItem(e,JSON.stringify(n))}catch(e){throw new Error(JSON.stringify({code:c.ERRORS.OPERATION_FAIL,msg:"[".concat((0,c.getSdkName)(),"][").concat(c.ERRORS.OPERATION_FAIL,"]setStore failed"),info:e}))}}else(0,u.printWarn)(c.ERRORS.INVALID_OPERATION,"current platform's storage is asynchronous, please use setStoreAsync insteed")},e.prototype.setStoreAsync=function(e,t,r){return i(this,void 0,void 0,(function(){var n;return s(this,(function(o){switch(o.label){case 0:if(!this.storage)return[2];o.label=1;case 1:return o.trys.push([1,3,,4]),n={version:r||"localCachev1",content:t},[4,this.storage.setItem(e,JSON.stringify(n))];case 2:return o.sent(),[3,4];case 3:return o.sent(),[2];case 4:return[2]}}))}))},e.prototype.getStore=function(e,t){var r;if("async"!==this.mode){try{if("undefined"!=typeof process&&(null===(r=process.env)||void 0===r?void 0:r.tcb_token))return process.env.tcb_token;if(!this.storage)return""}catch(e){return""}t=t||"localCachev1";var n=this.storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""}(0,u.printWarn)(c.ERRORS.INVALID_OPERATION,"current platform's storage is asynchronous, please use getStoreAsync insteed")},e.prototype.getStoreAsync=function(e,t){var r;return i(this,void 0,void 0,(function(){var n;return s(this,(function(o){switch(o.label){case 0:try{if("undefined"!=typeof process&&(null===(r=process.env)||void 0===r?void 0:r.tcb_token))return[2,process.env.tcb_token];if(!this.storage)return[2,""]}catch(e){return[2,""]}return t=t||"localCachev1",[4,this.storage.getItem(e)];case 1:return(n=o.sent())&&n.indexOf(t)>=0?[2,JSON.parse(n).content]:[2,""]}}))}))},e.prototype.removeStore=function(e){"async"!==this.mode?this.storage.removeItem(e):(0,u.printWarn)(c.ERRORS.INVALID_OPERATION,"current platform's storage is asynchronous, please use removeStoreAsync insteed")},e.prototype.removeStoreAsync=function(e){return i(this,void 0,void 0,(function(){return s(this,(function(t){switch(t.label){case 0:return[4,this.storage.removeItem(e)];case 1:return t.sent(),[2]}}))}))},e}();t.CloudbaseCache=h},689:function(e,t,r){var n,o=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),i=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.removeEventListener=t.activateEvent=t.addEventListener=t.CloudbaseEventEmitter=t.IErrorEvent=t.CloudbaseEvent=void 0;var s=r(616),a=function(e,t){this.data=t||null,this.name=e};t.CloudbaseEvent=a;var u=function(e){function t(t,r){var n=e.call(this,"error",{error:t,data:r})||this;return n.error=t,n}return o(t,e),t}(a);t.IErrorEvent=u;var c=function(){function e(){this.listeners={}}return e.prototype.on=function(e,t){return function(e,t,r){r[e]=r[e]||[],r[e].push(t)}(e,t,this.listeners),this},e.prototype.off=function(e,t){return function(e,t,r){if(null==r?void 0:r[e]){var n=r[e].indexOf(t);-1!==n&&r[e].splice(n,1)}}(e,t,this.listeners),this},e.prototype.fire=function(e,t){if((0,s.isInstanceOf)(e,u))return console.error(e.error),this;var r=(0,s.isString)(e)?new a(e,t||{}):e,n=r.name;if(this.listens(n)){r.target=this;for(var o=0,c=this.listeners[n]?i([],this.listeners[n],!0):[];o<c.length;o++)c[o].call(this,r)}return this},e.prototype.listens=function(e){return this.listeners[e]&&this.listeners[e].length>0},e}();t.CloudbaseEventEmitter=c;var l=new c;t.addEventListener=function(e,t){l.on(e,t)},t.activateEvent=function(e,t){void 0===t&&(t={}),l.fire(e,t)},t.removeEventListener=function(e,t){l.off(e,t)}},616:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.transformPhone=t.sleep=t.printGroupLog=t.throwError=t.printInfo=t.printError=t.printWarn=t.execCallback=t.createPromiseCallback=t.removeParam=t.getHash=t.getQuery=t.toQueryString=t.formatUrl=t.generateRequestId=t.genSeqId=t.isFormData=t.isInstanceOf=t.isNull=t.isPalinObject=t.isUndefined=t.isString=t.isArray=void 0;var n=r(794);t.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)},t.isString=function(e){return"string"==typeof e},t.isUndefined=function(e){return void 0===e},t.isPalinObject=function(e){return"[object Object]"===Object.prototype.toString.call(e)},t.isNull=function(e){return"[object Null]"===Object.prototype.toString.call(e)},t.isInstanceOf=function(e,t){return e instanceof t},t.isFormData=function(e){return"[object FormData]"===Object.prototype.toString.call(e)},t.genSeqId=function(){return Math.random().toString(16).slice(2)},t.generateRequestId=function(){var e=(new Date).getTime(),t=(null===performance||void 0===performance?void 0:performance.now)&&1e3*performance.now()||0;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(r){var n=16*Math.random();return e>0?(n=(e+n)%16|0,e=Math.floor(e/16)):(n=(t+n)%16|0,t=Math.floor(t/16)),("x"===r?n:7&n|8).toString(16)}))},t.formatUrl=function(e,t,r){void 0===r&&(r={});var n=/\?/.test(t),o="";return Object.keys(r).forEach((function(e){""===o?!n&&(t+="?"):o+="&",o+="".concat(e,"=").concat(encodeURIComponent(r[e]))})),/^http(s)?:\/\//.test(t+=o)?t:"".concat(e).concat(t)},t.toQueryString=function(e){void 0===e&&(e={});var t=[];return Object.keys(e).forEach((function(r){t.push("".concat(r,"=").concat(encodeURIComponent(e[r])))})),t.join("&")},t.getQuery=function(e,t){if("undefined"==typeof window)return!1;var r=t||window.location.search,n=new RegExp("(^|&)".concat(e,"=([^&]*)(&|$)")),o=r.substr(r.indexOf("?")+1).match(n);return null!=o?o[2]:""},t.getHash=function(e){if("undefined"==typeof window)return"";var t=window.location.hash.match(new RegExp("[#?&/]".concat(e,"=([^&#]*)")));return t?t[1]:""},t.removeParam=function(e,t){var r=t.split("?")[0],n=[],o=-1!==t.indexOf("?")?t.split("?")[1]:"";if(""!==o){for(var i=(n=o.split("&")).length-1;i>=0;i-=1)n[i].split("=")[0]===e&&n.splice(i,1);r="".concat(r,"?").concat(n.join("&"))}return r},t.createPromiseCallback=function(){var e;if(!Promise){(e=function(){}).promise={};var t=function(){throw new Error('Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.')};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}var r=new Promise((function(t,r){e=function(e,n){return e?r(e):t(n)}}));return e.promise=r,e},t.execCallback=function(e,t,r){if(void 0===r&&(r=null),e&&"function"==typeof e)return e(t,r);if(t)throw t;return r},t.printWarn=function(e,t){console.warn("[".concat((0,n.getSdkName)(),"][").concat(e,"]:").concat(t))},t.printError=function(e,t){console.error({code:e,msg:"[".concat((0,n.getSdkName)(),"][").concat(e,"]:").concat(t)})},t.printInfo=function(e,t){console.log("[".concat((0,n.getSdkName)(),"][").concat(e,"]:").concat(t))},t.throwError=function(e,t){throw new Error(JSON.stringify({code:e,msg:"[".concat((0,n.getSdkName)(),"][").concat(e,"]:").concat(t)}))},t.printGroupLog=function(e){var t=e.title,r=e.subtitle,n=void 0===r?"":r,o=e.content,i=void 0===o?[]:o,s=e.printTrace,a=void 0!==s&&s,u=e.collapsed;void 0!==u&&u?console.groupCollapsed(t,n):console.group(t,n);for(var c=0,l=i;c<l.length;c++){var h=l[c],d=h.type,f=h.body;switch(d){case"info":console.log(f);break;case"warn":console.warn(f);break;case"error":console.error(f)}}a&&console.trace("stack trace:"),console.groupEnd()},t.sleep=function(e){return void 0===e&&(e=0),new Promise((function(t){return setTimeout(t,e)}))},t.transformPhone=function(e){return"+86".concat(e)}}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n].call(i.exports,i,i.exports,r),i.exports}r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return(()=>{var e=n;Object.defineProperty(e,"__esModule",{value:!0}),e.registerAuth=void 0;var t=r(232);e.registerAuth=t.registerAuth})(),n})()));