import { ApiUrls, ErrorType } from '../auth/consts';
import { defaultStorage } from '../oauth2client/oauth2client';
import { isInMpWebView, isMp } from '../utils/mp';
import MyURLSearchParams from '../utils/urlSearchParams';
export class Captcha {
    constructor(opts) {
        if (!opts.openURIWithCallback) {
            opts.openURIWithCallback = this.getDefaultOpenURIWithCallback();
        }
        if (!opts.storage) {
            opts.storage = defaultStorage;
        }
        this.config = opts;
        this.tokenSectionName = `captcha_${opts.clientId}`;
    }
    async request(url, options) {
        if (!options) {
            options = {};
        }
        if (!options.method) {
            options.method = 'GET';
        }
        const state = `${options.method}:${url}`;
        let reqURL = url;
        if (options.withCaptcha) {
            reqURL = await this.appendCaptchaTokenToURL(url, state, false);
        }
        let resp;
        try {
            resp = await this.config.request(reqURL, options);
        }
        catch (err) {
            if (err.error === ErrorType.CAPTCHA_REQUIRED || err.error === ErrorType.CAPTCHA_INVALID) {
                url = await this.appendCaptchaTokenToURL(url, state, err.error === ErrorType.CAPTCHA_INVALID);
                return this.config.request(url, options);
            }
            return Promise.reject(err);
        }
        return resp;
    }
    getDefaultOpenURIWithCallback() {
        if (!isMp() && !isInMpWebView()) {
            if (window.location.search.indexOf('__captcha') > 0) {
                document.body.style.display = 'none';
            }
            if (document.getElementById('captcha_panel_wrap') === null) {
                const elementDiv = document.createElement('div');
                elementDiv.style.cssText = 'background-color: rgba(0, 0, 0, 0.7);position: fixed;left: 0px;right: 0px;top: 0px;bottom: 0px;padding: 9vw 0 0 0;display: none;z-index:100;';
                elementDiv.setAttribute('id', 'captcha_panel_wrap');
                setTimeout(() => {
                    document.body.appendChild(elementDiv);
                }, 0);
            }
        }
        return this.defaultOpenURIWithCallback;
    }
    async defaultOpenURIWithCallback(url, opts) {
        const { width = '355px', height = '355px' } = opts || {};
        const matched = url.match(/^(data:.*)$/);
        if (matched) {
            return Promise.reject({
                error: ErrorType.UNIMPLEMENTED,
                error_description: 'need to impl captcha data',
            });
        }
        const target = document.getElementById('captcha_panel_wrap');
        const iframe = document.createElement('iframe');
        target.innerHTML = '';
        iframe.setAttribute('src', url);
        iframe.setAttribute('id', 'review-panel-iframe');
        iframe.style.cssText = `min-width:${width};display:block;height:${height};margin:0 auto;background-color: rgb(255, 255, 255);border: none;`;
        target.appendChild(iframe);
        target.style.display = 'block';
        return new Promise((resolve, reject) => {
            iframe.onload = function () {
                try {
                    const windowLocation = window.location;
                    const iframeLocation = iframe.contentWindow.location;
                    if (iframeLocation.host + iframeLocation.pathname === windowLocation.host + windowLocation.pathname) {
                        target.style.display = 'none';
                        const iframeUrlParams = new MyURLSearchParams(iframeLocation.search);
                        const captchToken = iframeUrlParams.get('captcha_token');
                        if (captchToken) {
                            return resolve({
                                captcha_token: captchToken,
                                expires_in: Number(iframeUrlParams.get('expires_in')),
                            });
                        }
                        return reject({
                            error: iframeUrlParams.get('error'),
                            error_description: iframeUrlParams.get('error_description'),
                        });
                    }
                    target.style.display = 'block';
                }
                catch (error) {
                    target.style.display = 'block';
                }
            };
        });
    }
    async getCaptchaToken(forceNewToken, state) {
        if (!forceNewToken) {
            const captchaToken = await this.findCaptchaToken();
            if (captchaToken) {
                return captchaToken;
            }
        }
        let captchaTokenResp;
        if (!isMp() && !isInMpWebView()) {
            const redirect_uri = `${window.location.origin + window.location.pathname}?__captcha=on`;
            captchaTokenResp = await this.config.request(ApiUrls.GET_CAPTCHA_URL, {
                method: 'POST',
                body: {
                    client_id: this.config.clientId,
                    redirect_uri,
                    state,
                },
                withCredentials: false,
            });
            if (captchaTokenResp.captcha_token) {
                const captchaToken = {
                    captcha_token: captchaTokenResp.captcha_token,
                    expires_in: captchaTokenResp.expires_in,
                };
                this.saveCaptchaToken(captchaToken);
                return captchaTokenResp.captcha_token;
            }
        }
        else {
            const captchaDataResp = await this.config.request(ApiUrls.CAPTCHA_DATA_URL, {
                method: 'POST',
                body: {
                    state,
                    redirect_uri: '',
                },
                withCredentials: false,
            });
            captchaTokenResp = {
                url: `${captchaDataResp.data}?state=${encodeURIComponent(state)}&token=${encodeURIComponent(captchaDataResp.token)}`,
            };
        }
        const captchaToken = await this.config.openURIWithCallback(captchaTokenResp.url);
        this.saveCaptchaToken(captchaToken);
        return captchaToken.captcha_token;
    }
    async appendCaptchaTokenToURL(url, state, forceNewToken) {
        const captchaToken = await this.getCaptchaToken(forceNewToken, state);
        if (url.indexOf('?') > 0) {
            url += `&captcha_token=${captchaToken}`;
        }
        else {
            url += `?captcha_token=${captchaToken}`;
        }
        return url;
    }
    async saveCaptchaToken(token) {
        token.expires_at = new Date(Date.now() + (token.expires_in - 10) * 1000);
        const tokenStr = JSON.stringify(token);
        await this.config.storage.setItem(this.tokenSectionName, tokenStr);
    }
    async findCaptchaToken() {
        const tokenStr = await this.config.storage.getItem(this.tokenSectionName);
        if (tokenStr !== undefined && tokenStr !== null) {
            try {
                const captchaToken = JSON.parse(tokenStr);
                if (captchaToken?.expires_at) {
                    captchaToken.expires_at = new Date(captchaToken.expires_at);
                }
                const isExpired = captchaToken.expires_at < new Date();
                if (isExpired) {
                    return null;
                }
                return captchaToken.captcha_token;
            }
            catch (error) {
                await this.config.storage.removeItem(this.tokenSectionName);
                return null;
            }
        }
        return null;
    }
}
