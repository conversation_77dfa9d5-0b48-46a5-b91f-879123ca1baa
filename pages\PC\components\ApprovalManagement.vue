<template>
	<view class="approval-management">
		<view class="approval-header">
			<text class="approval-title">审批管理中心</text>
			<view class="approval-controls">
				<view class="status-filter">
					<picker :value="statusFilterIndex" :range="statusOptions" @change="onStatusChange">
						<view class="picker-text">{{ statusOptions[statusFilterIndex] }}</view>
					</picker>
				</view>
				<view class="refresh-btn" @click="refreshData">
					<text>刷新</text>
					<text class="refresh-icon">🔄</text>
				</view>
			</view>
		</view>

		<view class="approval-stats">
			<view class="stat-card pending">
				<view class="stat-icon">⏳</view>
				<view class="stat-content">
					<text class="stat-number">{{ stats.pending }}</text>
					<text class="stat-label">待审批</text>
				</view>
			</view>

			<view class="stat-card approved">
				<view class="stat-icon">✅</view>
				<view class="stat-content">
					<text class="stat-number">{{ stats.approved }}</text>
					<text class="stat-label">已通过</text>
				</view>
			</view>

			<view class="stat-card rejected">
				<view class="stat-icon">❌</view>
				<view class="stat-content">
					<text class="stat-number">{{ stats.rejected }}</text>
					<text class="stat-label">已拒绝</text>
				</view>
			</view>

			<view class="stat-card total">
				<view class="stat-icon">📊</view>
				<view class="stat-content">
					<text class="stat-number">{{ stats.total }}</text>
					<text class="stat-label">总计</text>
				</view>
			</view>
		</view>

		<view class="approval-table">
			<view class="table-header">
				<text class="table-title">审批列表</text>
				<view class="table-controls">
					<view class="batch-approve-btn" @click="batchApprove" v-if="selectedItems.length > 0">
						<text>批量审批({{ selectedItems.length }})</text>
					</view>
				</view>
			</view>

			<view class="table-content">
				<view class="table-row table-header-row">
					<view class="table-cell checkbox">
						<checkbox @change="selectAll" :checked="allSelected" />
					</view>
					<text class="table-cell submitter">申请人</text>
					<text class="table-cell type">类型</text>
					<text class="table-cell title">标题</text>
					<text class="table-cell amount">金额</text>
					<text class="table-cell submit-time">提交时间</text>
					<text class="table-cell status">状态</text>
					<text class="table-cell actions">操作</text>
				</view>

				<view v-if="loading" class="loading-row">
					<text>加载中...</text>
				</view>

				<view v-else-if="filteredApprovals.length === 0" class="empty-row">
					<text>暂无审批数据</text>
				</view>

				<view v-else v-for="(item, index) in filteredApprovals" :key="index" class="table-row">
					<view class="table-cell checkbox">
						<checkbox 
							:checked="selectedItems.includes(item._id)" 
							@change="toggleSelect(item._id)"
							:disabled="item.status !== 'pending'"
						/>
					</view>
					<text class="table-cell submitter">{{ item.submitter || '-' }}</text>
					<text class="table-cell type">
						<view class="type-tag" :class="item.type">
							{{ getApprovalTypeText(item.type) }}
						</view>
					</text>
					<text class="table-cell title">{{ item.title || item.reason || '-' }}</text>
					<text class="table-cell amount">¥{{ formatMoney(item.amount) }}</text>
					<text class="table-cell submit-time">{{ formatDate(item.submitTime) }}</text>
					<text class="table-cell status">
						<view class="status-tag" :class="item.status">
							{{ getStatusText(item.status) }}
						</view>
					</text>
					<view class="table-cell actions">
						<view v-if="item.status === 'pending'" class="action-buttons">
							<view class="approve-btn" @click="approveItem(item)">
								<text>通过</text>
							</view>
							<view class="reject-btn" @click="rejectItem(item)">
								<text>拒绝</text>
							</view>
						</view>
						<view class="view-btn" @click="viewDetail(item)">
							<text>查看</text>
						</view>
					</view>
				</view>
			</view>

			<view v-if="filteredApprovals.length > 0" class="table-pagination">
				<text class="pagination-info">
					共 {{ filteredApprovals.length }} 条记录
				</text>
			</view>
		</view>

		<!-- 审批详情弹窗 -->
		<view v-if="showDetail" class="detail-modal" @click="closeDetail">
			<view class="detail-content" @click.stop>
				<view class="detail-header">
					<text class="detail-title">审批详情</text>
					<view class="close-btn" @click="closeDetail">×</view>
				</view>
				<view class="detail-body">
					<view class="detail-item">
						<text class="detail-label">申请人:</text>
						<text class="detail-value">{{ currentItem.submitter }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">类型:</text>
						<text class="detail-value">{{ getApprovalTypeText(currentItem.type) }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">金额:</text>
						<text class="detail-value">¥{{ formatMoney(currentItem.amount) }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">说明:</text>
						<text class="detail-value">{{ currentItem.reason || currentItem.title || '-' }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">提交时间:</text>
						<text class="detail-value">{{ formatDate(currentItem.submitTime) }}</text>
					</view>
				</view>
				<view v-if="currentItem.status === 'pending'" class="detail-actions">
					<view class="approve-btn" @click="approveItem(currentItem)">
						<text>通过</text>
					</view>
					<view class="reject-btn" @click="rejectItem(currentItem)">
						<text>拒绝</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { formatMoney, formatDate, getStatusText, getApprovalTypeText } from '@/utils/pcUtils.js'

	export default {
		name: 'ApprovalManagement',
		data() {
			return {
				loading: false,
				showDetail: false,
				currentItem: {},
				
				// 筛选
				statusFilterIndex: 0,
				statusOptions: ['全部状态', '待审批', '已通过', '已拒绝'],
				
				// 数据
				approvals: [],
				stats: {
					pending: 0,
					approved: 0,
					rejected: 0,
					total: 0
				},
				
				// 选择
				selectedItems: [],
				allSelected: false
			}
		},
		
		computed: {
			filteredApprovals() {
				let filtered = [...this.approvals];
				
				// 按状态筛选
				if (this.statusFilterIndex === 1) {
					filtered = filtered.filter(item => item.status === 'pending');
				} else if (this.statusFilterIndex === 2) {
					filtered = filtered.filter(item => item.status === 'approved');
				} else if (this.statusFilterIndex === 3) {
					filtered = filtered.filter(item => item.status === 'rejected');
				}
				
				return filtered;
			}
		},
		
		mounted() {
			this.loadData();
		},
		
		methods: {
			async loadData() {
				this.loading = true;
				
				try {
					console.log('PC端审批管理 - 开始加载数据');
					
					// 模拟获取审批数据
					await this.loadApprovalData();
					
					// 计算统计数据
					this.calculateStats();
					
				} catch (error) {
					console.error('PC端审批管理 - 加载数据失败:', error);
					uni.showToast({
						title: '数据加载失败',
						icon: 'none'
					});
				} finally {
					this.loading = false;
				}
			},
			
			async loadApprovalData() {
				// 这里应该调用实际的审批数据获取接口
				// 暂时使用模拟数据
				this.approvals = [
					{
						_id: '1',
						submitter: '张三',
						type: 'expense',
						title: '办公用品采购',
						amount: 1500,
						submitTime: new Date().toISOString(),
						status: 'pending',
						reason: '购买打印纸、文具等办公用品'
					},
					{
						_id: '2',
						submitter: '李四',
						type: 'driver',
						title: '运输费用',
						amount: 800,
						submitTime: new Date(Date.now() - 86400000).toISOString(),
						status: 'approved',
						reason: '货物运输费用'
					},
					{
						_id: '3',
						submitter: '王五',
						type: 'worker',
						title: '加班费',
						amount: 600,
						submitTime: new Date(Date.now() - 172800000).toISOString(),
						status: 'rejected',
						reason: '周末加班费用'
					}
				];
			},
			
			calculateStats() {
				this.stats = {
					pending: this.approvals.filter(item => item.status === 'pending').length,
					approved: this.approvals.filter(item => item.status === 'approved').length,
					rejected: this.approvals.filter(item => item.status === 'rejected').length,
					total: this.approvals.length
				};
			},
			
			async refreshData() {
				await this.loadData();
				uni.showToast({
					title: '数据刷新成功',
					icon: 'success'
				});
			},
			
			onStatusChange(e) {
				this.statusFilterIndex = e.detail.value;
				this.selectedItems = [];
				this.allSelected = false;
			},
			
			selectAll(e) {
				this.allSelected = e.detail.value;
				if (this.allSelected) {
					this.selectedItems = this.filteredApprovals
						.filter(item => item.status === 'pending')
						.map(item => item._id);
				} else {
					this.selectedItems = [];
				}
			},
			
			toggleSelect(id) {
				const index = this.selectedItems.indexOf(id);
				if (index > -1) {
					this.selectedItems.splice(index, 1);
				} else {
					this.selectedItems.push(id);
				}
				
				const pendingItems = this.filteredApprovals.filter(item => item.status === 'pending');
				this.allSelected = pendingItems.length > 0 && 
					pendingItems.every(item => this.selectedItems.includes(item._id));
			},
			
			async approveItem(item) {
				try {
					// 这里应该调用实际的审批接口
					console.log('审批通过:', item);
					
					// 更新本地状态
					const index = this.approvals.findIndex(a => a._id === item._id);
					if (index > -1) {
						this.approvals[index].status = 'approved';
						this.calculateStats();
					}
					
					uni.showToast({
						title: '审批通过',
						icon: 'success'
					});
					
					this.closeDetail();
				} catch (error) {
					console.error('审批失败:', error);
					uni.showToast({
						title: '审批失败',
						icon: 'none'
					});
				}
			},
			
			async rejectItem(item) {
				try {
					// 这里应该调用实际的拒绝接口
					console.log('审批拒绝:', item);
					
					// 更新本地状态
					const index = this.approvals.findIndex(a => a._id === item._id);
					if (index > -1) {
						this.approvals[index].status = 'rejected';
						this.calculateStats();
					}
					
					uni.showToast({
						title: '已拒绝',
						icon: 'success'
					});
					
					this.closeDetail();
				} catch (error) {
					console.error('拒绝失败:', error);
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					});
				}
			},
			
			async batchApprove() {
				if (this.selectedItems.length === 0) return;
				
				try {
					// 批量审批
					for (const id of this.selectedItems) {
						const item = this.approvals.find(a => a._id === id);
						if (item && item.status === 'pending') {
							item.status = 'approved';
						}
					}
					
					this.calculateStats();
					this.selectedItems = [];
					this.allSelected = false;
					
					uni.showToast({
						title: '批量审批完成',
						icon: 'success'
					});
				} catch (error) {
					console.error('批量审批失败:', error);
					uni.showToast({
						title: '批量审批失败',
						icon: 'none'
					});
				}
			},
			
			viewDetail(item) {
				this.currentItem = item;
				this.showDetail = true;
			},
			
			closeDetail() {
				this.showDetail = false;
				this.currentItem = {};
			},
			
			formatMoney(amount) {
				return formatMoney(amount);
			},
			
			formatDate(date) {
				return formatDate(date);
			},
			
			getStatusText(status) {
				return getStatusText(status);
			},
			
			getApprovalTypeText(type) {
				return getApprovalTypeText(type);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.approval-management {
		padding: 20px;
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.approval-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20px;

		.approval-title {
			font-size: 24px;
			font-weight: bold;
			color: #333;
		}

		.approval-controls {
			display: flex;
			gap: 15px;
		}

		.status-filter {
			.picker-text {
				padding: 8px 16px;
				background-color: #fff;
				border-radius: 6px;
				border: 1px solid #ddd;
				font-size: 14px;
			}
		}

		.refresh-btn {
			display: flex;
			align-items: center;
			gap: 8px;
			padding: 8px 16px;
			background-color: #409EFF;
			color: #fff;
			border-radius: 6px;
			cursor: pointer;

			&:hover {
				background-color: #337ecc;
			}
		}
	}

	.approval-stats {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 20px;
		margin-bottom: 30px;

		.stat-card {
			display: flex;
			align-items: center;
			padding: 20px;
			background-color: #fff;
			border-radius: 12px;
			box-shadow: 0 2px 8px rgba(0,0,0,0.1);

			.stat-icon {
				font-size: 32px;
				margin-right: 15px;
			}

			.stat-content {
				.stat-number {
					display: block;
					font-size: 24px;
					font-weight: bold;
					color: #333;
					margin-bottom: 5px;
				}

				.stat-label {
					font-size: 12px;
					color: #666;
				}
			}

			&.pending {
				border-left: 4px solid #E6A23C;
			}

			&.approved {
				border-left: 4px solid #67C23A;
			}

			&.rejected {
				border-left: 4px solid #F56C6C;
			}

			&.total {
				border-left: 4px solid #409EFF;
			}
		}
	}

	.approval-table {
		background-color: #fff;
		border-radius: 12px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
		overflow: hidden;

		.table-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20px 25px;
			border-bottom: 1px solid #eee;

			.table-title {
				font-size: 18px;
				font-weight: 600;
				color: #333;
			}

			.table-controls {
				.batch-approve-btn {
					padding: 8px 16px;
					background-color: #67C23A;
					color: #fff;
					border-radius: 6px;
					cursor: pointer;
					font-size: 14px;

					&:hover {
						background-color: #5daf34;
					}
				}
			}
		}

		.table-content {
			.table-row {
				display: grid;
				grid-template-columns: 50px 100px 80px 1fr 100px 120px 80px 120px;
				gap: 15px;
				padding: 15px 25px;
				border-bottom: 1px solid #f0f0f0;
				align-items: center;

				&.table-header-row {
					background-color: #f8f9fa;
					font-weight: 600;
					color: #333;
				}

				&:hover:not(.table-header-row) {
					background-color: #f8f9fa;
				}

				.table-cell {
					display: flex;
					align-items: center;
					font-size: 13px;

					&.checkbox {
						justify-content: center;
					}

					&.submitter {
						font-weight: 500;
					}

					&.type {
						.type-tag {
							padding: 4px 8px;
							border-radius: 4px;
							font-size: 11px;
							font-weight: 500;

							&.expense {
								background-color: #E1F3FF;
								color: #409EFF;
							}

							&.driver {
								background-color: #F0F9FF;
								color: #67C23A;
							}

							&.worker {
								background-color: #FDF6EC;
								color: #E6A23C;
							}
						}
					}

					&.amount {
						font-weight: 600;
						color: #333;
					}

					&.submit-time {
						color: #666;
					}

					&.status {
						.status-tag {
							padding: 4px 8px;
							border-radius: 4px;
							font-size: 11px;
							font-weight: 500;

							&.pending {
								background-color: #FDF6EC;
								color: #E6A23C;
							}

							&.approved {
								background-color: #F0F9FF;
								color: #67C23A;
							}

							&.rejected {
								background-color: #FEF0F0;
								color: #F56C6C;
							}
						}
					}

					&.actions {
						.action-buttons {
							display: flex;
							gap: 8px;
						}

						.approve-btn, .reject-btn, .view-btn {
							padding: 4px 8px;
							border-radius: 4px;
							cursor: pointer;
							font-size: 11px;

							&:hover {
								opacity: 0.8;
							}
						}

						.approve-btn {
							background-color: #67C23A;
							color: #fff;
						}

						.reject-btn {
							background-color: #F56C6C;
							color: #fff;
						}

						.view-btn {
							background-color: #409EFF;
							color: #fff;
						}
					}
				}
			}

			.loading-row, .empty-row {
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 40px;
				color: #999;
			}
		}

		.table-pagination {
			padding: 15px 25px;
			border-top: 1px solid #eee;
			background-color: #f8f9fa;

			.pagination-info {
				font-size: 12px;
				color: #666;
			}
		}
	}

	.detail-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0,0,0,0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;

		.detail-content {
			background-color: #fff;
			border-radius: 12px;
			width: 500px;
			max-height: 80vh;
			overflow-y: auto;

			.detail-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20px 25px;
				border-bottom: 1px solid #eee;

				.detail-title {
					font-size: 18px;
					font-weight: 600;
					color: #333;
				}

				.close-btn {
					width: 30px;
					height: 30px;
					display: flex;
					justify-content: center;
					align-items: center;
					border-radius: 50%;
					background-color: #f5f5f5;
					cursor: pointer;
					font-size: 18px;
					color: #666;

					&:hover {
						background-color: #e9ecef;
					}
				}
			}

			.detail-body {
				padding: 25px;

				.detail-item {
					display: flex;
					margin-bottom: 15px;

					.detail-label {
						width: 80px;
						font-size: 14px;
						color: #666;
						font-weight: 500;
					}

					.detail-value {
						flex: 1;
						font-size: 14px;
						color: #333;
					}
				}
			}

			.detail-actions {
				display: flex;
				justify-content: center;
				gap: 15px;
				padding: 20px 25px;
				border-top: 1px solid #eee;

				.approve-btn, .reject-btn {
					padding: 10px 20px;
					border-radius: 6px;
					cursor: pointer;
					font-size: 14px;
					font-weight: 500;

					&:hover {
						opacity: 0.8;
					}
				}

				.approve-btn {
					background-color: #67C23A;
					color: #fff;
				}

				.reject-btn {
					background-color: #F56C6C;
					color: #fff;
				}
			}
		}
	}
</style>
