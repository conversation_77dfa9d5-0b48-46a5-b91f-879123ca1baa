!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("cloudbase_oauth",[],e):"object"==typeof exports?exports.cloudbase_oauth=e():t.cloudbase_oauth=e()}("undefined"!=typeof window?window:this,(()=>(()=>{"use strict";var t={118:function(t,e,r){var i=this&&this.__createBinding||(Object.create?function(t,e,r,i){void 0===i&&(i=r);var n=Object.getOwnPropertyDescriptor(e,r);n&&!("get"in n?!e.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,i,n)}:function(t,e,r,i){void 0===i&&(i=r),t[i]=e[r]}),n=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||i(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),n(r(488),e)},488:(t,e,r)=>{r.r(e),r.d(e,{Auth:()=>F,CloudbaseOAuth:()=>z,authModels:()=>h});var i,n,s,o,a,u,h={};function c(){return"xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g,(t=>{var e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)}))}r.r(h),function(t){t.AUTH_SIGN_UP_URL="/auth/v1/signup",t.AUTH_TOKEN_URL="/auth/v1/token",t.AUTH_REVOKE_URL="/auth/v1/revoke",t.WEDA_USER_URL="/auth/v1/plugin/weda/userinfo",t.AUTH_RESET_PASSWORD="/auth/v1/reset",t.AUTH_GET_DEVICE_CODE="/auth/v1/device/code",t.CHECK_USERNAME="/auth/v1/checkUsername",t.CHECK_IF_USER_EXIST="/auth/v1/checkIfUserExist",t.GET_PROVIDER_TYPE="/auth/v1/mgr/provider/providerSubType",t.AUTH_SIGN_IN_URL="/auth/v1/signin",t.AUTH_SIGN_IN_ANONYMOUSLY_URL="/auth/v1/signin/anonymously",t.AUTH_SIGN_IN_WITH_PROVIDER_URL="/auth/v1/signin/with/provider",t.AUTH_SIGN_IN_WITH_WECHAT_URL="/auth/v1/signin/wechat/noauth",t.AUTH_SIGN_IN_CUSTOM="/auth/v1/signin/custom",t.PROVIDER_TOKEN_URL="/auth/v1/provider/token",t.PROVIDER_URI_URL="/auth/v1/provider/uri",t.USER_ME_URL="/auth/v1/user/me",t.AUTH_SIGNOUT_URL="/auth/v1/user/signout",t.USER_QUERY_URL="/auth/v1/user/query",t.USER_PRIFILE_URL="/auth/v1/user/profile",t.USER_BASIC_EDIT_URL="/auth/v1/user/basic/edit",t.USER_TRANS_BY_PROVIDER_URL="/auth/v1/user/trans/by/provider",t.PROVIDER_LIST="/auth/v1/user/provider",t.PROVIDER_BIND_URL="/auth/v1/user/provider/bind",t.PROVIDER_UNBIND_URL="/auth/v1/user/provider",t.CHECK_PWD_URL="/auth/v1/user/sudo",t.SUDO_URL="/auth/v1/user/sudo",t.BIND_CONTACT_URL="/auth/v1/user/contact",t.AUTH_SET_PASSWORD="/auth/v1/user/password",t.AUTHORIZE_DEVICE_URL="/auth/v1/user/device/authorize",t.AUTHORIZE_URL="/auth/v1/user/authorize",t.AUTHORIZE_INFO_URL="/auth/v1/user/authorize/info",t.AUTHORIZED_DEVICES_DELETE_URL="/auth/v1/user/authorized/devices/",t.AUTH_REVOKE_ALL_URL="/auth/v1/user/revoke/all",t.GET_USER_BEHAVIOR_LOG="/auth/v1/user/security/history",t.VERIFICATION_URL="/auth/v1/verification",t.VERIFY_URL="/auth/v1/verification/verify",t.CAPTCHA_DATA_URL="/auth/v1/captcha/data",t.VERIFY_CAPTCHA_DATA_URL="/auth/v1/captcha/data/verify",t.GET_CAPTCHA_URL="/auth/v1/captcha/init",t.GET_MINIPROGRAM_QRCODE="/auth/v1/qrcode/generate",t.GET_MINIPROGRAM_QRCODE_STATUS="/auth/v1/qrcode/get/status"}(i||(i={})),function(t){t.AUTH_SIGN_IN_URL="/auth/v2/signin/username",t.AUTH_TOKEN_URL="/auth/v2/token",t.USER_ME_URL="/auth/v2/user/me",t.VERIFY_URL="/auth/v2/signin/verificationcode",t.AUTH_SIGN_IN_WITH_PROVIDER_URL="/auth/v2/signin/with/provider",t.AUTH_PUBLIC_KEY="/auth/v2/signin/publichkey",t.AUTH_RESET_PASSWORD="/auth/v2/signin/password/update"}(n||(n={})),function(t){t.REGISTER="REGISTER",t.SIGN_IN="SIGN_IN",t.PASSWORD_RESET="PASSWORD_RESET",t.EMAIL_ADDRESS_CHANGE="EMAIL_ADDRESS_CHANGE",t.PHONE_NUMBER_CHANGE="PHONE_NUMBER_CHANGE"}(s||(s={})),(u=o||(o={})).UNREACHABLE="unreachable",u.LOCAL="local",u.CANCELLED="cancelled",u.UNKNOWN="unknown",u.UNAUTHENTICATED="unauthenticated",u.RESOURCE_EXHAUSTED="resource_exhausted",u.FAILED_PRECONDITION="failed_precondition",u.INVALID_ARGUMENT="invalid_argument",u.DEADLINE_EXCEEDED="deadline_exceeded",u.NOT_FOUND="not_found",u.ALREADY_EXISTS="already_exists",u.PERMISSION_DENIED="permission_denied",u.ABORTED="aborted",u.OUT_OF_RANGE="out_of_range",u.UNIMPLEMENTED="unimplemented",u.INTERNAL="internal",u.UNAVAILABLE="unavailable",u.DATA_LOSS="data_loss",u.INVALID_PASSWORD="invalid_password",u.PASSWORD_NOT_SET="password_not_set",u.INVALID_STATUS="invalid_status",u.USER_PENDING="user_pending",u.USER_BLOCKED="user_blocked",u.INVALID_VERIFICATION_CODE="invalid_verification_code",u.TWO_FACTOR_REQUIRED="two_factor_required",u.INVALID_TWO_FACTOR="invalid_two_factor",u.INVALID_TWO_FACTOR_RECOVERY="invalid_two_factor_recovery",u.UNDER_REVIEW="under_review",u.INVALID_REQUEST="invalid_request",u.UNAUTHORIZED_CLIENT="unauthorized_client",u.ACCESS_DENIED="access_denied",u.UNSUPPORTED_RESPONSE_TYPE="unsupported_response_type",u.INVALID_SCOPE="invalid_scope",u.INVALID_GRANT="invalid_grant",u.SERVER_ERROR="server_error",u.TEMPORARILY_UNAVAILABLE="temporarily_unavailable",u.INTERACTION_REQUIRED="interaction_required",u.LOGIN_REQUIRED="login_required",u.ACCOUNT_SELECTION_REQUIRED="account_selection_required",u.CONSENT_REQUIRED="consent_required",u.INVALID_REQUEST_URI="invalid_request_uri",u.INVALID_REQUEST_OBJECT="invalid_request_object",u.REQUEST_NOT_SUPPORTED="request_not_supported",u.REQUEST_URI_NOT_SUPPORTED="request_uri_not_supported",u.REGISTRATION_NOT_SUPPORTED="registration_not_supported",u.CAPTCHA_REQUIRED="captcha_required",u.CAPTCHA_INVALID="captcha_invalid",function(t){t.CLIENT_ID="client_id",t.CLIENT_SECRET="client_secret",t.RESPONSE_TYPE="response_type",t.SCOPE="scope",t.STATE="state",t.REDIRECT_URI="redirect_uri",t.ERROR="error",t.ERROR_DESCRIPTION="error_description",t.ERROR_URI="error_uri",t.GRANT_TYPE="grant_type",t.CODE="code",t.ACCESS_TOKEN="access_token",t.TOKEN_TYPE="token_type",t.EXPIRES_IN="expires_in",t.USERNAME="username",t.PASSWORD="password",t.REFRESH_TOKEN="refresh_token"}(a||(a={}));var l=r(655);function d(t,e,r,i,n,s,o){try{var a=t[s](o),u=a.value}catch(t){return void r(t)}a.done?e(u):Promise.resolve(u).then(i,n)}function f(t){return function(){var e=this,r=arguments;return new Promise((function(i,n){var s=t.apply(e,r);function o(t){d(s,i,n,o,a,"next",t)}function a(t){d(s,i,n,o,a,"throw",t)}o(void 0)}))}}class p{constructor(t){this.clientId=(null==t?void 0:t.clientId)||"",globalThis.jsSdkFnPromiseMap=globalThis.jsSdkFnPromiseMap||new Map}run(t,e){var r=this;return f((function*(){t="".concat(r.clientId,"_").concat(t);var i=globalThis.jsSdkFnPromiseMap.get(t);return i||(i=new Promise(((i,n)=>{f((function*(){try{yield r.runIdlePromise();var s=e();i(yield s)}catch(t){n(t)}finally{globalThis.jsSdkFnPromiseMap.delete(t)}}))()})),globalThis.jsSdkFnPromiseMap.set(t,i)),i}))()}runIdlePromise(){return Promise.resolve()}}var v="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function m(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach((function(e){y(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function y(t,e,r){return(e=function(t){var e=function(t){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _(t,e,r,i,n,s,o){try{var a=t[s](o),u=a.value}catch(t){return void r(t)}a.done?e(u):Promise.resolve(u).then(i,n)}function T(t){return function(){var e=this,r=arguments;return new Promise((function(i,n){var s=t.apply(e,r);function o(t){_(s,i,n,o,a,"next",t)}function a(t){_(s,i,n,o,a,"throw",t)}o(void 0)}))}}var E="x-request-id",S="x-device-id",b="device_id",I=function(){var t=T((function*(t,e){var r=null,i=null;try{var n=Object.assign({},e);n.method||(n.method="GET"),n.body&&"string"!=typeof n.body&&(n.body=JSON.stringify(n.body));var s=yield fetch(t,n),a=yield s.json();null!=a&&a.error?(i=a).error_uri=new URL(t).pathname:r=a}catch(e){i={error:o.UNREACHABLE,error_description:e.message,error_uri:new URL(t).pathname}}if(i)throw i;return r}));return function(e,r){return t.apply(this,arguments)}}(),w=new class{constructor(t){this._env=(null==t?void 0:t.env)||""}getItem(t){var e=this;return T((function*(){return window.localStorage.getItem("".concat(t).concat(e._env))}))()}removeItem(t){var e=this;return T((function*(){window.localStorage.removeItem("".concat(t).concat(e._env))}))()}setItem(t,e){var r=this;return T((function*(){window.localStorage.setItem("".concat(t).concat(r._env),e)}))()}getItemSync(t){return window.localStorage.getItem("".concat(t).concat(this._env))}removeItemSync(t){window.localStorage.removeItem("".concat(t).concat(this._env))}setItemSync(t,e){window.localStorage.setItem("".concat(t).concat(this._env),e)}};function R(t){var e=!0;return null!=t&&t.expires_at&&null!=t&&t.access_token&&(e=t.expires_at<new Date),e}class O{constructor(t){this.credentials=null,this.singlePromise=null,this.tokenSectionName=t.tokenSectionName,this.storage=t.storage,this.clientId=t.clientId,this.singlePromise=new p({clientId:this.clientId})}getStorageCredentialsSync(){var t=null,e=this.storage.getItemSync(this.tokenSectionName);if(null!=e)try{var r;null!==(r=t=JSON.parse(e))&&void 0!==r&&r.expires_at&&(t.expires_at=new Date(t.expires_at))}catch(e){this.storage.removeItem(this.tokenSectionName),t=null}return t}setCredentials(t){var e=this;return T((function*(){if(null!=t&&t.expires_in){if(null!=t&&t.expires_at||(t.expires_at=new Date(Date.now()+1e3*(t.expires_in-30))),e.storage){var r=JSON.stringify(t);yield e.storage.setItem(e.tokenSectionName,r)}e.credentials=t}else e.storage&&(yield e.storage.removeItem(e.tokenSectionName)),e.credentials=null}))()}getCredentials(){var t=this;return T((function*(){return t.singlePromise.run("getCredentials",T((function*(){return R(t.credentials)&&(t.credentials=yield t.getStorageCredentials()),t.credentials})))}))()}getStorageCredentials(){var t=this;return T((function*(){return t.singlePromise.run("_getStorageCredentials",T((function*(){var e=null,r=yield t.storage.getItem(t.tokenSectionName);if(null!=r)try{var i;null!==(i=e=JSON.parse(r))&&void 0!==i&&i.expires_at&&(e.expires_at=new Date(e.expires_at))}catch(r){yield t.storage.removeItem(t.tokenSectionName),e=null}return e})))}))()}}class C{constructor(t){this.singlePromise=null,t.clientSecret||(t.clientSecret=""),!t.clientId&&t.env&&(t.clientId=t.env),this.apiOrigin=t.apiOrigin,this.clientId=t.clientId,this.singlePromise=new p({clientId:this.clientId}),this.retry=this.formatRetry(t.retry,C.defaultRetry),t.baseRequest?this.baseRequest=t.baseRequest:this.baseRequest=I,this.tokenInURL=t.tokenInURL,this.headers=t.headers,this.storage=t.storage||w,this.localCredentials=new O({tokenSectionName:"credentials_".concat(t.clientId),storage:this.storage,clientId:t.clientId}),this.clientSecret=t.clientSecret,t.clientId&&(this.basicAuth="Basic ".concat(function(t){for(var e,r,i,n,s="",o=0,a=(t=String(t)).length%3;o<t.length;){if((r=t.charCodeAt(o++))>255||(i=t.charCodeAt(o++))>255||(n=t.charCodeAt(o++))>255)throw new TypeError("Failed to execute 'btoa' on 'Window': The string to be encoded contains characters outside of the Latin1 range.");s+=v.charAt((e=r<<16|i<<8|n)>>18&63)+v.charAt(e>>12&63)+v.charAt(e>>6&63)+v.charAt(63&e)}return a?s.slice(0,a-3)+"===".substring(a):s}("".concat(t.clientId,":").concat(this.clientSecret)))),this.wxCloud=t.wxCloud;try{(function(){if("undefined"==typeof wx)return!1;if("undefined"==typeof Page)return!1;if(!wx.getSystemInfoSync)return!1;if(!wx.getStorageSync)return!1;if(!wx.setStorageSync)return!1;if(!wx.connectSocket)return!1;if(!wx.request)return!1;try{if(!wx.getSystemInfoSync())return!1;if("qq"===wx.getSystemInfoSync().AppPlatform)return!1}catch(t){return!1}return!0})()&&void 0===this.wxCloud&&t.env&&(wx.cloud.init({env:t.env}),this.wxCloud=wx.cloud)}catch(t){}this.refreshTokenFunc=t.refreshTokenFunc||this.defaultRefreshTokenFunc,this.anonymousSignInFunc=t.anonymousSignInFunc}setCredentials(t){return this.localCredentials.setCredentials(t)}getAccessToken(){var t=this;return T((function*(){var e=yield t.getCredentials();if(null!=e&&e.access_token)return Promise.resolve(e.access_token);var r={error:o.UNAUTHENTICATED};return Promise.reject(r)}))()}request(t,e){var r=this;return T((function*(){var i,n;e||(e={});var s=r.formatRetry(e.retry,r.retry);if(e.headers=e.headers||{},r.headers&&(e.headers=m(m({},r.headers),e.headers)),e.headers[E]||(e.headers[E]=c()),!e.headers[S]){var a=yield r.getDeviceId();e.headers[S]=a}if(null!==(i=e)&&void 0!==i&&i.withBasicAuth&&r.basicAuth&&(e.headers.Authorization=r.basicAuth),null!==(n=e)&&void 0!==n&&n.withCredentials){var u=yield r.getCredentials();u&&(r.tokenInURL?(t.indexOf("?")<0&&(t+="?"),t+="access_token=".concat(u.access_token)):e.headers.Authorization="".concat(u.token_type," ").concat(u.access_token))}else r.clientId&&t.indexOf("client_id")<0&&(t+=t.indexOf("?")<0?"?":"&",t+="client_id=".concat(r.clientId));t.startsWith("/")&&(t=r.apiOrigin+t);for(var h=null,l=s+1,d=0;d<l;d++){try{h=e.useWxCloud?yield r.wxCloudCallFunction(t,e):yield r.baseRequest(t,e);break}catch(t){if(e.withCredentials&&t&&t.error===o.UNAUTHENTICATED)return yield r.setCredentials(null),Promise.reject(t);if(d===s||!t||"unreachable"!==t.error)return Promise.reject(t)}yield r.sleep(C.retryInterval)}return h}))()}wxCloudCallFunction(t,e){var r=this;return T((function*(){var i=null,n=null;try{var s,a="";try{a=yield wx.getRendererUserAgent()}catch(t){}var{result:u}=yield r.wxCloud.callFunction({name:"httpOverCallFunction",data:{url:t,method:e.method,headers:m({origin:"https://servicewechat.com","User-Agent":a},e.headers),body:e.body}});null!=u&&null!==(s=u.body)&&void 0!==s&&s.error_code?(n=null==u?void 0:u.body).error_uri=(0,l.y)(t):i=null==u?void 0:u.body}catch(e){n={error:o.UNREACHABLE,error_description:e.message,error_uri:(0,l.y)(t)}}if(n)throw n;return i}))()}getCredentials(){var t=this;return T((function*(){var e=yield t.localCredentials.getCredentials();return e?(R(e)&&(e=e&&"anonymous"===e.scope?t.anonymousSignInFunc?(yield t.anonymousSignInFunc(e))||(yield t.localCredentials.getCredentials()):yield t.anonymousSignIn(e):yield t.refreshToken(e)),e):t.unAuthenticatedError("credentials not found")}))()}getCredentialsSync(){return this.localCredentials.getStorageCredentialsSync()}getCredentialsAsync(){return this.getCredentials()}getScope(){var t=this;return T((function*(){var e=yield t.localCredentials.getCredentials();return e?e.scope:t.unAuthenticatedError("credentials not found")}))()}getGroups(){var t=this;return T((function*(){var e=yield t.localCredentials.getCredentials();return e?e.groups:t.unAuthenticatedError("credentials not found")}))()}refreshToken(t){var e=this;return T((function*(){return e.singlePromise.run("_refreshToken",T((function*(){if(!t||!t.refresh_token)return e.unAuthenticatedError("no refresh token found in credentials");try{var r=yield e.refreshTokenFunc(t.refresh_token,t);return yield e.localCredentials.setCredentials(r),r}catch(t){return t.error===o.INVALID_GRANT?(yield e.localCredentials.setCredentials(null),e.unAuthenticatedError(t.error_description)):Promise.reject(t)}})))}))()}checkRetry(t){var e=null;if(("number"!=typeof t||t<C.minRetry||t>C.maxRetry)&&(e={error:o.UNREACHABLE,error_description:"wrong options param: retry"}),e)throw e;return t}formatRetry(t,e){return void 0===t?e:this.checkRetry(t)}sleep(t){return T((function*(){return new Promise((e=>{setTimeout((()=>{e()}),t)}))}))()}anonymousSignIn(t){var e=this;return T((function*(){return e.singlePromise.run("_anonymous",T((function*(){if(!t||"anonymous"!==t.scope)return e.unAuthenticatedError("no anonymous in credentials");try{var r=yield e.request(i.AUTH_SIGN_IN_ANONYMOUSLY_URL,{method:"POST",withBasicAuth:!0,body:{}});return yield e.localCredentials.setCredentials(r),r}catch(t){return t.error===o.INVALID_GRANT?(yield e.localCredentials.setCredentials(null),e.unAuthenticatedError(t.error_description)):Promise.reject(t)}})))}))()}defaultRefreshTokenFunc(t,e){var r=this;return T((function*(){if(void 0===t||""===t)return r.unAuthenticatedError("refresh token not found");var s=i.AUTH_TOKEN_URL;return"v2"===(null==e?void 0:e.version)&&(s=n.AUTH_TOKEN_URL),m(m({},yield r.request(s,{method:"POST",body:{client_id:r.clientId,client_secret:r.clientSecret,grant_type:"refresh_token",refresh_token:t}})),{},{version:(null==e?void 0:e.version)||"v1"})}))()}getDeviceId(){var t=this;return T((function*(){if(t.deviceID)return t.deviceID;var e=yield t.storage.getItem(b);return"string"==typeof e&&e.length>=16&&e.length<=48||(e=c(),yield t.storage.setItem(b,e)),t.deviceID=e,e}))()}unAuthenticatedError(t){var e={error:o.UNAUTHENTICATED,error_description:t};return Promise.reject(e)}}function A(){var{wx:t}=globalThis;if(void 0===t)return!1;if(void 0===globalThis.Page)return!1;if(!t.getSystemInfoSync)return!1;if(!t.getStorageSync)return!1;if(!t.setStorageSync)return!1;if(!t.connectSocket)return!1;if(!t.request)return!1;try{if(!t.getSystemInfoSync())return!1;if("qq"===t.getSystemInfoSync().AppPlatform)return!1}catch(t){return!1}return!0}C.defaultRetry=2,C.minRetry=0,C.maxRetry=5,C.retryInterval=1e3;var P=!1;function U(){P=P||void 0!==typeof window&&"miniprogram"===window.__wxjs_environment}try{A()||(P=P||!!navigator.userAgent.match(/miniprogram/i)||"miniprogram"===window.__wxjs_environment,window&&window.WeixinJSBridge&&window.WeixinJSBridge.invoke?U():"undefined"!=typeof document&&document.addEventListener("WeixinJSBridgeReady",U,!1))}catch(t){}function D(){return P}const N=class{constructor(t){if(this.params={},"string"==typeof t)this.parse(t);else if(t&&"object"==typeof t)for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&this.append(e,t[e])}parse(t){t.split("&").forEach((t=>{var[e,r]=t.split("=").map(decodeURIComponent);this.append(e,r)}))}append(t,e){this.params[t]?this.params[t]=this.params[t].concat([e]):this.params[t]=[e]}get(t){return this.params[t]?this.params[t][0]:null}getAll(t){return this.params[t]||[]}delete(t){delete this.params[t]}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}set(t,e){this.params[t]=[e]}toString(){var t=this,e=[],r=function(r){Object.prototype.hasOwnProperty.call(t.params,r)&&t.params[r].forEach((t=>{e.push("".concat(encodeURIComponent(r),"=").concat(encodeURIComponent(t)))}))};for(var i in this.params)r(i);return e.join("&")}};function L(t,e,r,i,n,s,o){try{var a=t[s](o),u=a.value}catch(t){return void r(t)}a.done?e(u):Promise.resolve(u).then(i,n)}function x(t){return function(){var e=this,r=arguments;return new Promise((function(i,n){var s=t.apply(e,r);function o(t){L(s,i,n,o,a,"next",t)}function a(t){L(s,i,n,o,a,"throw",t)}o(void 0)}))}}class k{constructor(t){t.openURIWithCallback||(t.openURIWithCallback=this.getDefaultOpenURIWithCallback()),t.storage||(t.storage=w),this.config=t,this.tokenSectionName="captcha_".concat(t.clientId)}request(t,e){var r=this;return x((function*(){e||(e={}),e.method||(e.method="GET");var i,n="".concat(e.method,":").concat(t),s=t;e.withCaptcha&&(s=yield r.appendCaptchaTokenToURL(t,n,!1));try{i=yield r.config.request(s,e)}catch(i){return i.error===o.CAPTCHA_REQUIRED||i.error===o.CAPTCHA_INVALID?(t=yield r.appendCaptchaTokenToURL(t,n,i.error===o.CAPTCHA_INVALID),r.config.request(t,e)):Promise.reject(i)}return i}))()}getDefaultOpenURIWithCallback(){if(!A()&&!D()&&(window.location.search.indexOf("__captcha")>0&&(document.body.style.display="none"),null===document.getElementById("captcha_panel_wrap"))){var t=document.createElement("div");t.style.cssText="background-color: rgba(0, 0, 0, 0.7);position: fixed;left: 0px;right: 0px;top: 0px;bottom: 0px;padding: 9vw 0 0 0;display: none;z-index:100;",t.setAttribute("id","captcha_panel_wrap"),setTimeout((()=>{document.body.appendChild(t)}),0)}return this.defaultOpenURIWithCallback}defaultOpenURIWithCallback(t,e){return x((function*(){var{width:r="355px",height:i="355px"}=e||{};if(t.match(/^(data:.*)$/))return Promise.reject({error:o.UNIMPLEMENTED,error_description:"need to impl captcha data"});var n=document.getElementById("captcha_panel_wrap"),s=document.createElement("iframe");return n.innerHTML="",s.setAttribute("src",t),s.setAttribute("id","review-panel-iframe"),s.style.cssText="min-width:".concat(r,";display:block;height:").concat(i,";margin:0 auto;background-color: rgb(255, 255, 255);border: none;"),n.appendChild(s),n.style.display="block",new Promise(((t,e)=>{s.onload=function(){try{var r=window.location,i=s.contentWindow.location;if(i.host+i.pathname===r.host+r.pathname){n.style.display="none";var o=new N(i.search),a=o.get("captcha_token");return a?t({captcha_token:a,expires_in:Number(o.get("expires_in"))}):e({error:o.get("error"),error_description:o.get("error_description")})}n.style.display="block"}catch(t){n.style.display="block"}}}))}))()}getCaptchaToken(t,e){var r=this;return x((function*(){if(!t){var n=yield r.findCaptchaToken();if(n)return n}var s;if(A()||D()){var o=yield r.config.request(i.CAPTCHA_DATA_URL,{method:"POST",body:{state:e,redirect_uri:""},withCredentials:!1});s={url:"".concat(o.data,"?state=").concat(encodeURIComponent(e),"&token=").concat(encodeURIComponent(o.token))}}else{var a="".concat(window.location.origin+window.location.pathname,"?__captcha=on");if((s=yield r.config.request(i.GET_CAPTCHA_URL,{method:"POST",body:{client_id:r.config.clientId,redirect_uri:a,state:e},withCredentials:!1})).captcha_token){var u={captcha_token:s.captcha_token,expires_in:s.expires_in};return r.saveCaptchaToken(u),s.captcha_token}}var h=yield r.config.openURIWithCallback(s.url);return r.saveCaptchaToken(h),h.captcha_token}))()}appendCaptchaTokenToURL(t,e,r){var i=this;return x((function*(){var n=yield i.getCaptchaToken(r,e);return t.indexOf("?")>0?t+="&captcha_token=".concat(n):t+="?captcha_token=".concat(n),t}))()}saveCaptchaToken(t){var e=this;return x((function*(){t.expires_at=new Date(Date.now()+1e3*(t.expires_in-10));var r=JSON.stringify(t);yield e.config.storage.setItem(e.tokenSectionName,r)}))()}findCaptchaToken(){var t=this;return x((function*(){var e=yield t.config.storage.getItem(t.tokenSectionName);if(null!=e)try{var r=JSON.parse(e);return null!=r&&r.expires_at&&(r.expires_at=new Date(r.expires_at)),r.expires_at<new Date?null:r.captcha_token}catch(e){return yield t.config.storage.removeItem(t.tokenSectionName),null}return null}))()}}var q=["provider_redirect_uri","other_params"];function B(t,e,r,i,n,s,o){try{var a=t[s](o),u=a.value}catch(t){return void r(t)}a.done?e(u):Promise.resolve(u).then(i,n)}function H(t){return function(){var e=this,r=arguments;return new Promise((function(i,n){var s=t.apply(e,r);function o(t){B(s,i,n,o,a,"next",t)}function a(t){B(s,i,n,o,a,"throw",t)}o(void 0)}))}}function V(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function M(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?V(Object(r),!0).forEach((function(e){j(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):V(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function j(t,e,r){return(e=function(t){var e=function(t){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function G(t){if(!globalThis.IS_MP_BUILD&&t)return r(171)}class F{static parseParamsToSearch(t){return Object.keys(t).forEach((e=>{t[e]||delete t[e]})),new N(t).toString()}constructor(t){var{request:e}=t,r=t.credentialsClient;if(!r){var i={apiOrigin:t.apiOrigin,clientId:t.clientId,storage:t.storage,env:t.env,baseRequest:t.baseRequest,anonymousSignInFunc:t.anonymousSignInFunc,wxCloud:t.wxCloud};r=new C(i)}if(!e){var n=r.request.bind(r),s=new k(M({clientId:t.clientId,request:n,storage:t.storage},t.captchaOptions));e=s.request.bind(s)}this.config={env:t.env,apiOrigin:t.apiOrigin,clientId:t.clientId,request:e,credentialsClient:r,storage:t.storage||w}}getParamsByVersion(t,e){var r,s=(0,l.I)(t),o=(null===(r={v2:n}[null==s?void 0:s.version])||void 0===r?void 0:r[e])||i[e];return s&&delete s.version,{params:s,url:o}}signIn(t){var e=this;return H((function*(){var r=t.version||"v1",i=e.getParamsByVersion(t,"AUTH_SIGN_IN_URL");i.params.query&&delete i.params.query;var n=yield e.getEncryptParams(i.params),s=yield e.config.request(i.url,{method:"POST",body:n});return yield e.config.credentialsClient.setCredentials(M(M({},s),{},{version:r})),Promise.resolve(s)}))()}signInAnonymously(){var t=arguments,e=this;return H((function*(){var r=t.length>0&&void 0!==t[0]?t[0]:{},n=t.length>1&&void 0!==t[1]&&t[1],s=yield e.config.request(i.AUTH_SIGN_IN_ANONYMOUSLY_URL,{method:"POST",body:r,useWxCloud:n});return yield e.config.credentialsClient.setCredentials(s),Promise.resolve(s)}))()}signUp(t){var e=this;return H((function*(){var r=yield e.config.request(i.AUTH_SIGN_UP_URL,{method:"POST",body:t});return yield e.config.credentialsClient.setCredentials(r),Promise.resolve(r)}))()}signOut(t){var e=this;return H((function*(){var r={};if(t){try{r=yield e.config.request(i.AUTH_SIGNOUT_URL,{method:"POST",withCredentials:!0,body:t})}catch(t){t.error!==o.UNAUTHENTICATED&&console.log("sign_out_error",t)}return yield e.config.credentialsClient.setCredentials(),r}var n=yield e.config.credentialsClient.getAccessToken(),s=yield e.config.request(i.AUTH_REVOKE_URL,{method:"POST",body:{token:n}});return yield e.config.credentialsClient.setCredentials(),Promise.resolve(s)}))()}revokeAllDevices(){var t=this;return H((function*(){yield t.config.request(i.AUTH_REVOKE_ALL_URL,{method:"DELETE",withCredentials:!0})}))()}revokeDevice(t){var e=this;return H((function*(){yield e.config.request(i.AUTHORIZED_DEVICES_DELETE_URL+t.device_id,{method:"DELETE",withCredentials:!0})}))()}getVerification(t,e){var r=this;return H((function*(){var n=!1;return("CUR_USER"===t.target||(yield r.hasLoginState()))&&(n=!0),r.config.request(i.VERIFICATION_URL,{method:"POST",body:t,withCaptcha:(null==e?void 0:e.withCaptcha)||!1,withCredentials:n})}))()}verify(t){var e=this;return H((function*(){var r=e.getParamsByVersion(t,"VERIFY_URL"),i=yield e.config.request(r.url,{method:"POST",body:r.params});return"v2"===(null==t?void 0:t.version)&&(yield e.config.credentialsClient.setCredentials(M(M({},i),{},{version:"v2"}))),i}))()}genProviderRedirectUri(t){var e=this;return H((function*(){var{provider_redirect_uri:r,other_params:n={}}=t,s=function(t,e){if(null==t)return{};var r,i,n=function(t,e){if(null==t)return{};var r={};for(var i in t)if({}.hasOwnProperty.call(t,i)){if(e.includes(i))continue;r[i]=t[i]}return r}(t,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);for(i=0;i<s.length;i++)r=s[i],e.includes(r)||{}.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}(t,q);r&&!s.redirect_uri&&(s.redirect_uri=r);var o="".concat(i.PROVIDER_URI_URL,"?").concat(F.parseParamsToSearch(s));return Object.keys(n).forEach((t=>{var e=n[t];("sign_out_uri"!==t||(null==e?void 0:e.length)>0)&&(o+="&other_params[".concat(t,"]=").concat(encodeURIComponent(e)))})),e.config.request(o,{method:"GET"})}))()}grantProviderToken(t){var e=arguments,r=this;return H((function*(){var n=e.length>1&&void 0!==e[1]&&e[1];return r.config.request(i.PROVIDER_TOKEN_URL,{method:"POST",body:t,useWxCloud:n})}))()}patchProviderToken(t){var e=this;return H((function*(){return e.config.request(i.PROVIDER_TOKEN_URL,{method:"PATCH",body:t})}))()}signInWithProvider(t){var e=arguments,r=this;return H((function*(){var i=e.length>1&&void 0!==e[1]&&e[1],n=r.getParamsByVersion(t,"AUTH_SIGN_IN_WITH_PROVIDER_URL"),s=yield r.config.request(n.url,{method:"POST",body:n.params,useWxCloud:i});return yield r.config.credentialsClient.setCredentials(M(M({},s),{},{version:(null==t?void 0:t.version)||"v1"})),Promise.resolve(s)}))()}signInCustom(t){var e=this;return H((function*(){var r=yield e.config.request(i.AUTH_SIGN_IN_CUSTOM,{method:"POST",body:t});return yield e.config.credentialsClient.setCredentials(r),Promise.resolve(r)}))()}signInWithWechat(){var t=arguments,e=this;return H((function*(){var r=t.length>0&&void 0!==t[0]?t[0]:{},n=yield e.config.request(i.AUTH_SIGN_IN_WITH_WECHAT_URL,{method:"POST",body:r});return yield e.config.credentialsClient.setCredentials(n),Promise.resolve(n)}))()}bindWithProvider(t){var e=this;return H((function*(){return e.config.request(i.PROVIDER_BIND_URL,{method:"POST",body:t,withCredentials:!0})}))()}getUserProfile(t){var e=this;return H((function*(){return e.getUserInfo(t)}))()}getUserInfo(){var t=arguments,e=this;return H((function*(){var r,i=t.length>0&&void 0!==t[0]?t[0]:{},n=e.getParamsByVersion(i,"USER_ME_URL");if(null!==(r=n.params)&&void 0!==r&&r.query){var s=new N(n.params.query);n.url+="?".concat(s.toString())}var o=yield e.config.request(n.url,{method:"GET",withCredentials:!0});return o.sub&&(o.uid=o.sub),o}))()}getWedaUserInfo(){var t=this;return H((function*(){return yield t.config.request(i.WEDA_USER_URL,{method:"GET",withCredentials:!0})}))()}deleteMe(t){var e=this;return H((function*(){var r=e.getParamsByVersion(t,"USER_ME_URL"),i="".concat(r.url,"?").concat(F.parseParamsToSearch(r.params));return e.config.request(i,{method:"DELETE",withCredentials:!0})}))()}hasLoginState(){var t=this;return H((function*(){try{return yield t.config.credentialsClient.getAccessToken(),!0}catch(t){return!1}}))()}hasLoginStateSync(){return this.config.credentialsClient.getCredentialsSync()}getLoginState(){var t=this;return H((function*(){return t.config.credentialsClient.getCredentialsAsync()}))()}transByProvider(t){var e=this;return H((function*(){return e.config.request(i.USER_TRANS_BY_PROVIDER_URL,{method:"PATCH",body:t,withCredentials:!0})}))()}grantToken(t){var e=this;return H((function*(){var r=e.getParamsByVersion(t,"AUTH_TOKEN_URL");return e.config.request(r.url,{method:"POST",body:r.params})}))()}getProviders(){var t=this;return H((function*(){return t.config.request(i.PROVIDER_LIST,{method:"GET",withCredentials:!0})}))()}unbindProvider(t){var e=this;return H((function*(){return e.config.request("".concat(i.PROVIDER_UNBIND_URL,"/").concat(t.provider_id),{method:"DELETE",withCredentials:!0})}))()}checkPassword(t){var e=this;return H((function*(){return e.config.request("".concat(i.CHECK_PWD_URL),{method:"POST",withCredentials:!0,body:t})}))()}editContact(t){var e=this;return H((function*(){return e.config.request("".concat(i.BIND_CONTACT_URL),{method:"PATCH",withCredentials:!0,body:t})}))()}setPassword(t){var e=this;return H((function*(){return e.config.request("".concat(i.AUTH_SET_PASSWORD),{method:"PATCH",withCredentials:!0,body:t})}))()}updatePasswordByOld(t){var e=this;return H((function*(){var r=yield e.sudo({password:t.old_password});return e.setPassword({sudo_token:r.sudo_token,new_password:t.new_password})}))()}sudo(t){var e=this;return H((function*(){return e.config.request("".concat(i.SUDO_URL),{method:"POST",withCredentials:!0,body:t})}))()}sendVerificationCodeToCurrentUser(t){var e=this;return H((function*(){return t.target="CUR_USER",e.config.request(i.VERIFICATION_URL,{method:"POST",body:t,withCredentials:!0,withCaptcha:!0})}))()}changeBoundProvider(t){var e=this;return H((function*(){return e.config.request("".concat(i.PROVIDER_LIST,"/").concat(t.provider_id,"/trans"),{method:"POST",body:{provider_trans_token:t.trans_token},withCredentials:!0})}))()}setUserProfile(t){var e=this;return H((function*(){return e.config.request(i.USER_PRIFILE_URL,{method:"PATCH",body:t,withCredentials:!0})}))()}updateUserBasicInfo(t){var e=this;return H((function*(){return e.config.request(i.USER_BASIC_EDIT_URL,{method:"POST",withCredentials:!0,body:t})}))()}queryUserProfile(t){var e=this;return H((function*(){var r=new N(t);return e.config.request("".concat(i.USER_QUERY_URL,"?").concat(r.toString()),{method:"GET",withCredentials:!0})}))()}setCustomSignFunc(t){this.getCustomSignTicketFn=t}signInWithCustomTicket(){var t=this;return H((function*(){var e=t.getCustomSignTicketFn;if(!e)return Promise.reject({error:"failed_precondition",error_description:"please use setCustomSignFunc to set custom sign function"});var r=yield e();return t.signInCustom({provider_id:"custom",ticket:r})}))()}resetPassword(t){var e=this;return H((function*(){return e.config.request(i.AUTH_RESET_PASSWORD,{method:"POST",body:t})}))()}authorize(t){var e=this;return H((function*(){return e.config.request(i.AUTHORIZE_URL,{method:"POST",withCredentials:!0,body:t})}))()}authorizeDevice(t){var e=this;return H((function*(){return e.config.request(i.AUTHORIZE_DEVICE_URL,{method:"POST",withCredentials:!0,body:t})}))()}deviceAuthorize(t){var e=this;return H((function*(){return e.config.request(i.AUTH_GET_DEVICE_CODE,{method:"POST",body:t,withCredentials:!0})}))()}authorizeInfo(t){var e=this;return H((function*(){var r="".concat(i.AUTHORIZE_INFO_URL,"?").concat(F.parseParamsToSearch(t)),n=!0,s=!1;return(yield e.hasLoginState())&&(s=!0,n=!1),e.config.request(r,{method:"GET",withBasicAuth:n,withCredentials:s})}))()}checkUsername(t){var e=this;return H((function*(){return e.config.request(i.CHECK_USERNAME,{method:"GET",body:t,withCredentials:!0})}))()}checkIfUserExist(t){var e=this;return H((function*(){var r=new N(t);return e.config.request("".concat(i.CHECK_IF_USER_EXIST,"?").concat(r.toString()),{method:"GET"})}))()}loginScope(){var t=this;return H((function*(){return t.config.credentialsClient.getScope()}))()}loginGroups(){var t=this;return H((function*(){return t.config.credentialsClient.getGroups()}))()}refreshTokenForce(t){var e=this;return H((function*(){var r=yield e.config.credentialsClient.getCredentials();return yield e.config.credentialsClient.refreshToken(M(M({},r),{},{version:(null==t?void 0:t.version)||"v1"}))}))()}getCredentials(){var t=this;return H((function*(){return t.config.credentialsClient.getCredentials()}))()}getPublicKey(){var t=this;return H((function*(){return t.config.request(n.AUTH_PUBLIC_KEY,{method:"POST",body:{}})}))()}getEncryptParams(t){var e=this;return H((function*(){var{isEncrypt:r}=t;delete t.isEncrypt;var i=(0,l.I)(t),n=G(r);if(!n)return t;var s="",o="";try{var a=yield e.getPublicKey();if(s=a.public_key,o=a.public_key_thumbprint,!s||!o)throw a}catch(t){throw t}return{params:n.getEncryptInfo({publicKey:s,payload:i}),public_key_thumbprint:o}}))()}getProviderSubType(){var t=this;return H((function*(){return t.config.request(i.GET_PROVIDER_TYPE,{method:"POST",body:{provider_id:"weda"}})}))()}verifyCaptchaData(t){var e=this;return H((function*(){var{token:r,key:n}=t;return e.config.request(i.VERIFY_CAPTCHA_DATA_URL,{method:"POST",body:{token:r,key:n},withCredentials:!1})}))()}createCaptchaData(t){var e=this;return H((function*(){var{state:r,redirect_uri:n}=t;return e.config.request(i.CAPTCHA_DATA_URL,{method:"POST",body:{state:r,redirect_uri:n},withCredentials:!1})}))()}getMiniProgramCode(t){var e=this;return H((function*(){return e.config.request(i.GET_MINIPROGRAM_QRCODE,{method:"POST",body:t})}))()}getMiniProgramQrCodeStatus(t){var e=this;return H((function*(){return e.config.request(i.GET_MINIPROGRAM_QRCODE_STATUS,{method:"POST",body:t})}))()}getUserBehaviorLog(t){var e=this;return H((function*(){var r="".concat(i.GET_USER_BEHAVIOR_LOG,"?").concat({LOGIN:"query[action]=USER_LOGIN",MODIFY:"ne_query[action]=USER_LOGIN"}[t.type],"&limit=").concat(t.limit).concat(t.page_token?"&page_token=".concat(t.page_token):"");return e.config.request(r,{method:"GET",withCredentials:!0})}))()}modifyPassword(t){var e=this;return H((function*(){var r="",n="",s=G(!0);if(!s)throw new Error("do not support encrypt, a encrypt util required.");try{var o=yield e.getPublicKey();if(r=o.public_key,n=o.public_key_thumbprint,!r||!n)throw o}catch(t){throw t}var a=t.password?s.getEncryptInfo({publicKey:r,payload:t.password}):"",u=s.getEncryptInfo({publicKey:r,payload:t.new_password});return e.config.request(i.USER_BASIC_EDIT_URL,{method:"POST",withCredentials:!0,body:{user_id:t.user_id,encrypt_password:a,encrypt_new_password:u,public_key_thumbprint:n}})}))()}modifyPasswordWithoutLogin(t){var e=this;return H((function*(){var r="",i="",s=G(!0);if(!s)throw new Error("do not support encrypt, a encrypt util required.");try{var o=yield e.getPublicKey();if(r=o.public_key,i=o.public_key_thumbprint,!r||!i)throw o}catch(t){throw t}var a=t.password?s.getEncryptInfo({publicKey:r,payload:t.password}):"",u=s.getEncryptInfo({publicKey:r,payload:t.new_password});return e.config.request(n.AUTH_RESET_PASSWORD,{method:"POST",body:{username:t.username,password:a,new_password:u,public_key_thumbprint:i}})}))()}}function K(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function W(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?K(Object(r),!0).forEach((function(e){Y(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):K(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Y(t,e,r){return(e=function(t){var e=function(t){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}class z{constructor(t){var{apiOrigin:e,clientId:r,env:i,storage:n,request:s,baseRequest:o,anonymousSignInFunc:a,wxCloud:u}=t;this.oauth2client=new C({apiOrigin:e,clientId:r,env:i,storage:n,baseRequest:o||s,anonymousSignInFunc:a,wxCloud:u}),this.authApi=new F(W(W({credentialsClient:this.oauth2client},t),{},{request:s?this.oauth2client.request.bind(this.oauth2client):void 0}))}}},171:(t,e,r)=>{var i;if(r.r(e),r.d(e,{getEncryptInfo:()=>H}),!globalThis.IS_MP_BUILD){var n="undefined"!=typeof globalThis?globalThis.navigator:window.globalThis,s="0123456789abcdefghijklmnopqrstuvwxyz";function V(t){return s.charAt(t)}function M(t,e){return t&e}function j(t,e){return t|e}function G(t,e){return t^e}function F(t,e){return t&~e}function K(t){if(0==t)return-1;var e=0;return 65535&t||(t>>=16,e+=16),255&t||(t>>=8,e+=8),15&t||(t>>=4,e+=4),3&t||(t>>=2,e+=2),1&t||++e,e}function W(t){for(var e=0;0!=t;)t&=t-1,++e;return e}var o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function Y(t){var e,r,i="";for(e=0;e+3<=t.length;e+=3)r=parseInt(t.substring(e,e+3),16),i+=o.charAt(r>>6)+o.charAt(63&r);for(e+1==t.length?(r=parseInt(t.substring(e,e+1),16),i+=o.charAt(r<<2)):e+2==t.length&&(r=parseInt(t.substring(e,e+2),16),i+=o.charAt(r>>2)+o.charAt((3&r)<<4));(3&i.length)>0;)i+="=";return i}function z(t){var e,r="",i=0,n=0;for(e=0;e<t.length&&"="!=t.charAt(e);++e){var s=o.indexOf(t.charAt(e));s<0||(0==i?(r+=V(s>>2),n=3&s,i=1):1==i?(r+=V(n<<2|s>>4),n=15&s,i=2):2==i?(r+=V(n),r+=V(s>>2),n=3&s,i=3):(r+=V(n<<2|s>>4),r+=V(15&s),i=0))}return 1==i&&(r+=V(n<<2)),r}var a,u,h={decode:function(t){var e;if(void 0===a){var r="0123456789ABCDEF";for(a={},e=0;e<16;++e)a[r.charAt(e)]=e;for(r=r.toLowerCase(),e=10;e<16;++e)a[r.charAt(e)]=e;for(e=0;e<8;++e)a[" \f\n\r\t \u2028\u2029".charAt(e)]=-1}var i=[],n=0,s=0;for(e=0;e<t.length;++e){var o=t.charAt(e);if("="==o)break;if(-1!=(o=a[o])){if(void 0===o)throw new Error("Illegal character at offset "+e);n|=o,++s>=2?(i[i.length]=n,n=0,s=0):n<<=4}}if(s)throw new Error("Hex encoding incomplete: 4 bits missing");return i}},c={decode:function(t){var e;if(void 0===u){for(u=Object.create(null),e=0;e<64;++e)u["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(e)]=e;for(e=0;e<9;++e)u["= \f\n\r\t \u2028\u2029".charAt(e)]=-1}var r=[],i=0,n=0;for(e=0;e<t.length;++e){var s=t.charAt(e);if("="==s)break;if(-1!=(s=u[s])){if(void 0===s)throw new Error("Illegal character at offset "+e);i|=s,++n>=4?(r[r.length]=i>>16,r[r.length]=i>>8&255,r[r.length]=255&i,i=0,n=0):i<<=6}}switch(n){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:r[r.length]=i>>10;break;case 3:r[r.length]=i>>16,r[r.length]=i>>8&255}return r},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(t){var e=c.re.exec(t);if(e)if(e[1])t=e[1];else{if(!e[2])throw new Error("RegExp out of sync");t=e[2]}return c.decode(t)}},l=1e13;class Z{constructor(t){this.buf=[+t||0]}mulAdd(t,e){var r,i,n=this.buf,s=n.length;for(r=0;r<s;++r)(i=n[r]*t+e)<l?e=0:i-=(e=0|i/l)*l,n[r]=i;e>0&&(n[r]=e)}sub(t){var e,r,i=this.buf,n=i.length;for(e=0;e<n;++e)(r=i[e]-t)<0?(r+=l,t=1):t=0,i[e]=r;for(;0===i[i.length-1];)i.pop()}toString(t){if(10!=(t||10))throw new Error("only base 10 is supported");for(var e=this.buf,r=e[e.length-1].toString(),i=e.length-2;i>=0;--i)r+=(l+e[i]).toString().substring(1);return r}valueOf(){for(var t=this.buf,e=0,r=t.length-1;r>=0;--r)e=e*l+t[r];return e}simplify(){var t=this.buf;return 1==t.length?t[0]:this}}var d,f=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,p=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function Q(t,e){return t.length>e&&(t=t.substring(0,e)+"…"),t}class J{constructor(t,e){this.hexDigits="0123456789ABCDEF",t instanceof J?(this.enc=t.enc,this.pos=t.pos):(this.enc=t,this.pos=e)}get(t){if(void 0===t&&(t=this.pos++),t>=this.enc.length)throw new Error("Requesting byte offset "+t+" on a stream of length "+this.enc.length);return"string"==typeof this.enc?this.enc.charCodeAt(t):this.enc[t]}hexByte(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(15&t)}hexDump(t,e,r){for(var i="",n=t;n<e;++n)if(i+=this.hexByte(this.get(n)),!0!==r)switch(15&n){case 7:i+="  ";break;case 15:i+="\n";break;default:i+=" "}return i}isASCII(t,e){for(var r=t;r<e;++r){var i=this.get(r);if(i<32||i>176)return!1}return!0}parseStringISO(t,e){for(var r="",i=t;i<e;++i)r+=String.fromCharCode(this.get(i));return r}parseStringUTF(t,e){for(var r="",i=t;i<e;){var n=this.get(i++);r+=n<128?String.fromCharCode(n):n>191&&n<224?String.fromCharCode((31&n)<<6|63&this.get(i++)):String.fromCharCode((15&n)<<12|(63&this.get(i++))<<6|63&this.get(i++))}return r}parseStringBMP(t,e){for(var r,i,n="",s=t;s<e;)r=this.get(s++),i=this.get(s++),n+=String.fromCharCode(r<<8|i);return n}parseTime(t,e,r){var i=this.parseStringISO(t,e),n=(r?f:p).exec(i);return n?(r&&(n[1]=+n[1],n[1]+=+n[1]<70?2e3:1900),i=n[1]+"-"+n[2]+"-"+n[3]+" "+n[4],n[5]&&(i+=":"+n[5],n[6]&&(i+=":"+n[6],n[7]&&(i+="."+n[7]))),n[8]&&(i+=" UTC","Z"!=n[8]&&(i+=n[8],n[9]&&(i+=":"+n[9]))),i):"Unrecognized time: "+i}parseInteger(t,e){for(var r,i=this.get(t),n=i>127,s=n?255:0,o="";i==s&&++t<e;)i=this.get(t);if(0==(r=e-t))return n?-1:0;if(r>4){for(o=i,r<<=3;!(128&(+o^s));)o=+o<<1,--r;o="("+r+" bit)\n"}n&&(i-=256);for(var a=new Z(i),u=t+1;u<e;++u)a.mulAdd(256,this.get(u));return o+a.toString()}parseBitString(t,e,r){for(var i=this.get(t),n="("+((e-t-1<<3)-i)+" bit)\n",s="",o=t+1;o<e;++o){for(var a=this.get(o),u=o==e-1?i:0,h=7;h>=u;--h)s+=a>>h&1?"1":"0";if(s.length>r)return n+Q(s,r)}return n+s}parseOctetString(t,e,r){if(this.isASCII(t,e))return Q(this.parseStringISO(t,e),r);var i=e-t,n="("+i+" byte)\n";i>(r/=2)&&(e=t+r);for(var s=t;s<e;++s)n+=this.hexByte(this.get(s));return i>r&&(n+="…"),n}parseOID(t,e,r){for(var i="",n=new Z,s=0,o=t;o<e;++o){var a=this.get(o);if(n.mulAdd(128,127&a),s+=7,!(128&a)){if(""===i)if((n=n.simplify())instanceof Z)n.sub(80),i="2."+n.toString();else{var u=n<80?n<40?0:1:2;i=u+"."+(n-40*u)}else i+="."+n.toString();if(i.length>r)return Q(i,r);n=new Z,s=0}}return s>0&&(i+=".incomplete"),i}}class X{constructor(t,e,r,i,n){if(!(i instanceof $))throw new Error("Invalid tag value.");this.stream=t,this.header=e,this.length=r,this.tag=i,this.sub=n}typeName(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}}content(t){if(void 0===this.tag)return null;void 0===t&&(t=1/0);var e=this.posContent(),r=Math.abs(this.length);if(!this.tag.isUniversal())return null!==this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);switch(this.tag.tagNumber){case 1:return 0===this.stream.get(e)?"false":"true";case 2:return this.stream.parseInteger(e,e+r);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(e,e+r,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);case 6:return this.stream.parseOID(e,e+r,t);case 16:case 17:return null!==this.sub?"("+this.sub.length+" elem)":"(no elem)";case 12:return Q(this.stream.parseStringUTF(e,e+r),t);case 18:case 19:case 20:case 21:case 22:case 26:return Q(this.stream.parseStringISO(e,e+r),t);case 30:return Q(this.stream.parseStringBMP(e,e+r),t);case 23:case 24:return this.stream.parseTime(e,e+r,23==this.tag.tagNumber)}return null}toString(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(null===this.sub?"null":this.sub.length)+"]"}toPrettyString(t){void 0===t&&(t="");var e=t+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(e+="+"),e+=this.length,this.tag.tagConstructed?e+=" (constructed)":!this.tag.isUniversal()||3!=this.tag.tagNumber&&4!=this.tag.tagNumber||null===this.sub||(e+=" (encapsulates)"),e+="\n",null!==this.sub){t+="  ";for(var r=0,i=this.sub.length;r<i;++r)e+=this.sub[r].toPrettyString(t)}return e}posStart(){return this.stream.pos}posContent(){return this.stream.pos+this.header}posEnd(){return this.stream.pos+this.header+Math.abs(this.length)}toHexString(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)}static decodeLength(t){var e=t.get(),r=127&e;if(r==e)return r;if(r>6)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(0===r)return null;e=0;for(var i=0;i<r;++i)e=256*e+t.get();return e}getHexStringValue(){var t=this.toHexString(),e=2*this.header,r=2*this.length;return t.substr(e,r)}static decode(t){var e;e=t instanceof J?t:new J(t,0);var r=new J(e),i=new $(e),n=X.decodeLength(e),s=e.pos,o=s-r.pos,a=null,u=function(){var t=[];if(null!==n){for(var r=s+n;e.pos<r;)t[t.length]=X.decode(e);if(e.pos!=r)throw new Error("Content size is not correct for container starting at offset "+s)}else try{for(;;){var i=X.decode(e);if(i.tag.isEOC())break;t[t.length]=i}n=s-e.pos}catch(t){throw new Error("Exception while decoding undefined length content: "+t)}return t};if(i.tagConstructed)a=u();else if(i.isUniversal()&&(3==i.tagNumber||4==i.tagNumber))try{if(3==i.tagNumber&&0!=e.get())throw new Error("BIT STRINGs with unused bits cannot encapsulate.");a=u();for(var h=0;h<a.length;++h)if(a[h].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch(t){a=null}if(null===a){if(null===n)throw new Error("We can't skip over an invalid tag with undefined length at offset "+s);e.pos=s+Math.abs(n)}return new X(r,o,n,i,a)}}class ${constructor(t){var e=t.get();if(this.tagClass=e>>6,this.tagConstructed=!!(32&e),this.tagNumber=31&e,31==this.tagNumber){var r=new Z;do{e=t.get(),r.mulAdd(128,127&e)}while(128&e);this.tagNumber=r.simplify()}}isUniversal(){return 0===this.tagClass}isEOC(){return 0===this.tagClass&&0===this.tagNumber}}var v=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],g=(1<<26)/v[v.length-1];class tt{constructor(t,e,r){null!=t&&("number"==typeof t?this.fromNumber(t,e,r):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}toString(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var r,i=(1<<e)-1,n=!1,s="",o=this.t,a=this.DB-o*this.DB%e;if(o-- >0)for(a<this.DB&&(r=this[o]>>a)>0&&(n=!0,s=V(r));o>=0;)a<e?(r=(this[o]&(1<<a)-1)<<e-a,r|=this[--o]>>(a+=this.DB-e)):(r=this[o]>>(a-=e)&i,a<=0&&(a+=this.DB,--o)),r>0&&(n=!0),n&&(s+=V(r));return n?s:"0"}negate(){var t=et();return tt.ZERO.subTo(this,t),t}abs(){return this.s<0?this.negate():this}compareTo(t){var e=this.s-t.s;if(0!=e)return e;var r=this.t;if(0!=(e=r-t.t))return this.s<0?-e:e;for(;--r>=0;)if(0!=(e=this[r]-t[r]))return e;return 0}bitLength(){return this.t<=0?0:this.DB*(this.t-1)+ut(this[this.t-1]^this.s&this.DM)}mod(t){var e=et();return this.abs().divRemTo(t,null,e),this.s<0&&e.compareTo(tt.ZERO)>0&&t.subTo(e,e),e}modPowInt(t,e){var r;return r=t<256||e.isEven()?new y(e):new _(e),this.exp(t,r)}clone(){var t=et();return this.copyTo(t),t}intValue(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}byteValue(){return 0==this.t?this.s:this[0]<<24>>24}shortValue(){return 0==this.t?this.s:this[0]<<16>>16}signum(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1}toByteArray(){var t=this.t,e=[];e[0]=this.s;var r,i=this.DB-t*this.DB%8,n=0;if(t-- >0)for(i<this.DB&&(r=this[t]>>i)!=(this.s&this.DM)>>i&&(e[n++]=r|this.s<<this.DB-i);t>=0;)i<8?(r=(this[t]&(1<<i)-1)<<8-i,r|=this[--t]>>(i+=this.DB-8)):(r=this[t]>>(i-=8)&255,i<=0&&(i+=this.DB,--t)),128&r&&(r|=-256),0==n&&(128&this.s)!=(128&r)&&++n,(n>0||r!=this.s)&&(e[n++]=r);return e}equals(t){return 0==this.compareTo(t)}min(t){return this.compareTo(t)<0?this:t}max(t){return this.compareTo(t)>0?this:t}and(t){var e=et();return this.bitwiseTo(t,M,e),e}or(t){var e=et();return this.bitwiseTo(t,j,e),e}xor(t){var e=et();return this.bitwiseTo(t,G,e),e}andNot(t){var e=et();return this.bitwiseTo(t,F,e),e}not(){for(var t=et(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t}shiftLeft(t){var e=et();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e}shiftRight(t){var e=et();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e}getLowestSetBit(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+K(this[t]);return this.s<0?this.t*this.DB:-1}bitCount(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=W(this[r]^e);return t}testBit(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:!!(this[e]&1<<t%this.DB)}setBit(t){return this.changeBit(t,j)}clearBit(t){return this.changeBit(t,F)}flipBit(t){return this.changeBit(t,G)}add(t){var e=et();return this.addTo(t,e),e}subtract(t){var e=et();return this.subTo(t,e),e}multiply(t){var e=et();return this.multiplyTo(t,e),e}divide(t){var e=et();return this.divRemTo(t,e,null),e}remainder(t){var e=et();return this.divRemTo(t,null,e),e}divideAndRemainder(t){var e=et(),r=et();return this.divRemTo(t,e,r),[e,r]}modPow(t,e){var r,i,n=t.bitLength(),s=at(1);if(n<=0)return s;r=n<18?1:n<48?3:n<144?4:n<768?5:6,i=n<8?new y(e):e.isEven()?new T(e):new _(e);var o=[],a=3,u=r-1,h=(1<<r)-1;if(o[1]=i.convert(this),r>1){var c=et();for(i.sqrTo(o[1],c);a<=h;)o[a]=et(),i.mulTo(c,o[a-2],o[a]),a+=2}var l,d,f=t.t-1,p=!0,v=et();for(n=ut(t[f])-1;f>=0;){for(n>=u?l=t[f]>>n-u&h:(l=(t[f]&(1<<n+1)-1)<<u-n,f>0&&(l|=t[f-1]>>this.DB+n-u)),a=r;!(1&l);)l>>=1,--a;if((n-=a)<0&&(n+=this.DB,--f),p)o[l].copyTo(s),p=!1;else{for(;a>1;)i.sqrTo(s,v),i.sqrTo(v,s),a-=2;a>0?i.sqrTo(s,v):(d=s,s=v,v=d),i.mulTo(v,o[l],s)}for(;f>=0&&!(t[f]&1<<n);)i.sqrTo(s,v),d=s,s=v,v=d,--n<0&&(n=this.DB-1,--f)}return i.revert(s)}modInverse(t){var e=t.isEven();if(this.isEven()&&e||0==t.signum())return tt.ZERO;for(var r=t.clone(),i=this.clone(),n=at(1),s=at(0),o=at(0),a=at(1);0!=r.signum();){for(;r.isEven();)r.rShiftTo(1,r),e?(n.isEven()&&s.isEven()||(n.addTo(this,n),s.subTo(t,s)),n.rShiftTo(1,n)):s.isEven()||s.subTo(t,s),s.rShiftTo(1,s);for(;i.isEven();)i.rShiftTo(1,i),e?(o.isEven()&&a.isEven()||(o.addTo(this,o),a.subTo(t,a)),o.rShiftTo(1,o)):a.isEven()||a.subTo(t,a),a.rShiftTo(1,a);r.compareTo(i)>=0?(r.subTo(i,r),e&&n.subTo(o,n),s.subTo(a,s)):(i.subTo(r,i),e&&o.subTo(n,o),a.subTo(s,a))}return 0!=i.compareTo(tt.ONE)?tt.ZERO:a.compareTo(t)>=0?a.subtract(t):a.signum()<0?(a.addTo(t,a),a.signum()<0?a.add(t):a):a}pow(t){return this.exp(t,new m)}gcd(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var i=e;e=r,r=i}var n=e.getLowestSetBit(),s=r.getLowestSetBit();if(s<0)return e;for(n<s&&(s=n),s>0&&(e.rShiftTo(s,e),r.rShiftTo(s,r));e.signum()>0;)(n=e.getLowestSetBit())>0&&e.rShiftTo(n,e),(n=r.getLowestSetBit())>0&&r.rShiftTo(n,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return s>0&&r.lShiftTo(s,r),r}isProbablePrime(t){var e,r=this.abs();if(1==r.t&&r[0]<=v[v.length-1]){for(e=0;e<v.length;++e)if(r[0]==v[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<v.length;){for(var i=v[e],n=e+1;n<v.length&&i<g;)i*=v[n++];for(i=r.modInt(i);e<n;)if(i%v[e++]==0)return!1}return r.millerRabin(t)}copyTo(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s}fromInt(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0}fromString(t,e){var r;if(16==e)r=4;else if(8==e)r=3;else if(256==e)r=8;else if(2==e)r=1;else if(32==e)r=5;else{if(4!=e)return void this.fromRadix(t,e);r=2}this.t=0,this.s=0;for(var i=t.length,n=!1,s=0;--i>=0;){var o=8==r?255&+t[i]:ot(t,i);o<0?"-"==t.charAt(i)&&(n=!0):(n=!1,0==s?this[this.t++]=o:s+r>this.DB?(this[this.t-1]|=(o&(1<<this.DB-s)-1)<<s,this[this.t++]=o>>this.DB-s):this[this.t-1]|=o<<s,(s+=r)>=this.DB&&(s-=this.DB))}8==r&&128&+t[0]&&(this.s=-1,s>0&&(this[this.t-1]|=(1<<this.DB-s)-1<<s)),this.clamp(),n&&tt.ZERO.subTo(this,this)}clamp(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t}dlShiftTo(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s}drShiftTo(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s}lShiftTo(t,e){for(var r=t%this.DB,i=this.DB-r,n=(1<<i)-1,s=Math.floor(t/this.DB),o=this.s<<r&this.DM,a=this.t-1;a>=0;--a)e[a+s+1]=this[a]>>i|o,o=(this[a]&n)<<r;for(a=s-1;a>=0;--a)e[a]=0;e[s]=o,e.t=this.t+s+1,e.s=this.s,e.clamp()}rShiftTo(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var i=t%this.DB,n=this.DB-i,s=(1<<i)-1;e[0]=this[r]>>i;for(var o=r+1;o<this.t;++o)e[o-r-1]|=(this[o]&s)<<n,e[o-r]=this[o]>>i;i>0&&(e[this.t-r-1]|=(this.s&s)<<n),e.t=this.t-r,e.clamp()}}subTo(t,e){for(var r=0,i=0,n=Math.min(t.t,this.t);r<n;)i+=this[r]-t[r],e[r++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i-=t.s;r<this.t;)i+=this[r],e[r++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;r<t.t;)i-=t[r],e[r++]=i&this.DM,i>>=this.DB;i-=t.s}e.s=i<0?-1:0,i<-1?e[r++]=this.DV+i:i>0&&(e[r++]=i),e.t=r,e.clamp()}multiplyTo(t,e){var r=this.abs(),i=t.abs(),n=r.t;for(e.t=n+i.t;--n>=0;)e[n]=0;for(n=0;n<i.t;++n)e[n+r.t]=r.am(0,i[n],e,n,0,r.t);e.s=0,e.clamp(),this.s!=t.s&&tt.ZERO.subTo(e,e)}squareTo(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t[r]=0;for(r=0;r<e.t-1;++r){var i=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,i,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()}divRemTo(t,e,r){var i=t.abs();if(!(i.t<=0)){var n=this.abs();if(n.t<i.t)return null!=e&&e.fromInt(0),void(null!=r&&this.copyTo(r));null==r&&(r=et());var s=et(),o=this.s,a=t.s,u=this.DB-ut(i[i.t-1]);u>0?(i.lShiftTo(u,s),n.lShiftTo(u,r)):(i.copyTo(s),n.copyTo(r));var h=s.t,c=s[h-1];if(0!=c){var l=c*(1<<this.F1)+(h>1?s[h-2]>>this.F2:0),d=this.FV/l,f=(1<<this.F1)/l,p=1<<this.F2,v=r.t,g=v-h,m=null==e?et():e;for(s.dlShiftTo(g,m),r.compareTo(m)>=0&&(r[r.t++]=1,r.subTo(m,r)),tt.ONE.dlShiftTo(h,m),m.subTo(s,s);s.t<h;)s[s.t++]=0;for(;--g>=0;){var y=r[--v]==c?this.DM:Math.floor(r[v]*d+(r[v-1]+p)*f);if((r[v]+=s.am(0,y,r,g,0,h))<y)for(s.dlShiftTo(g,m),r.subTo(m,r);r[v]<--y;)r.subTo(m,r)}null!=e&&(r.drShiftTo(h,e),o!=a&&tt.ZERO.subTo(e,e)),r.t=h,r.clamp(),u>0&&r.rShiftTo(u,r),o<0&&tt.ZERO.subTo(r,r)}}}invDigit(){if(this.t<1)return 0;var t=this[0];if(!(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e}isEven(){return 0==(this.t>0?1&this[0]:this.s)}exp(t,e){if(t>4294967295||t<1)return tt.ONE;var r=et(),i=et(),n=e.convert(this),s=ut(t)-1;for(n.copyTo(r);--s>=0;)if(e.sqrTo(r,i),(t&1<<s)>0)e.mulTo(i,n,r);else{var o=r;r=i,i=o}return e.revert(r)}chunkSize(t){return Math.floor(Math.LN2*this.DB/Math.log(t))}toRadix(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),r=Math.pow(t,e),i=at(r),n=et(),s=et(),o="";for(this.divRemTo(i,n,s);n.signum()>0;)o=(r+s.intValue()).toString(t).substr(1)+o,n.divRemTo(i,n,s);return s.intValue().toString(t)+o}fromRadix(t,e){this.fromInt(0),null==e&&(e=10);for(var r=this.chunkSize(e),i=Math.pow(e,r),n=!1,s=0,o=0,a=0;a<t.length;++a){var u=ot(t,a);u<0?"-"==t.charAt(a)&&0==this.signum()&&(n=!0):(o=e*o+u,++s>=r&&(this.dMultiply(i),this.dAddOffset(o,0),s=0,o=0))}s>0&&(this.dMultiply(Math.pow(e,s)),this.dAddOffset(o,0)),n&&tt.ZERO.subTo(this,this)}fromNumber(t,e,r){if("number"==typeof e)if(t<2)this.fromInt(1);else for(this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(tt.ONE.shiftLeft(t-1),j,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(e);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(tt.ONE.shiftLeft(t-1),this);else{var i=[],n=7&t;i.length=1+(t>>3),e.nextBytes(i),n>0?i[0]&=(1<<n)-1:i[0]=0,this.fromString(i,256)}}bitwiseTo(t,e,r){var i,n,s=Math.min(t.t,this.t);for(i=0;i<s;++i)r[i]=e(this[i],t[i]);if(t.t<this.t){for(n=t.s&this.DM,i=s;i<this.t;++i)r[i]=e(this[i],n);r.t=this.t}else{for(n=this.s&this.DM,i=s;i<t.t;++i)r[i]=e(n,t[i]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()}changeBit(t,e){var r=tt.ONE.shiftLeft(t);return this.bitwiseTo(r,e,r),r}addTo(t,e){for(var r=0,i=0,n=Math.min(t.t,this.t);r<n;)i+=this[r]+t[r],e[r++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i+=t.s;r<this.t;)i+=this[r],e[r++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;r<t.t;)i+=t[r],e[r++]=i&this.DM,i>>=this.DB;i+=t.s}e.s=i<0?-1:0,i>0?e[r++]=i:i<-1&&(e[r++]=this.DV+i),e.t=r,e.clamp()}dMultiply(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()}dAddOffset(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}}multiplyLowerTo(t,e,r){var i=Math.min(this.t+t.t,e);for(r.s=0,r.t=i;i>0;)r[--i]=0;for(var n=r.t-this.t;i<n;++i)r[i+this.t]=this.am(0,t[i],r,i,0,this.t);for(n=Math.min(t.t,e);i<n;++i)this.am(0,t[i],r,i,0,e-i);r.clamp()}multiplyUpperTo(t,e,r){--e;var i=r.t=this.t+t.t-e;for(r.s=0;--i>=0;)r[i]=0;for(i=Math.max(e-this.t,0);i<t.t;++i)r[this.t+i-e]=this.am(e-i,t[i],r,0,0,this.t+i-e);r.clamp(),r.drShiftTo(1,r)}modInt(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(0==e)r=this[0]%t;else for(var i=this.t-1;i>=0;--i)r=(e*r+this[i])%t;return r}millerRabin(t){var e=this.subtract(tt.ONE),r=e.getLowestSetBit();if(r<=0)return!1;var i=e.shiftRight(r);(t=t+1>>1)>v.length&&(t=v.length);for(var n=et(),s=0;s<t;++s){n.fromInt(v[Math.floor(Math.random()*v.length)]);var o=n.modPow(i,this);if(0!=o.compareTo(tt.ONE)&&0!=o.compareTo(e)){for(var a=1;a++<r&&0!=o.compareTo(e);)if(0==(o=o.modPowInt(2,this)).compareTo(tt.ONE))return!1;if(0!=o.compareTo(e))return!1}}return!0}square(){var t=et();return this.squareTo(t),t}gcda(t,e){var r=this.s<0?this.negate():this.clone(),i=t.s<0?t.negate():t.clone();if(r.compareTo(i)<0){var n=r;r=i,i=n}var s=r.getLowestSetBit(),o=i.getLowestSetBit();if(o<0)e(r);else{s<o&&(o=s),o>0&&(r.rShiftTo(o,r),i.rShiftTo(o,i));var a=()=>{(s=r.getLowestSetBit())>0&&r.rShiftTo(s,r),(s=i.getLowestSetBit())>0&&i.rShiftTo(s,i),r.compareTo(i)>=0?(r.subTo(i,r),r.rShiftTo(1,r)):(i.subTo(r,i),i.rShiftTo(1,i)),r.signum()>0?setTimeout(a,0):(o>0&&i.lShiftTo(o,i),setTimeout((function(){e(i)}),0))};setTimeout(a,10)}}fromNumberAsync(t,e,r,i){if("number"==typeof e)if(t<2)this.fromInt(1);else{this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(tt.ONE.shiftLeft(t-1),j,this),this.isEven()&&this.dAddOffset(1,0);var n=this,s=function(){n.dAddOffset(2,0),n.bitLength()>t&&n.subTo(tt.ONE.shiftLeft(t-1),n),n.isProbablePrime(e)?setTimeout((function(){i()}),0):setTimeout(s,0)};setTimeout(s,0)}else{var o=[],a=7&t;o.length=1+(t>>3),e.nextBytes(o),a>0?o[0]&=(1<<a)-1:o[0]=0,this.fromString(o,256)}}}var m=function(){function t(){}return t.prototype.convert=function(t){return t},t.prototype.revert=function(t){return t},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r)},t.prototype.sqrTo=function(t,e){t.squareTo(e)},t}(),y=function(){function t(t){this.m=t}return t.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),_=function(){function t(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return t.prototype.convert=function(t){var e=et();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(tt.ZERO)>0&&this.m.subTo(e,e),e},t.prototype.revert=function(t){var e=et();return t.copyTo(e),this.reduce(e),e},t.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],i=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[r=e+this.m.t]+=this.m.am(0,i,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),T=function(){function t(t){this.m=t,this.r2=et(),this.q3=et(),tt.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return t.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=et();return t.copyTo(e),this.reduce(e),e},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}();function et(){return new tt(null)}function rt(t,e){return new tt(t,e)}function it(t,e,r,i,n,s){for(;--s>=0;){var o=e*this[t++]+r[i]+n;n=Math.floor(o/67108864),r[i++]=67108863&o}return n}function nt(t,e,r,i,n,s){for(var o=32767&e,a=e>>15;--s>=0;){var u=32767&this[t],h=this[t++]>>15,c=a*u+h*o;n=((u=o*u+((32767&c)<<15)+r[i]+(1073741823&n))>>>30)+(c>>>15)+a*h+(n>>>30),r[i++]=1073741823&u}return n}function st(t,e,r,i,n,s){for(var o=16383&e,a=e>>14;--s>=0;){var u=16383&this[t],h=this[t++]>>14,c=a*u+h*o;n=((u=o*u+((16383&c)<<14)+r[i]+n)>>28)+(c>>14)+a*h,r[i++]=268435455&u}return n}void 0!==n&&"Microsoft Internet Explorer"==(null==n?void 0:n.appName)?(tt.prototype.am=nt,d=30):"Netscape"!=(null==n?void 0:n.appName)?(tt.prototype.am=it,d=26):(tt.prototype.am=st,d=28),tt.prototype.DB=d,tt.prototype.DM=(1<<d)-1,tt.prototype.DV=1<<d,tt.prototype.FV=Math.pow(2,52),tt.prototype.F1=52-d,tt.prototype.F2=2*d-52;var E,S,b=[];for(E="0".charCodeAt(0),S=0;S<=9;++S)b[E++]=S;for(E="a".charCodeAt(0),S=10;S<36;++S)b[E++]=S;for(E="A".charCodeAt(0),S=10;S<36;++S)b[E++]=S;function ot(t,e){var r=b[t.charCodeAt(e)];return null==r?-1:r}function at(t){var e=et();return e.fromInt(t),e}function ut(t){var e,r=1;return 0!=(e=t>>>16)&&(t=e,r+=16),0!=(e=t>>8)&&(t=e,r+=8),0!=(e=t>>4)&&(t=e,r+=4),0!=(e=t>>2)&&(t=e,r+=2),0!=(e=t>>1)&&(t=e,r+=1),r}tt.ZERO=at(0),tt.ONE=at(1);class ht{constructor(){this.i=0,this.j=0,this.S=[]}init(t){var e,r,i;for(e=0;e<256;++e)this.S[e]=e;for(r=0,e=0;e<256;++e)r=r+this.S[e]+t[e%t.length]&255,i=this.S[e],this.S[e]=this.S[r],this.S[r]=i;this.i=0,this.j=0}next(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]}}var I,w,R=null;if(null==R){var O,C,A,P;R=[],w=0;var U=void 0;if(null!==(O=window)&&void 0!==O&&O.crypto&&null!==(C=window)&&void 0!==C&&C.crypto.getRandomValues){var D,N=new Uint32Array(256);for(null===(D=window)||void 0===D||D.crypto.getRandomValues(N),U=0;U<N.length;++U)R[w++]=255&N[U]}var L,x=function(t){var e,r,i;if(this.count=this.count||0,this.count>=256||w>=256){if(null!==(e=window)&&void 0!==e&&e.removeEventListener)null===(i=window)||void 0===i||i.removeEventListener("mousemove",x,!1);else if(null!==(r=window)&&void 0!==r&&r.detachEvent){var n;null===(n=window)||void 0===n||n.detachEvent("onmousemove",x)}}else try{var s=t.x+t.y;R[w++]=255&s,this.count+=1}catch(t){}};if(null!==(A=window)&&void 0!==A&&A.addEventListener)null===(L=window)||void 0===L||L.addEventListener("mousemove",x,!1);else if(null!==(P=window)&&void 0!==P&&P.attachEvent){var k;null===(k=window)||void 0===k||k.attachEvent("onmousemove",x)}}function ct(){if(null==I){for(I=new ht;w<256;){var t=Math.floor(65536*Math.random());R[w++]=255&t}for(I.init(R),w=0;w<R.length;++w)R[w]=0;w=0}return I.next()}class lt{constructor(){}nextBytes(t){for(var e=0;e<t.length;++e)t[e]=ct()}}function dt(t,e){if(e<t.length+11)return console.error("Message too long for RSA"),null;for(var r=[],i=t.length-1;i>=0&&e>0;){var n=t.charCodeAt(i--);n<128?r[--e]=n:n>127&&n<2048?(r[--e]=63&n|128,r[--e]=n>>6|192):(r[--e]=63&n|128,r[--e]=n>>6&63|128,r[--e]=n>>12|224)}r[--e]=0;for(var s=new lt,o=[];e>2;){for(o[0]=0;0==o[0];)s.nextBytes(o);r[--e]=o[0]}return r[--e]=2,r[--e]=0,new tt(r)}class ft{constructor(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}doPublic(t){return t.modPowInt(this.e,this.n)}doPrivate(t){if(null==this.p||null==this.q)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),r=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(r)<0;)e=e.add(this.p);return e.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)}setPublic(t,e){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=rt(t,16),this.e=parseInt(e,16)):console.error("Invalid RSA public key")}encrypt(t){var e=dt(t,this.n.bitLength()+7>>3);if(null==e)return null;var r=this.doPublic(e);if(null==r)return null;var i=r.toString(16);return 1&i.length?"0"+i:i}encryptLong(t){var e=this,r=(this.n.bitLength()+7>>3)-11;try{var i="";return t.length>r?(t.match(/.{1,117}/g).forEach((function(t){var r=e.encrypt(t);i+=r})),Y(i)):Y(this.encrypt(t))}catch(t){return!1}}decryptLong(t){var e=this,r=this.n.bitLength()+7>>3;t=z(t);try{if(t.length>r){var i="";return t.match(/.{1,256}/g).forEach((function(t){var r=e.decrypt(t);i+=r})),i}return this.decrypt(t)}catch(t){return!1}}setPrivate(t,e,r){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=rt(t,16),this.e=parseInt(e,16),this.d=rt(r,16)):console.error("Invalid RSA private key")}setPrivateEx(t,e,r,i,n,s,o,a){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=rt(t,16),this.e=parseInt(e,16),this.d=rt(r,16),this.p=rt(i,16),this.q=rt(n,16),this.dmp1=rt(s,16),this.dmq1=rt(o,16),this.coeff=rt(a,16)):console.error("Invalid RSA private key")}generate(t,e){var r=new lt,i=t>>1;this.e=parseInt(e,16);for(var n=new tt(e,16);;){for(;this.p=new tt(t-i,1,r),0!=this.p.subtract(tt.ONE).gcd(n).compareTo(tt.ONE)||!this.p.isProbablePrime(10););for(;this.q=new tt(i,1,r),0!=this.q.subtract(tt.ONE).gcd(n).compareTo(tt.ONE)||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var s=this.p;this.p=this.q,this.q=s}var o=this.p.subtract(tt.ONE),a=this.q.subtract(tt.ONE),u=o.multiply(a);if(0==u.gcd(n).compareTo(tt.ONE)){this.n=this.p.multiply(this.q),this.d=n.modInverse(u),this.dmp1=this.d.mod(o),this.dmq1=this.d.mod(a),this.coeff=this.q.modInverse(this.p);break}}}decrypt(t){var e=rt(t,16),r=this.doPrivate(e);return null==r?null:pt(r,this.n.bitLength()+7>>3)}generateAsync(t,e,r){var i=new lt,n=t>>1;this.e=parseInt(e,16);var s=new tt(e,16),o=this,a=()=>{var e=()=>{if(o.p.compareTo(o.q)<=0){var t=o.p;o.p=o.q,o.q=t}var e=o.p.subtract(tt.ONE),i=o.q.subtract(tt.ONE),n=e.multiply(i);0==n.gcd(s).compareTo(tt.ONE)?(o.n=o.p.multiply(o.q),o.d=s.modInverse(n),o.dmp1=o.d.mod(e),o.dmq1=o.d.mod(i),o.coeff=o.q.modInverse(o.p),setTimeout((function(){r()}),0)):setTimeout(a,0)},u=()=>{o.q=et(),o.q.fromNumberAsync(n,1,i,(function(){o.q.subtract(tt.ONE).gcda(s,(function(t){0==t.compareTo(tt.ONE)&&o.q.isProbablePrime(10)?setTimeout(e,0):setTimeout(u,0)}))}))},h=()=>{o.p=et(),o.p.fromNumberAsync(t-n,1,i,(function(){o.p.subtract(tt.ONE).gcda(s,(function(t){0==t.compareTo(tt.ONE)&&o.p.isProbablePrime(10)?setTimeout(u,0):setTimeout(h,0)}))}))};setTimeout(h,0)};setTimeout(a,0)}}function pt(t,e){for(var r=t.toByteArray(),i=0;i<r.length&&0==r[i];)++i;if(r.length-i!=e-1||2!=r[i])return null;for(++i;0!=r[i];)if(++i>=r.length)return null;for(var n="";++i<r.length;){var s=255&r[i];s<128?n+=String.fromCharCode(s):s>191&&s<224?(n+=String.fromCharCode((31&s)<<6|63&r[i+1]),++i):(n+=String.fromCharCode((15&s)<<12|(63&r[i+1])<<6|63&r[i+2]),i+=2)}return n}class vt extends ft{constructor(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";super(),t&&("string"==typeof t?this.parseKey(t):(this.hasPrivateKeyProperty(t)||this.hasPublicKeyProperty(t))&&this.parsePropertiesFrom(t))}parseKey(t){try{var e=0,r=0,i=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(t)?h.decode(t):c.unarmor(t),n=X.decode(i);if(3===n.sub.length&&(n=n.sub[2].sub[0]),9===n.sub.length){e=n.sub[1].getHexStringValue(),this.n=rt(e,16),r=n.sub[2].getHexStringValue(),this.e=parseInt(r,16);var s=n.sub[3].getHexStringValue();this.d=rt(s,16);var o=n.sub[4].getHexStringValue();this.p=rt(o,16);var a=n.sub[5].getHexStringValue();this.q=rt(a,16);var u=n.sub[6].getHexStringValue();this.dmp1=rt(u,16);var l=n.sub[7].getHexStringValue();this.dmq1=rt(l,16);var d=n.sub[8].getHexStringValue();this.coeff=rt(d,16)}else{if(2!==n.sub.length)return!1;var f=n.sub[1].sub[0];e=f.sub[0].getHexStringValue(),this.n=rt(e,16),r=f.sub[1].getHexStringValue(),this.e=parseInt(r,16)}return!0}catch(t){return!1}}hasPublicKeyProperty(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")}hasPrivateKeyProperty(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")&&t.hasOwnProperty("d")&&t.hasOwnProperty("p")&&t.hasOwnProperty("q")&&t.hasOwnProperty("dmp1")&&t.hasOwnProperty("dmq1")&&t.hasOwnProperty("coeff")}parsePropertiesFrom(t){this.n=t.n,this.e=t.e,t.hasOwnProperty("d")&&(this.d=t.d,this.p=t.p,this.q=t.q,this.dmp1=t.dmp1,this.dmq1=t.dmq1,this.coeff=t.coeff)}}(i=function(t){t=t||{},this.default_key_size=parseInt(t.default_key_size,10)||1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=null}).prototype.setKey=function(t){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new vt(t)},i.prototype.setPrivateKey=function(t){this.setKey(t)},i.prototype.setPublicKey=function(t){this.setKey(t)},i.prototype.decrypt=function(t){try{return this.getKey().decrypt(z(t))}catch(t){return!1}},i.prototype.encrypt=function(t){try{return Y(this.getKey().encrypt(t))}catch(t){return!1}},i.prototype.encryptLong=function(t){try{for(var e=this.getKey().encryptLong(t)||"",r=this.getKey().decryptLong(e)||"",i=0,n=/null$/g;n.test(r)&&(i++,e=this.getKey().encryptLong(t)||"",r=this.getKey().decryptLong(e)||"",!(i>10)););return e}catch(t){return!1}},i.prototype.getKey=function(t){if(!this.key){if(this.key=new vt,t&&"[object Function]"==={}.toString.call(t))return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},i.version="3.1.4"}const q=i;var B=r(655),H=function(){var{publicKey:t="",payload:e={}}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!t)return"";try{var r=(0,B.I)(e),i=new q;return i.setPublicKey(t),i.encryptLong("object"==typeof r?JSON.stringify(r):r)}catch(t){console.error("encrypt error:",t)}return""}},655:(t,e,r)=>{r.d(e,{I:()=>i,y:()=>n});var i=t=>{var e=e=>{for(var r in t)t.hasOwnProperty(r)&&(e[r]=i(t[r]));return e},r=null==t?"NullOrUndefined":Object.prototype.toString.call(t).slice(8,-1);if(["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array","BigInt64Array","BigUint64Array"].includes(r))return t.slice();switch(r){case"Object":return e(Object.create(Object.getPrototypeOf(t)));case"Array":return e([]);case"Date":return new Date(t.valueOf());case"RegExp":return new RegExp(t.source,(t.global?"g":"")+(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.sticky?"y":"")+(t.unicode?"u":""));default:return t}},n=t=>{var e=t.match(/^(?:http(s)?:\/\/[^\/]+)?(\/[^\?#]*)/);return e&&e[2]||""}}},e={};function r(i){var n=e[i];if(void 0!==n)return n.exports;var s=e[i]={exports:{}};return t[i].call(s.exports,s,s.exports,r),s.exports}return r.d=(t,e)=>{for(var i in e)r.o(e,i)&&!r.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r(118)})()));