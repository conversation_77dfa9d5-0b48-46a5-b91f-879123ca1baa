"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPathName = exports.deepClone = void 0;
var deepClone = function (value) {
    var clone = function (copiedValue) {
        for (var key in value) {
            if (value.hasOwnProperty(key)) {
                copiedValue[key] = (0, exports.deepClone)(value[key]);
            }
        }
        return copiedValue;
    };
    var type = value === null || value === undefined ? 'NullOrUndefined' : Object.prototype.toString.call(value).slice(8, -1);
    if ([
        'Int8Array',
        'Uint8Array',
        'Uint8ClampedArray',
        'Int16Array',
        'Uint16Array',
        'Int32Array',
        'Uint32Array',
        'Float32Array',
        'Float64Array',
        'BigInt64Array',
        'BigUint64Array',
    ].includes(type)) {
        return value.slice();
    }
    switch (type) {
        case 'Object':
            return clone(Object.create(Object.getPrototypeOf(value)));
        case 'Array':
            return clone([]);
        case 'Date':
            return new Date(value.valueOf());
        case 'RegExp':
            return new RegExp(value.source, (value.global ? 'g' : '')
                + (value.ignoreCase ? 'i' : '')
                + (value.multiline ? 'm' : '')
                + (value.sticky ? 'y' : '')
                + (value.unicode ? 'u' : ''));
        default:
            return value;
    }
};
exports.deepClone = deepClone;
var getPathName = function (url) {
    var regex = /^(?:http(s)?:\/\/[^\/]+)?(\/[^\?#]*)/;
    var match = url.match(regex);
    if (match) {
        return match[2] || '';
    }
    return '';
};
exports.getPathName = getPathName;
//# sourceMappingURL=data:application/json;base64,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