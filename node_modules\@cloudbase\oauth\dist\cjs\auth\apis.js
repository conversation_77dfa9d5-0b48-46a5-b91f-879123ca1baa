'use strict';
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Auth = void 0;
var consts_1 = require("./consts");
var oauth2client_1 = require("../oauth2client/oauth2client");
var captcha_1 = require("../captcha/captcha");
var utils_1 = require("../utils");
var urlSearchParams_1 = __importDefault(require("../utils/urlSearchParams"));
function getEncryptUtils(isEncrypt) {
    if (globalThis.IS_MP_BUILD) {
        return;
    }
    if (isEncrypt) {
        var utils = require('../utils/encrypt');
        return utils;
    }
}
var Auth = (function () {
    function Auth(opts) {
        var request = opts.request;
        var oAuth2Client = opts.credentialsClient;
        if (!oAuth2Client) {
            var initOptions = {
                apiOrigin: opts.apiOrigin,
                clientId: opts.clientId,
                storage: opts.storage,
                env: opts.env,
                baseRequest: opts.baseRequest,
                anonymousSignInFunc: opts.anonymousSignInFunc,
                wxCloud: opts.wxCloud,
            };
            oAuth2Client = new oauth2client_1.OAuth2Client(initOptions);
        }
        if (!request) {
            var baseRequest = oAuth2Client.request.bind(oAuth2Client);
            var captcha = new captcha_1.Captcha(__assign({ clientId: opts.clientId, request: baseRequest, storage: opts.storage }, opts.captchaOptions));
            request = captcha.request.bind(captcha);
        }
        this.config = {
            env: opts.env,
            apiOrigin: opts.apiOrigin,
            clientId: opts.clientId,
            request: request,
            credentialsClient: oAuth2Client,
            storage: opts.storage || oauth2client_1.defaultStorage,
        };
    }
    Auth.parseParamsToSearch = function (params) {
        Object.keys(params).forEach(function (key) {
            if (!params[key]) {
                delete params[key];
            }
        });
        var searchParams = new urlSearchParams_1.default(params);
        return searchParams.toString();
    };
    Auth.prototype.getParamsByVersion = function (params, key) {
        var _a;
        var paramsTemp = (0, utils_1.deepClone)(params);
        var url = ((_a = { v2: consts_1.ApiUrlsV2 }[paramsTemp === null || paramsTemp === void 0 ? void 0 : paramsTemp.version]) === null || _a === void 0 ? void 0 : _a[key]) || consts_1.ApiUrls[key];
        if (paramsTemp) {
            delete paramsTemp.version;
        }
        return { params: paramsTemp, url: url };
    };
    Auth.prototype.signIn = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var version, res, body, credentials;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        version = params.version || 'v1';
                        res = this.getParamsByVersion(params, 'AUTH_SIGN_IN_URL');
                        if (res.params.query) {
                            delete res.params.query;
                        }
                        return [4, this.getEncryptParams(res.params)];
                    case 1:
                        body = _a.sent();
                        return [4, this.config.request(res.url, {
                                method: 'POST',
                                body: body,
                            })];
                    case 2:
                        credentials = _a.sent();
                        return [4, this.config.credentialsClient.setCredentials(__assign(__assign({}, credentials), { version: version }))];
                    case 3:
                        _a.sent();
                        return [2, Promise.resolve(credentials)];
                }
            });
        });
    };
    Auth.prototype.signInAnonymously = function (data, useWxCloud) {
        if (data === void 0) { data = {}; }
        if (useWxCloud === void 0) { useWxCloud = false; }
        return __awaiter(this, void 0, void 0, function () {
            var credentials;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, this.config.request(consts_1.ApiUrls.AUTH_SIGN_IN_ANONYMOUSLY_URL, {
                            method: 'POST',
                            body: data,
                            useWxCloud: useWxCloud,
                        })];
                    case 1:
                        credentials = _a.sent();
                        return [4, this.config.credentialsClient.setCredentials(credentials)];
                    case 2:
                        _a.sent();
                        return [2, Promise.resolve(credentials)];
                }
            });
        });
    };
    Auth.prototype.signUp = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, this.config.request(consts_1.ApiUrls.AUTH_SIGN_UP_URL, {
                            method: 'POST',
                            body: params,
                        })];
                    case 1:
                        data = _a.sent();
                        return [4, this.config.credentialsClient.setCredentials(data)];
                    case 2:
                        _a.sent();
                        return [2, Promise.resolve(data)];
                }
            });
        });
    };
    Auth.prototype.signOut = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var resp, err_1, accessToken, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        resp = {};
                        if (!params) return [3, 6];
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4, this.config.request(consts_1.ApiUrls.AUTH_SIGNOUT_URL, {
                                method: 'POST',
                                withCredentials: true,
                                body: params,
                            })];
                    case 2:
                        resp = _a.sent();
                        return [3, 4];
                    case 3:
                        err_1 = _a.sent();
                        if (err_1.error !== consts_1.ErrorType.UNAUTHENTICATED) {
                            console.log('sign_out_error', err_1);
                        }
                        return [3, 4];
                    case 4: return [4, this.config.credentialsClient.setCredentials()];
                    case 5:
                        _a.sent();
                        return [2, resp];
                    case 6: return [4, this.config.credentialsClient.getAccessToken()];
                    case 7:
                        accessToken = _a.sent();
                        return [4, this.config.request(consts_1.ApiUrls.AUTH_REVOKE_URL, {
                                method: 'POST',
                                body: {
                                    token: accessToken,
                                },
                            })];
                    case 8:
                        data = _a.sent();
                        return [4, this.config.credentialsClient.setCredentials()];
                    case 9:
                        _a.sent();
                        return [2, Promise.resolve(data)];
                }
            });
        });
    };
    Auth.prototype.revokeAllDevices = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, this.config.request(consts_1.ApiUrls.AUTH_REVOKE_ALL_URL, {
                            method: 'DELETE',
                            withCredentials: true,
                        })];
                    case 1:
                        _a.sent();
                        return [2];
                }
            });
        });
    };
    Auth.prototype.revokeDevice = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, this.config.request(consts_1.ApiUrls.AUTHORIZED_DEVICES_DELETE_URL + params.device_id, {
                            method: 'DELETE',
                            withCredentials: true,
                        })];
                    case 1:
                        _a.sent();
                        return [2];
                }
            });
        });
    };
    Auth.prototype.getVerification = function (params, options) {
        return __awaiter(this, void 0, void 0, function () {
            var withCredentials, hasLogin;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        withCredentials = false;
                        if (!(params.target === 'CUR_USER')) return [3, 1];
                        withCredentials = true;
                        return [3, 3];
                    case 1: return [4, this.hasLoginState()];
                    case 2:
                        hasLogin = _a.sent();
                        if (hasLogin) {
                            withCredentials = true;
                        }
                        _a.label = 3;
                    case 3: return [2, this.config.request(consts_1.ApiUrls.VERIFICATION_URL, {
                            method: 'POST',
                            body: params,
                            withCaptcha: (options === null || options === void 0 ? void 0 : options.withCaptcha) || false,
                            withCredentials: withCredentials,
                        })];
                }
            });
        });
    };
    Auth.prototype.verify = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var res, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        res = this.getParamsByVersion(params, 'VERIFY_URL');
                        return [4, this.config.request(res.url, {
                                method: 'POST',
                                body: res.params,
                            })];
                    case 1:
                        data = _a.sent();
                        if (!((params === null || params === void 0 ? void 0 : params.version) === 'v2')) return [3, 3];
                        return [4, this.config.credentialsClient.setCredentials(__assign(__assign({}, data), { version: 'v2' }))];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3: return [2, data];
                }
            });
        });
    };
    Auth.prototype.genProviderRedirectUri = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var redirect_uri, _a, otherParams, restParams, url;
            return __generator(this, function (_b) {
                redirect_uri = params.provider_redirect_uri, _a = params.other_params, otherParams = _a === void 0 ? {} : _a, restParams = __rest(params, ["provider_redirect_uri", "other_params"]);
                if (redirect_uri && !restParams.redirect_uri) {
                    restParams.redirect_uri = redirect_uri;
                }
                url = "".concat(consts_1.ApiUrls.PROVIDER_URI_URL, "?").concat(Auth.parseParamsToSearch(restParams));
                Object.keys(otherParams).forEach(function (key) {
                    var value = otherParams[key];
                    if (key === 'sign_out_uri' && !((value === null || value === void 0 ? void 0 : value.length) > 0)) {
                        return;
                    }
                    url += "&other_params[".concat(key, "]=").concat(encodeURIComponent(value));
                });
                return [2, this.config.request(url, {
                        method: 'GET',
                    })];
            });
        });
    };
    Auth.prototype.grantProviderToken = function (params, useWxCloud) {
        if (useWxCloud === void 0) { useWxCloud = false; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request(consts_1.ApiUrls.PROVIDER_TOKEN_URL, {
                        method: 'POST',
                        body: params,
                        useWxCloud: useWxCloud,
                    })];
            });
        });
    };
    Auth.prototype.patchProviderToken = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request(consts_1.ApiUrls.PROVIDER_TOKEN_URL, {
                        method: 'PATCH',
                        body: params,
                    })];
            });
        });
    };
    Auth.prototype.signInWithProvider = function (params, useWxCloud) {
        if (useWxCloud === void 0) { useWxCloud = false; }
        return __awaiter(this, void 0, void 0, function () {
            var res, credentials;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        res = this.getParamsByVersion(params, 'AUTH_SIGN_IN_WITH_PROVIDER_URL');
                        return [4, this.config.request(res.url, {
                                method: 'POST',
                                body: res.params,
                                useWxCloud: useWxCloud,
                            })];
                    case 1:
                        credentials = _a.sent();
                        return [4, this.config.credentialsClient.setCredentials(__assign(__assign({}, credentials), { version: (params === null || params === void 0 ? void 0 : params.version) || 'v1' }))];
                    case 2:
                        _a.sent();
                        return [2, Promise.resolve(credentials)];
                }
            });
        });
    };
    Auth.prototype.signInCustom = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var credentials;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, this.config.request(consts_1.ApiUrls.AUTH_SIGN_IN_CUSTOM, {
                            method: 'POST',
                            body: params,
                        })];
                    case 1:
                        credentials = _a.sent();
                        return [4, this.config.credentialsClient.setCredentials(credentials)];
                    case 2:
                        _a.sent();
                        return [2, Promise.resolve(credentials)];
                }
            });
        });
    };
    Auth.prototype.signInWithWechat = function (params) {
        if (params === void 0) { params = {}; }
        return __awaiter(this, void 0, void 0, function () {
            var credentials;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, this.config.request(consts_1.ApiUrls.AUTH_SIGN_IN_WITH_WECHAT_URL, {
                            method: 'POST',
                            body: params,
                        })];
                    case 1:
                        credentials = _a.sent();
                        return [4, this.config.credentialsClient.setCredentials(credentials)];
                    case 2:
                        _a.sent();
                        return [2, Promise.resolve(credentials)];
                }
            });
        });
    };
    Auth.prototype.bindWithProvider = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request(consts_1.ApiUrls.PROVIDER_BIND_URL, {
                        method: 'POST',
                        body: params,
                        withCredentials: true,
                    })];
            });
        });
    };
    Auth.prototype.getUserProfile = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.getUserInfo(params)];
            });
        });
    };
    Auth.prototype.getUserInfo = function (params) {
        var _a;
        if (params === void 0) { params = {}; }
        return __awaiter(this, void 0, void 0, function () {
            var res, searchParams, userInfo;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        res = this.getParamsByVersion(params, 'USER_ME_URL');
                        if ((_a = res.params) === null || _a === void 0 ? void 0 : _a.query) {
                            searchParams = new urlSearchParams_1.default(res.params.query);
                            res.url += "?".concat(searchParams.toString());
                        }
                        return [4, this.config.request(res.url, {
                                method: 'GET',
                                withCredentials: true,
                            })];
                    case 1:
                        userInfo = _b.sent();
                        if (userInfo.sub) {
                            userInfo.uid = userInfo.sub;
                        }
                        return [2, userInfo];
                }
            });
        });
    };
    Auth.prototype.getWedaUserInfo = function () {
        return __awaiter(this, void 0, void 0, function () {
            var userInfo;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, this.config.request(consts_1.ApiUrls.WEDA_USER_URL, {
                            method: 'GET',
                            withCredentials: true,
                        })];
                    case 1:
                        userInfo = _a.sent();
                        return [2, userInfo];
                }
            });
        });
    };
    Auth.prototype.deleteMe = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var res, url;
            return __generator(this, function (_a) {
                res = this.getParamsByVersion(params, 'USER_ME_URL');
                url = "".concat(res.url, "?").concat(Auth.parseParamsToSearch(res.params));
                return [2, this.config.request(url, {
                        method: 'DELETE',
                        withCredentials: true,
                    })];
            });
        });
    };
    Auth.prototype.hasLoginState = function () {
        return __awaiter(this, void 0, void 0, function () {
            var error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4, this.config.credentialsClient.getAccessToken()];
                    case 1:
                        _a.sent();
                        return [2, true];
                    case 2:
                        error_1 = _a.sent();
                        return [2, false];
                    case 3: return [2];
                }
            });
        });
    };
    Auth.prototype.hasLoginStateSync = function () {
        var credentials = this.config.credentialsClient.getCredentialsSync();
        return credentials;
    };
    Auth.prototype.getLoginState = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.credentialsClient.getCredentialsAsync()];
            });
        });
    };
    Auth.prototype.transByProvider = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request(consts_1.ApiUrls.USER_TRANS_BY_PROVIDER_URL, {
                        method: 'PATCH',
                        body: params,
                        withCredentials: true,
                    })];
            });
        });
    };
    Auth.prototype.grantToken = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var res;
            return __generator(this, function (_a) {
                res = this.getParamsByVersion(params, 'AUTH_TOKEN_URL');
                return [2, this.config.request(res.url, {
                        method: 'POST',
                        body: res.params,
                    })];
            });
        });
    };
    Auth.prototype.getProviders = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request(consts_1.ApiUrls.PROVIDER_LIST, {
                        method: 'GET',
                        withCredentials: true,
                    })];
            });
        });
    };
    Auth.prototype.unbindProvider = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request("".concat(consts_1.ApiUrls.PROVIDER_UNBIND_URL, "/").concat(params.provider_id), {
                        method: 'DELETE',
                        withCredentials: true,
                    })];
            });
        });
    };
    Auth.prototype.checkPassword = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request("".concat(consts_1.ApiUrls.CHECK_PWD_URL), {
                        method: 'POST',
                        withCredentials: true,
                        body: params,
                    })];
            });
        });
    };
    Auth.prototype.editContact = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request("".concat(consts_1.ApiUrls.BIND_CONTACT_URL), {
                        method: 'PATCH',
                        withCredentials: true,
                        body: params,
                    })];
            });
        });
    };
    Auth.prototype.setPassword = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request("".concat(consts_1.ApiUrls.AUTH_SET_PASSWORD), {
                        method: 'PATCH',
                        withCredentials: true,
                        body: params,
                    })];
            });
        });
    };
    Auth.prototype.updatePasswordByOld = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var sudoToken;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, this.sudo({ password: params.old_password })];
                    case 1:
                        sudoToken = _a.sent();
                        return [2, this.setPassword({
                                sudo_token: sudoToken.sudo_token,
                                new_password: params.new_password,
                            })];
                }
            });
        });
    };
    Auth.prototype.sudo = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request("".concat(consts_1.ApiUrls.SUDO_URL), {
                        method: 'POST',
                        withCredentials: true,
                        body: params,
                    })];
            });
        });
    };
    Auth.prototype.sendVerificationCodeToCurrentUser = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                params.target = 'CUR_USER';
                return [2, this.config.request(consts_1.ApiUrls.VERIFICATION_URL, {
                        method: 'POST',
                        body: params,
                        withCredentials: true,
                        withCaptcha: true,
                    })];
            });
        });
    };
    Auth.prototype.changeBoundProvider = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request("".concat(consts_1.ApiUrls.PROVIDER_LIST, "/").concat(params.provider_id, "/trans"), {
                        method: 'POST',
                        body: {
                            provider_trans_token: params.trans_token,
                        },
                        withCredentials: true,
                    })];
            });
        });
    };
    Auth.prototype.setUserProfile = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request(consts_1.ApiUrls.USER_PRIFILE_URL, {
                        method: 'PATCH',
                        body: params,
                        withCredentials: true,
                    })];
            });
        });
    };
    Auth.prototype.updateUserBasicInfo = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request(consts_1.ApiUrls.USER_BASIC_EDIT_URL, {
                        method: 'POST',
                        withCredentials: true,
                        body: params,
                    })];
            });
        });
    };
    Auth.prototype.queryUserProfile = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var searchParams;
            return __generator(this, function (_a) {
                searchParams = new urlSearchParams_1.default(params);
                return [2, this.config.request("".concat(consts_1.ApiUrls.USER_QUERY_URL, "?").concat(searchParams.toString()), {
                        method: 'GET',
                        withCredentials: true,
                    })];
            });
        });
    };
    Auth.prototype.setCustomSignFunc = function (getTickFn) {
        this.getCustomSignTicketFn = getTickFn;
    };
    Auth.prototype.signInWithCustomTicket = function () {
        return __awaiter(this, void 0, void 0, function () {
            var customSignTicketFn, customTicket;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        customSignTicketFn = this.getCustomSignTicketFn;
                        if (!customSignTicketFn) {
                            return [2, Promise.reject({
                                    error: 'failed_precondition',
                                    error_description: 'please use setCustomSignFunc to set custom sign function',
                                })];
                        }
                        return [4, customSignTicketFn()];
                    case 1:
                        customTicket = _a.sent();
                        return [2, this.signInCustom({
                                provider_id: 'custom',
                                ticket: customTicket,
                            })];
                }
            });
        });
    };
    Auth.prototype.resetPassword = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request(consts_1.ApiUrls.AUTH_RESET_PASSWORD, {
                        method: 'POST',
                        body: params,
                    })];
            });
        });
    };
    Auth.prototype.authorize = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request(consts_1.ApiUrls.AUTHORIZE_URL, {
                        method: 'POST',
                        withCredentials: true,
                        body: params,
                    })];
            });
        });
    };
    Auth.prototype.authorizeDevice = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request(consts_1.ApiUrls.AUTHORIZE_DEVICE_URL, {
                        method: 'POST',
                        withCredentials: true,
                        body: params,
                    })];
            });
        });
    };
    Auth.prototype.deviceAuthorize = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request(consts_1.ApiUrls.AUTH_GET_DEVICE_CODE, {
                        method: 'POST',
                        body: params,
                        withCredentials: true,
                    })];
            });
        });
    };
    Auth.prototype.authorizeInfo = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var url, withBasicAuth, withCredentials, hasLogin;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        url = "".concat(consts_1.ApiUrls.AUTHORIZE_INFO_URL, "?").concat(Auth.parseParamsToSearch(params));
                        withBasicAuth = true;
                        withCredentials = false;
                        return [4, this.hasLoginState()];
                    case 1:
                        hasLogin = _a.sent();
                        if (hasLogin) {
                            withCredentials = true;
                            withBasicAuth = false;
                        }
                        return [2, this.config.request(url, {
                                method: 'GET',
                                withBasicAuth: withBasicAuth,
                                withCredentials: withCredentials,
                            })];
                }
            });
        });
    };
    Auth.prototype.checkUsername = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request(consts_1.ApiUrls.CHECK_USERNAME, {
                        method: 'GET',
                        body: params,
                        withCredentials: true,
                    })];
            });
        });
    };
    Auth.prototype.checkIfUserExist = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var searchParams;
            return __generator(this, function (_a) {
                searchParams = new urlSearchParams_1.default(params);
                return [2, this.config.request("".concat(consts_1.ApiUrls.CHECK_IF_USER_EXIST, "?").concat(searchParams.toString()), {
                        method: 'GET',
                    })];
            });
        });
    };
    Auth.prototype.loginScope = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.credentialsClient.getScope()];
            });
        });
    };
    Auth.prototype.loginGroups = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.credentialsClient.getGroups()];
            });
        });
    };
    Auth.prototype.refreshTokenForce = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var credentials;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, this.config.credentialsClient.getCredentials()];
                    case 1:
                        credentials = _a.sent();
                        return [4, this.config.credentialsClient.refreshToken(__assign(__assign({}, credentials), { version: (params === null || params === void 0 ? void 0 : params.version) || 'v1' }))];
                    case 2: return [2, _a.sent()];
                }
            });
        });
    };
    Auth.prototype.getCredentials = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.credentialsClient.getCredentials()];
            });
        });
    };
    Auth.prototype.getPublicKey = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request(consts_1.ApiUrlsV2.AUTH_PUBLIC_KEY, {
                        method: 'POST',
                        body: {},
                    })];
            });
        });
    };
    Auth.prototype.getEncryptParams = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var isEncrypt, payload, encryptUtils, publicKey, public_key_thumbprint, res, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        isEncrypt = params.isEncrypt;
                        delete params.isEncrypt;
                        payload = (0, utils_1.deepClone)(params);
                        encryptUtils = getEncryptUtils(isEncrypt);
                        if (!encryptUtils) {
                            return [2, params];
                        }
                        publicKey = '';
                        public_key_thumbprint = '';
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4, this.getPublicKey()];
                    case 2:
                        res = _a.sent();
                        publicKey = res.public_key;
                        public_key_thumbprint = res.public_key_thumbprint;
                        if (!publicKey || !public_key_thumbprint) {
                            throw res;
                        }
                        return [3, 4];
                    case 3:
                        error_2 = _a.sent();
                        throw error_2;
                    case 4: return [2, {
                            params: encryptUtils.getEncryptInfo({ publicKey: publicKey, payload: payload }),
                            public_key_thumbprint: public_key_thumbprint,
                        }];
                }
            });
        });
    };
    Auth.prototype.getProviderSubType = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request(consts_1.ApiUrls.GET_PROVIDER_TYPE, {
                        method: 'POST',
                        body: {
                            provider_id: 'weda',
                        },
                    })];
            });
        });
    };
    Auth.prototype.verifyCaptchaData = function (_a) {
        var token = _a.token, key = _a.key;
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_b) {
                return [2, this.config.request(consts_1.ApiUrls.VERIFY_CAPTCHA_DATA_URL, {
                        method: 'POST',
                        body: { token: token, key: key },
                        withCredentials: false,
                    })];
            });
        });
    };
    Auth.prototype.createCaptchaData = function (_a) {
        var state = _a.state, _b = _a.redirect_uri, redirect_uri = _b === void 0 ? undefined : _b;
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_c) {
                return [2, this.config.request(consts_1.ApiUrls.CAPTCHA_DATA_URL, {
                        method: 'POST',
                        body: { state: state, redirect_uri: redirect_uri },
                        withCredentials: false,
                    })];
            });
        });
    };
    Auth.prototype.getMiniProgramCode = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request(consts_1.ApiUrls.GET_MINIPROGRAM_QRCODE, {
                        method: 'POST',
                        body: params,
                    })];
            });
        });
    };
    Auth.prototype.getMiniProgramQrCodeStatus = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, this.config.request(consts_1.ApiUrls.GET_MINIPROGRAM_QRCODE_STATUS, {
                        method: 'POST',
                        body: params,
                    })];
            });
        });
    };
    Auth.prototype.getUserBehaviorLog = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var action, url;
            return __generator(this, function (_a) {
                action = { LOGIN: 'query[action]=USER_LOGIN', MODIFY: 'ne_query[action]=USER_LOGIN' };
                url = "".concat(consts_1.ApiUrls.GET_USER_BEHAVIOR_LOG, "?").concat(action[params.type], "&limit=").concat(params.limit).concat(params.page_token ? "&page_token=".concat(params.page_token) : '');
                return [2, this.config.request(url, {
                        method: 'GET',
                        withCredentials: true,
                    })];
            });
        });
    };
    Auth.prototype.modifyPassword = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var publicKey, public_key_thumbprint, encryptUtils, res, error_3, encrypt_password, encrypt_new_password;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        publicKey = '';
                        public_key_thumbprint = '';
                        encryptUtils = getEncryptUtils(true);
                        if (!encryptUtils) {
                            throw new Error('do not support encrypt, a encrypt util required.');
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4, this.getPublicKey()];
                    case 2:
                        res = _a.sent();
                        publicKey = res.public_key;
                        public_key_thumbprint = res.public_key_thumbprint;
                        if (!publicKey || !public_key_thumbprint) {
                            throw res;
                        }
                        return [3, 4];
                    case 3:
                        error_3 = _a.sent();
                        throw error_3;
                    case 4:
                        encrypt_password = params.password ? encryptUtils.getEncryptInfo({ publicKey: publicKey, payload: params.password }) : '';
                        encrypt_new_password = encryptUtils.getEncryptInfo({ publicKey: publicKey, payload: params.new_password });
                        return [2, this.config.request(consts_1.ApiUrls.USER_BASIC_EDIT_URL, {
                                method: 'POST',
                                withCredentials: true,
                                body: {
                                    user_id: params.user_id,
                                    encrypt_password: encrypt_password,
                                    encrypt_new_password: encrypt_new_password,
                                    public_key_thumbprint: public_key_thumbprint,
                                },
                            })];
                }
            });
        });
    };
    Auth.prototype.modifyPasswordWithoutLogin = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var publicKey, public_key_thumbprint, encryptUtils, res, error_4, encrypt_password, encrypt_new_password;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        publicKey = '';
                        public_key_thumbprint = '';
                        encryptUtils = getEncryptUtils(true);
                        if (!encryptUtils) {
                            throw new Error('do not support encrypt, a encrypt util required.');
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4, this.getPublicKey()];
                    case 2:
                        res = _a.sent();
                        publicKey = res.public_key;
                        public_key_thumbprint = res.public_key_thumbprint;
                        if (!publicKey || !public_key_thumbprint) {
                            throw res;
                        }
                        return [3, 4];
                    case 3:
                        error_4 = _a.sent();
                        throw error_4;
                    case 4:
                        encrypt_password = params.password ? encryptUtils.getEncryptInfo({ publicKey: publicKey, payload: params.password }) : '';
                        encrypt_new_password = encryptUtils.getEncryptInfo({ publicKey: publicKey, payload: params.new_password });
                        return [2, this.config.request(consts_1.ApiUrlsV2.AUTH_RESET_PASSWORD, {
                                method: 'POST',
                                body: {
                                    username: params.username,
                                    password: encrypt_password,
                                    new_password: encrypt_new_password,
                                    public_key_thumbprint: public_key_thumbprint,
                                },
                            })];
                }
            });
        });
    };
    return Auth;
}());
exports.Auth = Auth;
//# sourceMappingURL=data:application/json;base64,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