!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("cloudbase_analytics",[],e):"object"==typeof exports?exports.cloudbase_analytics=e():t.cloudbase_analytics=e()}("undefined"!=typeof window?window:this,(()=>(()=>{"use strict";var t={159:(t,e,n)=>{n.r(e),n.d(e,{registerAnalytics:()=>N});var o="@cloudbase/js-sdk";function r(){return o}var i,s={INVALID_PARAMS:"INVALID_PARAMS",INVALID_SYNTAX:"INVALID_SYNTAX",INVALID_OPERATION:"INVALID_OPERATION",OPERATION_FAIL:"OPERATION_FAIL",NETWORK_ERROR:"NETWORK_ERROR",UNKOWN_ERROR:"UNKOWN_ERROR"};!function(t){t.local="local",t.none="none",t.session="session"}(i||(i={}));function a(t,e){console.warn("[".concat(r(),"][").concat(t,"]:").concat(e))}var c,u,l=(c=function(t,e){return c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},c(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),p=function(){return p=Object.assign||function(t){for(var e,n=1,o=arguments.length;n<o;n++)for(var r in e=arguments[n])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},p.apply(this,arguments)},f=function(t,e,n,o){return new(n||(n=Promise))((function(r,i){function s(t){try{c(o.next(t))}catch(t){i(t)}}function a(t){try{c(o.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?r(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(s,a)}c((o=o.apply(t,e||[])).next())}))},d=function(t,e){var n,o,r,i,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(c){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(n=1,o&&(r=2&a[0]?o.return:a[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,a[1])).done)return r;switch(o=0,r&&(a=[2&a[0],r.value]),a[0]){case 0:case 1:r=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,o=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((r=(r=s.trys).length>0&&r[r.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!r||a[1]>r[0]&&a[1]<r[3])){s.label=a[1];break}if(6===a[0]&&s.label<r[1]){s.label=r[1],r=a;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(a);break}r[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],o=0}finally{n=r=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}};!function(t){function e(e){var n=t.call(this)||this,o=e.timeout,r=e.timeoutMsg,i=e.restrictedMethods;return n.timeout=o||0,n.timeoutMsg=r||"请求超时",n.restrictedMethods=i||["get","post","upload","download"],n}l(e,t),e.prototype.get=function(t){return this.request(p(p({},t),{method:"get"}),this.restrictedMethods.includes("get"))},e.prototype.post=function(t){return this.request(p(p({},t),{method:"post"}),this.restrictedMethods.includes("post"))},e.prototype.put=function(t){return this.request(p(p({},t),{method:"put"}))},e.prototype.upload=function(t){var e=t.data,n=t.file,o=t.name,r=t.method,i=t.headers,s=void 0===i?{}:i,a={post:"post",put:"put"}[null==r?void 0:r.toLowerCase()]||"put",c=new FormData;return"post"===a?(Object.keys(e).forEach((function(t){c.append(t,e[t])})),c.append("key",o),c.append("file",n),this.request(p(p({},t),{data:c,method:a}),this.restrictedMethods.includes("upload"))):this.request(p(p({},t),{method:"put",headers:s,body:n}),this.restrictedMethods.includes("upload"))},e.prototype.download=function(t){return f(this,void 0,void 0,(function(){var e,n,o,r;return d(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,this.get(p(p({},t),{headers:{},responseType:"blob"}))];case 1:return e=i.sent().data,n=window.URL.createObjectURL(new Blob([e])),o=decodeURIComponent(new URL(t.url).pathname.split("/").pop()||""),(r=document.createElement("a")).href=n,r.setAttribute("download",o),r.style.display="none",document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(n),document.body.removeChild(r),[3,3];case 2:return i.sent(),[3,3];case 3:return[2,new Promise((function(e){e({statusCode:200,tempFilePath:t.url})}))]}}))}))},e.prototype.fetch=function(t){var e;return f(this,void 0,void 0,(function(){var n,o,r,i,s,a,c,u,l,h,y,b=this;return d(this,(function(v){switch(v.label){case 0:return n=new AbortController,o=t.url,r=t.enableAbort,i=void 0!==r&&r,s=t.stream,a=void 0!==s&&s,c=t.signal,u=t.timeout,l=null!=u?u:this.timeout,c&&(c.aborted&&n.abort(),c.addEventListener("abort",(function(){return n.abort()}))),h=null,i&&l&&(h=setTimeout((function(){console.warn(b.timeoutMsg),n.abort(new Error(b.timeoutMsg))}),l)),[4,fetch(o,p(p({},t),{signal:n.signal})).then((function(t){return f(b,void 0,void 0,(function(){var e,n,o;return d(this,(function(r){switch(r.label){case 0:return clearTimeout(h),t.ok?(e=t,[3,3]):[3,1];case 1:return o=(n=Promise).reject,[4,t.json()];case 2:e=o.apply(n,[r.sent()]),r.label=3;case 3:return[2,e]}}))}))})).catch((function(t){return clearTimeout(h),Promise.reject(t)}))];case 1:return y=v.sent(),[2,{data:a?y.body:(null===(e=y.headers.get("content-type"))||void 0===e?void 0:e.includes("application/json"))?y.json():y.text(),statusCode:y.status,header:y.headers}]}}))}))},e.prototype.request=function(t,e){var n=this;void 0===e&&(e=!1);var o=String(t.method).toLowerCase()||"get";return new Promise((function(r){var i,s,a,c=t.url,u=t.headers,l=void 0===u?{}:u,p=t.data,f=t.responseType,d=t.withCredentials,h=t.body,y=t.onUploadProgress,b=function(t,e,n){void 0===n&&(n={});var o=/\?/.test(e),r="";return Object.keys(n).forEach((function(t){""===r?!o&&(e+="?"):r+="&",r+="".concat(t,"=").concat(encodeURIComponent(n[t]))})),/^http(s)?:\/\//.test(e+=r)?e:"".concat(t).concat(e)}("https:",c,"get"===o?p:{}),v=new XMLHttpRequest;v.open(o,b),f&&(v.responseType=f),Object.keys(l).forEach((function(t){v.setRequestHeader(t,l[t])})),y&&v.upload.addEventListener("progress",y),v.onreadystatechange=function(){var t={};if(4===v.readyState){var e=v.getAllResponseHeaders().trim().split(/[\r\n]+/),n={};e.forEach((function(t){var e=t.split(": "),o=e.shift().toLowerCase(),r=e.join(": ");n[o]=r})),t.header=n,t.statusCode=v.status;try{t.data="blob"===f?v.response:JSON.parse(v.responseText)}catch(e){t.data="blob"===f?v.response:v.responseText}clearTimeout(i),r(t)}},e&&n.timeout&&(i=setTimeout((function(){console.warn(n.timeoutMsg),v.abort()}),n.timeout)),a=p,s="[object FormData]"===Object.prototype.toString.call(a)?p:"application/x-www-form-urlencoded"===l["content-type"]?function(t){void 0===t&&(t={});var e=[];return Object.keys(t).forEach((function(n){e.push("".concat(n,"=").concat(encodeURIComponent(t[n])))})),e.join("&")}(p):h||(p?JSON.stringify(p):void 0),d&&(v.withCredentials=!0),v.send(s)}))}}((function(){})),function(t){t.WEB="web",t.WX_MP="wx_mp"}(u||(u={}));var h=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},t(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function o(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),y=function(t,e,n,o){return new(n||(n=Promise))((function(r,i){function s(t){try{c(o.next(t))}catch(t){i(t)}}function a(t){try{c(o.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?r(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(s,a)}c((o=o.apply(t,e||[])).next())}))},b=function(t,e){var n,o,r,i,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(c){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(n=1,o&&(r=2&a[0]?o.return:a[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,a[1])).done)return r;switch(o=0,r&&(a=[2&a[0],r.value]),a[0]){case 0:case 1:r=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,o=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((r=(r=s.trys).length>0&&r[r.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!r||a[1]>r[0]&&a[1]<r[3])){s.label=a[1];break}if(6===a[0]&&s.label<r[1]){s.label=r[1],r=a;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(a);break}r[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],o=0}finally{n=r=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}},v=function(t){function e(e){var n=t.call(this)||this;return n.root=e,e.tcbCacheObject||(e.tcbCacheObject={}),n}return h(e,t),e.prototype.setItem=function(t,e){this.root.tcbCacheObject[t]=e},e.prototype.getItem=function(t){return this.root.tcbCacheObject[t]},e.prototype.removeItem=function(t){delete this.root.tcbCacheObject[t]},e.prototype.clear=function(){delete this.root.tcbCacheObject},e}((function(){}));!function(){function t(t){this.keys={};var e=t.persistence,n=t.platformInfo,o=void 0===n?{}:n,r=t.keys,i=void 0===r?{}:r;this.platformInfo=o,this.storage||(this.persistenceTag=this.platformInfo.adapter.primaryStorage||e,this.storage=function(t,e){switch(t){case"local":default:return e.localStorage?e.localStorage:(a(s.INVALID_PARAMS,"localStorage is not supported on current platform"),new v(e.root));case"none":return new v(e.root)}}(this.persistenceTag,this.platformInfo.adapter),this.keys=i)}Object.defineProperty(t.prototype,"mode",{get:function(){return this.storage.mode||"sync"},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"persistence",{get:function(){return this.persistenceTag},enumerable:!1,configurable:!0}),t.prototype.setStore=function(t,e,n){if("async"!==this.mode){if(this.storage)try{var o={version:n||"localCachev1",content:e};this.storage.setItem(t,JSON.stringify(o))}catch(t){throw new Error(JSON.stringify({code:s.OPERATION_FAIL,msg:"[".concat(r(),"][").concat(s.OPERATION_FAIL,"]setStore failed"),info:t}))}}else a(s.INVALID_OPERATION,"current platform's storage is asynchronous, please use setStoreAsync insteed")},t.prototype.setStoreAsync=function(t,e,n){return y(this,void 0,void 0,(function(){var o;return b(this,(function(r){switch(r.label){case 0:if(!this.storage)return[2];r.label=1;case 1:return r.trys.push([1,3,,4]),o={version:n||"localCachev1",content:e},[4,this.storage.setItem(t,JSON.stringify(o))];case 2:return r.sent(),[3,4];case 3:return r.sent(),[2];case 4:return[2]}}))}))},t.prototype.getStore=function(t,e){var n;if("async"!==this.mode){try{if("undefined"!=typeof process&&(null===(n=process.env)||void 0===n?void 0:n.tcb_token))return process.env.tcb_token;if(!this.storage)return""}catch(t){return""}e=e||"localCachev1";var o=this.storage.getItem(t);return o&&o.indexOf(e)>=0?JSON.parse(o).content:""}a(s.INVALID_OPERATION,"current platform's storage is asynchronous, please use getStoreAsync insteed")},t.prototype.getStoreAsync=function(t,e){var n;return y(this,void 0,void 0,(function(){var o;return b(this,(function(r){switch(r.label){case 0:try{if("undefined"!=typeof process&&(null===(n=process.env)||void 0===n?void 0:n.tcb_token))return[2,process.env.tcb_token];if(!this.storage)return[2,""]}catch(t){return[2,""]}return e=e||"localCachev1",[4,this.storage.getItem(t)];case 1:return(o=r.sent())&&o.indexOf(e)>=0?[2,JSON.parse(o).content]:[2,""]}}))}))},t.prototype.removeStore=function(t){"async"!==this.mode?this.storage.removeItem(t):a(s.INVALID_OPERATION,"current platform's storage is asynchronous, please use removeStoreAsync insteed")},t.prototype.removeStoreAsync=function(t){return y(this,void 0,void 0,(function(){return b(this,(function(e){switch(e.label){case 0:return[4,this.storage.removeItem(t)];case 1:return e.sent(),[2]}}))}))}}();var m=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},t(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function o(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),g=function(t,e){this.data=e||null,this.name=t},w=function(t){function e(e,n){var o=t.call(this,"error",{error:e,data:n})||this;return o.error=e,o}return m(e,t),e}(g);function O(t){this.message=t}function _(t){this.message=t}new(function(){function t(){this.listeners={}}return t.prototype.on=function(t,e){return function(t,e,n){n[t]=n[t]||[],n[t].push(e)}(t,e,this.listeners),this},t.prototype.off=function(t,e){return function(t,e,n){if(null==n?void 0:n[t]){var o=n[t].indexOf(e);-1!==o&&n[t].splice(o,1)}}(t,e,this.listeners),this},t.prototype.fire=function(t,e){if(t instanceof w)return console.error(t.error),this;var n="string"==typeof t?new g(t,e||{}):t,o=n.name;if(this.listens(o)){n.target=this;for(var r=0,i=this.listeners[o]?function(t,e,n){if(n||2===arguments.length)for(var o,r=0,i=e.length;r<i;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))}([],this.listeners[o],!0):[];r<i.length;r++)i[r].call(this,n)}return this},t.prototype.listens=function(t){return this.listeners[t]&&this.listeners[t].length>0},t}()),"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.indexOf("Firefox"),O.prototype=new Error,O.prototype.name="InvalidCharacterError","undefined"!=typeof window&&window.atob&&window.atob.bind(window),_.prototype=new Error,_.prototype.name="InvalidTokenError";var I=function(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)},A=s,j="analytics",S=["mall"],R=new(function(){function t(){}return t.prototype.analytics=function(t){return e=this,n=void 0,r=function(){var e,n,o;return function(t,e){var n,o,r,i,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(c){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(n=1,o&&(r=2&a[0]?o.return:a[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,a[1])).done)return r;switch(o=0,r&&(a=[2&a[0],r.value]),a[0]){case 0:case 1:r=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,o=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((r=(r=s.trys).length>0&&r[r.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!r||a[1]>r[0]&&a[1]<r[3])){s.label=a[1];break}if(6===a[0]&&s.label<r[1]){s.label=r[1],r=a;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(a);break}r[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],o=0}finally{n=r=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}(this,(function(r){if(!function(t){if("Object"!==Object.prototype.toString.call(t).slice(8,-1))return!1;var e=t.report_data,n=t.report_type;return!1!==S.includes(n)&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!(void 0!==e.action_time&&!Number.isInteger(e.action_time))&&"string"==typeof e.action_type}(t))throw new Error(JSON.stringify({code:A.INVALID_PARAMS,msg:"[".concat(j,".analytics] invalid report data")}));return e=void 0===t.report_data.action_time?Math.floor(Date.now()/1e3):t.report_data.action_time,n={analytics_scene:t.report_type,analytics_data:Object.assign({},t.report_data,{action_time:e})},o={requestData:n},this.request.send("analytics.report",o),[2]}))},new((o=void 0)||(o=Promise))((function(t,i){function s(t){try{c(r.next(t))}catch(t){i(t)}}function a(t){try{c(r.throw(t))}catch(t){i(t)}}function c(e){var n;e.done?t(e.value):(n=e.value,n instanceof o?n:new o((function(t){t(n)}))).then(s,a)}c((r=r.apply(e,n||[])).next())}));var e,n,o,r},function(t,e,n,o){var r,i=arguments.length,s=i<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,n,o);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(i<3?r(s):i>3?r(e,n,s):r(e,n))||s);i>3&&s&&Object.defineProperty(e,n,s)}([(e={customInfo:{className:"Cloudbase",methodName:"analytics"},title:"上报调用失败",messages:["请确认以下各项：","  1 - 调用 analytics() 的语法或参数是否正确","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat("https://support.qq.com/products/148793")]},e.mode,e.customInfo,e.title,e.messages,function(t,e,n){}),I("design:type",Function),I("design:paramtypes",[Object]),I("design:returntype",Promise)],t.prototype,"analytics",null),t;var e}()),P={name:j,entity:{analytics:R.analytics}};try{cloudbase.registerComponent(P)}catch(O){}function N(t){try{t.registerComponent(P)}catch(t){console.warn(t)}}}},e={};function n(o){var r=e[o];if(void 0!==r)return r.exports;var i=e[o]={exports:{}};return t[o](i,i.exports,n),i.exports}n.d=(t,e)=>{for(var o in e)n.o(e,o)&&!n.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var o={};return(()=>{var t=o;Object.defineProperty(t,"__esModule",{value:!0}),t.registerAnalytics=void 0;var e=n(159);t.registerAnalytics=e.registerAnalytics})(),o})()));