!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("cloudbase_cloudrun",[],e):"object"==typeof exports?exports.cloudbase_cloudrun=e():t.cloudbase_cloudrun=e()}("undefined"!=typeof window?window:this,(()=>(()=>{"use strict";var t={878:function(t,e,n){var r=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(e,n);o&&!("get"in o?!e.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,o)}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),o=this&&this.__exportStar||function(t,e){for(var n in t)"default"===n||Object.prototype.hasOwnProperty.call(e,n)||r(e,t,n)};Object.defineProperty(e,"__esModule",{value:!0}),o(n(455),e)},455:(t,e,n)=>{n.r(e),n.d(e,{registerCloudrun:()=>C,requestContainer:()=>N});var r="@cloudbase/js-sdk";function o(){return r}var i,a={INVALID_PARAMS:"INVALID_PARAMS",INVALID_SYNTAX:"INVALID_SYNTAX",INVALID_OPERATION:"INVALID_OPERATION",OPERATION_FAIL:"OPERATION_FAIL",NETWORK_ERROR:"NETWORK_ERROR",UNKOWN_ERROR:"UNKOWN_ERROR"};!function(t){t.local="local",t.none="none",t.session="session"}(i||(i={}));function s(t,e){console.warn("[".concat(o(),"][").concat(t,"]:").concat(e))}var c,u,l=(c=function(t,e){return c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},c(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),f=function(){return f=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},f.apply(this,arguments)},p=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function a(t){try{c(r.next(t))}catch(t){i(t)}}function s(t){try{c(r.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,s)}c((r=r.apply(t,e||[])).next())}))},d=function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],r=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}};!function(t){function e(e){var n=t.call(this)||this,r=e.timeout,o=e.timeoutMsg,i=e.restrictedMethods;return n.timeout=r||0,n.timeoutMsg=o||"请求超时",n.restrictedMethods=i||["get","post","upload","download"],n}l(e,t),e.prototype.get=function(t){return this.request(f(f({},t),{method:"get"}),this.restrictedMethods.includes("get"))},e.prototype.post=function(t){return this.request(f(f({},t),{method:"post"}),this.restrictedMethods.includes("post"))},e.prototype.put=function(t){return this.request(f(f({},t),{method:"put"}))},e.prototype.upload=function(t){var e=t.data,n=t.file,r=t.name,o=t.method,i=t.headers,a=void 0===i?{}:i,s={post:"post",put:"put"}[null==o?void 0:o.toLowerCase()]||"put",c=new FormData;return"post"===s?(Object.keys(e).forEach((function(t){c.append(t,e[t])})),c.append("key",r),c.append("file",n),this.request(f(f({},t),{data:c,method:s}),this.restrictedMethods.includes("upload"))):this.request(f(f({},t),{method:"put",headers:a,body:n}),this.restrictedMethods.includes("upload"))},e.prototype.download=function(t){return p(this,void 0,void 0,(function(){var e,n,r,o;return d(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,this.get(f(f({},t),{headers:{},responseType:"blob"}))];case 1:return e=i.sent().data,n=window.URL.createObjectURL(new Blob([e])),r=decodeURIComponent(new URL(t.url).pathname.split("/").pop()||""),(o=document.createElement("a")).href=n,o.setAttribute("download",r),o.style.display="none",document.body.appendChild(o),o.click(),window.URL.revokeObjectURL(n),document.body.removeChild(o),[3,3];case 2:return i.sent(),[3,3];case 3:return[2,new Promise((function(e){e({statusCode:200,tempFilePath:t.url})}))]}}))}))},e.prototype.fetch=function(t){var e;return p(this,void 0,void 0,(function(){var n,r,o,i,a,s,c,u,l,h,y,v=this;return d(this,(function(b){switch(b.label){case 0:return n=new AbortController,r=t.url,o=t.enableAbort,i=void 0!==o&&o,a=t.stream,s=void 0!==a&&a,c=t.signal,u=t.timeout,l=null!=u?u:this.timeout,c&&(c.aborted&&n.abort(),c.addEventListener("abort",(function(){return n.abort()}))),h=null,i&&l&&(h=setTimeout((function(){console.warn(v.timeoutMsg),n.abort(new Error(v.timeoutMsg))}),l)),[4,fetch(r,f(f({},t),{signal:n.signal})).then((function(t){return p(v,void 0,void 0,(function(){var e,n,r;return d(this,(function(o){switch(o.label){case 0:return clearTimeout(h),t.ok?(e=t,[3,3]):[3,1];case 1:return r=(n=Promise).reject,[4,t.json()];case 2:e=r.apply(n,[o.sent()]),o.label=3;case 3:return[2,e]}}))}))})).catch((function(t){return clearTimeout(h),Promise.reject(t)}))];case 1:return y=b.sent(),[2,{data:s?y.body:(null===(e=y.headers.get("content-type"))||void 0===e?void 0:e.includes("application/json"))?y.json():y.text(),statusCode:y.status,header:y.headers}]}}))}))},e.prototype.request=function(t,e){var n=this;void 0===e&&(e=!1);var r=String(t.method).toLowerCase()||"get";return new Promise((function(o){var i,a,s,c=t.url,u=t.headers,l=void 0===u?{}:u,f=t.data,p=t.responseType,d=t.withCredentials,h=t.body,y=t.onUploadProgress,v=function(t,e,n){void 0===n&&(n={});var r=/\?/.test(e),o="";return Object.keys(n).forEach((function(t){""===o?!r&&(e+="?"):o+="&",o+="".concat(t,"=").concat(encodeURIComponent(n[t]))})),/^http(s)?:\/\//.test(e+=o)?e:"".concat(t).concat(e)}("https:",c,"get"===r?f:{}),b=new XMLHttpRequest;b.open(r,v),p&&(b.responseType=p),Object.keys(l).forEach((function(t){b.setRequestHeader(t,l[t])})),y&&b.upload.addEventListener("progress",y),b.onreadystatechange=function(){var t={};if(4===b.readyState){var e=b.getAllResponseHeaders().trim().split(/[\r\n]+/),n={};e.forEach((function(t){var e=t.split(": "),r=e.shift().toLowerCase(),o=e.join(": ");n[r]=o})),t.header=n,t.statusCode=b.status;try{t.data="blob"===p?b.response:JSON.parse(b.responseText)}catch(e){t.data="blob"===p?b.response:b.responseText}clearTimeout(i),o(t)}},e&&n.timeout&&(i=setTimeout((function(){console.warn(n.timeoutMsg),b.abort()}),n.timeout)),s=f,a="[object FormData]"===Object.prototype.toString.call(s)?f:"application/x-www-form-urlencoded"===l["content-type"]?function(t){void 0===t&&(t={});var e=[];return Object.keys(t).forEach((function(n){e.push("".concat(n,"=").concat(encodeURIComponent(t[n])))})),e.join("&")}(f):h||(f?JSON.stringify(f):void 0),d&&(b.withCredentials=!0),b.send(a)}))}}((function(){})),function(t){t.WEB="web",t.WX_MP="wx_mp"}(u||(u={}));var h=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},t(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),y=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function a(t){try{c(r.next(t))}catch(t){i(t)}}function s(t){try{c(r.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,s)}c((r=r.apply(t,e||[])).next())}))},v=function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],r=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}},b=function(t){function e(e){var n=t.call(this)||this;return n.root=e,e.tcbCacheObject||(e.tcbCacheObject={}),n}return h(e,t),e.prototype.setItem=function(t,e){this.root.tcbCacheObject[t]=e},e.prototype.getItem=function(t){return this.root.tcbCacheObject[t]},e.prototype.removeItem=function(t){delete this.root.tcbCacheObject[t]},e.prototype.clear=function(){delete this.root.tcbCacheObject},e}((function(){}));!function(){function t(t){this.keys={};var e=t.persistence,n=t.platformInfo,r=void 0===n?{}:n,o=t.keys,i=void 0===o?{}:o;this.platformInfo=r,this.storage||(this.persistenceTag=this.platformInfo.adapter.primaryStorage||e,this.storage=function(t,e){switch(t){case"local":default:return e.localStorage?e.localStorage:(s(a.INVALID_PARAMS,"localStorage is not supported on current platform"),new b(e.root));case"none":return new b(e.root)}}(this.persistenceTag,this.platformInfo.adapter),this.keys=i)}Object.defineProperty(t.prototype,"mode",{get:function(){return this.storage.mode||"sync"},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"persistence",{get:function(){return this.persistenceTag},enumerable:!1,configurable:!0}),t.prototype.setStore=function(t,e,n){if("async"!==this.mode){if(this.storage)try{var r={version:n||"localCachev1",content:e};this.storage.setItem(t,JSON.stringify(r))}catch(t){throw new Error(JSON.stringify({code:a.OPERATION_FAIL,msg:"[".concat(o(),"][").concat(a.OPERATION_FAIL,"]setStore failed"),info:t}))}}else s(a.INVALID_OPERATION,"current platform's storage is asynchronous, please use setStoreAsync insteed")},t.prototype.setStoreAsync=function(t,e,n){return y(this,void 0,void 0,(function(){var r;return v(this,(function(o){switch(o.label){case 0:if(!this.storage)return[2];o.label=1;case 1:return o.trys.push([1,3,,4]),r={version:n||"localCachev1",content:e},[4,this.storage.setItem(t,JSON.stringify(r))];case 2:return o.sent(),[3,4];case 3:return o.sent(),[2];case 4:return[2]}}))}))},t.prototype.getStore=function(t,e){var n;if("async"!==this.mode){try{if("undefined"!=typeof process&&(null===(n=process.env)||void 0===n?void 0:n.tcb_token))return process.env.tcb_token;if(!this.storage)return""}catch(t){return""}e=e||"localCachev1";var r=this.storage.getItem(t);return r&&r.indexOf(e)>=0?JSON.parse(r).content:""}s(a.INVALID_OPERATION,"current platform's storage is asynchronous, please use getStoreAsync insteed")},t.prototype.getStoreAsync=function(t,e){var n;return y(this,void 0,void 0,(function(){var r;return v(this,(function(o){switch(o.label){case 0:try{if("undefined"!=typeof process&&(null===(n=process.env)||void 0===n?void 0:n.tcb_token))return[2,process.env.tcb_token];if(!this.storage)return[2,""]}catch(t){return[2,""]}return e=e||"localCachev1",[4,this.storage.getItem(t)];case 1:return(r=o.sent())&&r.indexOf(e)>=0?[2,JSON.parse(r).content]:[2,""]}}))}))},t.prototype.removeStore=function(t){"async"!==this.mode?this.storage.removeItem(t):s(a.INVALID_OPERATION,"current platform's storage is asynchronous, please use removeStoreAsync insteed")},t.prototype.removeStoreAsync=function(t){return y(this,void 0,void 0,(function(){return v(this,(function(e){switch(e.label){case 0:return[4,this.storage.removeItem(t)];case 1:return e.sent(),[2]}}))}))}}();var w=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},t(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),g=function(t,e){this.data=e||null,this.name=t},m=function(t){function e(e,n){var r=t.call(this,"error",{error:e,data:n})||this;return r.error=e,r}return w(e,t),e}(g);function O(t){this.message=t}function _(t){this.message=t}new(function(){function t(){this.listeners={}}return t.prototype.on=function(t,e){return function(t,e,n){n[t]=n[t]||[],n[t].push(e)}(t,e,this.listeners),this},t.prototype.off=function(t,e){return function(t,e,n){if(null==n?void 0:n[t]){var r=n[t].indexOf(e);-1!==r&&n[t].splice(r,1)}}(t,e,this.listeners),this},t.prototype.fire=function(t,e){if(t instanceof m)return console.error(t.error),this;var n="string"==typeof t?new g(t,e||{}):t,r=n.name;if(this.listens(r)){n.target=this;for(var o=0,i=this.listeners[r]?function(t,e,n){if(n||2===arguments.length)for(var r,o=0,i=e.length;o<i;o++)!r&&o in e||(r||(r=Array.prototype.slice.call(e,0,o)),r[o]=e[o]);return t.concat(r||Array.prototype.slice.call(e))}([],this.listeners[r],!0):[];o<i.length;o++)i[o].call(this,n)}return this},t.prototype.listens=function(t){return this.listeners[t]&&this.listeners[t].length>0},t}()),"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.indexOf("Firefox"),O.prototype=new Error,O.prototype.name="InvalidCharacterError","undefined"!=typeof window&&window.atob&&window.atob.bind(window),_.prototype=new Error,_.prototype.name="InvalidTokenError";var I=function(){return I=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},I.apply(this,arguments)},A=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function a(t){try{c(r.next(t))}catch(t){i(t)}}function s(t){try{c(r.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,s)}c((r=r.apply(t,e||[])).next())}))},j=function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],r=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}},S=a,P="cloudrun";function N(t){return A(this,void 0,void 0,(function(){var e,n,r,o,i,a,s,c,u;return j(this,(function(l){switch(l.label){case 0:return e=t.name,n=t.data,r=t.path,o=void 0===r?"":r,i=t.method,a=t.header,s=void 0===a?{}:a,c="https://".concat(this.config.env,".api.tcloudbasegateway.com/v1/cloudrun/").concat(e),u=o.startsWith("/")?o:"/".concat(o),[4,this.request.fetch({method:i||"POST",headers:Object.assign({},{"Content-Type":"application/json; charset=utf-8"},s),body:n,url:"".concat(c).concat(u)})];case 1:return[4,l.sent().data];case 2:return[2,l.sent()]}}))}))}var R=new(function(){function t(){}return t.prototype.callContainer=function(t){return A(this,void 0,void 0,(function(){var e,n,r;return j(this,(function(o){switch(o.label){case 0:if(e=t.name,n=t.data,!e)throw new Error(JSON.stringify({code:S.INVALID_PARAMS,msg:"[".concat(P,".callContainer] invalid name")}));try{r=n?JSON.stringify(n):""}catch(t){throw new Error(JSON.stringify({code:S.INVALID_PARAMS,msg:"[".concat(P,".callContainer] invalid data")}))}return[4,N.call(this,I(I({},t),{data:r}))];case 1:return[2,o.sent()]}}))}))},t}()),x={name:P,entity:{callContainer:R.callContainer}};try{cloudbase.registerComponent(x)}catch(O){}function C(t){try{t.registerComponent(x)}catch(t){console.warn(t)}}}},e={};function n(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return t[r].call(i.exports,i,i.exports,n),i.exports}return n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n(878)})()));