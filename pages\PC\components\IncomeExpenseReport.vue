<template>
	<view class="income-expense-report">
		<view class="report-header">
			<text class="report-title">收支统计报表</text>
			<view class="report-controls">
				<view class="date-selector">
					<text>{{ currentYear }}年{{ currentMonth }}月</text>
					<text class="selector-icon">📅</text>
				</view>
				<view class="refresh-btn" @click="refreshData">
					<text>刷新</text>
					<text class="refresh-icon">🔄</text>
				</view>
			</view>
		</view>

		<view class="report-summary">
			<view class="summary-card income">
				<view class="card-header">
					<text class="card-title">总收入</text>
					<text class="card-icon">💰</text>
				</view>
				<text class="card-amount">¥{{ formatMoney(summary.totalIncome) }}</text>
				<text class="card-count">{{ summary.inCount }}笔</text>
			</view>

			<view class="summary-card expense">
				<view class="card-header">
					<text class="card-title">总支出</text>
					<text class="card-icon">💸</text>
				</view>
				<text class="card-amount">¥{{ formatMoney(summary.totalExpense) }}</text>
				<text class="card-count">{{ summary.outCount }}笔</text>
			</view>

			<view class="summary-card profit">
				<view class="card-header">
					<text class="card-title">净利润</text>
					<text class="card-icon">📈</text>
				</view>
				<text class="card-amount" :style="{ color: netProfit >= 0 ? '#67C23A' : '#F56C6C' }">
					¥{{ formatMoney(netProfit) }}
				</text>
				<text class="card-count">{{ netProfit >= 0 ? '盈利' : '亏损' }}</text>
			</view>
		</view>

		<view class="report-table">
			<view class="table-header">
				<text class="table-title">收支明细</text>
				<view class="table-controls">
					<view class="filter-btn" @click="showFilter = !showFilter">
						<text>筛选</text>
						<text class="filter-icon">🔍</text>
					</view>
					<view class="export-btn" @click="exportData">
						<text>导出</text>
						<text class="export-icon">📤</text>
					</view>
				</view>
			</view>

			<view v-if="showFilter" class="filter-panel">
				<view class="filter-item">
					<text class="filter-label">类型:</text>
					<picker :value="filterTypeIndex" :range="typeOptions" @change="onTypeChange">
						<view class="picker-text">{{ typeOptions[filterTypeIndex] }}</view>
					</picker>
				</view>
				<view class="filter-item">
					<text class="filter-label">项目:</text>
					<picker :value="filterProjectIndex" :range="projectOptions" @change="onProjectChange">
						<view class="picker-text">{{ projectOptions[filterProjectIndex] }}</view>
					</picker>
				</view>
			</view>

			<view class="table-content">
				<view class="table-row table-header-row">
					<text class="table-cell date">日期</text>
					<text class="table-cell title">标题</text>
					<text class="table-cell type">类型</text>
					<text class="table-cell project">项目</text>
					<text class="table-cell amount">金额</text>
					<text class="table-cell status">状态</text>
				</view>

				<view v-if="loading" class="loading-row">
					<text>加载中...</text>
				</view>

				<view v-else-if="filteredTransactions.length === 0" class="empty-row">
					<text>暂无数据</text>
				</view>

				<view v-else v-for="(item, index) in filteredTransactions" :key="index" class="table-row">
					<text class="table-cell date">{{ formatDate(item.date) }}</text>
					<text class="table-cell title">{{ item.title || '-' }}</text>
					<text class="table-cell type" :class="item.type">
						{{ item.type === 'in' ? '收入' : '支出' }}
					</text>
					<text class="table-cell project">{{ item.projectName || '-' }}</text>
					<text class="table-cell amount" :class="item.type">
						{{ item.type === 'in' ? '+' : '-' }}¥{{ formatMoney(item.amount) }}
					</text>
					<text class="table-cell status">{{ getStatusText(item.status) }}</text>
				</view>
			</view>

			<view v-if="filteredTransactions.length > 0" class="table-pagination">
				<text class="pagination-info">
					共 {{ filteredTransactions.length }} 条记录
				</text>
			</view>
		</view>
	</view>
</template>

<script>
	import pcFinanceService from '@/utils/pcFinanceService.js'
	import { formatMoney, formatDate, getStatusText, getCurrentFinanceDate } from '@/utils/pcUtils.js'

	export default {
		name: 'IncomeExpenseReport',
		data() {
			return {
				loading: false,
				showFilter: false,
				currentYear: new Date().getFullYear(),
				currentMonth: new Date().getMonth() + 1,
				
				// 数据
				transactions: [],
				summary: {
					totalIncome: 0,
					totalExpense: 0,
					inCount: 0,
					outCount: 0
				},
				
				// 筛选
				filterTypeIndex: 0,
				filterProjectIndex: 0,
				typeOptions: ['全部', '收入', '支出'],
				projectOptions: ['全部项目'],
				
				// 项目数据
				projects: []
			}
		},
		
		computed: {
			netProfit() {
				return this.summary.totalIncome - this.summary.totalExpense;
			},
			
			filteredTransactions() {
				let filtered = [...this.transactions];
				
				// 按类型筛选
				if (this.filterTypeIndex === 1) {
					filtered = filtered.filter(item => item.type === 'in');
				} else if (this.filterTypeIndex === 2) {
					filtered = filtered.filter(item => item.type === 'out');
				}
				
				// 按项目筛选
				if (this.filterProjectIndex > 0) {
					const selectedProject = this.projects[this.filterProjectIndex - 1];
					if (selectedProject) {
						filtered = filtered.filter(item => item.projectId === selectedProject._id);
					}
				}
				
				return filtered;
			}
		},
		
		mounted() {
			this.loadData();
		},
		
		methods: {
			async loadData() {
				this.loading = true;
				
				try {
					console.log('PC端收支报表 - 开始加载数据');
					
					// 获取收支统计数据
					const data = await pcFinanceService.getIncomeExpenseData(
						this.currentYear,
						this.currentMonth,
						null,
						false
					);
					
					console.log('PC端收支报表 - 获取到数据:', data);
					
					if (data) {
						this.transactions = data.transactions || [];
						this.summary = data.summary || {
							totalIncome: 0,
							totalExpense: 0,
							inCount: 0,
							outCount: 0
						};
						
						// 提取项目列表
						this.extractProjects();
					}
				} catch (error) {
					console.error('PC端收支报表 - 加载数据失败:', error);
					uni.showToast({
						title: '数据加载失败',
						icon: 'none'
					});
				} finally {
					this.loading = false;
				}
			},
			
			async refreshData() {
				try {
					// 强制刷新数据
					const data = await pcFinanceService.getIncomeExpenseData(
						this.currentYear,
						this.currentMonth,
						null,
						true
					);
					
					if (data) {
						this.transactions = data.transactions || [];
						this.summary = data.summary || {
							totalIncome: 0,
							totalExpense: 0,
							inCount: 0,
							outCount: 0
						};
						
						this.extractProjects();
					}
					
					uni.showToast({
						title: '数据刷新成功',
						icon: 'success'
					});
				} catch (error) {
					console.error('PC端收支报表 - 刷新数据失败:', error);
					uni.showToast({
						title: '刷新失败',
						icon: 'none'
					});
				}
			},
			
			extractProjects() {
				const projectMap = new Map();
				
				this.transactions.forEach(item => {
					if (item.projectId && item.projectName) {
						projectMap.set(item.projectId, {
							_id: item.projectId,
							name: item.projectName
						});
					}
				});
				
				this.projects = Array.from(projectMap.values());
				this.projectOptions = ['全部项目', ...this.projects.map(p => p.name)];
			},
			
			onTypeChange(e) {
				this.filterTypeIndex = e.detail.value;
			},
			
			onProjectChange(e) {
				this.filterProjectIndex = e.detail.value;
			},
			
			exportData() {
				// 导出功能实现
				uni.showToast({
					title: '导出功能开发中',
					icon: 'none'
				});
			},
			
			formatMoney(amount) {
				return formatMoney(amount);
			},
			
			formatDate(date) {
				return formatDate(date);
			},
			
			getStatusText(status) {
				return getStatusText(status);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.income-expense-report {
		padding: 20px;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.report-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20px;
		
		.report-title {
			font-size: 24px;
			font-weight: bold;
			color: #333;
		}
		
		.report-controls {
			display: flex;
			gap: 15px;
		}
		
		.date-selector, .refresh-btn {
			display: flex;
			align-items: center;
			gap: 8px;
			padding: 8px 16px;
			background-color: #fff;
			border-radius: 6px;
			border: 1px solid #ddd;
			cursor: pointer;

			&:hover {
				background-color: #f8f9fa;
			}
		}
	}

	.report-summary {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 20px;
		margin-bottom: 30px;

		.summary-card {
			background-color: #fff;
			padding: 25px;
			border-radius: 12px;
			box-shadow: 0 2px 8px rgba(0,0,0,0.1);

			.card-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 15px;

				.card-title {
					font-size: 14px;
					color: #666;
					font-weight: 500;
				}

				.card-icon {
					font-size: 20px;
				}
			}

			.card-amount {
				display: block;
				font-size: 28px;
				font-weight: bold;
				color: #333;
				margin-bottom: 5px;
			}

			.card-count {
				font-size: 12px;
				color: #999;
			}

			&.income {
				border-left: 4px solid #67C23A;
			}

			&.expense {
				border-left: 4px solid #F56C6C;
			}

			&.profit {
				border-left: 4px solid #409EFF;
			}
		}
	}

	.report-table {
		background-color: #fff;
		border-radius: 12px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
		overflow: hidden;

		.table-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20px 25px;
			border-bottom: 1px solid #eee;

			.table-title {
				font-size: 18px;
				font-weight: 600;
				color: #333;
			}

			.table-controls {
				display: flex;
				gap: 10px;
			}

			.filter-btn, .export-btn {
				display: flex;
				align-items: center;
				gap: 5px;
				padding: 6px 12px;
				background-color: #f8f9fa;
				border-radius: 6px;
				cursor: pointer;
				font-size: 12px;

				&:hover {
					background-color: #e9ecef;
				}
			}
		}

		.filter-panel {
			display: flex;
			gap: 20px;
			padding: 15px 25px;
			background-color: #f8f9fa;
			border-bottom: 1px solid #eee;

			.filter-item {
				display: flex;
				align-items: center;
				gap: 8px;

				.filter-label {
					font-size: 12px;
					color: #666;
				}

				.picker-text {
					padding: 4px 8px;
					background-color: #fff;
					border-radius: 4px;
					border: 1px solid #ddd;
					font-size: 12px;
				}
			}
		}

		.table-content {
			.table-row {
				display: grid;
				grid-template-columns: 100px 1fr 80px 120px 120px 80px;
				gap: 15px;
				padding: 15px 25px;
				border-bottom: 1px solid #f0f0f0;

				&.table-header-row {
					background-color: #f8f9fa;
					font-weight: 600;
					color: #333;
				}

				&:hover:not(.table-header-row) {
					background-color: #f8f9fa;
				}

				.table-cell {
					display: flex;
					align-items: center;
					font-size: 13px;

					&.date {
						color: #666;
					}

					&.title {
						font-weight: 500;
					}

					&.type {
						font-weight: 500;

						&.in {
							color: #67C23A;
						}

						&.out {
							color: #F56C6C;
						}
					}

					&.amount {
						font-weight: 600;

						&.in {
							color: #67C23A;
						}

						&.out {
							color: #F56C6C;
						}
					}

					&.status {
						color: #666;
					}
				}
			}

			.loading-row, .empty-row {
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 40px;
				color: #999;
			}
		}

		.table-pagination {
			padding: 15px 25px;
			border-top: 1px solid #eee;
			background-color: #f8f9fa;

			.pagination-info {
				font-size: 12px;
				color: #666;
			}
		}
	}
</style>
