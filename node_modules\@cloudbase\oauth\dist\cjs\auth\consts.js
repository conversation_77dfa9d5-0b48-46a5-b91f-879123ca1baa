"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorType = exports.VerificationUsages = exports.ApiUrlsV2 = exports.ApiUrls = void 0;
var ApiUrls;
(function (ApiUrls) {
    ApiUrls["AUTH_SIGN_UP_URL"] = "/auth/v1/signup";
    ApiUrls["AUTH_TOKEN_URL"] = "/auth/v1/token";
    ApiUrls["AUTH_REVOKE_URL"] = "/auth/v1/revoke";
    ApiUrls["WEDA_USER_URL"] = "/auth/v1/plugin/weda/userinfo";
    ApiUrls["AUTH_RESET_PASSWORD"] = "/auth/v1/reset";
    ApiUrls["AUTH_GET_DEVICE_CODE"] = "/auth/v1/device/code";
    ApiUrls["CHECK_USERNAME"] = "/auth/v1/checkUsername";
    ApiUrls["CHECK_IF_USER_EXIST"] = "/auth/v1/checkIfUserExist";
    ApiUrls["GET_PROVIDER_TYPE"] = "/auth/v1/mgr/provider/providerSubType";
    ApiUrls["AUTH_SIGN_IN_URL"] = "/auth/v1/signin";
    ApiUrls["AUTH_SIGN_IN_ANONYMOUSLY_URL"] = "/auth/v1/signin/anonymously";
    ApiUrls["AUTH_SIGN_IN_WITH_PROVIDER_URL"] = "/auth/v1/signin/with/provider";
    ApiUrls["AUTH_SIGN_IN_WITH_WECHAT_URL"] = "/auth/v1/signin/wechat/noauth";
    ApiUrls["AUTH_SIGN_IN_CUSTOM"] = "/auth/v1/signin/custom";
    ApiUrls["PROVIDER_TOKEN_URL"] = "/auth/v1/provider/token";
    ApiUrls["PROVIDER_URI_URL"] = "/auth/v1/provider/uri";
    ApiUrls["USER_ME_URL"] = "/auth/v1/user/me";
    ApiUrls["AUTH_SIGNOUT_URL"] = "/auth/v1/user/signout";
    ApiUrls["USER_QUERY_URL"] = "/auth/v1/user/query";
    ApiUrls["USER_PRIFILE_URL"] = "/auth/v1/user/profile";
    ApiUrls["USER_BASIC_EDIT_URL"] = "/auth/v1/user/basic/edit";
    ApiUrls["USER_TRANS_BY_PROVIDER_URL"] = "/auth/v1/user/trans/by/provider";
    ApiUrls["PROVIDER_LIST"] = "/auth/v1/user/provider";
    ApiUrls["PROVIDER_BIND_URL"] = "/auth/v1/user/provider/bind";
    ApiUrls["PROVIDER_UNBIND_URL"] = "/auth/v1/user/provider";
    ApiUrls["CHECK_PWD_URL"] = "/auth/v1/user/sudo";
    ApiUrls["SUDO_URL"] = "/auth/v1/user/sudo";
    ApiUrls["BIND_CONTACT_URL"] = "/auth/v1/user/contact";
    ApiUrls["AUTH_SET_PASSWORD"] = "/auth/v1/user/password";
    ApiUrls["AUTHORIZE_DEVICE_URL"] = "/auth/v1/user/device/authorize";
    ApiUrls["AUTHORIZE_URL"] = "/auth/v1/user/authorize";
    ApiUrls["AUTHORIZE_INFO_URL"] = "/auth/v1/user/authorize/info";
    ApiUrls["AUTHORIZED_DEVICES_DELETE_URL"] = "/auth/v1/user/authorized/devices/";
    ApiUrls["AUTH_REVOKE_ALL_URL"] = "/auth/v1/user/revoke/all";
    ApiUrls["GET_USER_BEHAVIOR_LOG"] = "/auth/v1/user/security/history";
    ApiUrls["VERIFICATION_URL"] = "/auth/v1/verification";
    ApiUrls["VERIFY_URL"] = "/auth/v1/verification/verify";
    ApiUrls["CAPTCHA_DATA_URL"] = "/auth/v1/captcha/data";
    ApiUrls["VERIFY_CAPTCHA_DATA_URL"] = "/auth/v1/captcha/data/verify";
    ApiUrls["GET_CAPTCHA_URL"] = "/auth/v1/captcha/init";
    ApiUrls["GET_MINIPROGRAM_QRCODE"] = "/auth/v1/qrcode/generate";
    ApiUrls["GET_MINIPROGRAM_QRCODE_STATUS"] = "/auth/v1/qrcode/get/status";
})(ApiUrls = exports.ApiUrls || (exports.ApiUrls = {}));
var ApiUrlsV2;
(function (ApiUrlsV2) {
    ApiUrlsV2["AUTH_SIGN_IN_URL"] = "/auth/v2/signin/username";
    ApiUrlsV2["AUTH_TOKEN_URL"] = "/auth/v2/token";
    ApiUrlsV2["USER_ME_URL"] = "/auth/v2/user/me";
    ApiUrlsV2["VERIFY_URL"] = "/auth/v2/signin/verificationcode";
    ApiUrlsV2["AUTH_SIGN_IN_WITH_PROVIDER_URL"] = "/auth/v2/signin/with/provider";
    ApiUrlsV2["AUTH_PUBLIC_KEY"] = "/auth/v2/signin/publichkey";
    ApiUrlsV2["AUTH_RESET_PASSWORD"] = "/auth/v2/signin/password/update";
})(ApiUrlsV2 = exports.ApiUrlsV2 || (exports.ApiUrlsV2 = {}));
var VerificationUsages;
(function (VerificationUsages) {
    VerificationUsages["REGISTER"] = "REGISTER";
    VerificationUsages["SIGN_IN"] = "SIGN_IN";
    VerificationUsages["PASSWORD_RESET"] = "PASSWORD_RESET";
    VerificationUsages["EMAIL_ADDRESS_CHANGE"] = "EMAIL_ADDRESS_CHANGE";
    VerificationUsages["PHONE_NUMBER_CHANGE"] = "PHONE_NUMBER_CHANGE";
})(VerificationUsages = exports.VerificationUsages || (exports.VerificationUsages = {}));
var ErrorType;
(function (ErrorType) {
    ErrorType["UNREACHABLE"] = "unreachable";
    ErrorType["LOCAL"] = "local";
    ErrorType["CANCELLED"] = "cancelled";
    ErrorType["UNKNOWN"] = "unknown";
    ErrorType["UNAUTHENTICATED"] = "unauthenticated";
    ErrorType["RESOURCE_EXHAUSTED"] = "resource_exhausted";
    ErrorType["FAILED_PRECONDITION"] = "failed_precondition";
    ErrorType["INVALID_ARGUMENT"] = "invalid_argument";
    ErrorType["DEADLINE_EXCEEDED"] = "deadline_exceeded";
    ErrorType["NOT_FOUND"] = "not_found";
    ErrorType["ALREADY_EXISTS"] = "already_exists";
    ErrorType["PERMISSION_DENIED"] = "permission_denied";
    ErrorType["ABORTED"] = "aborted";
    ErrorType["OUT_OF_RANGE"] = "out_of_range";
    ErrorType["UNIMPLEMENTED"] = "unimplemented";
    ErrorType["INTERNAL"] = "internal";
    ErrorType["UNAVAILABLE"] = "unavailable";
    ErrorType["DATA_LOSS"] = "data_loss";
    ErrorType["INVALID_PASSWORD"] = "invalid_password";
    ErrorType["PASSWORD_NOT_SET"] = "password_not_set";
    ErrorType["INVALID_STATUS"] = "invalid_status";
    ErrorType["USER_PENDING"] = "user_pending";
    ErrorType["USER_BLOCKED"] = "user_blocked";
    ErrorType["INVALID_VERIFICATION_CODE"] = "invalid_verification_code";
    ErrorType["TWO_FACTOR_REQUIRED"] = "two_factor_required";
    ErrorType["INVALID_TWO_FACTOR"] = "invalid_two_factor";
    ErrorType["INVALID_TWO_FACTOR_RECOVERY"] = "invalid_two_factor_recovery";
    ErrorType["UNDER_REVIEW"] = "under_review";
    ErrorType["INVALID_REQUEST"] = "invalid_request";
    ErrorType["UNAUTHORIZED_CLIENT"] = "unauthorized_client";
    ErrorType["ACCESS_DENIED"] = "access_denied";
    ErrorType["UNSUPPORTED_RESPONSE_TYPE"] = "unsupported_response_type";
    ErrorType["INVALID_SCOPE"] = "invalid_scope";
    ErrorType["INVALID_GRANT"] = "invalid_grant";
    ErrorType["SERVER_ERROR"] = "server_error";
    ErrorType["TEMPORARILY_UNAVAILABLE"] = "temporarily_unavailable";
    ErrorType["INTERACTION_REQUIRED"] = "interaction_required";
    ErrorType["LOGIN_REQUIRED"] = "login_required";
    ErrorType["ACCOUNT_SELECTION_REQUIRED"] = "account_selection_required";
    ErrorType["CONSENT_REQUIRED"] = "consent_required";
    ErrorType["INVALID_REQUEST_URI"] = "invalid_request_uri";
    ErrorType["INVALID_REQUEST_OBJECT"] = "invalid_request_object";
    ErrorType["REQUEST_NOT_SUPPORTED"] = "request_not_supported";
    ErrorType["REQUEST_URI_NOT_SUPPORTED"] = "request_uri_not_supported";
    ErrorType["REGISTRATION_NOT_SUPPORTED"] = "registration_not_supported";
    ErrorType["CAPTCHA_REQUIRED"] = "captcha_required";
    ErrorType["CAPTCHA_INVALID"] = "captcha_invalid";
})(ErrorType = exports.ErrorType || (exports.ErrorType = {}));
//# sourceMappingURL=data:application/json;base64,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