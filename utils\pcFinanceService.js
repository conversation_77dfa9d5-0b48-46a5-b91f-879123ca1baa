/**
 * PC端财务数据服务
 * 基于app端的financeDataService，适配PC端使用
 */

class PCFinanceService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = new Map();
    this.CACHE_DURATION = 3 * 60 * 1000; // 3分钟缓存
    this.pendingRequests = new Map(); // 防止重复请求
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(params) {
    const { year, month, type, projectId, accountId } = params;
    return `pc_finance_${type}_${year}_${month}_${projectId || 'all'}_${accountId || 'all'}`;
  }

  /**
   * 检查缓存是否有效
   */
  isValidCache(cacheKey) {
    if (!this.cache.has(cacheKey)) return false;
    
    const expiry = this.cacheExpiry.get(cacheKey);
    if (!expiry || Date.now() > expiry) {
      this.cache.delete(cacheKey);
      this.cacheExpiry.delete(cacheKey);
      return false;
    }
    
    return true;
  }

  /**
   * 设置缓存
   */
  setCache(cacheKey, data, duration = this.CACHE_DURATION) {
    this.cache.set(cacheKey, data);
    this.cacheExpiry.set(cacheKey, Date.now() + duration);
  }

  /**
   * 统一的数据获取入口
   */
  async getFinanceData(params) {
    const { forceRefresh = false } = params;
    const cacheKey = this.generateCacheKey(params);
    
    // 检查是否有进行中的相同请求
    if (this.pendingRequests.has(cacheKey)) {
      return await this.pendingRequests.get(cacheKey);
    }
    
    // 检查缓存
    if (!forceRefresh && this.isValidCache(cacheKey)) {
      console.log('PC财务数据服务 - 使用缓存:', cacheKey);
      return this.cache.get(cacheKey);
    }

    // 创建请求Promise并缓存
    const requestPromise = this.fetchDataWithOptimization(params);
    this.pendingRequests.set(cacheKey, requestPromise);
    
    try {
      const data = await requestPromise;
      
      // 缓存结果
      this.setCache(cacheKey, data);
      
      return data;
    } finally {
      // 清理进行中的请求
      this.pendingRequests.delete(cacheKey);
    }
  }

  /**
   * 批量获取仪表板数据
   */
  async getDashboardData(year, month, forceRefresh = false) {
    return await this.getFinanceData({
      type: 'dashboard',
      year,
      month,
      forceRefresh
    });
  }

  /**
   * 获取资金流水数据
   */
  async getCashFlowData(year, month, withDetails = false, forceRefresh = false) {
    return await this.getFinanceData({
      type: 'cashFlow',
      year,
      month,
      withDetails,
      forceRefresh
    });
  }

  /**
   * 获取收支统计数据
   */
  async getIncomeExpenseData(year, month, projectId = null, forceRefresh = false) {
    return await this.getFinanceData({
      type: 'incomeExpense',
      year,
      month,
      projectId,
      forceRefresh
    });
  }

  /**
   * 优化的数据获取实现
   */
  async fetchDataWithOptimization(params) {
    const { type, year, month } = params;
    
    console.log(`PC财务数据服务 - 获取${type}数据:`, params);
    
    try {
      switch (type) {
        case 'dashboard':
          return await this.fetchDashboardDataOptimized(year, month);
          
        case 'cashFlow':
          return await this.fetchCashFlowDataOptimized(params);
          
        case 'incomeExpense':
          return await this.fetchIncomeExpenseDataOptimized(params);
          
        default:
          throw new Error(`不支持的数据类型: ${type}`);
      }
    } catch (error) {
      console.error(`PC财务数据服务 - 获取${type}数据失败:`, error);
      throw error;
    }
  }

  /**
   * 优化的仪表板数据获取
   */
  async fetchDashboardDataOptimized(year, month) {
    console.log('PC财务数据服务 - 获取仪表板数据 (优化版)');
    
    try {
      // 使用批量获取云函数
      const result = await uniCloud.callFunction({
        name: 'getFinanceBatch',
        data: {
          year,
          month,
          modules: ['cashFlow', 'incomeExpense'],
          fields: {
            cashFlow: ['cashIn', 'cashOut', 'cashInGrowth', 'cashOutGrowth'],
            incomeExpense: ['income', 'expense', 'incomeGrowth', 'expenseGrowth']
          }
        }
      });

      console.log('PC财务数据服务 - 批量云函数返回结果:', result);

      if (result?.result?.code === 0) {
        console.log('PC财务数据服务 - 批量云函数调用成功，数据:', result.result.data);
        return result.result.data;
      } else {
        console.log('PC财务数据服务 - 批量云函数返回错误，使用降级方案:', result);
        // 降级到原始方法
        return await this.fetchDashboardDataFallback(year, month);
      }
    } catch (error) {
      console.error('PC财务数据服务 - 批量云函数调用失败，使用降级方案:', error);
      return await this.fetchDashboardDataFallback(year, month);
    }
  }

  /**
   * 降级方案：仪表板数据获取
   */
  async fetchDashboardDataFallback(year, month) {
    console.log('PC财务数据服务 - 使用降级方案获取仪表板数据');
    
    try {
      // 并行获取资金流水和收支数据
      const [cashFlowRes, incomeExpenseRes] = await Promise.all([
        uniCloud.callFunction({
          name: 'getFinancialStats',
          data: {
            year, month, type: 'cashFlow'
          }
        }),
        uniCloud.callFunction({
          name: 'getFinancialStats',
          data: {
            year, month, type: 'incomeExpense'
          }
        })
      ]);

      const cashFlowData = cashFlowRes?.result?.data || {};
      const incomeExpenseData = incomeExpenseRes?.result?.data || {};

      return {
        cashFlow: {
          cashIn: cashFlowData.cashIn || 0,
          cashOut: cashFlowData.cashOut || 0,
          cashInGrowth: cashFlowData.cashInGrowth || 0,
          cashOutGrowth: cashFlowData.cashOutGrowth || 0
        },
        incomeExpense: {
          income: incomeExpenseData.income || 0,
          expense: incomeExpenseData.expense || 0,
          incomeGrowth: incomeExpenseData.incomeGrowth || 0,
          expenseGrowth: incomeExpenseData.expenseGrowth || 0
        }
      };
    } catch (error) {
      console.error('PC财务数据服务 - 降级方案也失败:', error);
      return this.getEmptyDashboardData();
    }
  }

  /**
   * 获取空的仪表板数据
   */
  getEmptyDashboardData() {
    return {
      cashFlow: {
        cashIn: 0,
        cashOut: 0,
        cashInGrowth: 0,
        cashOutGrowth: 0
      },
      incomeExpense: {
        income: 0,
        expense: 0,
        incomeGrowth: 0,
        expenseGrowth: 0
      }
    };
  }

  /**
   * 优化的资金流水数据获取
   */
  async fetchCashFlowDataOptimized(params) {
    const { year, month, withDetails = false } = params;
    
    console.log('PC财务数据服务 - 获取资金流水数据 (优化版)');
    
    try {
      const result = await uniCloud.callFunction({
        name: 'getFinanceBatch',
        data: {
          year,
          month,
          modules: ['accounts', 'defaultAccount'],
          withDetails,
          fields: {
            accounts: ['_id', 'accountType', 'alias', 'bankName', 'branchName', 'createTime'],
            accountStats: ['balance', 'initialBalance', 'cashIn', 'cashOut', 'inCount', 'outCount'],
            defaultAccount: ['cashOut', 'outCount']
          }
        }
      });

      if (result?.result?.code === 0) {
        return result.result.data;
      } else {
        return await this.fetchCashFlowDataFallback(params);
      }
    } catch (error) {
      console.error('PC财务数据服务 - 资金流水数据获取失败:', error);
      return await this.fetchCashFlowDataFallback(params);
    }
  }

  /**
   * 资金流水数据降级方案
   */
  async fetchCashFlowDataFallback(params) {
    // 实现降级逻辑
    return {
      accounts: [],
      accountBalances: {},
      cashIn: 0,
      cashOut: 0,
      balance: 0
    };
  }

  /**
   * 优化的收支统计数据获取
   */
  async fetchIncomeExpenseDataOptimized(params) {
    const { year, month, projectId } = params;
    
    console.log('PC财务数据服务 - 获取收支统计数据 (优化版)');
    
    try {
      const result = await uniCloud.callFunction({
        name: 'getFinanceBatch',
        data: {
          year,
          month,
          projectId,
          modules: ['transactions', 'summary'],
          fields: {
            transactions: [
              '_id', 'title', 'amount', 'date', 'type', 'category',
              'projectId', 'projectName', 'submitter', 'client', 'supplier',
              'status', 'approveTime', 'bookingTime'
            ],
            summary: ['totalIncome', 'totalExpense', 'inCount', 'outCount']
          }
        }
      });

      if (result?.result?.code === 0) {
        return result.result.data;
      } else {
        return await this.fetchIncomeExpenseDataFallback(params);
      }
    } catch (error) {
      console.error('PC财务数据服务 - 收支统计数据获取失败:', error);
      return await this.fetchIncomeExpenseDataFallback(params);
    }
  }

  /**
   * 收支统计数据降级方案
   */
  async fetchIncomeExpenseDataFallback(params) {
    // 实现降级逻辑
    return {
      transactions: [],
      summary: {
        totalIncome: 0,
        totalExpense: 0,
        inCount: 0,
        outCount: 0
      }
    };
  }
}

// 创建全局实例
const pcFinanceService = new PCFinanceService();

// 导出服务实例
export default pcFinanceService;
