import cloudbase from '@cloudbase/app';
import { registerAuth } from '@cloudbase/auth';
import { registerFunctions } from '@cloudbase/functions';
import { registerStorage } from '@cloudbase/storage';
import { registerRealtime } from '@cloudbase/realtime';
import { registerAnalytics } from '@cloudbase/analytics';
import { registerModel } from '@cloudbase/model';
import { registerAi } from '@cloudbase/ai';
import { registerCloudrun } from '@cloudbase/cloudrun';
import { registerDatabase } from './../database';
import pkg from '../package.json';
var version = pkg.version;
cloudbase.registerVersion(version);
try {
    registerAuth(cloudbase);
    registerFunctions(cloudbase);
    registerStorage(cloudbase);
    registerDatabase(cloudbase);
    registerRealtime(cloudbase);
    registerAnalytics(cloudbase);
    registerModel(cloudbase);
    registerAi(cloudbase);
    registerCloudrun(cloudbase);
}
catch (e) { }
try {
    window.cloudbase = cloudbase;
}
catch (e) { }
export default cloudbase;

//# sourceMappingURL=data:application/json;charset=utf8;base64,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
