var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
import { generateHTTPClient } from '@cloudbase/wx-cloud-client-sdk';
import { events } from '@cloudbase/utilities';
var CloudbaseEventEmitter = events.CloudbaseEventEmitter;
var COMPONENT_NAME = 'models';
function getEntity(cloudbase) {
    var _this = this;
    var callFunction = cloudbase.callFunction.bind(cloudbase);
    var cloudbaseFetch = cloudbase.request.fetch.bind(cloudbase.request);
    var fetch = function (fetchOptions) { return __awaiter(_this, void 0, void 0, function () {
        var res;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0: return [4, cloudbaseFetch(__assign(__assign({}, fetchOptions), { headers: __assign({ 'Content-Type': 'application/json' }, fetchOptions.headers) }))];
                case 1:
                    res = _a.sent();
                    return [4, res.data];
                case 2: return [2, _a.sent()];
            }
        });
    }); };
    var _a = cloudbase.getEndPointWithKey('GATEWAY'), BASE_URL = _a.BASE_URL, PROTOCOL = _a.PROTOCOL;
    var baseUrl = "".concat(PROTOCOL).concat(BASE_URL, "/model");
    var sqlBaseUrl = "".concat(PROTOCOL).concat(BASE_URL, "/sql");
    var entity = generateHTTPClient(callFunction, fetch, baseUrl, { sqlBaseUrl: sqlBaseUrl });
    return entity;
}
var cloudbaseModelMap = new WeakMap();
export function lazyGetEntity(cloudbase) {
    return new Proxy({}, {
        get: function (_, prop) {
            var entity = cloudbaseModelMap.get(cloudbase);
            if (!entity) {
                entity = getEntity(cloudbase);
                cloudbaseModelMap.set(cloudbase, entity);
            }
            return entity[prop];
        },
    });
}
var CLOUDBASE_INIT_EVENT = 'cloudbase_init';
var bus = new CloudbaseEventEmitter();
bus.on(CLOUDBASE_INIT_EVENT, function (_a) {
    var cloudbase = _a.data;
    Object.assign(cloudbase, { models: lazyGetEntity(cloudbase) });
});
var component = {
    name: COMPONENT_NAME,
    namespace: COMPONENT_NAME,
    entity: new Proxy({}, {
        get: function (_, prop) {
            console.warn("\u3010deprecated\u3011Accessing Cloudbase.prototype.models.".concat(prop, "."));
        },
    }),
    injectEvents: {
        bus: bus,
        events: [CLOUDBASE_INIT_EVENT],
    },
};
try {
    cloudbase.registerComponent(component);
}
catch (e) { }
export function registerModel(app) {
    try {
        app.registerComponent(component);
    }
    catch (e) {
        console.warn(e);
    }
}
export * from '@cloudbase/wx-cloud-client-sdk';
//# sourceMappingURL=data:application/json;base64,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