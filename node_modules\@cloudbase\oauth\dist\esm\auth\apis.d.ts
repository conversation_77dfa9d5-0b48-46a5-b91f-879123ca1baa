import { GetVerificationRequest, GetVerificationResponse, UserProfile, UserInfo, SignInRequest, SignUpRequest, VerifyRequest, VerifyResponse, GenProviderRedirectUriRequest, GenProviderRedirectUriResponse, GrantProviderTokenRequest, GrantProviderTokenResponse, PatchProviderTokenRequest, PatchProviderTokenResponse, SignInWithProviderRequest, SignInCustomRequest, BindWithProviderRequest, TransByProviderRequest, GrantTokenRequest, UnbindProviderRequest, CheckPasswordRequest, SetPasswordRequest, ChangeBoundProviderRequest, ChangeBoundProviderResponse, UpdatePasswordRequest, SudoResponse, SudoRequest, GetCustomSignTicketFn, QueryUserProfileRequest, QueryUserProfileResponse, ResetPasswordRequest, DeviceAuthorizeRequest, DeviceAuthorizeResponse, CheckUsernameRequest, CheckIfUserExistRequest, CheckIfUserExistResponse, WithSudoRequest, PublicKey, EncryptParams, ProviderSubType, GetMiniProgramQrCodeRequest, GetMiniProgramQrCodeStatusRequest, GetMiniProgramQrCodeResponse, GetMiniProgramQrCodeStatusResponse, ModifyUserBasicInfoRequest, EditContactRequest, AuthorizeInfoRequest, AuthorizeInfoResponse, AuthorizeDeviceRequest, AuthorizeRequest, AuthorizeResponse, GetUserBehaviorLog, GetUserBehaviorLogRes, RevokeDeviceRequest, SignoutReponse, ProvidersResponse, SignoutRequest, ModifyPasswordWithoutLoginRequest } from './models';
import { SimpleStorage, RequestFunction } from '../oauth2client/interface';
import { OAuth2Client } from '../oauth2client/oauth2client';
import { Credentials } from '../oauth2client/models';
import { CaptchaOptions } from '../captcha/captcha';
export interface AuthOptions {
    apiOrigin: string;
    clientId: string;
    clientSecret?: string;
    credentialsClient?: OAuth2Client;
    request?: RequestFunction;
    baseRequest?: RequestFunction;
    storage?: SimpleStorage;
    anonymousSignInFunc?: (Credentials: any) => Promise<Credentials | void>;
    captchaOptions?: Partial<CaptchaOptions>;
    env?: string;
    wxCloud?: any;
}
export declare class Auth {
    private static parseParamsToSearch;
    private config;
    private getCustomSignTicketFn?;
    constructor(opts: AuthOptions);
    getParamsByVersion(params: any, key: string): {
        params: any;
        url: any;
    };
    signIn(params: SignInRequest): Promise<Credentials>;
    signInAnonymously(data?: {
        provider_token?: string;
    }, useWxCloud?: boolean): Promise<Credentials>;
    signUp(params: SignUpRequest): Promise<Credentials>;
    signOut(params?: SignoutRequest): Promise<SignoutReponse>;
    revokeAllDevices(): Promise<void>;
    revokeDevice(params: RevokeDeviceRequest): Promise<void>;
    getVerification(params: GetVerificationRequest, options?: {
        withCaptcha: boolean;
    }): Promise<GetVerificationResponse>;
    verify(params: VerifyRequest): Promise<VerifyResponse>;
    genProviderRedirectUri(params: GenProviderRedirectUriRequest): Promise<GenProviderRedirectUriResponse>;
    grantProviderToken(params: GrantProviderTokenRequest, useWxCloud?: boolean): Promise<GrantProviderTokenResponse>;
    patchProviderToken(params: PatchProviderTokenRequest): Promise<PatchProviderTokenResponse>;
    signInWithProvider(params: SignInWithProviderRequest, useWxCloud?: boolean): Promise<Credentials>;
    signInCustom(params: SignInCustomRequest): Promise<Credentials>;
    signInWithWechat(params?: any): Promise<Credentials>;
    bindWithProvider(params: BindWithProviderRequest): Promise<void>;
    getUserProfile(params: {
        version?: string;
    }): Promise<UserProfile>;
    getUserInfo(params?: {
        version?: string;
        query?: string;
    }): Promise<UserInfo>;
    getWedaUserInfo(): Promise<any>;
    deleteMe(params: WithSudoRequest): Promise<UserProfile>;
    hasLoginState(): Promise<boolean>;
    hasLoginStateSync(): Credentials | null;
    getLoginState(): Promise<Credentials | null>;
    transByProvider(params: TransByProviderRequest): Promise<Credentials>;
    grantToken(params: GrantTokenRequest): Promise<Credentials>;
    getProviders(): Promise<ProvidersResponse>;
    unbindProvider(params: UnbindProviderRequest): Promise<void>;
    checkPassword(params: CheckPasswordRequest): Promise<void>;
    editContact(params: EditContactRequest): Promise<void>;
    setPassword(params: SetPasswordRequest): Promise<void>;
    updatePasswordByOld(params: UpdatePasswordRequest): Promise<void>;
    sudo(params: SudoRequest): Promise<SudoResponse>;
    sendVerificationCodeToCurrentUser(params: GetVerificationRequest): Promise<GetVerificationResponse>;
    changeBoundProvider(params: ChangeBoundProviderRequest): Promise<ChangeBoundProviderResponse>;
    setUserProfile(params: UserProfile): Promise<UserProfile>;
    updateUserBasicInfo(params: ModifyUserBasicInfoRequest): Promise<void>;
    queryUserProfile(params: QueryUserProfileRequest): Promise<QueryUserProfileResponse>;
    setCustomSignFunc(getTickFn: GetCustomSignTicketFn): void;
    signInWithCustomTicket(): Promise<Credentials>;
    resetPassword(params: ResetPasswordRequest): Promise<void>;
    authorize(params: AuthorizeRequest): Promise<AuthorizeResponse>;
    authorizeDevice(params: AuthorizeDeviceRequest): Promise<void>;
    deviceAuthorize(params: DeviceAuthorizeRequest): Promise<DeviceAuthorizeResponse>;
    authorizeInfo(params: AuthorizeInfoRequest): Promise<AuthorizeInfoResponse>;
    checkUsername(params: CheckUsernameRequest): Promise<void>;
    checkIfUserExist(params: CheckIfUserExistRequest): Promise<CheckIfUserExistResponse>;
    loginScope(): Promise<string>;
    loginGroups(): Promise<string[]>;
    refreshTokenForce(params: {
        version?: string;
    }): Promise<Credentials>;
    getCredentials(): Promise<Credentials>;
    getPublicKey(): Promise<PublicKey>;
    getEncryptParams(params: Record<any, any>): Promise<EncryptParams>;
    getProviderSubType(): Promise<ProviderSubType>;
    verifyCaptchaData({ token, key }: {
        token: string;
        key: string;
    }): Promise<{
        captcha_token: string;
        expires_in: number;
    }>;
    createCaptchaData({ state, redirect_uri }: {
        state: any;
        redirect_uri?: any;
    }): Promise<{
        token: string;
        data: string;
    }>;
    getMiniProgramCode(params: GetMiniProgramQrCodeRequest): Promise<GetMiniProgramQrCodeResponse>;
    getMiniProgramQrCodeStatus(params: GetMiniProgramQrCodeStatusRequest): Promise<GetMiniProgramQrCodeStatusResponse>;
    getUserBehaviorLog(params: GetUserBehaviorLog): Promise<GetUserBehaviorLogRes>;
    modifyPassword(params: ModifyUserBasicInfoRequest): Promise<void>;
    modifyPasswordWithoutLogin(params: ModifyPasswordWithoutLoginRequest): Promise<void>;
}
