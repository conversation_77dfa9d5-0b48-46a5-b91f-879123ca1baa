"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.weappJwtDecode = exports.base64_url_decode = exports.weAtob = exports.weBtoa = void 0;
var b64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
var b64re = /^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;
function weBtoa(string) {
    string = String(string);
    var bitmap;
    var a;
    var b;
    var c;
    var result = '';
    var i = 0;
    var rest = string.length % 3;
    for (; i < string.length;) {
        if ((a = string.charCodeAt(i++)) > 255 || (b = string.charCodeAt(i++)) > 255 || (c = string.charCodeAt(i++)) > 255)
            throw new TypeError('Failed to execute \'btoa\' on \'Window\': The string to be encoded contains characters outside of the Latin1 range.');
        bitmap = (a << 16) | (b << 8) | c;
        result
            += b64.charAt((bitmap >> 18) & 63)
                + b64.charAt((bitmap >> 12) & 63)
                + b64.charAt((bitmap >> 6) & 63)
                + b64.charAt(bitmap & 63);
    }
    return rest ? result.slice(0, rest - 3) + '==='.substring(rest) : result;
}
exports.weBtoa = weBtoa;
var weAtob = function (string) {
    string = String(string).replace(/[\t\n\f\r ]+/g, '');
    if (!b64re.test(string))
        throw new TypeError('Failed to execute \'atob\' on \'Window\': The string to be decoded is not correctly encoded.');
    string += '=='.slice(2 - (string.length & 3));
    var bitmap;
    var result = '';
    var r1;
    var r2;
    var i = 0;
    for (; i < string.length;) {
        bitmap = (b64.indexOf(string.charAt(i++)) << 18)
            | (b64.indexOf(string.charAt(i++)) << 12)
            | ((r1 = b64.indexOf(string.charAt(i++))) << 6)
            | (r2 = b64.indexOf(string.charAt(i++)));
        result
            += r1 === 64
                ? String.fromCharCode((bitmap >> 16) & 255)
                : r2 === 64
                    ? String.fromCharCode((bitmap >> 16) & 255, (bitmap >> 8) & 255)
                    : String.fromCharCode((bitmap >> 16) & 255, (bitmap >> 8) & 255, bitmap & 255);
    }
    return result;
};
exports.weAtob = weAtob;
function b64DecodeUnicode(str) {
    return decodeURIComponent((0, exports.weAtob)(str).replace(/(.)/g, function (p) {
        var code = p.charCodeAt(0).toString(16)
            .toUpperCase();
        if (code.length < 2) {
            code = "0".concat(code);
        }
        return "%".concat(code);
    }));
}
function base64_url_decode(str) {
    var output = str.replace(/-/g, '+').replace(/_/g, '/');
    switch (output.length % 4) {
        case 0:
            break;
        case 2:
            output += '==';
            break;
        case 3:
            output += '=';
            break;
        default:
            throw new Error('Illegal base64url string!');
    }
    try {
        return b64DecodeUnicode(output);
    }
    catch (err) {
        return (0, exports.weAtob)(output);
    }
}
exports.base64_url_decode = base64_url_decode;
function weappJwtDecode(token, options) {
    if (typeof token !== 'string') {
        throw new Error('Invalid token specified');
    }
    options = options || {};
    var pos = options.header === true ? 0 : 1;
    try {
        return JSON.parse(base64_url_decode(token.split('.')[pos]));
    }
    catch (e) {
        throw new Error("Invalid token specified: ".concat(e) ? e.message : '');
    }
}
exports.weappJwtDecode = weappJwtDecode;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYmFzZTY0LmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vc3JjL3V0aWxzL2Jhc2U2NC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFFQSxJQUFNLEdBQUcsR0FBRyxtRUFBbUUsQ0FBQTtBQUMvRSxJQUFNLEtBQUssR0FBRyxzRUFBc0UsQ0FBQTtBQUdwRixTQUFnQixNQUFNLENBQUMsTUFBYztJQUNuQyxNQUFNLEdBQUcsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFBO0lBQ3ZCLElBQUksTUFBTSxDQUFBO0lBQ1YsSUFBSSxDQUFDLENBQUE7SUFDTCxJQUFJLENBQUMsQ0FBQTtJQUNMLElBQUksQ0FBQyxDQUFBO0lBQ0wsSUFBSSxNQUFNLEdBQUcsRUFBRSxDQUFBO0lBQ2YsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO0lBQ1QsSUFBTSxJQUFJLEdBQUcsTUFBTSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUE7SUFFOUIsT0FBTyxDQUFDLEdBQUcsTUFBTSxDQUFDLE1BQU0sR0FBRztRQUN6QixJQUFJLENBQUMsQ0FBQyxHQUFHLE1BQU0sQ0FBQyxVQUFVLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBSSxDQUFDLENBQUMsR0FBRyxNQUFNLENBQUMsVUFBVSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUksQ0FBQyxDQUFDLEdBQUcsTUFBTSxDQUFDLFVBQVUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEdBQUcsR0FBRztZQUFFLE1BQU0sSUFBSSxTQUFTLENBQUMscUhBQXFILENBQUUsQ0FBQTtRQUUvUCxNQUFNLEdBQUcsQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFBO1FBQ2pDLE1BQU07ZUFDRCxHQUFHLENBQUMsTUFBTSxDQUFDLENBQUMsTUFBTSxJQUFJLEVBQUUsQ0FBQyxHQUFHLEVBQUUsQ0FBQztrQkFDaEMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUFDLE1BQU0sSUFBSSxFQUFFLENBQUMsR0FBRyxFQUFFLENBQUM7a0JBQy9CLEdBQUcsQ0FBQyxNQUFNLENBQUMsQ0FBQyxNQUFNLElBQUksQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFDO2tCQUM5QixHQUFHLENBQUMsTUFBTSxDQUFDLE1BQU0sR0FBRyxFQUFFLENBQUMsQ0FBQTtLQUM1QjtJQUVELE9BQU8sSUFBSSxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRSxJQUFJLEdBQUcsQ0FBQyxDQUFDLEdBQUcsS0FBSyxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFBO0FBQzFFLENBQUM7QUF0QkQsd0JBc0JDO0FBRU0sSUFBTSxNQUFNLEdBQUcsVUFBVSxNQUFjO0lBQzVDLE1BQU0sR0FBRyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsT0FBTyxDQUFDLGVBQWUsRUFBRSxFQUFFLENBQUMsQ0FBQTtJQUNwRCxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUM7UUFBRSxNQUFNLElBQUksU0FBUyxDQUFDLDhGQUE4RixDQUFDLENBQUE7SUFDNUksTUFBTSxJQUFJLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFBO0lBQzdDLElBQUksTUFBTSxDQUFBO0lBQ1YsSUFBSSxNQUFNLEdBQUcsRUFBRSxDQUFBO0lBQ2YsSUFBSSxFQUFFLENBQUE7SUFDTixJQUFJLEVBQUUsQ0FBQTtJQUNOLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtJQUNULE9BQU8sQ0FBQyxHQUFHLE1BQU0sQ0FBQyxNQUFNLEdBQUc7UUFDekIsTUFBTSxHQUFRLENBQUMsR0FBRyxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsSUFBSSxFQUFFLENBQUM7Y0FDakQsQ0FBQyxHQUFHLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxJQUFJLEVBQUUsQ0FBQztjQUN2QyxDQUFDLENBQUMsRUFBRSxHQUFHLEdBQUcsQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUM7Y0FDN0MsQ0FBQyxFQUFFLEdBQUcsR0FBRyxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFBO1FBRTFDLE1BQU07ZUFDRCxFQUFFLEtBQUssRUFBRTtnQkFDVixDQUFDLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLE1BQU0sSUFBSSxFQUFFLENBQUMsR0FBRyxHQUFHLENBQUM7Z0JBQzNDLENBQUMsQ0FBQyxFQUFFLEtBQUssRUFBRTtvQkFDVCxDQUFDLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLE1BQU0sSUFBSSxFQUFFLENBQUMsR0FBRyxHQUFHLEVBQUUsQ0FBQyxNQUFNLElBQUksQ0FBQyxDQUFDLEdBQUcsR0FBRyxDQUFDO29CQUNoRSxDQUFDLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLE1BQU0sSUFBSSxFQUFFLENBQUMsR0FBRyxHQUFHLEVBQUUsQ0FBQyxNQUFNLElBQUksQ0FBQyxDQUFDLEdBQUcsR0FBRyxFQUFFLE1BQU0sR0FBRyxHQUFHLENBQUMsQ0FBQTtLQUNyRjtJQUNELE9BQU8sTUFBTSxDQUFBO0FBQ2YsQ0FBQyxDQUFBO0FBdkJZLFFBQUEsTUFBTSxVQXVCbEI7QUFFRCxTQUFTLGdCQUFnQixDQUFDLEdBQVc7SUFDbkMsT0FBTyxrQkFBa0IsQ0FBQyxJQUFBLGNBQU0sRUFBQyxHQUFHLENBQUMsQ0FBQyxPQUFPLENBQUMsTUFBTSxFQUFFLFVBQUMsQ0FBQztRQUN0RCxJQUFJLElBQUksR0FBRyxDQUFDLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUM7YUFDcEMsV0FBVyxFQUFFLENBQUE7UUFDaEIsSUFBSSxJQUFJLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRTtZQUNuQixJQUFJLEdBQUcsV0FBSSxJQUFJLENBQUUsQ0FBQTtTQUNsQjtRQUNELE9BQU8sV0FBSSxJQUFJLENBQUUsQ0FBQTtJQUNuQixDQUFDLENBQUMsQ0FBRSxDQUFBO0FBQ04sQ0FBQztBQUVELFNBQWdCLGlCQUFpQixDQUFDLEdBQVc7SUFDM0MsSUFBSSxNQUFNLEdBQUcsR0FBRyxDQUFDLE9BQU8sQ0FBQyxJQUFJLEVBQUUsR0FBRyxDQUFDLENBQUMsT0FBTyxDQUFDLElBQUksRUFBRSxHQUFHLENBQUMsQ0FBQTtJQUN0RCxRQUFRLE1BQU0sQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFO1FBQ3pCLEtBQUssQ0FBQztZQUNKLE1BQUs7UUFDUCxLQUFLLENBQUM7WUFDSixNQUFNLElBQUksSUFBSSxDQUFBO1lBQ2QsTUFBSztRQUNQLEtBQUssQ0FBQztZQUNKLE1BQU0sSUFBSSxHQUFHLENBQUE7WUFDYixNQUFLO1FBQ1A7WUFDRSxNQUFNLElBQUksS0FBSyxDQUFDLDJCQUEyQixDQUFDLENBQUE7S0FDL0M7SUFFRCxJQUFJO1FBQ0YsT0FBTyxnQkFBZ0IsQ0FBQyxNQUFNLENBQUMsQ0FBQTtLQUNoQztJQUFDLE9BQU8sR0FBRyxFQUFFO1FBQ1osT0FBTyxJQUFBLGNBQU0sRUFBQyxNQUFNLENBQUMsQ0FBQTtLQUN0QjtBQUNILENBQUM7QUFwQkQsOENBb0JDO0FBRUQsU0FBZ0IsY0FBYyxDQUFDLEtBQWEsRUFBRSxPQUFhO0lBQ3pELElBQUksT0FBTyxLQUFLLEtBQUssUUFBUSxFQUFFO1FBQzdCLE1BQU0sSUFBSSxLQUFLLENBQUMseUJBQXlCLENBQUMsQ0FBQTtLQUMzQztJQUNELE9BQU8sR0FBRyxPQUFPLElBQUksRUFBRSxDQUFBO0lBQ3ZCLElBQU0sR0FBRyxHQUFHLE9BQU8sQ0FBQyxNQUFNLEtBQUssSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQTtJQUMzQyxJQUFJO1FBQ0YsT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFDLGlCQUFpQixDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFBO0tBQzVEO0lBQUMsT0FBTyxDQUFDLEVBQUU7UUFDVixNQUFNLElBQUksS0FBSyxDQUFDLG1DQUE0QixDQUFDLENBQUUsQ0FBQyxDQUFDLENBQUUsQ0FBUyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUE7S0FDM0U7QUFDSCxDQUFDO0FBWEQsd0NBV0MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBlc2xpbnQtZGlzYWJsZSAqL1xuLy8gd2VhcHAgand0LWRlY29kZVxuY29uc3QgYjY0ID0gJ0FCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFlaYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5Ky89J1xuY29uc3QgYjY0cmUgPSAvXig/OltBLVphLXpcXGQrL117NH0pKj8oPzpbQS1aYS16XFxkKy9dezJ9KD86PT0pP3xbQS1aYS16XFxkKy9dezN9PT8pPyQvXG5cbi8vIGJ0b2FcbmV4cG9ydCBmdW5jdGlvbiB3ZUJ0b2Eoc3RyaW5nOiBzdHJpbmcpIHtcbiAgc3RyaW5nID0gU3RyaW5nKHN0cmluZylcbiAgbGV0IGJpdG1hcFxuICBsZXQgYVxuICBsZXQgYlxuICBsZXQgY1xuICBsZXQgcmVzdWx0ID0gJydcbiAgbGV0IGkgPSAwXG4gIGNvbnN0IHJlc3QgPSBzdHJpbmcubGVuZ3RoICUgM1xuXG4gIGZvciAoOyBpIDwgc3RyaW5nLmxlbmd0aDspIHtcbiAgICBpZiAoKGEgPSBzdHJpbmcuY2hhckNvZGVBdChpKyspKSA+IDI1NSB8fCAoYiA9IHN0cmluZy5jaGFyQ29kZUF0KGkrKykpID4gMjU1IHx8IChjID0gc3RyaW5nLmNoYXJDb2RlQXQoaSsrKSkgPiAyNTUpIHRocm93IG5ldyBUeXBlRXJyb3IoJ0ZhaWxlZCB0byBleGVjdXRlIFxcJ2J0b2FcXCcgb24gXFwnV2luZG93XFwnOiBUaGUgc3RyaW5nIHRvIGJlIGVuY29kZWQgY29udGFpbnMgY2hhcmFjdGVycyBvdXRzaWRlIG9mIHRoZSBMYXRpbjEgcmFuZ2UuJywpXG5cbiAgICBiaXRtYXAgPSAoYSA8PCAxNikgfCAoYiA8PCA4KSB8IGNcbiAgICByZXN1bHRcbiAgICAgICs9IGI2NC5jaGFyQXQoKGJpdG1hcCA+PiAxOCkgJiA2MylcbiAgICAgICsgYjY0LmNoYXJBdCgoYml0bWFwID4+IDEyKSAmIDYzKVxuICAgICAgKyBiNjQuY2hhckF0KChiaXRtYXAgPj4gNikgJiA2MylcbiAgICAgICsgYjY0LmNoYXJBdChiaXRtYXAgJiA2MylcbiAgfVxuXG4gIHJldHVybiByZXN0ID8gcmVzdWx0LnNsaWNlKDAsIHJlc3QgLSAzKSArICc9PT0nLnN1YnN0cmluZyhyZXN0KSA6IHJlc3VsdFxufVxuLy8gYXRvYlxuZXhwb3J0IGNvbnN0IHdlQXRvYiA9IGZ1bmN0aW9uIChzdHJpbmc6IHN0cmluZykge1xuICBzdHJpbmcgPSBTdHJpbmcoc3RyaW5nKS5yZXBsYWNlKC9bXFx0XFxuXFxmXFxyIF0rL2csICcnKVxuICBpZiAoIWI2NHJlLnRlc3Qoc3RyaW5nKSkgdGhyb3cgbmV3IFR5cGVFcnJvcignRmFpbGVkIHRvIGV4ZWN1dGUgXFwnYXRvYlxcJyBvbiBcXCdXaW5kb3dcXCc6IFRoZSBzdHJpbmcgdG8gYmUgZGVjb2RlZCBpcyBub3QgY29ycmVjdGx5IGVuY29kZWQuJylcbiAgc3RyaW5nICs9ICc9PScuc2xpY2UoMiAtIChzdHJpbmcubGVuZ3RoICYgMykpXG4gIGxldCBiaXRtYXBcbiAgbGV0IHJlc3VsdCA9ICcnXG4gIGxldCByMVxuICBsZXQgcjJcbiAgbGV0IGkgPSAwXG4gIGZvciAoOyBpIDwgc3RyaW5nLmxlbmd0aDspIHtcbiAgICBiaXRtYXAgPSAgICAgIChiNjQuaW5kZXhPZihzdHJpbmcuY2hhckF0KGkrKykpIDw8IDE4KVxuICAgICAgfCAoYjY0LmluZGV4T2Yoc3RyaW5nLmNoYXJBdChpKyspKSA8PCAxMilcbiAgICAgIHwgKChyMSA9IGI2NC5pbmRleE9mKHN0cmluZy5jaGFyQXQoaSsrKSkpIDw8IDYpXG4gICAgICB8IChyMiA9IGI2NC5pbmRleE9mKHN0cmluZy5jaGFyQXQoaSsrKSkpXG5cbiAgICByZXN1bHRcbiAgICAgICs9IHIxID09PSA2NFxuICAgICAgICA/IFN0cmluZy5mcm9tQ2hhckNvZGUoKGJpdG1hcCA+PiAxNikgJiAyNTUpXG4gICAgICAgIDogcjIgPT09IDY0XG4gICAgICAgICAgPyBTdHJpbmcuZnJvbUNoYXJDb2RlKChiaXRtYXAgPj4gMTYpICYgMjU1LCAoYml0bWFwID4+IDgpICYgMjU1KVxuICAgICAgICAgIDogU3RyaW5nLmZyb21DaGFyQ29kZSgoYml0bWFwID4+IDE2KSAmIDI1NSwgKGJpdG1hcCA+PiA4KSAmIDI1NSwgYml0bWFwICYgMjU1KVxuICB9XG4gIHJldHVybiByZXN1bHRcbn1cblxuZnVuY3Rpb24gYjY0RGVjb2RlVW5pY29kZShzdHI6IHN0cmluZykge1xuICByZXR1cm4gZGVjb2RlVVJJQ29tcG9uZW50KHdlQXRvYihzdHIpLnJlcGxhY2UoLyguKS9nLCAocCkgPT4ge1xuICAgIGxldCBjb2RlID0gcC5jaGFyQ29kZUF0KDApLnRvU3RyaW5nKDE2KVxuICAgICAgLnRvVXBwZXJDYXNlKClcbiAgICBpZiAoY29kZS5sZW5ndGggPCAyKSB7XG4gICAgICBjb2RlID0gYDAke2NvZGV9YFxuICAgIH1cbiAgICByZXR1cm4gYCUke2NvZGV9YFxuICB9KSwpXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBiYXNlNjRfdXJsX2RlY29kZShzdHI6IHN0cmluZykge1xuICBsZXQgb3V0cHV0ID0gc3RyLnJlcGxhY2UoLy0vZywgJysnKS5yZXBsYWNlKC9fL2csICcvJylcbiAgc3dpdGNoIChvdXRwdXQubGVuZ3RoICUgNCkge1xuICAgIGNhc2UgMDpcbiAgICAgIGJyZWFrXG4gICAgY2FzZSAyOlxuICAgICAgb3V0cHV0ICs9ICc9PSdcbiAgICAgIGJyZWFrXG4gICAgY2FzZSAzOlxuICAgICAgb3V0cHV0ICs9ICc9J1xuICAgICAgYnJlYWtcbiAgICBkZWZhdWx0OlxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbGxlZ2FsIGJhc2U2NHVybCBzdHJpbmchJylcbiAgfVxuXG4gIHRyeSB7XG4gICAgcmV0dXJuIGI2NERlY29kZVVuaWNvZGUob3V0cHV0KVxuICB9IGNhdGNoIChlcnIpIHtcbiAgICByZXR1cm4gd2VBdG9iKG91dHB1dClcbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gd2VhcHBKd3REZWNvZGUodG9rZW46IHN0cmluZywgb3B0aW9ucz86IGFueSkge1xuICBpZiAodHlwZW9mIHRva2VuICE9PSAnc3RyaW5nJykge1xuICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCB0b2tlbiBzcGVjaWZpZWQnKVxuICB9XG4gIG9wdGlvbnMgPSBvcHRpb25zIHx8IHt9XG4gIGNvbnN0IHBvcyA9IG9wdGlvbnMuaGVhZGVyID09PSB0cnVlID8gMCA6IDFcbiAgdHJ5IHtcbiAgICByZXR1cm4gSlNPTi5wYXJzZShiYXNlNjRfdXJsX2RlY29kZSh0b2tlbi5zcGxpdCgnLicpW3Bvc10pKVxuICB9IGNhdGNoIChlKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGBJbnZhbGlkIHRva2VuIHNwZWNpZmllZDogJHtlfWAgPyAoZSBhcyBhbnkpLm1lc3NhZ2UgOiAnJylcbiAgfVxufVxuIl19