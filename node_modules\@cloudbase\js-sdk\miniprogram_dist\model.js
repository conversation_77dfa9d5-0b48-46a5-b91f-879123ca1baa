!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("cloudbase_model",[],t):"object"==typeof exports?exports.cloudbase_model=t():e.cloudbase_model=t()}("undefined"!=typeof window?window:this,(()=>(()=>{var e={530:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),o(n(679),t)},276:function(e,t){!function(e){"use strict";var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},t(e,n)};var n=function(){return n=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},n.apply(this,arguments)};function r(e,t,n,r){return new(n||(n=Promise))((function(t,o){function a(e){try{c(r.next(e))}catch(e){o(e)}}function i(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var r;e.done?t(e.value):(r=e.value,r instanceof n?r:new n((function(e){e(r)}))).then(a,i)}c((r=r.apply(e,[])).next())}))}function o(e,t){var n,r,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(a){return function(c){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!((o=(o=i.trys).length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(e){a=[6,e],r=0}finally{n=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}var a=function(e){function n(t,n){var r=e.call(this,t)||this;return r.name="WxCloudSDKError",r.code=null==n?void 0:n.code,r.requestId=null==n?void 0:n.requestId,r.originError=null==n?void 0:n.originError,r}return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}(n,e),n}(Error);function i(){return"undefined"!=typeof window&&window||"undefined"!=typeof globalThis&&globalThis}function c(){try{var e=i();if(!e)return;return"undefined"==typeof wx?i().location.href:e.__wxRoute}catch(e){}}var s=function(e){var t=e.dataSourceName,n=e.methodName,s=e.params,u=e.realMethodName,l=e.callFunction,d=e.envType,f=void 0===d?"prod":d,p=e.mode;return r(void 0,0,void 0,(function(){var e,r,d,h,y,v;return o(this,(function(o){switch(o.label){case 0:e={data:{},requestId:""},o.label=1;case 1:return o.trys.push([1,3,,4]),[4,l({name:"lowcode-datasource",data:{dataSourceName:t,methodName:n,params:s,userAgent:(b=i(),(null==b?void 0:b.navigator)?b.navigator.userAgent:"undefined"!=typeof wx&&wx.getSystemInfo?(wx.getSystemInfo({success:function(e){e&&(m=["brand","model","version","system","platform","SDKVersion","language"].map((function(t){return"".concat(t,": ").concat(e[t])})).join(", "))}}),m):void 0),referrer:c(),"x-sdk-version":"1.5.0",envType:f,mode:p}})];case 2:if(r=o.sent(),d=(null===(y=null==r?void 0:r.result)||void 0===y?void 0:y.requestId)||(null==r?void 0:r.requestId)||(null==r?void 0:r.requestID),null==r?void 0:r.result.code)throw new a("【错误】".concat(null==r?void 0:r.result.message,"\n【操作】调用 models.").concat(t?"".concat(t,"."):"").concat(u,"\n【错误码】").concat(null==r?void 0:r.result.code,"\n【请求ID】").concat(d||"N/A"),{code:null==r?void 0:r.result.code,requestId:d});return e.data=(null===(v=null==r?void 0:r.result)||void 0===v?void 0:v.data)||{},e.requestId=d,[3,4];case 3:throw"WxCloudSDKError"===(h=o.sent()).name?h:(console.log(h),new a("【错误】".concat(h.message,"\n      【操作】调用 models.").concat(t?"".concat(t,"."):"").concat(u,"\n      【请求ID】N/A"),{code:"UnknownError",originError:h}));case 4:return[2,e]}var m,b}))}))},u=function(e){var t=e.sql,n=e.params,a=e.config,i=e.callFunction,c=e.unsafe,u=void 0!==c&&c;return r(void 0,0,void 0,(function(){return o(this,(function(e){return[2,s({realMethodName:"$runSQL",methodName:"callWedaApi",params:{action:"RunMysqlCommand",data:{sqlTemplate:t,config:a,parameter:u?"":Object.entries(n||{}).reduce((function(e,t){var n=t[0],r=t[1];if(void 0!==r){var o="OBJECT";switch(typeof r){case"boolean":o="BOOLEAN";break;case"number":o="NUMBER";break;case"string":o="STRING";break;default:o=Array.isArray(r)?"ARRAY":"OBJECT"}e.push({key:n,type:o,value:"STRING"===o?r:JSON.stringify(r)})}return e}),[])||[]}},callFunction:i,mode:"sdk"})]}))}))},l=function(e){return{$runSQL:function(t,a,i){return r(this,0,void 0,(function(){return o(this,(function(r){switch(r.label){case 0:return[4,u({sql:t,params:a,config:n(n({},i),{preparedStatements:!0}),callFunction:e})];case 1:return[2,r.sent()]}}))}))},$runSQLRaw:function(t,a){return r(this,0,void 0,(function(){return o(this,(function(r){switch(r.label){case 0:return[4,u({sql:t,params:[],config:n(n({},a),{preparedStatements:!1}),callFunction:e})];case 1:return[2,r.sent()]}}))}))}}},d={create:{methodName:"wedaCreateV2"},createMany:{methodName:"wedaBatchCreateV2"},update:{methodName:"wedaUpdateV2"},upsert:{methodName:"wedaUpsertV2"},updateMany:{methodName:"wedaBatchUpdateV2"},delete:{methodName:"wedaDeleteV2"},deleteMany:{methodName:"wedaBatchDeleteV2"},get:{methodName:"wedaGetItemV2",defaultParams:{filter:{where:{}},select:{$master:!0}}},list:{methodName:"wedaGetRecordsV2",defaultParams:{filter:{where:{}},select:{$master:!0}}}},f=function(e){var t=l(e);return new Proxy({},{get:function(i,c){if("string"==typeof c)return Object.prototype.hasOwnProperty.call(t,c)?t[c]:function(e,t){return new Proxy({},{get:function(i,c){var u=d[c];if(!u){var l=new Error("不支持的操作: ".concat(c));throw new a(l.message||"Unknown error occurred",{originError:l,code:"NotSupported",requestId:"N/A"})}return function(a){return r(void 0,0,void 0,(function(){var r,i,l,d,f;return o(this,(function(o){switch(o.label){case 0:return r=n(n({},u.defaultParams||{}),a||{}),[4,s({callFunction:t,dataSourceName:e,methodName:u.methodName,realMethodName:c,params:r,envType:null==a?void 0:a.envType})];case 1:return i=o.sent(),l={data:{}},d=u.responseKey,l.data=d?null===(f=null==i?void 0:i.data)||void 0===f?void 0:f[d]:null==i?void 0:i.data,[2,l]}}))}))}}})}(c,e)}})},p={filter:{where:{}},select:{$master:!0}};function h(e){return{getUrl:function(t){return"".concat(t,"/").concat(e)},method:"post"}}var y={get:n(n({},h("get")),{defaultParams:n({},p)}),list:n(n({},h("list")),{defaultParams:n({},p)}),create:h("create"),createMany:h("createMany"),update:n(n({},h("update")),{method:"put"}),updateMany:n(n({},h("updateMany")),{method:"put"}),upsert:h("upsert"),delete:h("delete"),deleteMany:h("deleteMany")},v="Unknown error occurred",m="NotSupported",b=function(e,t,n,r){var o=l(e);return new Proxy({},{get:function(e,a){if("string"==typeof a)return Object.prototype.hasOwnProperty.call(o,a)?o[a]:g(n,a,t,r)}})},w=function(e,t,n,r,o){return new a("【错误】".concat(e,"\n【操作】调用 models.").concat(t,".").concat(n,"\n【错误码】").concat(r,"\n【请求ID】").concat(o),{code:r,requestId:o})},g=function(e,t,i,c){return new Proxy({},{get:function(s,u){if("runSQLTemplate"!==u){var l=y[u];if(!l){var d=new Error("不支持的操作: ".concat(u));throw new a(d.message||v,{originError:d,code:m,requestId:"N/A"})}return function(n){return void 0===n&&(n={}),r(void 0,0,void 0,(function(){var r,c,s,d,f,p,h,y,m,b;return o(this,(function(o){switch(o.label){case 0:r=l.getUrl,c=l.method,s=l.defaultParams,d=void 0===s?{}:s,f=Object.assign({},d,n),p="pre"===f.envType?"pre":"prod",h=[e,p,r(t)].join("/"),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,i({url:h,body:JSON.stringify(f),method:c})];case 2:if((y=o.sent()).code)throw w(null==y?void 0:y.message,t,u,null==y?void 0:y.code,null==y?void 0:y.requestId);return"get"===u&&Object.assign(y,{data:null!==(b=y.data.record)&&void 0!==b?b:y.data}),[2,y];case 3:throw m=o.sent(),new a((null==m?void 0:m.message)||v,{originError:m});case 4:return[2]}}))}))}}if(!(null==c?void 0:c.sqlBaseUrl))throw d=new Error("不支持的操作: ".concat(u)),new a(d.message||v,{originError:d,code:m,requestId:"N/A"});return function(e){return r(void 0,0,void 0,(function(){var r,s,l,d,f,p,h,y,m,b,g,O,S,j,_,E;return o(this,(function(o){switch(o.label){case 0:r=e.params,s=e.templateName,l=e.envType,d="pre"===l?"pre":"prod",f=[c.sqlBaseUrl,d,s,"run"].join("/"),p=Object.entries(r||{}).reduce((function(e,t){var n=t[0],r=t[1];if(void 0!==r){var o="OBJECT";switch(typeof r){case"boolean":o="BOOLEAN";break;case"number":o="NUMBER";break;case"string":o="STRING";break;default:o=Array.isArray(r)?"ARRAY":"OBJECT"}e.push({key:n,type:o,value:"STRING"===o?r:JSON.stringify(r)})}return e}),[]),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,i({url:f,body:JSON.stringify({parameter:p}),method:"POST"})];case 2:if(h=o.sent(),null===(m=null==h?void 0:h.Response)||void 0===m?void 0:m.Error)throw w(null===(g=null===(b=null==h?void 0:h.Response)||void 0===b?void 0:b.Error)||void 0===g?void 0:g.Message,t,u,null===(S=null===(O=null==h?void 0:h.Response)||void 0===O?void 0:O.Error)||void 0===S?void 0:S.Code,null===(j=null==h?void 0:h.Response)||void 0===j?void 0:j.RequestId);return[2,n(n({},null!==(_=null==h?void 0:h.Response)&&void 0!==_?_:{}),{data:null===(E=null==h?void 0:h.Response)||void 0===E?void 0:E.Data})];case 3:throw y=o.sent(),new a((null==y?void 0:y.message)||v,{originError:y});case 4:return[2]}}))}))}}})};function O(e){if(!e)throw new Error("cloud is required");if(!e.callFunction)throw new Error("cloud.callFunction is required");var t=f(e.callFunction.bind(e));return e.models=t,e}var S={init:O,generateHTTPClient:b};e.default=S,e.generateHTTPClient=b,e.init=O,Object.defineProperty(e,"__esModule",{value:!0})}(t)},679:(e,t,n)=>{"use strict";n.r(t),n.d(t,{lazyGetEntity:()=>f,registerModel:()=>v});var r=n(276),o=n(19),a={};for(const e in r)["default","lazyGetEntity","registerModel"].indexOf(e)<0&&(a[e]=()=>r[e]);n.d(t,a);var i=function(){return i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},i.apply(this,arguments)},c=function(e,t,n,r){return new(n||(n=Promise))((function(o,a){function i(e){try{s(r.next(e))}catch(e){a(e)}}function c(e){try{s(r.throw(e))}catch(e){a(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,c)}s((r=r.apply(e,t||[])).next())}))},s=function(e,t){var n,r,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(i=0)),i;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return i.label++,{value:c[1],done:!1};case 5:i.label++,r=c[1],c=[0];continue;case 7:c=i.ops.pop(),i.trys.pop();continue;default:if(!((o=(o=i.trys).length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){i=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){i.label=c[1];break}if(6===c[0]&&i.label<o[1]){i.label=o[1],o=c;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(c);break}o[2]&&i.ops.pop(),i.trys.pop();continue}c=t.call(e,i)}catch(e){c=[6,e],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}},u=o.U3.yE,l="models",d=new WeakMap;function f(e){return new Proxy({},{get:function(t,n){var o=d.get(e);return o||(o=function(e){var t=this,n=e.callFunction.bind(e),o=e.request.fetch.bind(e.request),a=e.getEndPointWithKey("GATEWAY"),u=a.BASE_URL,l=a.PROTOCOL,d="".concat(l).concat(u,"/model"),f="".concat(l).concat(u,"/sql"),p=(0,r.generateHTTPClient)(n,(function(e){return c(t,void 0,void 0,(function(){return s(this,(function(t){switch(t.label){case 0:return[4,o(i(i({},e),{headers:i({"Content-Type":"application/json"},e.headers)}))];case 1:return[4,t.sent().data];case 2:return[2,t.sent()]}}))}))}),d,{sqlBaseUrl:f});return p}(e),d.set(e,o)),o[n]}})}var p="cloudbase_init",h=new u;h.on(p,(function(e){var t=e.data;Object.assign(t,{models:f(t)})}));var y={name:l,namespace:l,entity:new Proxy({},{get:function(e,t){console.warn("【deprecated】Accessing Cloudbase.prototype.models.".concat(t,"."))}}),injectEvents:{bus:h,events:[p]}};try{cloudbase.registerComponent(y)}catch(e){}function v(e){try{e.registerComponent(y)}catch(e){console.warn(e)}}},19:(e,t,n)=>{"use strict";n.d(t,{U3:()=>r});var r={};n.r(r),n.d(r,{yE:()=>j});var o="@cloudbase/js-sdk";function a(){return o}var i,c="INVALID_OPERATION",s="OPERATION_FAIL";!function(e){e.local="local",e.none="none",e.session="session"}(i||(i={}));function u(e,t){console.warn("[".concat(a(),"][").concat(e,"]:").concat(t))}var l,d,f=(l=function(e,t){return l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},l(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}l(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),p=function(){return p=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},p.apply(this,arguments)},h=function(e,t,n,r){return new(n||(n=Promise))((function(o,a){function i(e){try{s(r.next(e))}catch(e){a(e)}}function c(e){try{s(r.throw(e))}catch(e){a(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,c)}s((r=r.apply(e,t||[])).next())}))},y=function(e,t){var n,r,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(i=0)),i;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return i.label++,{value:c[1],done:!1};case 5:i.label++,r=c[1],c=[0];continue;case 7:c=i.ops.pop(),i.trys.pop();continue;default:if(!((o=(o=i.trys).length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){i=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){i.label=c[1];break}if(6===c[0]&&i.label<o[1]){i.label=o[1],o=c;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(c);break}o[2]&&i.ops.pop(),i.trys.pop();continue}c=t.call(e,i)}catch(e){c=[6,e],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}};!function(e){function t(t){var n=e.call(this)||this,r=t.timeout,o=t.timeoutMsg,a=t.restrictedMethods;return n.timeout=r||0,n.timeoutMsg=o||"请求超时",n.restrictedMethods=a||["get","post","upload","download"],n}f(t,e),t.prototype.get=function(e){return this.request(p(p({},e),{method:"get"}),this.restrictedMethods.includes("get"))},t.prototype.post=function(e){return this.request(p(p({},e),{method:"post"}),this.restrictedMethods.includes("post"))},t.prototype.put=function(e){return this.request(p(p({},e),{method:"put"}))},t.prototype.upload=function(e){var t=e.data,n=e.file,r=e.name,o=e.method,a=e.headers,i=void 0===a?{}:a,c={post:"post",put:"put"}[null==o?void 0:o.toLowerCase()]||"put",s=new FormData;return"post"===c?(Object.keys(t).forEach((function(e){s.append(e,t[e])})),s.append("key",r),s.append("file",n),this.request(p(p({},e),{data:s,method:c}),this.restrictedMethods.includes("upload"))):this.request(p(p({},e),{method:"put",headers:i,body:n}),this.restrictedMethods.includes("upload"))},t.prototype.download=function(e){return h(this,void 0,void 0,(function(){var t,n,r,o;return y(this,(function(a){switch(a.label){case 0:return a.trys.push([0,2,,3]),[4,this.get(p(p({},e),{headers:{},responseType:"blob"}))];case 1:return t=a.sent().data,n=window.URL.createObjectURL(new Blob([t])),r=decodeURIComponent(new URL(e.url).pathname.split("/").pop()||""),(o=document.createElement("a")).href=n,o.setAttribute("download",r),o.style.display="none",document.body.appendChild(o),o.click(),window.URL.revokeObjectURL(n),document.body.removeChild(o),[3,3];case 2:return a.sent(),[3,3];case 3:return[2,new Promise((function(t){t({statusCode:200,tempFilePath:e.url})}))]}}))}))},t.prototype.fetch=function(e){var t;return h(this,void 0,void 0,(function(){var n,r,o,a,i,c,s,u,l,d,f,v=this;return y(this,(function(m){switch(m.label){case 0:return n=new AbortController,r=e.url,o=e.enableAbort,a=void 0!==o&&o,i=e.stream,c=void 0!==i&&i,s=e.signal,u=e.timeout,l=null!=u?u:this.timeout,s&&(s.aborted&&n.abort(),s.addEventListener("abort",(function(){return n.abort()}))),d=null,a&&l&&(d=setTimeout((function(){console.warn(v.timeoutMsg),n.abort(new Error(v.timeoutMsg))}),l)),[4,fetch(r,p(p({},e),{signal:n.signal})).then((function(e){return h(v,void 0,void 0,(function(){var t,n,r;return y(this,(function(o){switch(o.label){case 0:return clearTimeout(d),e.ok?(t=e,[3,3]):[3,1];case 1:return r=(n=Promise).reject,[4,e.json()];case 2:t=r.apply(n,[o.sent()]),o.label=3;case 3:return[2,t]}}))}))})).catch((function(e){return clearTimeout(d),Promise.reject(e)}))];case 1:return f=m.sent(),[2,{data:c?f.body:(null===(t=f.headers.get("content-type"))||void 0===t?void 0:t.includes("application/json"))?f.json():f.text(),statusCode:f.status,header:f.headers}]}}))}))},t.prototype.request=function(e,t){var n=this;void 0===t&&(t=!1);var r=String(e.method).toLowerCase()||"get";return new Promise((function(o){var a,i,c,s=e.url,u=e.headers,l=void 0===u?{}:u,d=e.data,f=e.responseType,p=e.withCredentials,h=e.body,y=e.onUploadProgress,v=function(e,t,n){void 0===n&&(n={});var r=/\?/.test(t),o="";return Object.keys(n).forEach((function(e){""===o?!r&&(t+="?"):o+="&",o+="".concat(e,"=").concat(encodeURIComponent(n[e]))})),/^http(s)?:\/\//.test(t+=o)?t:"".concat(e).concat(t)}("https:",s,"get"===r?d:{}),m=new XMLHttpRequest;m.open(r,v),f&&(m.responseType=f),Object.keys(l).forEach((function(e){m.setRequestHeader(e,l[e])})),y&&m.upload.addEventListener("progress",y),m.onreadystatechange=function(){var e={};if(4===m.readyState){var t=m.getAllResponseHeaders().trim().split(/[\r\n]+/),n={};t.forEach((function(e){var t=e.split(": "),r=t.shift().toLowerCase(),o=t.join(": ");n[r]=o})),e.header=n,e.statusCode=m.status;try{e.data="blob"===f?m.response:JSON.parse(m.responseText)}catch(t){e.data="blob"===f?m.response:m.responseText}clearTimeout(a),o(e)}},t&&n.timeout&&(a=setTimeout((function(){console.warn(n.timeoutMsg),m.abort()}),n.timeout)),c=d,i="[object FormData]"===Object.prototype.toString.call(c)?d:"application/x-www-form-urlencoded"===l["content-type"]?function(e){void 0===e&&(e={});var t=[];return Object.keys(e).forEach((function(n){t.push("".concat(n,"=").concat(encodeURIComponent(e[n])))})),t.join("&")}(d):h||(d?JSON.stringify(d):void 0),p&&(m.withCredentials=!0),m.send(i)}))}}((function(){})),function(e){e.WEB="web",e.WX_MP="wx_mp"}(d||(d={}));var v=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),m=function(e,t,n,r){return new(n||(n=Promise))((function(o,a){function i(e){try{s(r.next(e))}catch(e){a(e)}}function c(e){try{s(r.throw(e))}catch(e){a(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,c)}s((r=r.apply(e,t||[])).next())}))},b=function(e,t){var n,r,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(i=0)),i;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return i.label++,{value:c[1],done:!1};case 5:i.label++,r=c[1],c=[0];continue;case 7:c=i.ops.pop(),i.trys.pop();continue;default:if(!((o=(o=i.trys).length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){i=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){i.label=c[1];break}if(6===c[0]&&i.label<o[1]){i.label=o[1],o=c;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(c);break}o[2]&&i.ops.pop(),i.trys.pop();continue}c=t.call(e,i)}catch(e){c=[6,e],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}},w=function(e){function t(t){var n=e.call(this)||this;return n.root=t,t.tcbCacheObject||(t.tcbCacheObject={}),n}return v(t,e),t.prototype.setItem=function(e,t){this.root.tcbCacheObject[e]=t},t.prototype.getItem=function(e){return this.root.tcbCacheObject[e]},t.prototype.removeItem=function(e){delete this.root.tcbCacheObject[e]},t.prototype.clear=function(){delete this.root.tcbCacheObject},t}((function(){}));!function(){function e(e){this.keys={};var t=e.persistence,n=e.platformInfo,r=void 0===n?{}:n,o=e.keys,a=void 0===o?{}:o;this.platformInfo=r,this.storage||(this.persistenceTag=this.platformInfo.adapter.primaryStorage||t,this.storage=function(e,t){switch(e){case"local":default:return t.localStorage?t.localStorage:(u("INVALID_PARAMS","localStorage is not supported on current platform"),new w(t.root));case"none":return new w(t.root)}}(this.persistenceTag,this.platformInfo.adapter),this.keys=a)}Object.defineProperty(e.prototype,"mode",{get:function(){return this.storage.mode||"sync"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"persistence",{get:function(){return this.persistenceTag},enumerable:!1,configurable:!0}),e.prototype.setStore=function(e,t,n){if("async"!==this.mode){if(this.storage)try{var r={version:n||"localCachev1",content:t};this.storage.setItem(e,JSON.stringify(r))}catch(e){throw new Error(JSON.stringify({code:s,msg:"[".concat(a(),"][").concat(s,"]setStore failed"),info:e}))}}else u(c,"current platform's storage is asynchronous, please use setStoreAsync insteed")},e.prototype.setStoreAsync=function(e,t,n){return m(this,void 0,void 0,(function(){var r;return b(this,(function(o){switch(o.label){case 0:if(!this.storage)return[2];o.label=1;case 1:return o.trys.push([1,3,,4]),r={version:n||"localCachev1",content:t},[4,this.storage.setItem(e,JSON.stringify(r))];case 2:return o.sent(),[3,4];case 3:return o.sent(),[2];case 4:return[2]}}))}))},e.prototype.getStore=function(e,t){var n;if("async"!==this.mode){try{if("undefined"!=typeof process&&(null===(n=process.env)||void 0===n?void 0:n.tcb_token))return process.env.tcb_token;if(!this.storage)return""}catch(e){return""}t=t||"localCachev1";var r=this.storage.getItem(e);return r&&r.indexOf(t)>=0?JSON.parse(r).content:""}u(c,"current platform's storage is asynchronous, please use getStoreAsync insteed")},e.prototype.getStoreAsync=function(e,t){var n;return m(this,void 0,void 0,(function(){var r;return b(this,(function(o){switch(o.label){case 0:try{if("undefined"!=typeof process&&(null===(n=process.env)||void 0===n?void 0:n.tcb_token))return[2,process.env.tcb_token];if(!this.storage)return[2,""]}catch(e){return[2,""]}return t=t||"localCachev1",[4,this.storage.getItem(e)];case 1:return(r=o.sent())&&r.indexOf(t)>=0?[2,JSON.parse(r).content]:[2,""]}}))}))},e.prototype.removeStore=function(e){"async"!==this.mode?this.storage.removeItem(e):u(c,"current platform's storage is asynchronous, please use removeStoreAsync insteed")},e.prototype.removeStoreAsync=function(e){return m(this,void 0,void 0,(function(){return b(this,(function(t){switch(t.label){case 0:return[4,this.storage.removeItem(e)];case 1:return t.sent(),[2]}}))}))}}();var g=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),O=function(e,t){this.data=t||null,this.name=e},S=function(e){function t(t,n){var r=e.call(this,"error",{error:t,data:n})||this;return r.error=t,r}return g(t,e),t}(O),j=function(){function e(){this.listeners={}}return e.prototype.on=function(e,t){return function(e,t,n){n[e]=n[e]||[],n[e].push(t)}(e,t,this.listeners),this},e.prototype.off=function(e,t){return function(e,t,n){if(null==n?void 0:n[e]){var r=n[e].indexOf(t);-1!==r&&n[e].splice(r,1)}}(e,t,this.listeners),this},e.prototype.fire=function(e,t){if(e instanceof S)return console.error(e.error),this;var n="string"==typeof e?new O(e,t||{}):e,r=n.name;if(this.listens(r)){n.target=this;for(var o=0,a=this.listeners[r]?function(e,t,n){if(n||2===arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}([],this.listeners[r],!0):[];o<a.length;o++)a[o].call(this,n)}return this},e.prototype.listens=function(e){return this.listeners[e]&&this.listeners[e].length>0},e}();function _(e){this.message=e}function E(e){this.message=e}new j,"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.indexOf("Firefox"),_.prototype=new Error,_.prototype.name="InvalidCharacterError","undefined"!=typeof window&&window.atob&&window.atob.bind(window),E.prototype=new Error,E.prototype.name="InvalidTokenError"}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r].call(a.exports,a,a.exports,n),a.exports}return n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n(530)})()));