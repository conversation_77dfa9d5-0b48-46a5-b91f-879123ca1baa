!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("cloudbase_realtime",[],t):"object"==typeof exports?exports.cloudbase_realtime=t():e.cloudbase_realtime=t()}("undefined"!=typeof window?window:this,(()=>(()=>{var e={9300:(e,t,r)=>{var n=r(8663)(r(8594),"DataView");e.exports=n},2648:(e,t,r)=>{var n=r(6253),o=r(1629),i=r(7332),s=r(4641),a=r(4383);function c(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=s,c.prototype.set=a,e.exports=c},819:(e,t,r)=>{var n=r(3371),o=r(5827),i=r(4662),s=r(8109),a=r(5660);function c(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=s,c.prototype.set=a,e.exports=c},1107:(e,t,r)=>{var n=r(8663)(r(8594),"Map");e.exports=n},705:(e,t,r)=>{var n=r(256),o=r(2462),i=r(7718),s=r(1477),a=r(5466);function c(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=s,c.prototype.set=a,e.exports=c},40:(e,t,r)=>{var n=r(8663)(r(8594),"Promise");e.exports=n},2482:(e,t,r)=>{var n=r(8663)(r(8594),"Set");e.exports=n},6912:(e,t,r)=>{var n=r(819),o=r(1363),i=r(5406),s=r(3145),a=r(8189),c=r(7502);function u(e){var t=this.__data__=new n(e);this.size=t.size}u.prototype.clear=o,u.prototype.delete=i,u.prototype.get=s,u.prototype.has=a,u.prototype.set=c,e.exports=u},8190:(e,t,r)=>{var n=r(8594).Symbol;e.exports=n},563:(e,t,r)=>{var n=r(8594).Uint8Array;e.exports=n},2538:(e,t,r)=>{var n=r(8663)(r(8594),"WeakMap");e.exports=n},1043:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n&&!1!==t(e[r],r,e););return e}},4799:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,i=[];++r<n;){var s=e[r];t(s,r,e)&&(i[o++]=s)}return i}},7471:(e,t,r)=>{var n=r(8056),o=r(1819),i=r(7396),s=r(9153),a=r(4607),c=r(5780),u=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=i(e),l=!r&&o(e),h=!r&&!l&&s(e),d=!r&&!l&&!h&&c(e),p=r||l||h||d,f=p?n(e.length,String):[],v=f.length;for(var E in e)!t&&!u.call(e,E)||p&&("length"==E||h&&("offset"==E||"parent"==E)||d&&("buffer"==E||"byteLength"==E||"byteOffset"==E)||a(E,v))||f.push(E);return f}},534:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=Array(n);++r<n;)o[r]=t(e[r],r,e);return o}},3246:e=>{e.exports=function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}},4067:(e,t,r)=>{var n=r(4622),o=r(6344),i=Object.prototype.hasOwnProperty;e.exports=function(e,t,r){var s=e[t];i.call(e,t)&&o(s,r)&&(void 0!==r||t in e)||n(e,t,r)}},2694:(e,t,r)=>{var n=r(6344);e.exports=function(e,t){for(var r=e.length;r--;)if(n(e[r][0],t))return r;return-1}},2457:(e,t,r)=>{var n=r(1560),o=r(5683);e.exports=function(e,t){return e&&n(t,o(t),e)}},2489:(e,t,r)=>{var n=r(1560),o=r(4344);e.exports=function(e,t){return e&&n(t,o(t),e)}},4622:(e,t,r)=>{var n=r(3446);e.exports=function(e,t,r){"__proto__"==t&&n?n(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}},4420:(e,t,r)=>{var n=r(6912),o=r(1043),i=r(4067),s=r(2457),a=r(2489),c=r(2579),u=r(7895),l=r(801),h=r(6538),d=r(1984),p=r(3573),f=r(3434),v=r(2054),E=r(3822),g=r(2792),I=r(7396),b=r(9153),m=r(4583),w=r(2861),y=r(3493),_=r(5683),S=r(4344),T="[object Arguments]",A="[object Function]",R="[object Object]",C={};C[T]=C["[object Array]"]=C["[object ArrayBuffer]"]=C["[object DataView]"]=C["[object Boolean]"]=C["[object Date]"]=C["[object Float32Array]"]=C["[object Float64Array]"]=C["[object Int8Array]"]=C["[object Int16Array]"]=C["[object Int32Array]"]=C["[object Map]"]=C["[object Number]"]=C[R]=C["[object RegExp]"]=C["[object Set]"]=C["[object String]"]=C["[object Symbol]"]=C["[object Uint8Array]"]=C["[object Uint8ClampedArray]"]=C["[object Uint16Array]"]=C["[object Uint32Array]"]=!0,C["[object Error]"]=C[A]=C["[object WeakMap]"]=!1,e.exports=function e(t,r,x,D,N,O){var L,W=1&r,j=2&r,P=4&r;if(x&&(L=N?x(t,D,N,O):x(t)),void 0!==L)return L;if(!w(t))return t;var M=I(t);if(M){if(L=v(t),!W)return u(t,L)}else{var k=f(t),B=k==A||"[object GeneratorFunction]"==k;if(b(t))return c(t,W);if(k==R||k==T||B&&!N){if(L=j||B?{}:g(t),!W)return j?h(t,a(L,t)):l(t,s(L,t))}else{if(!C[k])return N?t:{};L=E(t,k,W)}}O||(O=new n);var K=O.get(t);if(K)return K;O.set(t,L),y(t)?t.forEach((function(n){L.add(e(n,r,x,n,t,O))})):m(t)&&t.forEach((function(n,o){L.set(o,e(n,r,x,o,t,O))}));var q=M?void 0:(P?j?p:d:j?S:_)(t);return o(q||t,(function(n,o){q&&(n=t[o=n]),i(L,o,e(n,r,x,o,t,O))})),L}},2726:(e,t,r)=>{var n=r(2861),o=Object.create,i=function(){function e(){}return function(t){if(!n(t))return{};if(o)return o(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();e.exports=i},8081:(e,t,r)=>{var n=r(1380),o=r(82);e.exports=function(e,t){for(var r=0,i=(t=n(t,e)).length;null!=e&&r<i;)e=e[o(t[r++])];return r&&r==i?e:void 0}},3052:(e,t,r)=>{var n=r(3246),o=r(7396);e.exports=function(e,t,r){var i=t(e);return o(e)?i:n(i,r(e))}},8072:(e,t,r)=>{var n=r(8190),o=r(835),i=r(8786),s=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":s&&s in Object(e)?o(e):i(e)}},4315:(e,t,r)=>{var n=r(8072),o=r(5834);e.exports=function(e){return o(e)&&"[object Arguments]"==n(e)}},277:(e,t,r)=>{var n=r(3434),o=r(5834);e.exports=function(e){return o(e)&&"[object Map]"==n(e)}},5836:(e,t,r)=>{var n=r(470),o=r(8178),i=r(2861),s=r(3288),a=/^\[object .+?Constructor\]$/,c=Function.prototype,u=Object.prototype,l=c.toString,h=u.hasOwnProperty,d=RegExp("^"+l.call(h).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||o(e))&&(n(e)?d:a).test(s(e))}},2887:(e,t,r)=>{var n=r(3434),o=r(5834);e.exports=function(e){return o(e)&&"[object Set]"==n(e)}},1349:(e,t,r)=>{var n=r(8072),o=r(6970),i=r(5834),s={};s["[object Float32Array]"]=s["[object Float64Array]"]=s["[object Int8Array]"]=s["[object Int16Array]"]=s["[object Int32Array]"]=s["[object Uint8Array]"]=s["[object Uint8ClampedArray]"]=s["[object Uint16Array]"]=s["[object Uint32Array]"]=!0,s["[object Arguments]"]=s["[object Array]"]=s["[object ArrayBuffer]"]=s["[object Boolean]"]=s["[object DataView]"]=s["[object Date]"]=s["[object Error]"]=s["[object Function]"]=s["[object Map]"]=s["[object Number]"]=s["[object Object]"]=s["[object RegExp]"]=s["[object Set]"]=s["[object String]"]=s["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&o(e.length)&&!!s[n(e)]}},1265:(e,t,r)=>{var n=r(3848),o=r(9161),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return o(e);var t=[];for(var r in Object(e))i.call(e,r)&&"constructor"!=r&&t.push(r);return t}},2075:(e,t,r)=>{var n=r(2861),o=r(3848),i=r(3854),s=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return i(e);var t=o(e),r=[];for(var a in e)("constructor"!=a||!t&&s.call(e,a))&&r.push(a);return r}},5433:(e,t,r)=>{var n=r(4067),o=r(1380),i=r(4607),s=r(2861),a=r(82);e.exports=function(e,t,r,c){if(!s(e))return e;for(var u=-1,l=(t=o(t,e)).length,h=l-1,d=e;null!=d&&++u<l;){var p=a(t[u]),f=r;if("__proto__"===p||"constructor"===p||"prototype"===p)return e;if(u!=h){var v=d[p];void 0===(f=c?c(v,p,d):void 0)&&(f=s(v)?v:i(t[u+1])?[]:{})}n(d,p,f),d=d[p]}return e}},5770:e=>{e.exports=function(e,t,r){var n=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(r=r>o?o:r)<0&&(r+=o),o=t>r?0:r-t>>>0,t>>>=0;for(var i=Array(o);++n<o;)i[n]=e[n+t];return i}},8056:e=>{e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},1169:(e,t,r)=>{var n=r(8190),o=r(534),i=r(7396),s=r(3596),a=n?n.prototype:void 0,c=a?a.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return o(t,e)+"";if(s(t))return c?c.call(t):"";var r=t+"";return"0"==r&&1/t==-1/0?"-0":r}},7254:e=>{e.exports=function(e){return function(t){return e(t)}}},1366:(e,t,r)=>{var n=r(1380),o=r(1711),i=r(7668),s=r(82);e.exports=function(e,t){return t=n(t,e),null==(e=i(e,t))||delete e[s(o(t))]}},1380:(e,t,r)=>{var n=r(7396),o=r(7090),i=r(2254),s=r(6189);e.exports=function(e,t){return n(e)?e:o(e,t)?[e]:i(s(e))}},2219:(e,t,r)=>{var n=r(563);e.exports=function(e){var t=new e.constructor(e.byteLength);return new n(t).set(new n(e)),t}},2579:(e,t,r)=>{e=r.nmd(e);var n=r(8594),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,s=i&&i.exports===o?n.Buffer:void 0,a=s?s.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var r=e.length,n=a?a(r):new e.constructor(r);return e.copy(n),n}},5892:(e,t,r)=>{var n=r(2219);e.exports=function(e,t){var r=t?n(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.byteLength)}},8778:e=>{var t=/\w*$/;e.exports=function(e){var r=new e.constructor(e.source,t.exec(e));return r.lastIndex=e.lastIndex,r}},5380:(e,t,r)=>{var n=r(8190),o=n?n.prototype:void 0,i=o?o.valueOf:void 0;e.exports=function(e){return i?Object(i.call(e)):{}}},5639:(e,t,r)=>{var n=r(2219);e.exports=function(e,t){var r=t?n(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}},7895:e=>{e.exports=function(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t}},1560:(e,t,r)=>{var n=r(4067),o=r(4622);e.exports=function(e,t,r,i){var s=!r;r||(r={});for(var a=-1,c=t.length;++a<c;){var u=t[a],l=i?i(r[u],e[u],u,r,e):void 0;void 0===l&&(l=e[u]),s?o(r,u,l):n(r,u,l)}return r}},801:(e,t,r)=>{var n=r(1560),o=r(18);e.exports=function(e,t){return n(e,o(e),t)}},6538:(e,t,r)=>{var n=r(1560),o=r(4546);e.exports=function(e,t){return n(e,o(e),t)}},2289:(e,t,r)=>{var n=r(8594)["__core-js_shared__"];e.exports=n},3446:(e,t,r)=>{var n=r(8663),o=function(){try{var e=n(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();e.exports=o},5177:(e,t,r)=>{var n="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;e.exports=n},1984:(e,t,r)=>{var n=r(3052),o=r(18),i=r(5683);e.exports=function(e){return n(e,i,o)}},3573:(e,t,r)=>{var n=r(3052),o=r(4546),i=r(4344);e.exports=function(e){return n(e,i,o)}},3261:(e,t,r)=>{var n=r(9491);e.exports=function(e,t){var r=e.__data__;return n(t)?r["string"==typeof t?"string":"hash"]:r.map}},8663:(e,t,r)=>{var n=r(5836),o=r(9040);e.exports=function(e,t){var r=o(e,t);return n(r)?r:void 0}},8591:(e,t,r)=>{var n=r(8952)(Object.getPrototypeOf,Object);e.exports=n},835:(e,t,r)=>{var n=r(8190),o=Object.prototype,i=o.hasOwnProperty,s=o.toString,a=n?n.toStringTag:void 0;e.exports=function(e){var t=i.call(e,a),r=e[a];try{e[a]=void 0;var n=!0}catch(e){}var o=s.call(e);return n&&(t?e[a]=r:delete e[a]),o}},18:(e,t,r)=>{var n=r(4799),o=r(8861),i=Object.prototype.propertyIsEnumerable,s=Object.getOwnPropertySymbols,a=s?function(e){return null==e?[]:(e=Object(e),n(s(e),(function(t){return i.call(e,t)})))}:o;e.exports=a},4546:(e,t,r)=>{var n=r(3246),o=r(8591),i=r(18),s=r(8861),a=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)n(t,i(e)),e=o(e);return t}:s;e.exports=a},3434:(e,t,r)=>{var n=r(9300),o=r(1107),i=r(40),s=r(2482),a=r(2538),c=r(8072),u=r(3288),l="[object Map]",h="[object Promise]",d="[object Set]",p="[object WeakMap]",f="[object DataView]",v=u(n),E=u(o),g=u(i),I=u(s),b=u(a),m=c;(n&&m(new n(new ArrayBuffer(1)))!=f||o&&m(new o)!=l||i&&m(i.resolve())!=h||s&&m(new s)!=d||a&&m(new a)!=p)&&(m=function(e){var t=c(e),r="[object Object]"==t?e.constructor:void 0,n=r?u(r):"";if(n)switch(n){case v:return f;case E:return l;case g:return h;case I:return d;case b:return p}return t}),e.exports=m},9040:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},6253:(e,t,r)=>{var n=r(1934);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},1629:e=>{e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},7332:(e,t,r)=>{var n=r(1934),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(t,e)?t[e]:void 0}},4641:(e,t,r)=>{var n=r(1934),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:o.call(t,e)}},4383:(e,t,r)=>{var n=r(1934);e.exports=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=n&&void 0===t?"__lodash_hash_undefined__":t,this}},2054:e=>{var t=Object.prototype.hasOwnProperty;e.exports=function(e){var r=e.length,n=new e.constructor(r);return r&&"string"==typeof e[0]&&t.call(e,"index")&&(n.index=e.index,n.input=e.input),n}},3822:(e,t,r)=>{var n=r(2219),o=r(5892),i=r(8778),s=r(5380),a=r(5639);e.exports=function(e,t,r){var c=e.constructor;switch(t){case"[object ArrayBuffer]":return n(e);case"[object Boolean]":case"[object Date]":return new c(+e);case"[object DataView]":return o(e,r);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return a(e,r);case"[object Map]":case"[object Set]":return new c;case"[object Number]":case"[object String]":return new c(e);case"[object RegExp]":return i(e);case"[object Symbol]":return s(e)}}},2792:(e,t,r)=>{var n=r(2726),o=r(8591),i=r(3848);e.exports=function(e){return"function"!=typeof e.constructor||i(e)?{}:n(o(e))}},4607:e=>{var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}},7090:(e,t,r)=>{var n=r(7396),o=r(3596),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,s=/^\w*$/;e.exports=function(e,t){if(n(e))return!1;var r=typeof e;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=e&&!o(e))||s.test(e)||!i.test(e)||null!=t&&e in Object(t)}},9491:e=>{e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},8178:(e,t,r)=>{var n,o=r(2289),i=(n=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"";e.exports=function(e){return!!i&&i in e}},3848:e=>{var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}},3371:e=>{e.exports=function(){this.__data__=[],this.size=0}},5827:(e,t,r)=>{var n=r(2694),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,r=n(t,e);return!(r<0||(r==t.length-1?t.pop():o.call(t,r,1),--this.size,0))}},4662:(e,t,r)=>{var n=r(2694);e.exports=function(e){var t=this.__data__,r=n(t,e);return r<0?void 0:t[r][1]}},8109:(e,t,r)=>{var n=r(2694);e.exports=function(e){return n(this.__data__,e)>-1}},5660:(e,t,r)=>{var n=r(2694);e.exports=function(e,t){var r=this.__data__,o=n(r,e);return o<0?(++this.size,r.push([e,t])):r[o][1]=t,this}},256:(e,t,r)=>{var n=r(2648),o=r(819),i=r(1107);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},2462:(e,t,r)=>{var n=r(3261);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=t?1:0,t}},7718:(e,t,r)=>{var n=r(3261);e.exports=function(e){return n(this,e).get(e)}},1477:(e,t,r)=>{var n=r(3261);e.exports=function(e){return n(this,e).has(e)}},5466:(e,t,r)=>{var n=r(3261);e.exports=function(e,t){var r=n(this,e),o=r.size;return r.set(e,t),this.size+=r.size==o?0:1,this}},3624:(e,t,r)=>{var n=r(426);e.exports=function(e){var t=n(e,(function(e){return 500===r.size&&r.clear(),e})),r=t.cache;return t}},1934:(e,t,r)=>{var n=r(8663)(Object,"create");e.exports=n},9161:(e,t,r)=>{var n=r(8952)(Object.keys,Object);e.exports=n},3854:e=>{e.exports=function(e){var t=[];if(null!=e)for(var r in Object(e))t.push(r);return t}},7510:(e,t,r)=>{e=r.nmd(e);var n=r(5177),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,s=i&&i.exports===o&&n.process,a=function(){try{return i&&i.require&&i.require("util").types||s&&s.binding&&s.binding("util")}catch(e){}}();e.exports=a},8786:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},8952:e=>{e.exports=function(e,t){return function(r){return e(t(r))}}},7668:(e,t,r)=>{var n=r(8081),o=r(5770);e.exports=function(e,t){return t.length<2?e:n(e,o(t,0,-1))}},8594:(e,t,r)=>{var n=r(5177),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();e.exports=i},1363:(e,t,r)=>{var n=r(819);e.exports=function(){this.__data__=new n,this.size=0}},5406:e=>{e.exports=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}},3145:e=>{e.exports=function(e){return this.__data__.get(e)}},8189:e=>{e.exports=function(e){return this.__data__.has(e)}},7502:(e,t,r)=>{var n=r(819),o=r(1107),i=r(705);e.exports=function(e,t){var r=this.__data__;if(r instanceof n){var s=r.__data__;if(!o||s.length<199)return s.push([e,t]),this.size=++r.size,this;r=this.__data__=new i(s)}return r.set(e,t),this.size=r.size,this}},2254:(e,t,r)=>{var n=r(3624),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,s=n((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(o,(function(e,r,n,o){t.push(n?o.replace(i,"$1"):r||e)})),t}));e.exports=s},82:(e,t,r)=>{var n=r(3596);e.exports=function(e){if("string"==typeof e||n(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}},3288:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},7657:(e,t,r)=>{var n=r(4420);e.exports=function(e){return n(e,5)}},6344:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},1819:(e,t,r)=>{var n=r(4315),o=r(5834),i=Object.prototype,s=i.hasOwnProperty,a=i.propertyIsEnumerable,c=n(function(){return arguments}())?n:function(e){return o(e)&&s.call(e,"callee")&&!a.call(e,"callee")};e.exports=c},7396:e=>{var t=Array.isArray;e.exports=t},9369:(e,t,r)=>{var n=r(470),o=r(6970);e.exports=function(e){return null!=e&&o(e.length)&&!n(e)}},9153:(e,t,r)=>{e=r.nmd(e);var n=r(8594),o=r(3791),i=t&&!t.nodeType&&t,s=i&&e&&!e.nodeType&&e,a=s&&s.exports===i?n.Buffer:void 0,c=(a?a.isBuffer:void 0)||o;e.exports=c},470:(e,t,r)=>{var n=r(8072),o=r(2861);e.exports=function(e){if(!o(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},6970:e=>{e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},4583:(e,t,r)=>{var n=r(277),o=r(7254),i=r(7510),s=i&&i.isMap,a=s?o(s):n;e.exports=a},2861:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},5834:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},3493:(e,t,r)=>{var n=r(2887),o=r(7254),i=r(7510),s=i&&i.isSet,a=s?o(s):n;e.exports=a},3596:(e,t,r)=>{var n=r(8072),o=r(5834);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==n(e)}},5780:(e,t,r)=>{var n=r(1349),o=r(7254),i=r(7510),s=i&&i.isTypedArray,a=s?o(s):n;e.exports=a},5683:(e,t,r)=>{var n=r(7471),o=r(1265),i=r(9369);e.exports=function(e){return i(e)?n(e):o(e)}},4344:(e,t,r)=>{var n=r(7471),o=r(2075),i=r(9369);e.exports=function(e){return i(e)?n(e,!0):o(e)}},1711:e=>{e.exports=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}},426:(e,t,r)=>{var n=r(705);function o(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var r=function(){var n=arguments,o=t?t.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var s=e.apply(this,n);return r.cache=i.set(o,s)||i,s};return r.cache=new(o.Cache||n),r}o.Cache=n,e.exports=o},8705:(e,t,r)=>{var n=r(5433);e.exports=function(e,t,r){return null==e?e:n(e,t,r)}},8861:e=>{e.exports=function(){return[]}},3791:e=>{e.exports=function(){return!1}},6189:(e,t,r)=>{var n=r(1169);e.exports=function(e){return null==e?"":n(e)}},8712:(e,t,r)=>{var n=r(1366);e.exports=function(e,t){return null==e||n(e,t)}},9063:(e,t,r)=>{"use strict";r.r(t),r.d(t,{registerRealtime:()=>q});var n=r(8705),o=r.n(n),i=r(8712),s=r.n(i),a=r(7657),c=r.n(a);function u(e){return void 0===e&&(e=""),"".concat(e?"".concat(e,"_"):"").concat(+new Date,"_").concat(Math.random())}var l,h,d=function(e){this.close=e.close,this.onChange=e.onChange,this.onError=e.onError,e.debug&&Object.defineProperty(this,"virtualClient",{get:function(){return e.virtualClient}})},p=function(e){var t,r,n=e.id,o=e.docChanges,i=e.docs,s=e.msgType,a=e.type;Object.defineProperties(this,{id:{get:function(){return n},enumerable:!0},docChanges:{get:function(){return t||(t=JSON.parse(JSON.stringify(o))),t},enumerable:!0},docs:{get:function(){return r||(r=JSON.parse(JSON.stringify(i))),r},enumerable:!0},msgType:{get:function(){return s},enumerable:!0},type:{get:function(){return a},enumerable:!0}})},f=(l=function(e,t){return l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},l(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}l(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),v=function(e){function t(t){var r=e.call(this,"Watch Error ".concat(JSON.stringify(t.msgData)," (requestid: ").concat(t.requestId,")"))||this;return r.isRealtimeErrorMessageError=!0,r.payload=t,r}return f(t,e),t}(Error),E=function(e){return null==e?void 0:e.isRealtimeErrorMessageError},g=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type="timeout",t.payload=null,t.generic=!0,t}return f(t,e),t}(Error),I=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type="cancelled",t.payload=null,t.generic=!0,t}return f(t,e),t}(Error),b=function(e){function t(t){var r=e.call(this,t.errMsg)||this;return r.errCode="UNKNOWN_ERROR",Object.defineProperties(r,{message:{get:function(){return"errCode: ".concat(this.errCode," ").concat(m[this.errCode]||""," | errMsg: ").concat(this.errMsg)},set:function(e){this.errMsg=e}}}),r.errCode=t.errCode||"UNKNOWN_ERROR",r.errMsg=t.errMsg,r}return f(t,e),Object.defineProperty(t.prototype,"message",{get:function(){return"errCode: ".concat(this.errCode," | errMsg: ").concat(this.errMsg)},set:function(e){this.errMsg=e},enumerable:!1,configurable:!0}),t}(Error),m={UNKNOWN_ERROR:"UNKNOWN_ERROR",SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL:"SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL",SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL:"SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL",SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL:"SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL",SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL:"SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL",SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG:"SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG",SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA:"SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA",SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR:"SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR",SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED:"SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED",SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL:"SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL",SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR:"SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR"},w=function(e){return void 0===e&&(e=0),new Promise((function(t){return setTimeout(t,e)}))},y=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function s(e){try{c(n.next(e))}catch(e){i(e)}}function a(e){try{c(n.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}c((n=n.apply(e,t||[])).next())}))},_=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(c){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((o=(o=s.trys).length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}},S=function(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};!function(e){e.LOGGINGIN="LOGGINGIN",e.INITING="INITING",e.REBUILDING="REBUILDING",e.ACTIVE="ACTIVE",e.ERRORED="ERRORED",e.CLOSING="CLOSING",e.CLOSED="CLOSED",e.PAUSED="PAUSED",e.RESUMING="RESUMING"}(h||(h={}));var T=function(){function e(e){var t=this;this.watchStatus=h.INITING,this.wsLogin=function(e,r){return y(t,void 0,void 0,(function(){var t;return _(this,(function(n){switch(n.label){case 0:return this.watchStatus=h.LOGGINGIN,[4,this.login(e,r)];case 1:return t=n.sent(),this.envId||(this.envId=t.envId),[2,t]}}))}))},this.initWatch=function(e){return y(t,void 0,void 0,(function(){var t,r=this;return _(this,(function(n){switch(n.label){case 0:if(null!==this.initWatchPromise&&void 0!==this.initWatchPromise)return[2,this.initWatchPromise];this.initWatchPromise=new Promise((function(t,n){y(r,void 0,void 0,(function(){var r,o,i,s,a,c,l,d,f,v;return _(this,(function(E){switch(E.label){case 0:return E.trys.push([0,3,,4]),this.watchStatus===h.PAUSED?(console.log("[realtime] initWatch cancelled on pause"),[2,t()]):[4,this.wsLogin(this.envId,e)];case 1:return r=E.sent().envId,this.watchStatus===h.PAUSED?(console.log("[realtime] initWatch cancelled on pause"),[2,t()]):(this.watchStatus=h.INITING,o={watchId:this.watchId,requestId:u(),msgType:"INIT_WATCH",msgData:{envId:r,collName:this.collectionName,query:this.query,limit:this.limit,orderBy:this.orderBy}},[4,this.send({msg:o,waitResponse:!0,skipOnMessage:!0,timeout:1e4})]);case 2:if(i=E.sent(),s=i.msgData,a=s.events,c=s.currEvent,this.sessionInfo={queryID:i.msgData.queryID,currentEventId:c-1,currentDocs:[]},a.length>0){for(l=0,d=a;l<d.length;l++)d[l].ID=c;this.handleServerEvents(i)}else this.sessionInfo.currentEventId=c,f=new p({id:c,docChanges:[],docs:[],type:"init"}),this.listener.onChange(f),this.scheduleSendACK();return this.onWatchStart(this,this.sessionInfo.queryID),this.watchStatus=h.ACTIVE,this.availableRetries.INIT_WATCH=2,t(),[3,4];case 3:return v=E.sent(),this.handleWatchEstablishmentError(v,{operationName:"INIT_WATCH",resolve:t,reject:n}),[3,4];case 4:return[2]}}))}))})),t=!1,n.label=1;case 1:return n.trys.push([1,,3,4]),[4,this.initWatchPromise];case 2:return n.sent(),t=!0,[3,4];case 3:return this.initWatchPromise=void 0,[7];case 4:return console.log("[realtime] initWatch ".concat(t?"success":"fail")),[2]}}))}))},this.rebuildWatch=function(e){return y(t,void 0,void 0,(function(){var t,r=this;return _(this,(function(n){switch(n.label){case 0:if(null!==this.rebuildWatchPromise&&void 0!==this.rebuildWatchPromise)return[2,this.rebuildWatchPromise];this.rebuildWatchPromise=new Promise((function(t,n){y(r,void 0,void 0,(function(){var r,o,i,s;return _(this,(function(a){switch(a.label){case 0:return a.trys.push([0,3,,4]),this.watchStatus===h.PAUSED?(console.log("[realtime] rebuildWatch cancelled on pause"),[2,t()]):[4,this.wsLogin(this.envId,e)];case 1:if(r=a.sent().envId,!this.sessionInfo)throw new Error("can not rebuildWatch without a successful initWatch (lack of sessionInfo)");return this.watchStatus===h.PAUSED?(console.log("[realtime] rebuildWatch cancelled on pause"),[2,t()]):(this.watchStatus=h.REBUILDING,o={watchId:this.watchId,requestId:u(),msgType:"REBUILD_WATCH",msgData:{envId:r,collName:this.collectionName,queryID:this.sessionInfo.queryID,eventID:this.sessionInfo.currentEventId}},[4,this.send({msg:o,waitResponse:!0,skipOnMessage:!1,timeout:1e4})]);case 2:return i=a.sent(),this.handleServerEvents(i),this.watchStatus=h.ACTIVE,this.availableRetries.REBUILD_WATCH=2,t(),[3,4];case 3:return s=a.sent(),this.handleWatchEstablishmentError(s,{operationName:"REBUILD_WATCH",resolve:t,reject:n}),[3,4];case 4:return[2]}}))}))})),t=!1,n.label=1;case 1:return n.trys.push([1,,3,4]),[4,this.rebuildWatchPromise];case 2:return n.sent(),t=!0,[3,4];case 3:return this.rebuildWatchPromise=void 0,[7];case 4:return console.log("[realtime] rebuildWatch ".concat(t?"success":"fail")),[2]}}))}))},this.handleWatchEstablishmentError=function(e,r){return y(t,void 0,void 0,(function(){var t,n,o,i=this;return _(this,(function(s){return t="INIT_WATCH"===r.operationName,n=function(){i.closeWithError(new b({errCode:t?m.SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL:m.SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL,errMsg:e})),r.reject(e)},o=function(e){i.useRetryTicket(r.operationName)?t?(i.initWatchPromise=void 0,r.resolve(i.initWatch(e))):(i.rebuildWatchPromise=void 0,r.resolve(i.rebuildWatch(e))):n()},this.handleCommonError(e,{onSignError:function(){return o(!0)},onTimeoutError:function(){return o(!1)},onNotRetryableError:n,onCancelledError:r.reject,onUnknownError:function(){y(i,void 0,void 0,(function(){var e,t=this;return _(this,(function(n){switch(n.label){case 0:return n.trys.push([0,8,,9]),e=function(){return y(t,void 0,void 0,(function(){return _(this,(function(e){switch(e.label){case 0:return this.pause(),[4,this.onceWSConnected()];case 1:return e.sent(),o(!0),[2]}}))}))},this.isWSConnected()?[3,2]:[4,e()];case 1:return n.sent(),[3,7];case 2:return[4,w(100)];case 3:return n.sent(),this.watchStatus!==h.PAUSED?[3,4]:(r.reject(new I("".concat(r.operationName," cancelled due to pause after unknownError"))),[3,7]);case 4:return this.isWSConnected()?[3,6]:[4,e()];case 5:return n.sent(),[3,7];case 6:o(!1),n.label=7;case 7:return[3,9];case 8:return n.sent(),o(!0),[3,9];case 9:return[2]}}))}))}}),[2]}))}))},this.closeWatch=function(){return y(t,void 0,void 0,(function(){var e,t,r;return _(this,(function(n){switch(n.label){case 0:if(e=this.sessionInfo?this.sessionInfo.queryID:"",this.watchStatus!==h.ACTIVE)return this.watchStatus=h.CLOSED,this.onWatchClose(this,e),[2];n.label=1;case 1:return n.trys.push([1,3,4,5]),this.watchStatus=h.CLOSING,t={watchId:this.watchId,requestId:u(),msgType:"CLOSE_WATCH",msgData:null},[4,this.send({msg:t})];case 2:return n.sent(),this.sessionInfo=void 0,this.watchStatus=h.CLOSED,[3,5];case 3:return r=n.sent(),this.closeWithError(new b({errCode:m.SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL,errMsg:r})),[3,5];case 4:return this.onWatchClose(this,e),[7];case 5:return[2]}}))}))},this.scheduleSendACK=function(){t.clearACKSchedule(),t.ackTimeoutId=setTimeout((function(){t.waitExpectedTimeoutId?t.scheduleSendACK():t.sendACK()}),1e4)},this.clearACKSchedule=function(){t.ackTimeoutId&&clearTimeout(t.ackTimeoutId)},this.sendACK=function(){return y(t,void 0,void 0,(function(){var e,t,r;return _(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),this.watchStatus!==h.ACTIVE?(this.scheduleSendACK(),[2]):this.sessionInfo?(e={watchId:this.watchId,requestId:u(),msgType:"CHECK_LAST",msgData:{queryID:this.sessionInfo.queryID,eventID:this.sessionInfo.currentEventId}},[4,this.send({msg:e})]):(console.warn("[realtime listener] can not send ack without a successful initWatch (lack of sessionInfo)"),[2]);case 1:return n.sent(),this.scheduleSendACK(),[3,3];case 2:if(t=n.sent(),E(t))switch((r=t.payload).msgData.code){case"CHECK_LOGIN_FAILED":case"SIGN_EXPIRED_ERROR":case"SIGN_INVALID_ERROR":case"SIGN_PARAM_INVALID":return this.rebuildWatch(),[2];case"QUERYID_INVALID_ERROR":case"SYS_ERR":case"INVALIID_ENV":case"COLLECTION_PERMISSION_DENIED":return this.closeWithError(new b({errCode:m.SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL,errMsg:r.msgData.code})),[2]}return this.availableRetries.CHECK_LAST&&this.availableRetries.CHECK_LAST>0?(this.availableRetries.CHECK_LAST-=1,this.scheduleSendACK()):this.closeWithError(new b({errCode:m.SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL,errMsg:t})),[3,3];case 3:return[2]}}))}))},this.handleCommonError=function(e,t){if(E(e))switch(e.payload.msgData.code){case"CHECK_LOGIN_FAILED":case"SIGN_EXPIRED_ERROR":case"SIGN_INVALID_ERROR":case"SIGN_PARAM_INVALID":return void t.onSignError(e);default:return void t.onNotRetryableError(e)}else{if(function(e){return"timeout"===e.type}(e))return void t.onTimeoutError(e);if(function(e){return"cancelled"===e.type}(e))return void t.onCancelledError(e)}t.onUnknownError(e)},this.watchId="watchid_".concat(+new Date,"_").concat(Math.random()),this.envId=e.envId,this.collectionName=e.collectionName,this.query=e.query,this.limit=e.limit,this.orderBy=e.orderBy,this.send=e.send,this.login=e.login,this.isWSConnected=e.isWSConnected,this.onceWSConnected=e.onceWSConnected,this.getWaitExpectedTimeoutLength=e.getWaitExpectedTimeoutLength,this.onWatchStart=e.onWatchStart,this.onWatchClose=e.onWatchClose,this.debug=e.debug,this.availableRetries={INIT_WATCH:2,REBUILD_WATCH:2,CHECK_LAST:2},this.listener=new d({close:function(){t.closeWatch()},onChange:e.onChange,onError:e.onError,debug:this.debug,virtualClient:this}),this.initWatch()}return e.prototype.onMessage=function(e){var t=this;switch(this.watchStatus){case h.PAUSED:if("ERROR"!==e.msgType)return;break;case h.LOGGINGIN:case h.INITING:case h.REBUILDING:return void console.warn("[realtime listener] internal non-fatal error: unexpected message received while ".concat(this.watchStatus));case h.CLOSED:return void console.warn("[realtime listener] internal non-fatal error: unexpected message received when the watch has closed");case h.ERRORED:return void console.warn("[realtime listener] internal non-fatal error: unexpected message received when the watch has ended with error")}if(this.sessionInfo)switch(this.scheduleSendACK(),e.msgType){case"NEXT_EVENT":console.warn("nextevent ".concat(e.msgData.currEvent," ignored"),e),this.handleServerEvents(e);break;case"CHECK_EVENT":this.sessionInfo.currentEventId<e.msgData.currEvent&&(this.sessionInfo.expectEventId=e.msgData.currEvent,this.clearWaitExpectedEvent(),this.waitExpectedTimeoutId=setTimeout((function(){t.rebuildWatch()}),this.getWaitExpectedTimeoutLength()),console.log("[realtime] waitExpectedTimeoutLength ".concat(this.getWaitExpectedTimeoutLength())));break;case"ERROR":this.closeWithError(new b({errCode:m.SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG,errMsg:"".concat(e.msgData.code," - ").concat(e.msgData.message)}));break;default:console.warn("[realtime listener] virtual client receive unexpected msg ".concat(e.msgType,": "),e)}else console.warn("[realtime listener] internal non-fatal error: sessionInfo not found while message is received.")},e.prototype.closeWithError=function(e){var t;this.watchStatus=h.ERRORED,this.clearACKSchedule(),this.listener.onError(e),this.onWatchClose(this,(null===(t=this.sessionInfo)||void 0===t?void 0:t.queryID)||""),console.log("[realtime] client closed (".concat(this.collectionName," ").concat(this.query,") (watchId ").concat(this.watchId,")"))},e.prototype.pause=function(){this.watchStatus=h.PAUSED,console.log("[realtime] client paused (".concat(this.collectionName," ").concat(this.query,") (watchId ").concat(this.watchId,")"))},e.prototype.resume=function(){return y(this,void 0,void 0,(function(){var e;return _(this,(function(t){switch(t.label){case 0:this.watchStatus=h.RESUMING,console.log("[realtime] client resuming with ".concat(this.sessionInfo?"REBUILD_WATCH":"INIT_WATCH"," (").concat(this.collectionName," ").concat(this.query,") (").concat(this.watchId,")")),t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.sessionInfo?this.rebuildWatch():this.initWatch()];case 2:return t.sent(),console.log("[realtime] client successfully resumed (".concat(this.collectionName," ").concat(this.query,") (").concat(this.watchId,")")),[3,4];case 3:return e=t.sent(),console.error("[realtime] client resume failed (".concat(this.collectionName," ").concat(this.query,") (").concat(this.watchId,")"),e),[3,4];case 4:return[2]}}))}))},e.prototype.useRetryTicket=function(e){return!!(this.availableRetries[e]&&this.availableRetries[e]>0)&&(this.availableRetries[e]-=1,console.log("[realtime] ".concat(e," use a retry ticket, now only ").concat(this.availableRetries[e]," retry left")),!0)},e.prototype.handleServerEvents=function(e){return y(this,void 0,void 0,(function(){var t;return _(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),this.scheduleSendACK(),[4,this.handleServerEventsInternel(e)];case 1:return r.sent(),this.postHandleServerEventsValidityCheck(e),[3,3];case 2:throw t=r.sent(),console.error("[realtime listener] internal non-fatal error: handle server events failed with error: ",t),t;case 3:return[2]}}))}))},e.prototype.handleServerEventsInternel=function(e){return y(this,void 0,void 0,(function(){var t,r,n,i,a,u,l,h,d,f,v,E;return _(this,(function(g){switch(g.label){case 0:if(t=e.requestId,r=e.msgData.events,n=e.msgType,!r.length||!this.sessionInfo)return[2];i=this.sessionInfo;try{a=r.map(A)}catch(e){return this.closeWithError(new b({errCode:m.SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA,errMsg:e})),[2]}u=S([],i.currentDocs,!0),l=!1,h=function(r,h){var f,v,E,g,I,w,y,T,A,R,C,x;return _(this,(function(_){switch(_.label){case 0:return f=a[r],i.currentEventId>=f.id?(!a[r-1]||f.id>a[r-1].id?console.warn("[realtime] duplicate event received, cur ".concat(i.currentEventId," but got ").concat(f.id)):console.error("[realtime listener] server non-fatal error: events out of order (the latter event's id is smaller than that of the former) (requestId ".concat(t,")")),[2,"continue"]):[3,1];case 1:if(i.currentEventId!==f.id-1)return[3,2];switch(f.dataType){case"update":if(!f.doc)switch(f.queueType){case"update":case"dequeue":if(v=u.find((function(e){return e._id===f.docId})),v){if(E=c()(v),f.updatedFields&&Object.keys(f.updatedFields).forEach((function(e){o()(E,e,f.updatedFields[e])})),f.removedFields)for(g=0,I=f.removedFields;g<I.length;g++)w=I[g],s()(E,w);f.doc=E}else console.error("[realtime listener] internal non-fatal server error: unexpected update dataType event where no doc is associated.");break;case"enqueue":throw T=new b({errCode:m.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,errMsg:'HandleServerEvents: full doc is not provided with dataType="update" and queueType="enqueue" (requestId '.concat(e.requestId,")")}),d.closeWithError(T),T}break;case"replace":if(!f.doc)throw T=new b({errCode:m.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,errMsg:'HandleServerEvents: full doc is not provided with dataType="replace" (requestId '.concat(e.requestId,")")}),d.closeWithError(T),T;break;case"remove":(y=u.find((function(e){return e._id===f.docId})))?f.doc=y:console.error("[realtime listener] internal non-fatal server error: unexpected remove event where no doc is associated.");break;case"limit":if(!f.doc)switch(f.queueType){case"dequeue":(y=u.find((function(e){return e._id===f.docId})))?f.doc=y:console.error("[realtime listener] internal non-fatal server error: unexpected limit dataType event where no doc is associated.");break;case"enqueue":throw T=new b({errCode:m.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,errMsg:'HandleServerEvents: full doc is not provided with dataType="limit" and queueType="enqueue" (requestId '.concat(e.requestId,")")}),d.closeWithError(T),T}}switch(f.queueType){case"init":l?u.push(f.doc):(l=!0,u=[f.doc]);break;case"enqueue":u.push(f.doc);break;case"dequeue":A=u.findIndex((function(e){return e._id===f.docId})),A>-1?u.splice(A,1):console.error("[realtime listener] internal non-fatal server error: unexpected dequeue event where no doc is associated.");break;case"update":A=u.findIndex((function(e){return e._id===f.docId})),A>-1?u[A]=f.doc:console.error("[realtime listener] internal non-fatal server error: unexpected queueType update event where no doc is associated.")}return(r===h-1||a[r+1]&&a[r+1].id!==f.id)&&(R=S([],u,!0),C=a.slice(0,r+1).filter((function(e){return e.id===f.id})),d.sessionInfo.currentEventId=f.id,d.sessionInfo.currentDocs=u,x=new p({id:f.id,docChanges:C,docs:R,msgType:n}),d.listener.onChange(x)),[3,4];case 2:return console.warn("[realtime listener] event received is out of order, cur ".concat(d.sessionInfo.currentEventId," but got ").concat(f.id)),[4,d.rebuildWatch()];case 3:return _.sent(),[2,{value:void 0}];case 4:return[2]}}))},d=this,f=0,v=a.length,g.label=1;case 1:return f<v?[5,h(f,v)]:[3,4];case 2:if("object"==typeof(E=g.sent()))return[2,E.value];g.label=3;case 3:return f++,[3,1];case 4:return[2]}}))}))},e.prototype.postHandleServerEventsValidityCheck=function(e){this.sessionInfo?(this.sessionInfo.expectEventId&&this.sessionInfo.currentEventId>=this.sessionInfo.expectEventId&&this.clearWaitExpectedEvent(),this.sessionInfo.currentEventId<e.msgData.currEvent&&console.warn("[realtime listener] internal non-fatal error: client eventId does not match with server event id after server event handling")):console.error("[realtime listener] internal non-fatal error: sessionInfo lost after server event handling, this should never occur")},e.prototype.clearWaitExpectedEvent=function(){this.waitExpectedTimeoutId&&(clearTimeout(this.waitExpectedTimeoutId),this.waitExpectedTimeoutId=void 0)},e}();function A(e){var t={id:e.ID,dataType:e.DataType,queueType:e.QueueType,docId:e.DocID,doc:e.Doc&&"{}"!==e.Doc?JSON.parse(e.Doc):void 0};return"update"===e.DataType&&(e.UpdatedFields&&(t.updatedFields=JSON.parse(e.UpdatedFields)),(e.removedFields||e.RemovedFields)&&(t.removedFields=JSON.parse(e.removedFields))),t}var R,C={1e3:{code:1e3,name:"Normal Closure",description:"Normal closure; the connection successfully completed whatever purpose for which it was created."},1001:{code:1001,name:"Going Away",description:"The endpoint is going away, either because of a server failure or because the browser is navigating away from the page that opened the connection."},1002:{code:1002,name:"Protocol Error",description:"The endpoint is terminating the connection due to a protocol error."},1003:{code:1003,name:"Unsupported Data",description:"The connection is being terminated because the endpoint received data of a type it cannot accept (for example, a text-only endpoint received binary data)."},1005:{code:1005,name:"No Status Received",description:"Indicates that no status code was provided even though one was expected."},1006:{code:1006,name:"Abnormal Closure",description:"Used to indicate that a connection was closed abnormally (that is, with no close frame being sent) when a status code is expected."},1007:{code:1007,name:"Invalid frame payload data",description:"The endpoint is terminating the connection because a message was received that contained inconsistent data (e.g., non-UTF-8 data within a text message)."},1008:{code:1008,name:"Policy Violation",description:"The endpoint is terminating the connection because it received a message that violates its policy. This is a generic status code, used when codes 1003 and 1009 are not suitable."},1009:{code:1009,name:"Message too big",description:"The endpoint is terminating the connection because a data frame was received that is too large."},1010:{code:1010,name:"Missing Extension",description:"The client is terminating the connection because it expected the server to negotiate one or more extension, but the server didn't."},1011:{code:1011,name:"Internal Error",description:"The server is terminating the connection because it encountered an unexpected condition that prevented it from fulfilling the request."},1012:{code:1012,name:"Service Restart",description:"The server is terminating the connection because it is restarting."},1013:{code:1013,name:"Try Again Later",description:"The server is terminating the connection due to a temporary condition, e.g. it is overloaded and is casting off some of its clients."},1014:{code:1014,name:"Bad Gateway",description:"The server was acting as a gateway or proxy and received an invalid response from the upstream server. This is similar to 502 HTTP Status Code."},1015:{code:1015,name:"TLS Handshake",description:"Indicates that the connection was closed due to a failure to perform a TLS handshake (e.g., the server certificate can't be verified)."},3e3:{code:3e3,name:"Reconnect WebSocket",description:"The client is terminating the connection because it wants to reconnect"},3001:{code:3001,name:"No Realtime Listeners",description:"The client is terminating the connection because no more realtime listeners exist"},3002:{code:3002,name:"Heartbeat Ping Error",description:"The client is terminating the connection due to its failure in sending heartbeat messages"},3003:{code:3003,name:"Heartbeat Pong Timeout Error",description:"The client is terminating the connection because no heartbeat response is received from the server"},3050:{code:3050,name:"Server Close",description:"The client is terminating the connection because no heartbeat response is received from the server"}};!function(e){e[e.NormalClosure=1e3]="NormalClosure",e[e.GoingAway=1001]="GoingAway",e[e.ProtocolError=1002]="ProtocolError",e[e.UnsupportedData=1003]="UnsupportedData",e[e.NoStatusReceived=1005]="NoStatusReceived",e[e.AbnormalClosure=1006]="AbnormalClosure",e[e.InvalidFramePayloadData=1007]="InvalidFramePayloadData",e[e.PolicyViolation=1008]="PolicyViolation",e[e.MessageTooBig=1009]="MessageTooBig",e[e.MissingExtension=1010]="MissingExtension",e[e.InternalError=1011]="InternalError",e[e.ServiceRestart=1012]="ServiceRestart",e[e.TryAgainLater=1013]="TryAgainLater",e[e.BadGateway=1014]="BadGateway",e[e.TLSHandshake=1015]="TLSHandshake",e[e.ReconnectWebSocket=3e3]="ReconnectWebSocket",e[e.NoRealtimeListeners=3001]="NoRealtimeListeners",e[e.HeartbeatPingError=3002]="HeartbeatPingError",e[e.HeartbeatPongTimeoutError=3003]="HeartbeatPongTimeoutError",e[e.NoAuthentication=3050]="NoAuthentication"}(R||(R={}));var x=function(e,t){var r=C[e],n=r?"".concat(r.name,", code ").concat(e,", reason ").concat(t||r.description):"code ".concat(e);return new b({errCode:m.SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED,errMsg:n})},D=null,N="web";function O(){return D}function L(){return N}function W(e){N=e}var j=function(){return j=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},j.apply(this,arguments)},P=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function s(e){try{c(n.next(e))}catch(e){i(e)}}function a(e){try{c(n.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}c((n=n.apply(e,t||[])).next())}))},M=function(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(c){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((o=(o=s.trys).length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}},k=function(){function e(e){var t=this;this.virtualWSClient=new Set,this.queryIdClientMap=new Map,this.watchIdClientMap=new Map,this.pingFailed=0,this.pongMissed=0,this.logins=new Map,this.wsReadySubsribers=[],this.wsResponseWait=new Map,this.rttObserved=[],this.send=function(e){return P(t,void 0,void 0,(function(){var t=this;return M(this,(function(r){return[2,new Promise((function(r,n){P(t,void 0,void 0,(function(){var t,o,i,s,a,c,u,l,h=this;return M(this,(function(d){switch(d.label){case 0:o=!1,i=!1,s=function(e){o=!0,t&&clearTimeout(t),r(e)},a=function(e){i=!0,t&&clearTimeout(t),n(e)},e.timeout&&(t=setTimeout((function(){P(h,void 0,void 0,(function(){return M(this,(function(e){switch(e.label){case 0:return o&&i?[3,2]:[4,w(0)];case 1:e.sent(),o&&i||a(new g("wsclient.send timedout")),e.label=2;case 2:return[2]}}))}))}),e.timeout)),d.label=1;case 1:return d.trys.push([1,8,,9]),void 0===this.wsInitPromise&&null===this.wsInitPromise?[3,3]:[4,this.wsInitPromise];case 2:d.sent(),d.label=3;case 3:if(!this.ws)return a(new Error("invalid state: ws connection not exists, can not send message")),[2];if(1!==this.ws.readyState)return a(new Error("ws readyState invalid: ".concat(this.ws.readyState,", can not send message"))),[2];e.waitResponse&&(c={resolve:s,reject:a,skipOnMessage:e.skipOnMessage},this.wsResponseWait.set(e.msg.requestId,c)),d.label=4;case 4:return d.trys.push([4,6,,7]),[4,this.ws.send(JSON.stringify(e.msg))];case 5:return d.sent(),e.waitResponse||s(void 0),[3,7];case 6:return(u=d.sent())&&(a(u),e.waitResponse&&this.wsResponseWait.delete(e.msg.requestId)),[3,7];case 7:return[3,9];case 8:return l=d.sent(),a(l),[3,9];case 9:return[2]}}))}))}))]}))}))},this.closeAllClients=function(e){t.virtualWSClient.forEach((function(t){t.closeWithError(e)}))},this.pauseClients=function(e){(e||t.virtualWSClient).forEach((function(e){e.pause()}))},this.resumeClients=function(e){(e||t.virtualWSClient).forEach((function(e){e.resume()}))},this.initWebSocketConnection=function(e,r){return void 0===r&&(r=t.maxReconnect),P(t,void 0,void 0,(function(){var t=this;return M(this,(function(n){switch(n.label){case 0:if(e&&this.reconnectState)return[2];if(e&&(this.reconnectState=!0),void 0!==this.wsInitPromise&&null!==this.wsInitPromise)return[2,this.wsInitPromise];e&&this.pauseClients(),this.close(R.ReconnectWebSocket),this.wsInitPromise=new Promise((function(n,o){P(t,void 0,void 0,(function(){var t,i,s=this;return M(this,(function(a){switch(a.label){case 0:return a.trys.push([0,6,,11]),[4,this.getWsSign()];case 1:return t=a.sent(),[4,new Promise((function(e){var r=t.wsUrl||"wss://tcb-ws.tencentcloudapi.com",n=O();s.ws=n?new n(r):new WebSocket(r),e(void 0)}))];case 2:return a.sent(),this.ws.connect?[4,this.ws.connect()]:[3,4];case 3:a.sent(),a.label=4;case 4:return[4,this.initWebSocketEvent()];case 5:return a.sent(),n(),e&&(this.resumeClients(),this.reconnectState=!1),[3,11];case 6:return i=a.sent(),console.error("[realtime] initWebSocketConnection connect fail",i),r>0?(this.wsInitPromise=void 0,[4,w(this.reconnectInterval)]):[3,9];case 7:a.sent(),e&&(this.reconnectState=!1),a.label=8;case 8:return n(this.initWebSocketConnection(e,r-1)),[3,10];case 9:o(i),e&&this.closeAllClients(new b({errCode:m.SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL,errMsg:i})),a.label=10;case 10:return[3,11];case 11:return[2]}}))}))})),n.label=1;case 1:return n.trys.push([1,3,4,5]),[4,this.wsInitPromise];case 2:return n.sent(),this.wsReadySubsribers.forEach((function(e){return(0,e.resolve)()})),[3,5];case 3:return n.sent(),this.wsReadySubsribers.forEach((function(e){return(0,e.reject)()})),[3,5];case 4:return this.wsInitPromise=void 0,this.wsReadySubsribers=[],[7];case 5:return[2]}}))}))},this.initWebSocketEvent=function(){return new Promise((function(e,r){if(!t.ws)throw new Error("can not initWebSocketEvent, ws not exists");var n=!1;t.ws.onopen=function(t){console.warn("[realtime] ws event: open",t),n=!0,e()},t.ws.onerror=function(e){t.logins=new Map,n?(console.error("[realtime] ws event: error",e),t.clearHeartbeat(),t.virtualWSClient.forEach((function(t){return t.closeWithError(new b({errCode:m.SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR,errMsg:e}))}))):(console.error("[realtime] ws open failed with ws event: error",e),r(e))},t.ws.onclose=function(e){switch(console.warn("[realtime] ws event: close",e),t.logins=new Map,t.clearHeartbeat(),e.code){case R.ReconnectWebSocket:case R.NoRealtimeListeners:break;case R.HeartbeatPingError:case R.HeartbeatPongTimeoutError:case R.NormalClosure:case R.AbnormalClosure:t.maxReconnect>0?t.initWebSocketConnection(!0,t.maxReconnect):t.closeAllClients(x(e.code));break;case R.NoAuthentication:t.closeAllClients(x(e.code,e.reason));break;default:t.maxReconnect>0?t.initWebSocketConnection(!0,t.maxReconnect):t.closeAllClients(x(e.code))}},t.ws.onmessage=function(e){var r,n=e.data;t.heartbeat();try{r=JSON.parse(n)}catch(e){throw new Error("[realtime] onMessage parse res.data error: ".concat(e))}if("ERROR"===r.msgType){var o=null;t.virtualWSClient.forEach((function(e){e.watchId===r.watchId&&(o=e)})),o&&o.listener.onError(r)}var i=t.wsResponseWait.get(r.requestId);if(i){try{"ERROR"===r.msgType?i.reject(new v(r)):i.resolve(r)}catch(e){console.error("ws onMessage responseWaitSpec.resolve(msg) errored:",e)}finally{t.wsResponseWait.delete(r.requestId)}if(i.skipOnMessage)return}if("PONG"!==r.msgType){var s=r.watchId&&t.watchIdClientMap.get(r.watchId);if(s)s.onMessage(r);else switch(console.error("[realtime] no realtime listener found responsible for watchId ".concat(r.watchId,": "),r),r.msgType){case"INIT_EVENT":case"NEXT_EVENT":case"CHECK_EVENT":(s=t.queryIdClientMap.get(r.msgData.queryID))&&s.onMessage(r);break;default:for(var a=0,c=Array.from(t.watchIdClientMap.entries());a<c.length;a++){c[a][1].onMessage(r);break}}}else if(t.lastPingSendTS){var u=Date.now()-t.lastPingSendTS;if(u>1e4)return void console.warn("[realtime] untrusted rtt observed: ".concat(u));t.rttObserved.length>=3&&t.rttObserved.splice(0,t.rttObserved.length-3+1),t.rttObserved.push(u)}},t.heartbeat()}))},this.isWSConnected=function(){return Boolean(t.ws&&1===t.ws.readyState)},this.onceWSConnected=function(){return P(t,void 0,void 0,(function(){var e=this;return M(this,(function(t){return this.isWSConnected()?[2]:null!==this.wsInitPromise&&void 0!==this.wsInitPromise?[2,this.wsInitPromise]:[2,new Promise((function(t,r){e.wsReadySubsribers.push({resolve:t,reject:r})}))]}))}))},this.webLogin=function(e,r){return P(t,void 0,void 0,(function(){var t,n,o,i,s,a,c,l,h=this;return M(this,(function(d){switch(d.label){case 0:if(!r)if(e){if(t=this.logins.get(e)){if(t.loggedIn&&t.loginResult)return[2,t.loginResult];if(null!==t.loggingInPromise&&void 0!==t.loggingInPromise)return[2,t.loggingInPromise]}}else if(null!==(null==(n=this.logins.get(""))?void 0:n.loggingInPromise)&&void 0!==(null==n?void 0:n.loggingInPromise))return[2,n.loggingInPromise];o=new Promise((function(e,t){P(h,void 0,void 0,(function(){var r,n,o,i,s;return M(this,(function(a){switch(a.label){case 0:return a.trys.push([0,3,,4]),[4,this.getWsSign()];case 1:return r=a.sent(),n={envId:r.envId||"",accessToken:"",referrer:"web",sdkVersion:"",dataVersion:""},o={watchId:void 0,requestId:u(),msgType:"LOGIN",msgData:n,exMsgData:{runtime:L(),signStr:r.signStr,secretVersion:r.secretVersion}},[4,this.send({msg:o,waitResponse:!0,skipOnMessage:!0,timeout:5e3})];case 2:return(i=a.sent()).msgData.code?t(new Error("".concat(i.msgData.code," ").concat(i.msgData.message))):e({envId:r.envId}),[3,4];case 3:return s=a.sent(),t(s),[3,4];case 4:return[2]}}))}))})),i=e&&this.logins.get(e),s=Date.now(),i?(i.loggedIn=!1,i.loggingInPromise=o,i.loginStartTS=s):(i={loggedIn:!1,loggingInPromise:o,loginStartTS:s},this.logins.set(e||"",i)),d.label=1;case 1:return d.trys.push([1,3,,4]),[4,o];case 2:if(a=d.sent(),(c=e&&this.logins.get(e))&&c===i&&c.loginStartTS===s)return i.loggedIn=!0,i.loggingInPromise=void 0,i.loginStartTS=void 0,i.loginResult=a,[2,a];if(c){if(c.loggedIn&&c.loginResult)return[2,c.loginResult];if(null!==c.loggingInPromise&&void 0!==c.loggingInPromise)return[2,c.loggingInPromise];throw new Error("ws unexpected login info")}throw new Error("ws login info reset");case 3:throw l=d.sent(),i.loggedIn=!1,i.loggingInPromise=void 0,i.loginStartTS=void 0,i.loginResult=void 0,l;case 4:return[2]}}))}))},this.getWsSign=function(){return P(t,void 0,void 0,(function(){var e,t,r,n,o,i,s;return M(this,(function(a){switch(a.label){case 0:return this.wsSign&&this.wsSign.expiredTs>Date.now()?[2,this.wsSign]:(e=Date.now()+6e4,[4,this.context.appConfig.request.send("auth.wsWebSign",{runtime:L()})]);case 1:if((t=a.sent()).code)throw new Error("[tcb-js-sdk] 获取实时数据推送登录票据失败: ".concat(t.code));if(t.data)return r=t.data,n=r.signStr,o=r.wsUrl,i=r.secretVersion,s=r.envId,[2,{signStr:n,wsUrl:o,secretVersion:i,envId:s,expiredTs:e}];throw new Error("[tcb-js-sdk] 获取实时数据推送登录票据失败")}}))}))},this.getWaitExpectedTimeoutLength=function(){return t.rttObserved.length?t.rttObserved.reduce((function(e,t){return e+t}))/t.rttObserved.length*1.5:5e3},this.ping=function(){return P(t,void 0,void 0,(function(){var e;return M(this,(function(t){switch(t.label){case 0:return e={watchId:void 0,requestId:u(),msgType:"PING",msgData:null},[4,this.send({msg:e})];case 1:return t.sent(),[2]}}))}))},this.onWatchStart=function(e,r){t.queryIdClientMap.set(r,e)},this.onWatchClose=function(e,r){r&&t.queryIdClientMap.delete(r),t.watchIdClientMap.delete(e.watchId),t.virtualWSClient.delete(e),t.virtualWSClient.size||t.close(R.NoRealtimeListeners)},this.maxReconnect=e.maxReconnect||5,this.reconnectInterval=e.reconnectInterval||1e4,this.context=e.context}return e.prototype.clearHeartbeat=function(){this.pingTimeoutId&&clearTimeout(this.pingTimeoutId),this.pongTimeoutId&&clearTimeout(this.pongTimeoutId)},e.prototype.close=function(e){this.clearHeartbeat(),this.ws&&(this.ws.close(e,C[e].name),this.ws=void 0)},e.prototype.watch=function(e){this.ws||void 0!==this.wsInitPromise&&null!==this.wsInitPromise||this.initWebSocketConnection(!1);var t=new T(j(j({},e),{send:this.send,login:this.webLogin,isWSConnected:this.isWSConnected,onceWSConnected:this.onceWSConnected,getWaitExpectedTimeoutLength:this.getWaitExpectedTimeoutLength,onWatchStart:this.onWatchStart,onWatchClose:this.onWatchClose,debug:!0}));return this.virtualWSClient.add(t),this.watchIdClientMap.set(t.watchId,t),t.listener},e.prototype.heartbeat=function(e){var t=this;this.clearHeartbeat(),this.pingTimeoutId=setTimeout((function(){P(t,void 0,void 0,(function(){var e=this;return M(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),this.ws&&1===this.ws.readyState?(this.lastPingSendTS=Date.now(),[4,this.ping()]):[2];case 1:return t.sent(),this.pingFailed=0,this.pongTimeoutId=setTimeout((function(){console.error("pong timed out"),e.pongMissed<2?(e.pongMissed+=1,e.heartbeat(!0)):e.initWebSocketConnection(!0)}),this.context.appConfig.realtimePongWaitTimeout),[3,3];case 2:return t.sent(),this.pingFailed<2?(this.pingFailed+=1,this.heartbeat()):this.close(R.HeartbeatPingError),[3,3];case 3:return[2]}}))}))}),e?0:this.context.appConfig.realtimePingInterval)},e}(),B={target:"database",entity:function(){var e,t=this.platform,r=t.adapter,n=t.runtime;e=r.wsClass,D=e,W(n)}},K={name:"realtime",IIFE:!0,entity:function(){this.prototype.wsClientClass=k}};try{cloudbase.registerComponent(K),cloudbase.registerHook(B)}catch(e){}function q(e){try{e.registerComponent(K),e.registerHook(B)}catch(e){console.warn(e)}}}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={id:n,loaded:!1,exports:{}};return e[n](i,i.exports,r),i.loaded=!0,i.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.nmd=e=>(e.paths=[],e.children||(e.children=[]),e);var n={};return(()=>{"use strict";var e=n;Object.defineProperty(e,"__esModule",{value:!0}),e.registerRealtime=void 0;var t=r(9063);e.registerRealtime=t.registerRealtime})(),n})()));