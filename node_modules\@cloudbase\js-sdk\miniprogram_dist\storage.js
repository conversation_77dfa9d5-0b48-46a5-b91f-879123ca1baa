!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("cloudbase_storage",[],e):"object"==typeof exports?exports.cloudbase_storage=e():t.cloudbase_storage=e()}("undefined"!=typeof window?window:this,(()=>(()=>{"use strict";var t={864:(t,e,o)=>{o.r(e),o.d(e,{registerStorage:()=>q});var n="@cloudbase/js-sdk";function r(){return n}var i,s={INVALID_PARAMS:"INVALID_PARAMS",INVALID_SYNTAX:"INVALID_SYNTAX",INVALID_OPERATION:"INVALID_OPERATION",OPERATION_FAIL:"OPERATION_FAIL",NETWORK_ERROR:"NETWORK_ERROR",UNKOWN_ERROR:"UNKOWN_ERROR"};!function(t){t.local="local",t.none="none",t.session="session"}(i||(i={}));function a(t){return"string"==typeof t}function c(t,e){console.warn("[".concat(r(),"][").concat(t,"]:").concat(e))}var u,l,p=(u=function(t,e){return u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])},u(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}u(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),f=function(){return f=Object.assign||function(t){for(var e,o=1,n=arguments.length;o<n;o++)for(var r in e=arguments[o])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},f.apply(this,arguments)},d=function(t,e,o,n){return new(o||(o=Promise))((function(r,i){function s(t){try{c(n.next(t))}catch(t){i(t)}}function a(t){try{c(n.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?r(t.value):(e=t.value,e instanceof o?e:new o((function(t){t(e)}))).then(s,a)}c((n=n.apply(t,e||[])).next())}))},h=function(t,e){var o,n,r,i,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(c){return function(a){if(o)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(o=1,n&&(r=2&a[0]?n.return:a[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,a[1])).done)return r;switch(n=0,r&&(a=[2&a[0],r.value]),a[0]){case 0:case 1:r=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((r=(r=s.trys).length>0&&r[r.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!r||a[1]>r[0]&&a[1]<r[3])){s.label=a[1];break}if(6===a[0]&&s.label<r[1]){s.label=r[1],r=a;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(a);break}r[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],n=0}finally{o=r=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}};!function(t){function e(e){var o=t.call(this)||this,n=e.timeout,r=e.timeoutMsg,i=e.restrictedMethods;return o.timeout=n||0,o.timeoutMsg=r||"请求超时",o.restrictedMethods=i||["get","post","upload","download"],o}p(e,t),e.prototype.get=function(t){return this.request(f(f({},t),{method:"get"}),this.restrictedMethods.includes("get"))},e.prototype.post=function(t){return this.request(f(f({},t),{method:"post"}),this.restrictedMethods.includes("post"))},e.prototype.put=function(t){return this.request(f(f({},t),{method:"put"}))},e.prototype.upload=function(t){var e=t.data,o=t.file,n=t.name,r=t.method,i=t.headers,s=void 0===i?{}:i,a={post:"post",put:"put"}[null==r?void 0:r.toLowerCase()]||"put",c=new FormData;return"post"===a?(Object.keys(e).forEach((function(t){c.append(t,e[t])})),c.append("key",n),c.append("file",o),this.request(f(f({},t),{data:c,method:a}),this.restrictedMethods.includes("upload"))):this.request(f(f({},t),{method:"put",headers:s,body:o}),this.restrictedMethods.includes("upload"))},e.prototype.download=function(t){return d(this,void 0,void 0,(function(){var e,o,n,r;return h(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,this.get(f(f({},t),{headers:{},responseType:"blob"}))];case 1:return e=i.sent().data,o=window.URL.createObjectURL(new Blob([e])),n=decodeURIComponent(new URL(t.url).pathname.split("/").pop()||""),(r=document.createElement("a")).href=o,r.setAttribute("download",n),r.style.display="none",document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(o),document.body.removeChild(r),[3,3];case 2:return i.sent(),[3,3];case 3:return[2,new Promise((function(e){e({statusCode:200,tempFilePath:t.url})}))]}}))}))},e.prototype.fetch=function(t){var e;return d(this,void 0,void 0,(function(){var o,n,r,i,s,a,c,u,l,p,y,m=this;return h(this,(function(g){switch(g.label){case 0:return o=new AbortController,n=t.url,r=t.enableAbort,i=void 0!==r&&r,s=t.stream,a=void 0!==s&&s,c=t.signal,u=t.timeout,l=null!=u?u:this.timeout,c&&(c.aborted&&o.abort(),c.addEventListener("abort",(function(){return o.abort()}))),p=null,i&&l&&(p=setTimeout((function(){console.warn(m.timeoutMsg),o.abort(new Error(m.timeoutMsg))}),l)),[4,fetch(n,f(f({},t),{signal:o.signal})).then((function(t){return d(m,void 0,void 0,(function(){var e,o,n;return h(this,(function(r){switch(r.label){case 0:return clearTimeout(p),t.ok?(e=t,[3,3]):[3,1];case 1:return n=(o=Promise).reject,[4,t.json()];case 2:e=n.apply(o,[r.sent()]),r.label=3;case 3:return[2,e]}}))}))})).catch((function(t){return clearTimeout(p),Promise.reject(t)}))];case 1:return y=g.sent(),[2,{data:a?y.body:(null===(e=y.headers.get("content-type"))||void 0===e?void 0:e.includes("application/json"))?y.json():y.text(),statusCode:y.status,header:y.headers}]}}))}))},e.prototype.request=function(t,e){var o=this;void 0===e&&(e=!1);var n=String(t.method).toLowerCase()||"get";return new Promise((function(r){var i,s,a,c=t.url,u=t.headers,l=void 0===u?{}:u,p=t.data,f=t.responseType,d=t.withCredentials,h=t.body,y=t.onUploadProgress,m=function(t,e,o){void 0===o&&(o={});var n=/\?/.test(e),r="";return Object.keys(o).forEach((function(t){""===r?!n&&(e+="?"):r+="&",r+="".concat(t,"=").concat(encodeURIComponent(o[t]))})),/^http(s)?:\/\//.test(e+=r)?e:"".concat(t).concat(e)}("https:",c,"get"===n?p:{}),g=new XMLHttpRequest;g.open(n,m),f&&(g.responseType=f),Object.keys(l).forEach((function(t){g.setRequestHeader(t,l[t])})),y&&g.upload.addEventListener("progress",y),g.onreadystatechange=function(){var t={};if(4===g.readyState){var e=g.getAllResponseHeaders().trim().split(/[\r\n]+/),o={};e.forEach((function(t){var e=t.split(": "),n=e.shift().toLowerCase(),r=e.join(": ");o[n]=r})),t.header=o,t.statusCode=g.status;try{t.data="blob"===f?g.response:JSON.parse(g.responseText)}catch(e){t.data="blob"===f?g.response:g.responseText}clearTimeout(i),r(t)}},e&&o.timeout&&(i=setTimeout((function(){console.warn(o.timeoutMsg),g.abort()}),o.timeout)),a=p,s="[object FormData]"===Object.prototype.toString.call(a)?p:"application/x-www-form-urlencoded"===l["content-type"]?function(t){void 0===t&&(t={});var e=[];return Object.keys(t).forEach((function(o){e.push("".concat(o,"=").concat(encodeURIComponent(t[o])))})),e.join("&")}(p):h||(p?JSON.stringify(p):void 0),d&&(g.withCredentials=!0),g.send(s)}))}}((function(){})),function(t){t.WEB="web",t.WX_MP="wx_mp"}(l||(l={}));var y=function(){var t=function(e,o){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])},t(e,o)};return function(e,o){if("function"!=typeof o&&null!==o)throw new TypeError("Class extends value "+String(o)+" is not a constructor or null");function n(){this.constructor=e}t(e,o),e.prototype=null===o?Object.create(o):(n.prototype=o.prototype,new n)}}(),m=function(t,e,o,n){return new(o||(o=Promise))((function(r,i){function s(t){try{c(n.next(t))}catch(t){i(t)}}function a(t){try{c(n.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?r(t.value):(e=t.value,e instanceof o?e:new o((function(t){t(e)}))).then(s,a)}c((n=n.apply(t,e||[])).next())}))},g=function(t,e){var o,n,r,i,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(c){return function(a){if(o)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(o=1,n&&(r=2&a[0]?n.return:a[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,a[1])).done)return r;switch(n=0,r&&(a=[2&a[0],r.value]),a[0]){case 0:case 1:r=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((r=(r=s.trys).length>0&&r[r.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!r||a[1]>r[0]&&a[1]<r[3])){s.label=a[1];break}if(6===a[0]&&s.label<r[1]){s.label=r[1],r=a;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(a);break}r[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],n=0}finally{o=r=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}},b=function(t){function e(e){var o=t.call(this)||this;return o.root=e,e.tcbCacheObject||(e.tcbCacheObject={}),o}return y(e,t),e.prototype.setItem=function(t,e){this.root.tcbCacheObject[t]=e},e.prototype.getItem=function(t){return this.root.tcbCacheObject[t]},e.prototype.removeItem=function(t){delete this.root.tcbCacheObject[t]},e.prototype.clear=function(){delete this.root.tcbCacheObject},e}((function(){}));!function(){function t(t){this.keys={};var e=t.persistence,o=t.platformInfo,n=void 0===o?{}:o,r=t.keys,i=void 0===r?{}:r;this.platformInfo=n,this.storage||(this.persistenceTag=this.platformInfo.adapter.primaryStorage||e,this.storage=function(t,e){switch(t){case"local":default:return e.localStorage?e.localStorage:(c(s.INVALID_PARAMS,"localStorage is not supported on current platform"),new b(e.root));case"none":return new b(e.root)}}(this.persistenceTag,this.platformInfo.adapter),this.keys=i)}Object.defineProperty(t.prototype,"mode",{get:function(){return this.storage.mode||"sync"},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"persistence",{get:function(){return this.persistenceTag},enumerable:!1,configurable:!0}),t.prototype.setStore=function(t,e,o){if("async"!==this.mode){if(this.storage)try{var n={version:o||"localCachev1",content:e};this.storage.setItem(t,JSON.stringify(n))}catch(t){throw new Error(JSON.stringify({code:s.OPERATION_FAIL,msg:"[".concat(r(),"][").concat(s.OPERATION_FAIL,"]setStore failed"),info:t}))}}else c(s.INVALID_OPERATION,"current platform's storage is asynchronous, please use setStoreAsync insteed")},t.prototype.setStoreAsync=function(t,e,o){return m(this,void 0,void 0,(function(){var n;return g(this,(function(r){switch(r.label){case 0:if(!this.storage)return[2];r.label=1;case 1:return r.trys.push([1,3,,4]),n={version:o||"localCachev1",content:e},[4,this.storage.setItem(t,JSON.stringify(n))];case 2:return r.sent(),[3,4];case 3:return r.sent(),[2];case 4:return[2]}}))}))},t.prototype.getStore=function(t,e){var o;if("async"!==this.mode){try{if("undefined"!=typeof process&&(null===(o=process.env)||void 0===o?void 0:o.tcb_token))return process.env.tcb_token;if(!this.storage)return""}catch(t){return""}e=e||"localCachev1";var n=this.storage.getItem(t);return n&&n.indexOf(e)>=0?JSON.parse(n).content:""}c(s.INVALID_OPERATION,"current platform's storage is asynchronous, please use getStoreAsync insteed")},t.prototype.getStoreAsync=function(t,e){var o;return m(this,void 0,void 0,(function(){var n;return g(this,(function(r){switch(r.label){case 0:try{if("undefined"!=typeof process&&(null===(o=process.env)||void 0===o?void 0:o.tcb_token))return[2,process.env.tcb_token];if(!this.storage)return[2,""]}catch(t){return[2,""]}return e=e||"localCachev1",[4,this.storage.getItem(t)];case 1:return(n=r.sent())&&n.indexOf(e)>=0?[2,JSON.parse(n).content]:[2,""]}}))}))},t.prototype.removeStore=function(t){"async"!==this.mode?this.storage.removeItem(t):c(s.INVALID_OPERATION,"current platform's storage is asynchronous, please use removeStoreAsync insteed")},t.prototype.removeStoreAsync=function(t){return m(this,void 0,void 0,(function(){return g(this,(function(e){switch(e.label){case 0:return[4,this.storage.removeItem(t)];case 1:return e.sent(),[2]}}))}))}}();var v=function(){var t=function(e,o){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])},t(e,o)};return function(e,o){if("function"!=typeof o&&null!==o)throw new TypeError("Class extends value "+String(o)+" is not a constructor or null");function n(){this.constructor=e}t(e,o),e.prototype=null===o?Object.create(o):(n.prototype=o.prototype,new n)}}(),w=function(t,e){this.data=e||null,this.name=t},O=function(t){function e(e,o){var n=t.call(this,"error",{error:e,data:o})||this;return n.error=e,n}return v(e,t),e}(w);function I(t){this.message=t}function A(t){this.message=t}new(function(){function t(){this.listeners={}}return t.prototype.on=function(t,e){return function(t,e,o){o[t]=o[t]||[],o[t].push(e)}(t,e,this.listeners),this},t.prototype.off=function(t,e){return function(t,e,o){if(null==o?void 0:o[t]){var n=o[t].indexOf(e);-1!==n&&o[t].splice(n,1)}}(t,e,this.listeners),this},t.prototype.fire=function(t,e){if(t instanceof O)return console.error(t.error),this;var o=a(t)?new w(t,e||{}):t,n=o.name;if(this.listens(n)){o.target=this;for(var r=0,i=this.listeners[n]?function(t,e,o){if(o||2===arguments.length)for(var n,r=0,i=e.length;r<i;r++)!n&&r in e||(n||(n=Array.prototype.slice.call(e,0,r)),n[r]=e[r]);return t.concat(n||Array.prototype.slice.call(e))}([],this.listeners[n],!0):[];r<i.length;r++)i[r].call(this,o)}return this},t.prototype.listens=function(t){return this.listeners[t]&&this.listeners[t].length>0},t}()),"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.indexOf("Firefox"),I.prototype=new Error,I.prototype.name="InvalidCharacterError","undefined"!=typeof window&&window.atob&&window.atob.bind(window),A.prototype=new Error,A.prototype.name="InvalidTokenError";var _,S=function(){return S=Object.assign||function(t){for(var e,o=1,n=arguments.length;o<n;o++)for(var r in e=arguments[o])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},S.apply(this,arguments)},R=function(t,e,o,n){var r,i=arguments.length,s=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,o):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,o,n);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(i<3?r(s):i>3?r(e,o,s):r(e,o))||s);return i>3&&s&&Object.defineProperty(e,o,s),s},j=function(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)},N=function(t,e,o,n){return new(o||(o=Promise))((function(r,i){function s(t){try{c(n.next(t))}catch(t){i(t)}}function a(t){try{c(n.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?r(t.value):(e=t.value,e instanceof o?e:new o((function(t){t(e)}))).then(s,a)}c((n=n.apply(t,e||[])).next())}))},P=function(t,e){var o,n,r,i,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(c){return function(a){if(o)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(o=1,n&&(r=2&a[0]?n.return:a[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,a[1])).done)return r;switch(n=0,r&&(a=[2&a[0],r.value]),a[0]){case 0:case 1:r=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((r=(r=s.trys).length>0&&r[r.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!r||a[1]>r[0]&&a[1]<r[3])){s.label=a[1];break}if(6===a[0]&&s.label<r[1]){s.label=r[1],r=a;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(a);break}r[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],n=0}finally{o=r=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}};!function(t){t.put="put",t.post="post"}(_||(_={}));var L=r,F=s,E="https://support.qq.com/products/148793",T=function(t){return"[object Array]"===Object.prototype.toString.call(t)},x=a,C=function(t,e,o){if(void 0===o&&(o=null),t&&"function"==typeof t)return t(e,o);if(e)throw e;return o},M=function(t){return t.mode,t.customInfo,t.title,t.messages,function(t,e,o){}},U="storage",k=new(function(){function t(){}return t.prototype.uploadFile=function(t,e){return N(this,void 0,void 0,(function(){var o,n,r,i,s,a,c,u,l,p,f,d,h,y,m,g,b,v,w,O,I,A,R,j,N;return P(this,(function(P){switch(P.label){case 0:if(o=t.cloudPath,n=t.filePath,r=t.onUploadProgress,i=t.method,s=void 0===i?"put":i,a=t.headers,c=void 0===a?{}:a,!x(o)||!n)throw new Error(JSON.stringify({code:F.INVALID_PARAMS,msg:"[".concat(U,".uploadFile] invalid params")}));return u={put:_.put,post:_.post}[s.toLocaleLowerCase()]||_.put,l=this.request,p={path:o,method:u},u===_.put&&(p.headers=c),[4,l.send("storage.getUploadMetadata",p)];case 1:return f=P.sent(),d=f.data,h=d.url,y=d.authorization,m=d.token,g=d.fileId,b=d.cosFileId,v=d.download_url,w=f.requestId,I=S(S({},O={url:h,file:n,name:o,onUploadProgress:r}),{method:_.put,headers:S(S({},c),{authorization:y,"x-cos-meta-fileid":b,"x-cos-security-token":m})}),A=S(S({},O),{method:_.post,data:{key:o,signature:y,"x-cos-meta-fileid":b,success_action_status:"201","x-cos-security-token":m}}),(N={})[_.put]={params:I,isSuccess:function(t){return t>=200&&t<300}},N[_.post]={params:A,isSuccess:function(t){return 201===t}},R=N,[4,l.upload(R[u].params)];case 2:return j=P.sent(),R[u].isSuccess(j.statusCode)?[2,C(e,null,{fileID:g,download_url:v,requestId:w})]:[2,C(e,new Error("[".concat(L(),"][").concat(F.OPERATION_FAIL,"][").concat(U,"]:").concat(j.data)))]}}))}))},t.prototype.getUploadMetadata=function(t,e){return N(this,void 0,void 0,(function(){var o,n,r,i,s;return P(this,(function(a){switch(a.label){case 0:if(o=t.cloudPath,!x(o))throw new Error(JSON.stringify({code:F.INVALID_PARAMS,msg:"[".concat(U,".getUploadMetadata] invalid cloudPath")}));n=this.request,r="storage.getUploadMetadata",a.label=1;case 1:return a.trys.push([1,3,,4]),[4,n.send(r,{path:o})];case 2:return i=a.sent(),[2,C(e,null,i)];case 3:return s=a.sent(),[2,C(e,s)];case 4:return[2]}}))}))},t.prototype.deleteFile=function(t,e){return N(this,void 0,void 0,(function(){var o,n,r,i,s,a;return P(this,(function(c){switch(c.label){case 0:if(!(o=t.fileList)||!T(o)||0===o.length)throw new Error(JSON.stringify({code:F.INVALID_PARAMS,msg:"[".concat(U,".deleteFile] fileList must not be empty")}));for(n=0,r=o;n<r.length;n++)if(!(i=r[n])||!x(i))throw new Error(JSON.stringify({code:F.INVALID_PARAMS,msg:"[".concat(U,".deleteFile] fileID must be string")}));return[4,this.request.send("storage.batchDeleteFile",{fileid_list:o})];case 1:return(s=c.sent()).code?[2,C(e,null,s)]:(a={fileList:s.data.delete_list,requestId:s.requestId},[2,C(e,null,a)])}}))}))},t.prototype.getTempFileURL=function(t,e){return N(this,void 0,void 0,(function(){var o,n,r,i,s,a;return P(this,(function(c){switch(c.label){case 0:if(!(o=t.fileList)||!T(o)||0===o.length)throw new Error(JSON.stringify({code:F.INVALID_PARAMS,msg:"[".concat(U,".getTempFileURL] fileList must not be empty")}));for(n=[],r=0,i=o;r<i.length;r++)if(s=i[r],u=s,"[object Object]"===Object.prototype.toString.call(u)){if(!Object.prototype.hasOwnProperty.call(s,"fileID")||!Object.prototype.hasOwnProperty.call(s,"maxAge"))throw new Error(JSON.stringify({code:F.INVALID_PARAMS,msg:"[".concat(U,".getTempFileURL] file info must include fileID and maxAge")}));n.push({fileid:s.fileID,max_age:s.maxAge})}else{if(!x(s))throw new Error(JSON.stringify({code:F.INVALID_PARAMS,msg:"[".concat(U,".getTempFileURL] invalid fileList")}));n.push({fileid:s})}return[4,this.request.send("storage.batchGetDownloadUrl",{file_list:n})];case 1:return(a=c.sent()).code?[2,C(e,null,a)]:[2,C(e,null,{fileList:a.data.download_list,requestId:a.requestId})]}var u}))}))},t.prototype.downloadFile=function(t,e){return N(this,void 0,void 0,(function(){var o,n,r,i,s,a;return P(this,(function(c){switch(c.label){case 0:if(o=t.fileID,!x(o))throw new Error(JSON.stringify({code:F.INVALID_PARAMS,msg:"[".concat(U,".getTempFileURL] fileID must be string")}));return[4,this.getTempFileURL.call(this,{fileList:[{fileID:o,maxAge:600}]})];case 1:return n=c.sent(),"SUCCESS"!==(r=n.fileList[0]).code?[2,C(e,r)]:(i=this.request,s=encodeURI(r.download_url),[4,i.download({url:s})]);case 2:return a=c.sent(),[2,C(e,null,a)]}}))}))},R([M({customInfo:{className:"Cloudbase",methodName:"uploadFile"},title:"上传文件失败",messages:["请确认以下各项：","  1 - 调用 uploadFile() 的语法或参数是否正确","  2 - 当前域名是否在安全域名列表中：https://console.cloud.tencent.com/tcb/env/safety","  3 - 云存储安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(E)]}),j("design:type",Function),j("design:paramtypes",[Object,Function]),j("design:returntype",Promise)],t.prototype,"uploadFile",null),R([M({customInfo:{className:"Cloudbase",methodName:"getUploadMetadata"},title:"获取上传元信息失败",messages:["请确认以下各项：","  1 - 调用 getUploadMetadata() 的语法或参数是否正确","  2 - 当前域名是否在安全域名列表中：https://console.cloud.tencent.com/tcb/env/safety","  3 - 云存储安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(E)]}),j("design:type",Function),j("design:paramtypes",[Object,Function]),j("design:returntype",Promise)],t.prototype,"getUploadMetadata",null),R([M({customInfo:{className:"Cloudbase",methodName:"deleteFile"},title:"删除文件失败",messages:["请确认以下各项：","  1 - 调用 deleteFile() 的语法或参数是否正确","  2 - 当前域名是否在安全域名列表中：https://console.cloud.tencent.com/tcb/env/safety","  3 - 云存储安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(E)]}),j("design:type",Function),j("design:paramtypes",[Object,Function]),j("design:returntype",Promise)],t.prototype,"deleteFile",null),R([M({customInfo:{className:"Cloudbase",methodName:"getTempFileURL"},title:"获取文件下载链接",messages:["请确认以下各项：","  1 - 调用 getTempFileURL() 的语法或参数是否正确","  2 - 当前域名是否在安全域名列表中：https://console.cloud.tencent.com/tcb/env/safety","  3 - 云存储安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(E)]}),j("design:type",Function),j("design:paramtypes",[Object,Function]),j("design:returntype",Promise)],t.prototype,"getTempFileURL",null),R([M({customInfo:{className:"Cloudbase",methodName:"downloadFile"},title:"下载文件失败",messages:["请确认以下各项：","  1 - 调用 downloadFile() 的语法或参数是否正确","  2 - 当前域名是否在安全域名列表中：https://console.cloud.tencent.com/tcb/env/safety","  3 - 云存储安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(E)]}),j("design:type",Function),j("design:paramtypes",[Object,Function]),j("design:returntype",Promise)],t.prototype,"downloadFile",null),t}()),D={name:U,entity:{uploadFile:k.uploadFile,deleteFile:k.deleteFile,getTempFileURL:k.getTempFileURL,downloadFile:k.downloadFile,getUploadMetadata:k.getUploadMetadata}};try{cloudbase.registerComponent(D)}catch(I){}function q(t){try{t.registerComponent(D)}catch(t){console.warn(t)}}}},e={};function o(n){var r=e[n];if(void 0!==r)return r.exports;var i=e[n]={exports:{}};return t[n](i,i.exports,o),i.exports}o.d=(t,e)=>{for(var n in e)o.o(e,n)&&!o.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),o.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n={};return(()=>{var t=n;Object.defineProperty(t,"__esModule",{value:!0}),t.registerStorage=void 0;var e=o(864);t.registerStorage=e.registerStorage})(),n})()));