"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerFunctions = void 0;
var utilities_1 = require("@cloudbase/utilities");
var cloudrun_1 = require("@cloudbase/cloudrun");
var getSdkName = utilities_1.constants.getSdkName, ERRORS = utilities_1.constants.ERRORS, COMMUNITY_SITE_URL = utilities_1.constants.COMMUNITY_SITE_URL;
var execCallback = utilities_1.utils.execCallback;
var catchErrorsDecorator = utilities_1.helpers.catchErrorsDecorator;
var COMPONENT_NAME = 'functions';
var CloudbaseFunctions = (function () {
    function CloudbaseFunctions() {
    }
    CloudbaseFunctions.prototype.callFunction = function (options, callback) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var name, data, query, parse, search, type, _c, path, method, _d, header, jsonData, requestId, result, action, params, request, res, result, e_1;
            return __generator(this, function (_e) {
                switch (_e.label) {
                    case 0:
                        name = options.name, data = options.data, query = options.query, parse = options.parse, search = options.search, type = options.type, _c = options.path, path = _c === void 0 ? '' : _c, method = options.method, _d = options.header, header = _d === void 0 ? {} : _d;
                        if (!name) {
                            throw new Error(JSON.stringify({
                                code: ERRORS.INVALID_PARAMS,
                                msg: "[".concat(COMPONENT_NAME, ".callFunction] invalid function name"),
                            }));
                        }
                        try {
                            jsonData = data ? JSON.stringify(data) : '';
                        }
                        catch (e) {
                            throw new Error(JSON.stringify({
                                code: ERRORS.INVALID_PARAMS,
                                msg: "[".concat(COMPONENT_NAME, ".callFunction] invalid data"),
                            }));
                        }
                        if (!(type === 'cloudrun')) return [3, 2];
                        requestId = utilities_1.utils.generateRequestId();
                        return [4, cloudrun_1.requestContainer.call(this, {
                                name: name,
                                data: jsonData,
                                path: path,
                                method: method,
                                header: __assign(__assign({}, header), { 'X-Request-Id': requestId }),
                            })];
                    case 1:
                        result = _e.sent();
                        return [2, {
                                result: result,
                                requestId: requestId,
                            }];
                    case 2:
                        action = 'functions.invokeFunction';
                        params = {
                            inQuery: query,
                            parse: parse,
                            search: search,
                            function_name: name,
                            request_data: jsonData,
                        };
                        request = this.request;
                        _e.label = 3;
                    case 3:
                        _e.trys.push([3, 5, , 6]);
                        return [4, request.send(action, params, {
                                defaultQuery: ((_a = data === null || data === void 0 ? void 0 : data.params) === null || _a === void 0 ? void 0 : _a.action) ? { action: (_b = data === null || data === void 0 ? void 0 : data.params) === null || _b === void 0 ? void 0 : _b.action } : {},
                            })];
                    case 4:
                        res = _e.sent();
                        if (res.code) {
                            return [2, execCallback(callback, null, res)];
                        }
                        result = res.data.response_data;
                        if (parse) {
                            return [2, execCallback(callback, null, {
                                    result: result,
                                    requestId: res.requestId,
                                })];
                        }
                        try {
                            result = JSON.parse(res.data.response_data);
                            return [2, execCallback(callback, null, {
                                    result: result,
                                    requestId: res.requestId,
                                })];
                        }
                        catch (e) {
                            execCallback(callback, new Error("[".concat(getSdkName(), "][").concat(ERRORS.INVALID_PARAMS, "][").concat(COMPONENT_NAME, ".callFunction] response data must be json")));
                        }
                        return [3, 6];
                    case 5:
                        e_1 = _e.sent();
                        execCallback(callback, e_1);
                        return [3, 6];
                    case 6: return [2];
                }
            });
        });
    };
    __decorate([
        catchErrorsDecorator({
            customInfo: {
                className: 'Cloudbase',
                methodName: 'callFunction',
            },
            title: '函数调用失败',
            messages: [
                '请确认以下各项：',
                '  1 - 调用 callFunction() 的语法或参数是否正确',
                '  2 - 当前环境下是否存在此函数',
                '  3 - 函数安全规则是否限制了当前登录状态访问',
                "\u5982\u679C\u95EE\u9898\u4F9D\u7136\u5B58\u5728\uFF0C\u5EFA\u8BAE\u5230\u5B98\u65B9\u95EE\u7B54\u793E\u533A\u63D0\u95EE\u6216\u5BFB\u627E\u5E2E\u52A9\uFF1A".concat(COMMUNITY_SITE_URL),
            ],
        }),
        __metadata("design:type", Function),
        __metadata("design:paramtypes", [Object, Function]),
        __metadata("design:returntype", Promise)
    ], CloudbaseFunctions.prototype, "callFunction", null);
    return CloudbaseFunctions;
}());
var cloudbaseFunctions = new CloudbaseFunctions();
var component = {
    name: COMPONENT_NAME,
    entity: {
        callFunction: cloudbaseFunctions.callFunction,
    },
};
try {
    cloudbase.registerComponent(component);
}
catch (e) { }
function registerFunctions(app) {
    try {
        app.registerComponent(component);
    }
    catch (e) {
        console.warn(e);
    }
}
exports.registerFunctions = registerFunctions;
//# sourceMappingURL=data:application/json;base64,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