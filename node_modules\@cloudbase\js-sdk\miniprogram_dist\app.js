/*! For license information please see app.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("cloudbase",[],t):"object"==typeof exports?exports.cloudbase=t():e.cloudbase=t()}("undefined"!=typeof window?window:this,(function(){return function(){var e={23:function(e,t,r){"use strict";var o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}},n=o(r(635)),i=o(r(878));n.default.registerVersion(i.default.version);try{window.cloudbase=n.default}catch(e){}t.default=n.default,e.exports=n.default},635:function(e,t,r){"use strict";r.r(t),r.d(t,{cloudbase:function(){return Hn},default:function(){return Yn},getBaseEndPoint:function(){return Tn}});var o="@cloudbase/js-sdk";function n(){return o}var i,a="https:",s={INVALID_PARAMS:"INVALID_PARAMS",INVALID_SYNTAX:"INVALID_SYNTAX",INVALID_OPERATION:"INVALID_OPERATION",OPERATION_FAIL:"OPERATION_FAIL",NETWORK_ERROR:"NETWORK_ERROR",UNKOWN_ERROR:"UNKOWN_ERROR"};!function(e){e.local="local",e.none="none",e.session="session"}(i||(i={}));var l=function(){};function u(e,t,r){void 0===r&&(r={});var o=/\?/.test(t),n="";for(var i in r)""===n?!o&&(t+="?"):n+="&",n+=i+"="+encodeURIComponent(r[i]);return/^http(s)?\:\/\//.test(t+=n)?t:""+e+t}function c(e){return"[object FormData]"===Object.prototype.toString.call(e)}function f(e,t,r){void 0===r&&(r={});var o=/\?/.test(t),n="";return Object.keys(r).forEach((function(e){""===n?!o&&(t+="?"):n+="&",n+="".concat(e,"=").concat(encodeURIComponent(r[e]))})),/^http(s)?:\/\//.test(t+=n)?t:"".concat(e).concat(t)}function d(e,t){console.warn("[".concat(n(),"][").concat(e,"]:").concat(t))}var h,p,b=(h=function(e,t){return h=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},h(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}h(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),y=function(){return y=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},y.apply(this,arguments)},m=function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function a(e){try{l(o.next(e))}catch(e){i(e)}}function s(e){try{l(o.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((o=o.apply(e,t||[])).next())}))},_=function(e,t){var r,o,n,i,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,o&&(n=2&s[0]?o.return:s[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,s[1])).done)return n;switch(o=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,o=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((n=(n=a.trys).length>0&&n[n.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){a.label=s[1];break}if(6===s[0]&&a.label<n[1]){a.label=n[1],n=s;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(s);break}n[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],o=0}finally{r=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}},v=function(e){function t(t){var r=e.call(this)||this,o=t.timeout,n=t.timeoutMsg,i=t.restrictedMethods;return r.timeout=o||0,r.timeoutMsg=n||"请求超时",r.restrictedMethods=i||["get","post","upload","download"],r}return b(t,e),t.prototype.get=function(e){return this.request(y(y({},e),{method:"get"}),this.restrictedMethods.includes("get"))},t.prototype.post=function(e){return this.request(y(y({},e),{method:"post"}),this.restrictedMethods.includes("post"))},t.prototype.put=function(e){return this.request(y(y({},e),{method:"put"}))},t.prototype.upload=function(e){var t=e.data,r=e.file,o=e.name,n=e.method,i=e.headers,a=void 0===i?{}:i,s={post:"post",put:"put"}[null==n?void 0:n.toLowerCase()]||"put",l=new FormData;return"post"===s?(Object.keys(t).forEach((function(e){l.append(e,t[e])})),l.append("key",o),l.append("file",r),this.request(y(y({},e),{data:l,method:s}),this.restrictedMethods.includes("upload"))):this.request(y(y({},e),{method:"put",headers:a,body:r}),this.restrictedMethods.includes("upload"))},t.prototype.download=function(e){return m(this,void 0,void 0,(function(){var t,r,o,n;return _(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,this.get(y(y({},e),{headers:{},responseType:"blob"}))];case 1:return t=i.sent().data,r=window.URL.createObjectURL(new Blob([t])),o=decodeURIComponent(new URL(e.url).pathname.split("/").pop()||""),(n=document.createElement("a")).href=r,n.setAttribute("download",o),n.style.display="none",document.body.appendChild(n),n.click(),window.URL.revokeObjectURL(r),document.body.removeChild(n),[3,3];case 2:return i.sent(),[3,3];case 3:return[2,new Promise((function(t){t({statusCode:200,tempFilePath:e.url})}))]}}))}))},t.prototype.fetch=function(e){var t;return m(this,void 0,void 0,(function(){var r,o,n,i,a,s,l,u,c,f,d,h=this;return _(this,(function(p){switch(p.label){case 0:return r=new AbortController,o=e.url,n=e.enableAbort,i=void 0!==n&&n,a=e.stream,s=void 0!==a&&a,l=e.signal,u=e.timeout,c=null!=u?u:this.timeout,l&&(l.aborted&&r.abort(),l.addEventListener("abort",(function(){return r.abort()}))),f=null,i&&c&&(f=setTimeout((function(){console.warn(h.timeoutMsg),r.abort(new Error(h.timeoutMsg))}),c)),[4,fetch(o,y(y({},e),{signal:r.signal})).then((function(e){return m(h,void 0,void 0,(function(){var t,r,o;return _(this,(function(n){switch(n.label){case 0:return clearTimeout(f),e.ok?(t=e,[3,3]):[3,1];case 1:return o=(r=Promise).reject,[4,e.json()];case 2:t=o.apply(r,[n.sent()]),n.label=3;case 3:return[2,t]}}))}))})).catch((function(e){return clearTimeout(f),Promise.reject(e)}))];case 1:return d=p.sent(),[2,{data:s?d.body:(null===(t=d.headers.get("content-type"))||void 0===t?void 0:t.includes("application/json"))?d.json():d.text(),statusCode:d.status,header:d.headers}]}}))}))},t.prototype.request=function(e,t){var r=this;void 0===t&&(t=!1);var o=String(e.method).toLowerCase()||"get";return new Promise((function(n){var i,s,l=e.url,u=e.headers,d=void 0===u?{}:u,h=e.data,p=e.responseType,b=e.withCredentials,y=e.body,m=e.onUploadProgress,_=f(a,l,"get"===o?h:{}),v=new XMLHttpRequest;v.open(o,_),p&&(v.responseType=p),Object.keys(d).forEach((function(e){v.setRequestHeader(e,d[e])})),m&&v.upload.addEventListener("progress",m),v.onreadystatechange=function(){var e={};if(4===v.readyState){var t=v.getAllResponseHeaders().trim().split(/[\r\n]+/),r={};t.forEach((function(e){var t=e.split(": "),o=t.shift().toLowerCase(),n=t.join(": ");r[o]=n})),e.header=r,e.statusCode=v.status;try{e.data="blob"===p?v.response:JSON.parse(v.responseText)}catch(t){e.data="blob"===p?v.response:v.responseText}clearTimeout(i),n(e)}},t&&r.timeout&&(i=setTimeout((function(){console.warn(r.timeoutMsg),v.abort()}),r.timeout)),s=c(h)?h:"application/x-www-form-urlencoded"===d["content-type"]?function(e){void 0===e&&(e={});var t=[];return Object.keys(e).forEach((function(r){t.push("".concat(r,"=").concat(encodeURIComponent(e[r])))})),t.join("&")}(h):y||(h?JSON.stringify(h):void 0),b&&(v.withCredentials=!0),v.send(s)}))},t}(l);!function(e){e.WEB="web",e.WX_MP="wx_mp"}(p||(p={}));var g=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function o(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o)}}(),w=function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function a(e){try{l(o.next(e))}catch(e){i(e)}}function s(e){try{l(o.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((o=o.apply(e,t||[])).next())}))},S=function(e,t){var r,o,n,i,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,o&&(n=2&s[0]?o.return:s[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,s[1])).done)return n;switch(o=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,o=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((n=(n=a.trys).length>0&&n[n.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){a.label=s[1];break}if(6===s[0]&&a.label<n[1]){a.label=n[1],n=s;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(s);break}n[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],o=0}finally{r=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}},T=function(e){function t(t){var r=e.call(this)||this;return r.root=t,t.tcbCacheObject||(t.tcbCacheObject={}),r}return g(t,e),t.prototype.setItem=function(e,t){this.root.tcbCacheObject[e]=t},t.prototype.getItem=function(e){return this.root.tcbCacheObject[e]},t.prototype.removeItem=function(e){delete this.root.tcbCacheObject[e]},t.prototype.clear=function(){delete this.root.tcbCacheObject},t}((function(){})),P=function(){function e(e){this.keys={};var t=e.persistence,r=e.platformInfo,o=void 0===r?{}:r,n=e.keys,i=void 0===n?{}:n;this.platformInfo=o,this.storage||(this.persistenceTag=this.platformInfo.adapter.primaryStorage||t,this.storage=function(e,t){switch(e){case"local":default:return t.localStorage?t.localStorage:(d(s.INVALID_PARAMS,"localStorage is not supported on current platform"),new T(t.root));case"none":return new T(t.root)}}(this.persistenceTag,this.platformInfo.adapter),this.keys=i)}return Object.defineProperty(e.prototype,"mode",{get:function(){return this.storage.mode||"sync"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"persistence",{get:function(){return this.persistenceTag},enumerable:!1,configurable:!0}),e.prototype.setStore=function(e,t,r){if("async"!==this.mode){if(this.storage)try{var o={version:r||"localCachev1",content:t};this.storage.setItem(e,JSON.stringify(o))}catch(e){throw new Error(JSON.stringify({code:s.OPERATION_FAIL,msg:"[".concat(n(),"][").concat(s.OPERATION_FAIL,"]setStore failed"),info:e}))}}else d(s.INVALID_OPERATION,"current platform's storage is asynchronous, please use setStoreAsync insteed")},e.prototype.setStoreAsync=function(e,t,r){return w(this,void 0,void 0,(function(){var o;return S(this,(function(n){switch(n.label){case 0:if(!this.storage)return[2];n.label=1;case 1:return n.trys.push([1,3,,4]),o={version:r||"localCachev1",content:t},[4,this.storage.setItem(e,JSON.stringify(o))];case 2:return n.sent(),[3,4];case 3:return n.sent(),[2];case 4:return[2]}}))}))},e.prototype.getStore=function(e,t){var r;if("async"!==this.mode){try{if("undefined"!=typeof process&&(null===(r=process.env)||void 0===r?void 0:r.tcb_token))return process.env.tcb_token;if(!this.storage)return""}catch(e){return""}t=t||"localCachev1";var o=this.storage.getItem(e);return o&&o.indexOf(t)>=0?JSON.parse(o).content:""}d(s.INVALID_OPERATION,"current platform's storage is asynchronous, please use getStoreAsync insteed")},e.prototype.getStoreAsync=function(e,t){var r;return w(this,void 0,void 0,(function(){var o;return S(this,(function(n){switch(n.label){case 0:try{if("undefined"!=typeof process&&(null===(r=process.env)||void 0===r?void 0:r.tcb_token))return[2,process.env.tcb_token];if(!this.storage)return[2,""]}catch(e){return[2,""]}return t=t||"localCachev1",[4,this.storage.getItem(e)];case 1:return(o=n.sent())&&o.indexOf(t)>=0?[2,JSON.parse(o).content]:[2,""]}}))}))},e.prototype.removeStore=function(e){"async"!==this.mode?this.storage.removeItem(e):d(s.INVALID_OPERATION,"current platform's storage is asynchronous, please use removeStoreAsync insteed")},e.prototype.removeStoreAsync=function(e){return w(this,void 0,void 0,(function(){return S(this,(function(t){switch(t.label){case 0:return[4,this.storage.removeItem(e)];case 1:return t.sent(),[2]}}))}))},e}(),E=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function o(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o)}}(),O=function(e,t){this.data=t||null,this.name=e},R=function(e){function t(t,r){var o=e.call(this,"error",{error:t,data:r})||this;return o.error=t,o}return E(t,e),t}(O);function C(e){this.message=e}function A(e){this.message=e}function j(){}function q(e){return"object"==typeof e&&null!==e||"function"==typeof e}new(function(){function e(){this.listeners={}}return e.prototype.on=function(e,t){return function(e,t,r){r[e]=r[e]||[],r[e].push(t)}(e,t,this.listeners),this},e.prototype.off=function(e,t){return function(e,t,r){if(null==r?void 0:r[e]){var o=r[e].indexOf(t);-1!==o&&r[e].splice(o,1)}}(e,t,this.listeners),this},e.prototype.fire=function(e,t){if(e instanceof R)return console.error(e.error),this;var r="string"==typeof e?new O(e,t||{}):e,o=r.name;if(this.listens(o)){r.target=this;for(var n=0,i=this.listeners[o]?function(e,t,r){if(r||2===arguments.length)for(var o,n=0,i=t.length;n<i;n++)!o&&n in t||(o||(o=Array.prototype.slice.call(t,0,n)),o[n]=t[n]);return e.concat(o||Array.prototype.slice.call(t))}([],this.listeners[o],!0):[];n<i.length;n++)i[n].call(this,r)}return this},e.prototype.listens=function(e){return this.listeners[e]&&this.listeners[e].length>0},e}()),"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.indexOf("Firefox"),C.prototype=new Error,C.prototype.name="InvalidCharacterError","undefined"!=typeof window&&window.atob&&window.atob.bind(window),A.prototype=new Error,A.prototype.name="InvalidTokenError";const k=j;function I(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0})}catch(e){}}const x=Promise,L=Promise.resolve.bind(x),W=Promise.prototype.then,M=Promise.reject.bind(x),N=L;function F(e){return new x(e)}function B(e){return F((t=>t(e)))}function z(e){return M(e)}function D(e,t,r){return W.call(e,t,r)}function U(e,t,r){D(D(e,t,r),void 0,k)}function $(e,t){U(e,t)}function V(e,t){U(e,void 0,t)}function H(e,t,r){return D(e,t,r)}function Y(e){D(e,void 0,k)}let Q=e=>{if("function"==typeof queueMicrotask)Q=queueMicrotask;else{const e=B(void 0);Q=t=>D(e,t)}return Q(e)};function G(e,t,r){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function K(e,t,r){try{return B(G(e,t,r))}catch(e){return z(e)}}class J{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){const t=this._back;let r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){const e=this._front;let t=e;const r=this._cursor;let o=r+1;const n=e._elements,i=n[r];return 16384===o&&(t=e._next,o=0),--this._size,this._cursor=o,e!==t&&(this._front=t),n[r]=void 0,i}forEach(e){let t=this._cursor,r=this._front,o=r._elements;for(;!(t===o.length&&void 0===r._next||t===o.length&&(r=r._next,o=r._elements,t=0,0===o.length));)e(o[t]),++t}peek(){const e=this._front,t=this._cursor;return e._elements[t]}}const X=Symbol("[[AbortSteps]]"),Z=Symbol("[[ErrorSteps]]"),ee=Symbol("[[CancelSteps]]"),te=Symbol("[[PullSteps]]"),re=Symbol("[[ReleaseSteps]]");function oe(e,t){e._ownerReadableStream=t,t._reader=e,"readable"===t._state?se(e):"closed"===t._state?function(e){se(e),ce(e)}(e):le(e,t._storedError)}function ne(e,t){return ho(e._ownerReadableStream,t)}function ie(e){const t=e._ownerReadableStream;"readable"===t._state?ue(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e){le(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness"))}(e),t._readableStreamController[re](),t._reader=void 0,e._ownerReadableStream=void 0}function ae(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function se(e){e._closedPromise=F(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r}))}function le(e,t){se(e),ue(e,t)}function ue(e,t){void 0!==e._closedPromise_reject&&(Y(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function ce(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}const fe=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},de=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function he(e,t){if(void 0!==e&&"object"!=typeof(r=e)&&"function"!=typeof r)throw new TypeError(`${t} is not an object.`);var r}function pe(e,t){if("function"!=typeof e)throw new TypeError(`${t} is not a function.`)}function be(e,t){if(!function(e){return"object"==typeof e&&null!==e||"function"==typeof e}(e))throw new TypeError(`${t} is not an object.`)}function ye(e,t,r){if(void 0===e)throw new TypeError(`Parameter ${t} is required in '${r}'.`)}function me(e,t,r){if(void 0===e)throw new TypeError(`${t} is required in '${r}'.`)}function _e(e){return Number(e)}function ve(e){return 0===e?0:e}function ge(e,t){const r=Number.MAX_SAFE_INTEGER;let o=Number(e);if(o=ve(o),!fe(o))throw new TypeError(`${t} is not a finite number`);if(o=function(e){return ve(de(e))}(o),o<0||o>r)throw new TypeError(`${t} is outside the accepted range of 0 to ${r}, inclusive`);return fe(o)&&0!==o?o:0}function we(e,t){if(!co(e))throw new TypeError(`${t} is not a ReadableStream.`)}function Se(e){return new Re(e)}function Te(e,t){e._reader._readRequests.push(t)}function Pe(e,t,r){const o=e._reader._readRequests.shift();r?o._closeSteps():o._chunkSteps(t)}function Ee(e){return e._reader._readRequests.length}function Oe(e){const t=e._reader;return void 0!==t&&!!Ce(t)}class Re{constructor(e){if(ye(e,1,"ReadableStreamDefaultReader"),we(e,"First parameter"),fo(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");oe(this,e),this._readRequests=new J}get closed(){return Ce(this)?this._closedPromise:z(qe("closed"))}cancel(e=void 0){return Ce(this)?void 0===this._ownerReadableStream?z(ae("cancel")):ne(this,e):z(qe("cancel"))}read(){if(!Ce(this))return z(qe("read"));if(void 0===this._ownerReadableStream)return z(ae("read from"));let e,t;const r=F(((r,o)=>{e=r,t=o}));return Ae(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!Ce(this))throw qe("releaseLock");void 0!==this._ownerReadableStream&&function(e){ie(e),je(e,new TypeError("Reader was released"))}(this)}}function Ce(e){return!!q(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof Re}function Ae(e,t){const r=e._ownerReadableStream;r._disturbed=!0,"closed"===r._state?t._closeSteps():"errored"===r._state?t._errorSteps(r._storedError):r._readableStreamController[te](t)}function je(e,t){const r=e._readRequests;e._readRequests=new J,r.forEach((e=>{e._errorSteps(t)}))}function qe(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}var ke,Ie,xe;function Le(e){return e.slice()}function We(e,t,r,o,n){new Uint8Array(e).set(new Uint8Array(r,o,n),t)}Object.defineProperties(Re.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),I(Re.prototype.cancel,"cancel"),I(Re.prototype.read,"read"),I(Re.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Re.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});let Me=e=>(Me="function"==typeof e.transfer?e=>e.transfer():"function"==typeof structuredClone?e=>structuredClone(e,{transfer:[e]}):e=>e,Me(e)),Ne=e=>(Ne="boolean"==typeof e.detached?e=>e.detached:e=>0===e.byteLength,Ne(e));function Fe(e,t,r){if(e.slice)return e.slice(t,r);const o=r-t,n=new ArrayBuffer(o);return We(n,0,e,t,o),n}function Be(e,t){const r=e[t];if(null!=r){if("function"!=typeof r)throw new TypeError(`${String(t)} is not a function`);return r}}function ze(e){try{const t=e.done,r=e.value;return D(N(r),(e=>({done:t,value:e})))}catch(e){return z(e)}}const De=null!==(xe=null!==(ke=Symbol.asyncIterator)&&void 0!==ke?ke:null===(Ie=Symbol.for)||void 0===Ie?void 0:Ie.call(Symbol,"Symbol.asyncIterator"))&&void 0!==xe?xe:"@@asyncIterator";function Ue(e,t="sync",r){if(void 0===r)if("async"===t){if(void 0===(r=Be(e,De)))return function(e){const t={next(){let t;try{t=$e(e)}catch(e){return z(e)}return ze(t)},return(t){let r;try{const o=Be(e.iterator,"return");if(void 0===o)return B({done:!0,value:t});r=G(o,e.iterator,[t])}catch(e){return z(e)}return q(r)?ze(r):z(new TypeError("The iterator.return() method must return an object"))}};return{iterator:t,nextMethod:t.next,done:!1}}(Ue(e,"sync",Be(e,Symbol.iterator)))}else r=Be(e,Symbol.iterator);if(void 0===r)throw new TypeError("The object is not iterable");const o=G(r,e,[]);if(!q(o))throw new TypeError("The iterator method must return an object");return{iterator:o,nextMethod:o.next,done:!1}}function $e(e){const t=G(e.nextMethod,e.iterator,[]);if(!q(t))throw new TypeError("The iterator.next() method must return an object");return t}class Ve{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){const e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?H(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){const t=()=>this._returnSteps(e);return this._ongoingPromise?H(this._ongoingPromise,t,t):t()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});const e=this._reader;let t,r;const o=F(((e,o)=>{t=e,r=o}));return Ae(e,{_chunkSteps:e=>{this._ongoingPromise=void 0,Q((()=>t({value:e,done:!1})))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,ie(e),t({value:void 0,done:!0})},_errorSteps:t=>{this._ongoingPromise=void 0,this._isFinished=!0,ie(e),r(t)}}),o}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;const t=this._reader;if(!this._preventCancel){const r=ne(t,e);return ie(t),H(r,(()=>({value:e,done:!0})))}return ie(t),B({value:e,done:!0})}}const He={next(){return Ye(this)?this._asyncIteratorImpl.next():z(Qe("next"))},return(e){return Ye(this)?this._asyncIteratorImpl.return(e):z(Qe("return"))},[De](){return this}};function Ye(e){if(!q(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof Ve}catch(e){return!1}}function Qe(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}Object.defineProperty(He,De,{enumerable:!1});const Ge=Number.isNaN||function(e){return e!=e};function Ke(e){const t=Fe(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function Je(e){const t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function Xe(e,t,r){if("number"!=typeof(o=r)||Ge(o)||o<0||r===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");var o;e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function Ze(e){e._queue=new J,e._queueTotalSize=0}function et(e){return e===DataView}class tt{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!nt(this))throw At("view");return this._view}respond(e){if(!nt(this))throw At("respond");if(ye(e,1,"respond"),e=ge(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(Ne(this._view.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");Ot(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!nt(this))throw At("respondWithNewView");if(ye(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(Ne(e.buffer))throw new TypeError("The given view's buffer has been detached and so cannot be used as a response");Rt(this._associatedReadableByteStreamController,e)}}Object.defineProperties(tt.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),I(tt.prototype.respond,"respond"),I(tt.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(tt.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class rt{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!ot(this))throw jt("byobRequest");return Pt(this)}get desiredSize(){if(!ot(this))throw jt("desiredSize");return Et(this)}close(){if(!ot(this))throw jt("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");const e=this._controlledReadableByteStream._state;if("readable"!==e)throw new TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);gt(this)}enqueue(e){if(!ot(this))throw jt("enqueue");if(ye(e,1,"enqueue"),!ArrayBuffer.isView(e))throw new TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");const t=this._controlledReadableByteStream._state;if("readable"!==t)throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);wt(this,e)}error(e=void 0){if(!ot(this))throw jt("error");St(this,e)}[ee](e){at(this),Ze(this);const t=this._cancelAlgorithm(e);return vt(this),t}[te](e){const t=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void Tt(this,e);const r=this._autoAllocateChunkSize;if(void 0!==r){let o;try{o=new ArrayBuffer(r)}catch(t){return void e._errorSteps(t)}const n={buffer:o,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(n)}Te(t,e),it(this)}[re](){if(this._pendingPullIntos.length>0){const e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new J,this._pendingPullIntos.push(e)}}}function ot(e){return!!q(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof rt}function nt(e){return!!q(e)&&!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof tt}function it(e){const t=function(e){const t=e._controlledReadableByteStream;return"readable"===t._state&&(!e._closeRequested&&(!!e._started&&(!!(Oe(t)&&Ee(t)>0)||(!!(Lt(t)&&xt(t)>0)||Et(e)>0))))}(e);t&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,U(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,it(e)),null)),(t=>(St(e,t),null)))))}function at(e){bt(e),e._pendingPullIntos=new J}function st(e,t){let r=!1;"closed"===e._state&&(r=!0);const o=lt(t);"default"===t.readerType?Pe(e,o,r):function(e,t,r){const o=e._reader._readIntoRequests.shift();r?o._closeSteps(t):o._chunkSteps(t)}(e,o,r)}function lt(e){const t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function ut(e,t,r,o){e._queue.push({buffer:t,byteOffset:r,byteLength:o}),e._queueTotalSize+=o}function ct(e,t,r,o){let n;try{n=Fe(t,r,r+o)}catch(t){throw St(e,t),t}ut(e,n,0,o)}function ft(e,t){t.bytesFilled>0&&ct(e,t.buffer,t.byteOffset,t.bytesFilled),_t(e)}function dt(e,t){const r=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),o=t.bytesFilled+r;let n=r,i=!1;const a=o-o%t.elementSize;a>=t.minimumFill&&(n=a-t.bytesFilled,i=!0);const s=e._queue;for(;n>0;){const r=s.peek(),o=Math.min(n,r.byteLength),i=t.byteOffset+t.bytesFilled;We(t.buffer,i,r.buffer,r.byteOffset,o),r.byteLength===o?s.shift():(r.byteOffset+=o,r.byteLength-=o),e._queueTotalSize-=o,ht(0,o,t),n-=o}return i}function ht(e,t,r){r.bytesFilled+=t}function pt(e){0===e._queueTotalSize&&e._closeRequested?(vt(e),po(e._controlledReadableByteStream)):it(e)}function bt(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function yt(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;const t=e._pendingPullIntos.peek();dt(e,t)&&(_t(e),st(e._controlledReadableByteStream,t))}}function mt(e,t){const r=e._pendingPullIntos.peek();bt(e),"closed"===e._controlledReadableByteStream._state?function(e,t){"none"===t.readerType&&_t(e);const r=e._controlledReadableByteStream;if(Lt(r))for(;xt(r)>0;)st(r,_t(e))}(e,r):function(e,t,r){if(ht(0,t,r),"none"===r.readerType)return ft(e,r),void yt(e);if(r.bytesFilled<r.minimumFill)return;_t(e);const o=r.bytesFilled%r.elementSize;if(o>0){const t=r.byteOffset+r.bytesFilled;ct(e,r.buffer,t-o,o)}r.bytesFilled-=o,st(e._controlledReadableByteStream,r),yt(e)}(e,t,r),it(e)}function _t(e){return e._pendingPullIntos.shift()}function vt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function gt(e){const t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state)if(e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();if(t.bytesFilled%t.elementSize!=0){const t=new TypeError("Insufficient bytes to fill elements in the given buffer");throw St(e,t),t}}vt(e),po(t)}}function wt(e,t){const r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;const{buffer:o,byteOffset:n,byteLength:i}=t;if(Ne(o))throw new TypeError("chunk's buffer is detached and so cannot be enqueued");const a=Me(o);if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();if(Ne(t.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");bt(e),t.buffer=Me(t.buffer),"none"===t.readerType&&ft(e,t)}Oe(r)?(function(e){const t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;Tt(e,t._readRequests.shift())}}(e),0===Ee(r)?ut(e,a,n,i):(e._pendingPullIntos.length>0&&_t(e),Pe(r,new Uint8Array(a,n,i),!1))):Lt(r)?(ut(e,a,n,i),yt(e)):ut(e,a,n,i),it(e)}function St(e,t){const r=e._controlledReadableByteStream;"readable"===r._state&&(at(e),Ze(e),vt(e),bo(r,t))}function Tt(e,t){const r=e._queue.shift();e._queueTotalSize-=r.byteLength,pt(e);const o=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(o)}function Pt(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),o=Object.create(tt.prototype);!function(e,t,r){e._associatedReadableByteStreamController=t,e._view=r}(o,e,r),e._byobRequest=o}return e._byobRequest}function Et(e){const t=e._controlledReadableByteStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function Ot(e,t){const r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===t)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw new RangeError("bytesWritten out of range")}r.buffer=Me(r.buffer),mt(e,t)}function Rt(e,t){const r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===t.byteLength)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");const o=t.byteLength;r.buffer=Me(t.buffer),mt(e,o)}function Ct(e,t,r,o,n,i,a){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,Ze(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=i,t._pullAlgorithm=o,t._cancelAlgorithm=n,t._autoAllocateChunkSize=a,t._pendingPullIntos=new J,e._readableStreamController=t,U(B(r()),(()=>(t._started=!0,it(t),null)),(e=>(St(t,e),null)))}function At(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function jt(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function qt(e,t){if("byob"!=(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function kt(e){return new Wt(e)}function It(e,t){e._reader._readIntoRequests.push(t)}function xt(e){return e._reader._readIntoRequests.length}function Lt(e){const t=e._reader;return void 0!==t&&!!Mt(t)}Object.defineProperties(rt.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),I(rt.prototype.close,"close"),I(rt.prototype.enqueue,"enqueue"),I(rt.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(rt.prototype,Symbol.toStringTag,{value:"ReadableByteStreamController",configurable:!0});class Wt{constructor(e){if(ye(e,1,"ReadableStreamBYOBReader"),we(e,"First parameter"),fo(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!ot(e._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");oe(this,e),this._readIntoRequests=new J}get closed(){return Mt(this)?this._closedPromise:z(Bt("closed"))}cancel(e=void 0){return Mt(this)?void 0===this._ownerReadableStream?z(ae("cancel")):ne(this,e):z(Bt("cancel"))}read(e,t={}){if(!Mt(this))return z(Bt("read"));if(!ArrayBuffer.isView(e))return z(new TypeError("view must be an array buffer view"));if(0===e.byteLength)return z(new TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return z(new TypeError("view's buffer must have non-zero byteLength"));if(Ne(e.buffer))return z(new TypeError("view's buffer has been detached"));let r;try{r=function(e,t){var r;return he(e,t),{min:ge(null!==(r=null==e?void 0:e.min)&&void 0!==r?r:1,`${t} has member 'min' that`)}}(t,"options")}catch(e){return z(e)}const o=r.min;if(0===o)return z(new TypeError("options.min must be greater than 0"));if(function(e){return et(e.constructor)}(e)){if(o>e.byteLength)return z(new RangeError("options.min must be less than or equal to view's byteLength"))}else if(o>e.length)return z(new RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return z(ae("read from"));let n,i;const a=F(((e,t)=>{n=e,i=t}));return Nt(this,e,o,{_chunkSteps:e=>n({value:e,done:!1}),_closeSteps:e=>n({value:e,done:!0}),_errorSteps:e=>i(e)}),a}releaseLock(){if(!Mt(this))throw Bt("releaseLock");void 0!==this._ownerReadableStream&&function(e){ie(e),Ft(e,new TypeError("Reader was released"))}(this)}}function Mt(e){return!!q(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof Wt}function Nt(e,t,r,o){const n=e._ownerReadableStream;n._disturbed=!0,"errored"===n._state?o._errorSteps(n._storedError):function(e,t,r,o){const n=e._controlledReadableByteStream,i=t.constructor,a=function(e){return et(e)?1:e.BYTES_PER_ELEMENT}(i),{byteOffset:s,byteLength:l}=t,u=r*a;let c;try{c=Me(t.buffer)}catch(e){return void o._errorSteps(e)}const f={buffer:c,bufferByteLength:c.byteLength,byteOffset:s,byteLength:l,bytesFilled:0,minimumFill:u,elementSize:a,viewConstructor:i,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(f),void It(n,o);if("closed"!==n._state){if(e._queueTotalSize>0){if(dt(e,f)){const t=lt(f);return pt(e),void o._chunkSteps(t)}if(e._closeRequested){const t=new TypeError("Insufficient bytes to fill elements in the given buffer");return St(e,t),void o._errorSteps(t)}}e._pendingPullIntos.push(f),It(n,o),it(e)}else{const e=new i(f.buffer,f.byteOffset,0);o._closeSteps(e)}}(n._readableStreamController,t,r,o)}function Ft(e,t){const r=e._readIntoRequests;e._readIntoRequests=new J,r.forEach((e=>{e._errorSteps(t)}))}function Bt(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function zt(e,t){const{highWaterMark:r}=e;if(void 0===r)return t;if(Ge(r)||r<0)throw new RangeError("Invalid highWaterMark");return r}function Dt(e){const{size:t}=e;return t||(()=>1)}function Ut(e,t){he(e,t);const r=null==e?void 0:e.highWaterMark,o=null==e?void 0:e.size;return{highWaterMark:void 0===r?void 0:_e(r),size:void 0===o?void 0:$t(o,`${t} has member 'size' that`)}}function $t(e,t){return pe(e,t),t=>_e(e(t))}function Vt(e,t,r){return pe(e,r),r=>K(e,t,[r])}function Ht(e,t,r){return pe(e,r),()=>K(e,t,[])}function Yt(e,t,r){return pe(e,r),r=>G(e,t,[r])}function Qt(e,t,r){return pe(e,r),(r,o)=>K(e,t,[r,o])}function Gt(e,t){if(!er(e))throw new TypeError(`${t} is not a WritableStream.`)}Object.defineProperties(Wt.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),I(Wt.prototype.cancel,"cancel"),I(Wt.prototype.read,"read"),I(Wt.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Wt.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});const Kt="function"==typeof AbortController;class Jt{constructor(e={},t={}){void 0===e?e=null:be(e,"First parameter");const r=Ut(t,"Second parameter"),o=function(e,t){he(e,t);const r=null==e?void 0:e.abort,o=null==e?void 0:e.close,n=null==e?void 0:e.start,i=null==e?void 0:e.type,a=null==e?void 0:e.write;return{abort:void 0===r?void 0:Vt(r,e,`${t} has member 'abort' that`),close:void 0===o?void 0:Ht(o,e,`${t} has member 'close' that`),start:void 0===n?void 0:Yt(n,e,`${t} has member 'start' that`),write:void 0===a?void 0:Qt(a,e,`${t} has member 'write' that`),type:i}}(e,"First parameter");if(Zt(this),void 0!==o.type)throw new RangeError("Invalid type is specified");const n=Dt(r);!function(e,t,r,o){const n=Object.create(mr.prototype);let i,a,s,l;i=void 0!==t.start?()=>t.start(n):()=>{},a=void 0!==t.write?e=>t.write(e,n):()=>B(void 0),s=void 0!==t.close?()=>t.close():()=>B(void 0),l=void 0!==t.abort?e=>t.abort(e):()=>B(void 0),vr(e,n,i,a,s,l,r,o)}(this,o,zt(r,1),n)}get locked(){if(!er(this))throw Or("locked");return tr(this)}abort(e=void 0){return er(this)?tr(this)?z(new TypeError("Cannot abort a stream that already has a writer")):rr(this,e):z(Or("abort"))}close(){return er(this)?tr(this)?z(new TypeError("Cannot close a stream that already has a writer")):sr(this)?z(new TypeError("Cannot close an already-closing stream")):or(this):z(Or("close"))}getWriter(){if(!er(this))throw Or("getWriter");return Xt(this)}}function Xt(e){return new cr(e)}function Zt(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new J,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function er(e){return!!q(e)&&!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof Jt}function tr(e){return void 0!==e._writer}function rr(e,t){var r;if("closed"===e._state||"errored"===e._state)return B(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort(t);const o=e._state;if("closed"===o||"errored"===o)return B(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let n=!1;"erroring"===o&&(n=!0,t=void 0);const i=F(((r,o)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:o,_reason:t,_wasAlreadyErroring:n}}));return e._pendingAbortRequest._promise=i,n||ir(e,t),i}function or(e){const t=e._state;if("closed"===t||"errored"===t)return z(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));const r=F(((t,r)=>{const o={_resolve:t,_reject:r};e._closeRequest=o})),o=e._writer;var n;return void 0!==o&&e._backpressure&&"writable"===t&&Nr(o),Xe(n=e._writableStreamController,yr,0),Sr(n),r}function nr(e,t){"writable"!==e._state?ar(e):ir(e,t)}function ir(e,t){const r=e._writableStreamController;e._state="erroring",e._storedError=t;const o=e._writer;void 0!==o&&hr(o,t),!function(e){return void 0!==e._inFlightWriteRequest||void 0!==e._inFlightCloseRequest}(e)&&r._started&&ar(e)}function ar(e){e._state="errored",e._writableStreamController[Z]();const t=e._storedError;if(e._writeRequests.forEach((e=>{e._reject(t)})),e._writeRequests=new J,void 0===e._pendingAbortRequest)return void lr(e);const r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring)return r._reject(t),void lr(e);U(e._writableStreamController[X](r._reason),(()=>(r._resolve(),lr(e),null)),(t=>(r._reject(t),lr(e),null)))}function sr(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function lr(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);const t=e._writer;void 0!==t&&kr(t,e._storedError)}function ur(e,t){const r=e._writer;void 0!==r&&t!==e._backpressure&&(t?function(e){xr(e)}(r):Nr(r)),e._backpressure=t}Object.defineProperties(Jt.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),I(Jt.prototype.abort,"abort"),I(Jt.prototype.close,"close"),I(Jt.prototype.getWriter,"getWriter"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Jt.prototype,Symbol.toStringTag,{value:"WritableStream",configurable:!0});class cr{constructor(e){if(ye(e,1,"WritableStreamDefaultWriter"),Gt(e,"First parameter"),tr(e))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;const t=e._state;if("writable"===t)!sr(e)&&e._backpressure?xr(this):Wr(this),jr(this);else if("erroring"===t)Lr(this,e._storedError),jr(this);else if("closed"===t)Wr(this),jr(this),Ir(this);else{const t=e._storedError;Lr(this,t),qr(this,t)}}get closed(){return fr(this)?this._closedPromise:z(Cr("closed"))}get desiredSize(){if(!fr(this))throw Cr("desiredSize");if(void 0===this._ownerWritableStream)throw Ar("desiredSize");return function(e){const t=e._ownerWritableStream,r=t._state;return"errored"===r||"erroring"===r?null:"closed"===r?0:wr(t._writableStreamController)}(this)}get ready(){return fr(this)?this._readyPromise:z(Cr("ready"))}abort(e=void 0){return fr(this)?void 0===this._ownerWritableStream?z(Ar("abort")):function(e,t){return rr(e._ownerWritableStream,t)}(this,e):z(Cr("abort"))}close(){if(!fr(this))return z(Cr("close"));const e=this._ownerWritableStream;return void 0===e?z(Ar("close")):sr(e)?z(new TypeError("Cannot close an already-closing stream")):dr(this)}releaseLock(){if(!fr(this))throw Cr("releaseLock");void 0!==this._ownerWritableStream&&pr(this)}write(e=void 0){return fr(this)?void 0===this._ownerWritableStream?z(Ar("write to")):br(this,e):z(Cr("write"))}}function fr(e){return!!q(e)&&!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof cr}function dr(e){return or(e._ownerWritableStream)}function hr(e,t){"pending"===e._readyPromiseState?Mr(e,t):function(e,t){Lr(e,t)}(e,t)}function pr(e){const t=e._ownerWritableStream,r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");hr(e,r),function(e,t){"pending"===e._closedPromiseState?kr(e,t):function(e,t){qr(e,t)}(e,t)}(e,r),t._writer=void 0,e._ownerWritableStream=void 0}function br(e,t){const r=e._ownerWritableStream,o=r._writableStreamController,n=function(e,t){try{return e._strategySizeAlgorithm(t)}catch(t){return Tr(e,t),1}}(o,t);if(r!==e._ownerWritableStream)return z(Ar("write to"));const i=r._state;if("errored"===i)return z(r._storedError);if(sr(r)||"closed"===i)return z(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===i)return z(r._storedError);const a=function(e){return F(((t,r)=>{const o={_resolve:t,_reject:r};e._writeRequests.push(o)}))}(r);return function(e,t,r){try{Xe(e,t,r)}catch(t){return void Tr(e,t)}const o=e._controlledWritableStream;sr(o)||"writable"!==o._state||ur(o,Pr(e)),Sr(e)}(o,t,n),a}Object.defineProperties(cr.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),I(cr.prototype.abort,"abort"),I(cr.prototype.close,"close"),I(cr.prototype.releaseLock,"releaseLock"),I(cr.prototype.write,"write"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(cr.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});const yr={};class mr{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!_r(this))throw Rr("abortReason");return this._abortReason}get signal(){if(!_r(this))throw Rr("signal");if(void 0===this._abortController)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(e=void 0){if(!_r(this))throw Rr("error");"writable"===this._controlledWritableStream._state&&Er(this,e)}[X](e){const t=this._abortAlgorithm(e);return gr(this),t}[Z](){Ze(this)}}function _r(e){return!!q(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof mr}function vr(e,t,r,o,n,i,a,s){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,Ze(t),t._abortReason=void 0,t._abortController=function(){if(Kt)return new AbortController}(),t._started=!1,t._strategySizeAlgorithm=s,t._strategyHWM=a,t._writeAlgorithm=o,t._closeAlgorithm=n,t._abortAlgorithm=i;const l=Pr(t);ur(e,l),U(B(r()),(()=>(t._started=!0,Sr(t),null)),(r=>(t._started=!0,nr(e,r),null)))}function gr(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function wr(e){return e._strategyHWM-e._queueTotalSize}function Sr(e){const t=e._controlledWritableStream;if(!e._started)return;if(void 0!==t._inFlightWriteRequest)return;if("erroring"===t._state)return void ar(t);if(0===e._queue.length)return;const r=e._queue.peek().value;r===yr?function(e){const t=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(t),Je(e);const r=e._closeAlgorithm();gr(e),U(r,(()=>(function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";const t=e._writer;void 0!==t&&Ir(t)}(t),null)),(e=>(function(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),nr(e,t)}(t,e),null)))}(e):function(e,t){const r=e._controlledWritableStream;!function(e){e._inFlightWriteRequest=e._writeRequests.shift()}(r),U(e._writeAlgorithm(t),(()=>{!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(r);const t=r._state;if(Je(e),!sr(r)&&"writable"===t){const t=Pr(e);ur(r,t)}return Sr(e),null}),(t=>("writable"===r._state&&gr(e),function(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,nr(e,t)}(r,t),null)))}(e,r)}function Tr(e,t){"writable"===e._controlledWritableStream._state&&Er(e,t)}function Pr(e){return wr(e)<=0}function Er(e,t){const r=e._controlledWritableStream;gr(e),ir(r,t)}function Or(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function Rr(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function Cr(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function Ar(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function jr(e){e._closedPromise=F(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"}))}function qr(e,t){jr(e),kr(e,t)}function kr(e,t){void 0!==e._closedPromise_reject&&(Y(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function Ir(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function xr(e){e._readyPromise=F(((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r})),e._readyPromiseState="pending"}function Lr(e,t){xr(e),Mr(e,t)}function Wr(e){xr(e),Nr(e)}function Mr(e,t){void 0!==e._readyPromise_reject&&(Y(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function Nr(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(mr.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(mr.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});const Fr="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0,Br=function(){const e=null==Fr?void 0:Fr.DOMException;return function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;if("DOMException"!==e.name)return!1;try{return new e,!0}catch(e){return!1}}(e)?e:void 0}()||function(){const e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return I(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}();function zr(e,t,r,o,n,i){const a=Se(e),s=Xt(t);e._disturbed=!0;let l=!1,u=B(void 0);return F(((c,f)=>{let d;if(void 0!==i){if(d=()=>{const r=void 0!==i.reason?i.reason:new Br("Aborted","AbortError"),a=[];o||a.push((()=>"writable"===t._state?rr(t,r):B(void 0))),n||a.push((()=>"readable"===e._state?ho(e,r):B(void 0))),_((()=>Promise.all(a.map((e=>e())))),!0,r)},i.aborted)return void d();i.addEventListener("abort",d)}var h,p,b;if(m(e,a._closedPromise,(e=>(o?v(!0,e):_((()=>rr(t,e)),!0,e),null))),m(t,s._closedPromise,(t=>(n?v(!0,t):_((()=>ho(e,t)),!0,t),null))),h=e,p=a._closedPromise,b=()=>(r?v():_((()=>function(e){const t=e._ownerWritableStream,r=t._state;return sr(t)||"closed"===r?B(void 0):"errored"===r?z(t._storedError):dr(e)}(s))),null),"closed"===h._state?b():$(p,b),sr(t)||"closed"===t._state){const t=new TypeError("the destination writable stream closed before all data could be piped to it");n?v(!0,t):_((()=>ho(e,t)),!0,t)}function y(){const e=u;return D(u,(()=>e!==u?y():void 0))}function m(e,t,r){"errored"===e._state?r(e._storedError):V(t,r)}function _(e,r,o){function n(){return U(e(),(()=>g(r,o)),(e=>g(!0,e))),null}l||(l=!0,"writable"!==t._state||sr(t)?n():$(y(),n))}function v(e,r){l||(l=!0,"writable"!==t._state||sr(t)?g(e,r):$(y(),(()=>g(e,r))))}function g(e,t){return pr(s),ie(a),void 0!==i&&i.removeEventListener("abort",d),e?f(t):c(void 0),null}Y(F(((e,t)=>{!function r(o){o?e():D(l?B(!0):D(s._readyPromise,(()=>F(((e,t)=>{Ae(a,{_chunkSteps:t=>{u=D(br(s,t),void 0,j),e(!1)},_closeSteps:()=>e(!0),_errorSteps:t})})))),r,t)}(!1)})))}))}class Dr{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!Ur(this))throw Zr("desiredSize");return Kr(this)}close(){if(!Ur(this))throw Zr("close");if(!Jr(this))throw new TypeError("The stream is not in a state that permits close");Yr(this)}enqueue(e=void 0){if(!Ur(this))throw Zr("enqueue");if(!Jr(this))throw new TypeError("The stream is not in a state that permits enqueue");return Qr(this,e)}error(e=void 0){if(!Ur(this))throw Zr("error");Gr(this,e)}[ee](e){Ze(this);const t=this._cancelAlgorithm(e);return Hr(this),t}[te](e){const t=this._controlledReadableStream;if(this._queue.length>0){const r=Je(this);this._closeRequested&&0===this._queue.length?(Hr(this),po(t)):$r(this),e._chunkSteps(r)}else Te(t,e),$r(this)}[re](){}}function Ur(e){return!!q(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof Dr}function $r(e){Vr(e)&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,U(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,$r(e)),null)),(t=>(Gr(e,t),null)))))}function Vr(e){const t=e._controlledReadableStream;return!!Jr(e)&&!!e._started&&(!!(fo(t)&&Ee(t)>0)||Kr(e)>0)}function Hr(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Yr(e){if(!Jr(e))return;const t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(Hr(e),po(t))}function Qr(e,t){if(!Jr(e))return;const r=e._controlledReadableStream;if(fo(r)&&Ee(r)>0)Pe(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw Gr(e,t),t}try{Xe(e,t,r)}catch(t){throw Gr(e,t),t}}$r(e)}function Gr(e,t){const r=e._controlledReadableStream;"readable"===r._state&&(Ze(e),Hr(e),bo(r,t))}function Kr(e){const t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function Jr(e){const t=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===t}function Xr(e,t,r,o,n,i,a){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,Ze(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=a,t._strategyHWM=i,t._pullAlgorithm=o,t._cancelAlgorithm=n,e._readableStreamController=t,U(B(r()),(()=>(t._started=!0,$r(t),null)),(e=>(Gr(t,e),null)))}function Zr(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function eo(e){return q(t=e)&&void 0!==t.getReader?function(e){let t;return t=so(j,(function(){let r;try{r=e.read()}catch(r){return z(r)}return H(r,(e=>{if(!q(e))throw new TypeError("The promise returned by the reader.read() method must fulfill with an object");if(e.done)Yr(t._readableStreamController);else{const r=e.value;Qr(t._readableStreamController,r)}}))}),(function(t){try{return B(e.cancel(t))}catch(t){return z(t)}}),0),t}(e.getReader()):function(e){let t;const r=Ue(e,"async");return t=so(j,(function(){let e;try{e=$e(r)}catch(e){return z(e)}return H(B(e),(e=>{if(!q(e))throw new TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(e.done)Yr(t._readableStreamController);else{const r=e.value;Qr(t._readableStreamController,r)}}))}),(function(e){const t=r.iterator;let o;try{o=Be(t,"return")}catch(e){return z(e)}return void 0===o?B(void 0):H(K(o,t,[e]),(e=>{if(!q(e))throw new TypeError("The promise returned by the iterator.return() method must fulfill with an object")}))}),0),t}(e);var t}function to(e,t,r){return pe(e,r),r=>K(e,t,[r])}function ro(e,t,r){return pe(e,r),r=>K(e,t,[r])}function oo(e,t,r){return pe(e,r),r=>G(e,t,[r])}function no(e,t){if("bytes"!=(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function io(e,t){he(e,t);const r=null==e?void 0:e.preventAbort,o=null==e?void 0:e.preventCancel,n=null==e?void 0:e.preventClose,i=null==e?void 0:e.signal;return void 0!==i&&function(e,t){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError(`${t} is not an AbortSignal.`)}(i,`${t} has member 'signal' that`),{preventAbort:Boolean(r),preventCancel:Boolean(o),preventClose:Boolean(n),signal:i}}Object.defineProperties(Dr.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),I(Dr.prototype.close,"close"),I(Dr.prototype.enqueue,"enqueue"),I(Dr.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Dr.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class ao{constructor(e={},t={}){void 0===e?e=null:be(e,"First parameter");const r=Ut(t,"Second parameter"),o=function(e,t){he(e,t);const r=e,o=null==r?void 0:r.autoAllocateChunkSize,n=null==r?void 0:r.cancel,i=null==r?void 0:r.pull,a=null==r?void 0:r.start,s=null==r?void 0:r.type;return{autoAllocateChunkSize:void 0===o?void 0:ge(o,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===n?void 0:to(n,r,`${t} has member 'cancel' that`),pull:void 0===i?void 0:ro(i,r,`${t} has member 'pull' that`),start:void 0===a?void 0:oo(a,r,`${t} has member 'start' that`),type:void 0===s?void 0:no(s,`${t} has member 'type' that`)}}(e,"First parameter");if(uo(this),"bytes"===o.type){if(void 0!==r.size)throw new RangeError("The strategy for a byte stream cannot have a size function");!function(e,t,r){const o=Object.create(rt.prototype);let n,i,a;n=void 0!==t.start?()=>t.start(o):()=>{},i=void 0!==t.pull?()=>t.pull(o):()=>B(void 0),a=void 0!==t.cancel?e=>t.cancel(e):()=>B(void 0);const s=t.autoAllocateChunkSize;if(0===s)throw new TypeError("autoAllocateChunkSize must be greater than 0");Ct(e,o,n,i,a,r,s)}(this,o,zt(r,0))}else{const e=Dt(r);!function(e,t,r,o){const n=Object.create(Dr.prototype);let i,a,s;i=void 0!==t.start?()=>t.start(n):()=>{},a=void 0!==t.pull?()=>t.pull(n):()=>B(void 0),s=void 0!==t.cancel?e=>t.cancel(e):()=>B(void 0),Xr(e,n,i,a,s,r,o)}(this,o,zt(r,1),e)}}get locked(){if(!co(this))throw yo("locked");return fo(this)}cancel(e=void 0){return co(this)?fo(this)?z(new TypeError("Cannot cancel a stream that already has a reader")):ho(this,e):z(yo("cancel"))}getReader(e=void 0){if(!co(this))throw yo("getReader");return void 0===function(e,t){he(e,t);const r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:qt(r,`${t} has member 'mode' that`)}}(e,"First parameter").mode?Se(this):kt(this)}pipeThrough(e,t={}){if(!co(this))throw yo("pipeThrough");ye(e,1,"pipeThrough");const r=function(e,t){he(e,t);const r=null==e?void 0:e.readable;me(r,"readable","ReadableWritablePair"),we(r,`${t} has member 'readable' that`);const o=null==e?void 0:e.writable;return me(o,"writable","ReadableWritablePair"),Gt(o,`${t} has member 'writable' that`),{readable:r,writable:o}}(e,"First parameter"),o=io(t,"Second parameter");if(fo(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(tr(r.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return Y(zr(this,r.writable,o.preventClose,o.preventAbort,o.preventCancel,o.signal)),r.readable}pipeTo(e,t={}){if(!co(this))return z(yo("pipeTo"));if(void 0===e)return z("Parameter 1 is required in 'pipeTo'.");if(!er(e))return z(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let r;try{r=io(t,"Second parameter")}catch(e){return z(e)}return fo(this)?z(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):tr(e)?z(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):zr(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!co(this))throw yo("tee");return Le(function(e){return ot(e._readableStreamController)?function(e){let t,r,o,n,i,a=Se(e),s=!1,l=!1,u=!1,c=!1,f=!1;const d=F((e=>{i=e}));function h(e){V(e._closedPromise,(t=>(e!==a||(St(o._readableStreamController,t),St(n._readableStreamController,t),c&&f||i(void 0)),null)))}function p(){Mt(a)&&(ie(a),a=Se(e),h(a)),Ae(a,{_chunkSteps:t=>{Q((()=>{l=!1,u=!1;const r=t;let a=t;if(!c&&!f)try{a=Ke(t)}catch(t){return St(o._readableStreamController,t),St(n._readableStreamController,t),void i(ho(e,t))}c||wt(o._readableStreamController,r),f||wt(n._readableStreamController,a),s=!1,l?y():u&&m()}))},_closeSteps:()=>{s=!1,c||gt(o._readableStreamController),f||gt(n._readableStreamController),o._readableStreamController._pendingPullIntos.length>0&&Ot(o._readableStreamController,0),n._readableStreamController._pendingPullIntos.length>0&&Ot(n._readableStreamController,0),c&&f||i(void 0)},_errorSteps:()=>{s=!1}})}function b(t,r){Ce(a)&&(ie(a),a=kt(e),h(a));const d=r?n:o,p=r?o:n;Nt(a,t,1,{_chunkSteps:t=>{Q((()=>{l=!1,u=!1;const o=r?f:c;if(r?c:f)o||Rt(d._readableStreamController,t);else{let r;try{r=Ke(t)}catch(t){return St(d._readableStreamController,t),St(p._readableStreamController,t),void i(ho(e,t))}o||Rt(d._readableStreamController,t),wt(p._readableStreamController,r)}s=!1,l?y():u&&m()}))},_closeSteps:e=>{s=!1;const t=r?f:c,o=r?c:f;t||gt(d._readableStreamController),o||gt(p._readableStreamController),void 0!==e&&(t||Rt(d._readableStreamController,e),!o&&p._readableStreamController._pendingPullIntos.length>0&&Ot(p._readableStreamController,0)),t&&o||i(void 0)},_errorSteps:()=>{s=!1}})}function y(){if(s)return l=!0,B(void 0);s=!0;const e=Pt(o._readableStreamController);return null===e?p():b(e._view,!1),B(void 0)}function m(){if(s)return u=!0,B(void 0);s=!0;const e=Pt(n._readableStreamController);return null===e?p():b(e._view,!0),B(void 0)}function _(){}return o=lo(_,y,(function(o){if(c=!0,t=o,f){const o=Le([t,r]),n=ho(e,o);i(n)}return d})),n=lo(_,m,(function(o){if(f=!0,r=o,c){const o=Le([t,r]),n=ho(e,o);i(n)}return d})),h(a),[o,n]}(e):function(e){const t=Se(e);let r,o,n,i,a,s=!1,l=!1,u=!1,c=!1;const f=F((e=>{a=e}));function d(){return s?(l=!0,B(void 0)):(s=!0,Ae(t,{_chunkSteps:e=>{Q((()=>{l=!1;const t=e,r=e;u||Qr(n._readableStreamController,t),c||Qr(i._readableStreamController,r),s=!1,l&&d()}))},_closeSteps:()=>{s=!1,u||Yr(n._readableStreamController),c||Yr(i._readableStreamController),u&&c||a(void 0)},_errorSteps:()=>{s=!1}}),B(void 0))}function h(){}return n=so(h,d,(function(t){if(u=!0,r=t,c){const t=Le([r,o]),n=ho(e,t);a(n)}return f})),i=so(h,d,(function(t){if(c=!0,o=t,u){const t=Le([r,o]),n=ho(e,t);a(n)}return f})),V(t._closedPromise,(e=>(Gr(n._readableStreamController,e),Gr(i._readableStreamController,e),u&&c||a(void 0),null))),[n,i]}(e)}(this))}values(e=void 0){if(!co(this))throw yo("values");return function(e,t){const r=Se(e),o=new Ve(r,t),n=Object.create(He);return n._asyncIteratorImpl=o,n}(this,function(e){he(e,"First parameter");const t=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(t)}}(e).preventCancel)}[De](e){return this.values(e)}static from(e){return eo(e)}}function so(e,t,r,o=1,n=()=>1){const i=Object.create(ao.prototype);return uo(i),Xr(i,Object.create(Dr.prototype),e,t,r,o,n),i}function lo(e,t,r){const o=Object.create(ao.prototype);return uo(o),Ct(o,Object.create(rt.prototype),e,t,r,0,void 0),o}function uo(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function co(e){return!!q(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof ao}function fo(e){return void 0!==e._reader}function ho(e,t){if(e._disturbed=!0,"closed"===e._state)return B(void 0);if("errored"===e._state)return z(e._storedError);po(e);const r=e._reader;if(void 0!==r&&Mt(r)){const e=r._readIntoRequests;r._readIntoRequests=new J,e.forEach((e=>{e._closeSteps(void 0)}))}return H(e._readableStreamController[ee](t),j)}function po(e){e._state="closed";const t=e._reader;if(void 0!==t&&(ce(t),Ce(t))){const e=t._readRequests;t._readRequests=new J,e.forEach((e=>{e._closeSteps()}))}}function bo(e,t){e._state="errored",e._storedError=t;const r=e._reader;void 0!==r&&(ue(r,t),Ce(r)?je(r,t):Ft(r,t))}function yo(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function mo(e,t){he(e,t);const r=null==e?void 0:e.highWaterMark;return me(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:_e(r)}}Object.defineProperties(ao,{from:{enumerable:!0}}),Object.defineProperties(ao.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),I(ao.from,"from"),I(ao.prototype.cancel,"cancel"),I(ao.prototype.getReader,"getReader"),I(ao.prototype.pipeThrough,"pipeThrough"),I(ao.prototype.pipeTo,"pipeTo"),I(ao.prototype.tee,"tee"),I(ao.prototype.values,"values"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ao.prototype,Symbol.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(ao.prototype,De,{value:ao.prototype.values,writable:!0,configurable:!0});const _o=e=>e.byteLength;I(_o,"size");class vo{constructor(e){ye(e,1,"ByteLengthQueuingStrategy"),e=mo(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!wo(this))throw go("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!wo(this))throw go("size");return _o}}function go(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function wo(e){return!!q(e)&&!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof vo}Object.defineProperties(vo.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(vo.prototype,Symbol.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});const So=()=>1;I(So,"size");class To{constructor(e){ye(e,1,"CountQueuingStrategy"),e=mo(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!Eo(this))throw Po("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!Eo(this))throw Po("size");return So}}function Po(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function Eo(e){return!!q(e)&&!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof To}function Oo(e,t,r){return pe(e,r),r=>K(e,t,[r])}function Ro(e,t,r){return pe(e,r),r=>G(e,t,[r])}function Co(e,t,r){return pe(e,r),(r,o)=>K(e,t,[r,o])}function Ao(e,t,r){return pe(e,r),r=>K(e,t,[r])}Object.defineProperties(To.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(To.prototype,Symbol.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class jo{constructor(e={},t={},r={}){void 0===e&&(e=null);const o=Ut(t,"Second parameter"),n=Ut(r,"Third parameter"),i=function(e,t){he(e,t);const r=null==e?void 0:e.cancel,o=null==e?void 0:e.flush,n=null==e?void 0:e.readableType,i=null==e?void 0:e.start,a=null==e?void 0:e.transform,s=null==e?void 0:e.writableType;return{cancel:void 0===r?void 0:Ao(r,e,`${t} has member 'cancel' that`),flush:void 0===o?void 0:Oo(o,e,`${t} has member 'flush' that`),readableType:n,start:void 0===i?void 0:Ro(i,e,`${t} has member 'start' that`),transform:void 0===a?void 0:Co(a,e,`${t} has member 'transform' that`),writableType:s}}(e,"First parameter");if(void 0!==i.readableType)throw new RangeError("Invalid readableType specified");if(void 0!==i.writableType)throw new RangeError("Invalid writableType specified");const a=zt(n,0),s=Dt(n),l=zt(o,1),u=Dt(o);let c;!function(e,t,r,o,n,i){function a(){return t}e._writable=function(e,t,r,o,n=1,i=()=>1){const a=Object.create(Jt.prototype);return Zt(a),vr(a,Object.create(mr.prototype),e,t,r,o,n,i),a}(a,(function(t){return function(e,t){const r=e._transformStreamController;return e._backpressure?H(e._backpressureChangePromise,(()=>{const o=e._writable;if("erroring"===o._state)throw o._storedError;return Bo(r,t)})):Bo(r,t)}(e,t)}),(function(){return function(e){const t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;const r=e._readable;t._finishPromise=F(((e,r)=>{t._finishPromise_resolve=e,t._finishPromise_reject=r}));const o=t._flushAlgorithm();return No(t),U(o,(()=>("errored"===r._state?Uo(t,r._storedError):(Yr(r._readableStreamController),Do(t)),null)),(e=>(Gr(r._readableStreamController,e),Uo(t,e),null))),t._finishPromise}(e)}),(function(t){return function(e,t){const r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;const o=e._readable;r._finishPromise=F(((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t}));const n=r._cancelAlgorithm(t);return No(r),U(n,(()=>("errored"===o._state?Uo(r,o._storedError):(Gr(o._readableStreamController,t),Do(r)),null)),(e=>(Gr(o._readableStreamController,e),Uo(r,e),null))),r._finishPromise}(e,t)}),r,o),e._readable=so(a,(function(){return function(e){return Lo(e,!1),e._backpressureChangePromise}(e)}),(function(t){return function(e,t){const r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;const o=e._writable;r._finishPromise=F(((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t}));const n=r._cancelAlgorithm(t);return No(r),U(n,(()=>("errored"===o._state?Uo(r,o._storedError):(Tr(o._writableStreamController,t),xo(e),Do(r)),null)),(t=>(Tr(o._writableStreamController,t),xo(e),Uo(r,t),null))),r._finishPromise}(e,t)}),n,i),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,Lo(e,!0),e._transformStreamController=void 0}(this,F((e=>{c=e})),l,u,a,s),function(e,t){const r=Object.create(Wo.prototype);let o,n,i;o=void 0!==t.transform?e=>t.transform(e,r):e=>{try{return Fo(r,e),B(void 0)}catch(e){return z(e)}},n=void 0!==t.flush?()=>t.flush(r):()=>B(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>B(void 0),function(e,t,r,o,n){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=r,t._flushAlgorithm=o,t._cancelAlgorithm=n,t._finishPromise=void 0,t._finishPromise_resolve=void 0,t._finishPromise_reject=void 0}(e,r,o,n,i)}(this,i),void 0!==i.start?c(i.start(this._transformStreamController)):c(void 0)}get readable(){if(!qo(this))throw $o("readable");return this._readable}get writable(){if(!qo(this))throw $o("writable");return this._writable}}function qo(e){return!!q(e)&&!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof jo}function ko(e,t){Gr(e._readable._readableStreamController,t),Io(e,t)}function Io(e,t){No(e._transformStreamController),Tr(e._writable._writableStreamController,t),xo(e)}function xo(e){e._backpressure&&Lo(e,!1)}function Lo(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=F((t=>{e._backpressureChangePromise_resolve=t})),e._backpressure=t}Object.defineProperties(jo.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(jo.prototype,Symbol.toStringTag,{value:"TransformStream",configurable:!0});class Wo{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!Mo(this))throw zo("desiredSize");return Kr(this._controlledTransformStream._readable._readableStreamController)}enqueue(e=void 0){if(!Mo(this))throw zo("enqueue");Fo(this,e)}error(e=void 0){if(!Mo(this))throw zo("error");var t;t=e,ko(this._controlledTransformStream,t)}terminate(){if(!Mo(this))throw zo("terminate");!function(e){const t=e._controlledTransformStream;Yr(t._readable._readableStreamController),Io(t,new TypeError("TransformStream terminated"))}(this)}}function Mo(e){return!!q(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof Wo}function No(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}function Fo(e,t){const r=e._controlledTransformStream,o=r._readable._readableStreamController;if(!Jr(o))throw new TypeError("Readable side is not in a state that permits enqueue");try{Qr(o,t)}catch(e){throw Io(r,e),r._readable._storedError}const n=function(e){return!Vr(e)}(o);n!==r._backpressure&&Lo(r,!0)}function Bo(e,t){return H(e._transformAlgorithm(t),void 0,(t=>{throw ko(e._controlledTransformStream,t),t}))}function zo(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function Do(e){void 0!==e._finishPromise_resolve&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function Uo(e,t){void 0!==e._finishPromise_reject&&(Y(e._finishPromise),e._finishPromise_reject(t),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function $o(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}Object.defineProperties(Wo.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),I(Wo.prototype.enqueue,"enqueue"),I(Wo.prototype.error,"error"),I(Wo.prototype.terminate,"terminate"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Wo.prototype,Symbol.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});var Vo,Ho=(Vo=function(e,t){return Vo=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},Vo(e,t)},function(e,t){function r(){this.constructor=e}Vo(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),Yo=function(){return Yo=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},Yo.apply(this,arguments)},Qo="Error when aborting requestTask",Go=function(e){function t(t){void 0===t&&(t={});var r=e.call(this)||this,o=t.timeout,n=t.timeoutMsg,i=t.restrictedMethods;return r._timeout=o||0,r._timeoutMsg=n||"请求超时",r._restrictedMethods=i||["get","post","upload","download"],r}return Ho(t,e),t.prototype.post=function(e){var t=this;return new Promise((function(r,o){var n=e.url,i=e.data,a=e.headers,s=wx.request({url:u("https:",n),data:i,timeout:t._timeout,method:"POST",header:a,success:function(e){r(e)},fail:function(e){o(e)},complete:function(e){if(e&&e.errMsg&&t._timeout&&-1!==t._restrictedMethods.indexOf("post")&&"request:fail timeout"===e.errMsg){console.warn(t._timeoutMsg);try{s.abort()}catch(e){}}}})}))},t.prototype.upload=function(e){var t=this,r=this;return new Promise((function(o){return function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function a(e){try{l(o.next(e))}catch(e){i(e)}}function s(e){try{l(o.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((o=o.apply(e,t||[])).next())}))}(t,void 0,void 0,(function(){var t,n,i,a,s,l;return function(e,t){var r,o,n,i,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,o&&(n=2&i[0]?o.return:i[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,i[1])).done)return n;switch(o=0,n&&(i=[2&i[0],n.value]),i[0]){case 0:case 1:n=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,o=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((n=(n=a.trys).length>0&&n[n.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!n||i[1]>n[0]&&i[1]<n[3])){a.label=i[1];break}if(6===i[0]&&a.label<n[1]){a.label=n[1],n=i;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(i);break}n[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],o=0}finally{r=n=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}(this,(function(u){return t=e.url,n=e.file,i=e.data,a=e.headers,s=e.onUploadProgress,l=wx.uploadFile({url:t,filePath:n,name:"file",formData:Yo({},i),header:a,timeout:this._timeout,success:function(e){var t={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&i.success_action_status&&(t.statusCode=parseInt(i.success_action_status,10)),o(t)},fail:function(e){o(e)},complete:function(e){if(e&&e.errMsg&&r._timeout&&-1!==r._restrictedMethods.indexOf("upload")&&"request:fail timeout"===e.errMsg){console.warn(r._timeoutMsg);try{l.abort()}catch(e){}}}}),s&&l.onProgressUpdate((function(e){s(e)})),[2]}))}))}))},t.prototype.download=function(e){var t=this,r=this;return new Promise((function(o,n){var i=e.url,a=e.headers,s=wx.downloadFile({url:u("https:",i),header:a,timeout:t._timeout,success:function(e){200===e.statusCode&&e.tempFilePath?o({statusCode:200,tempFilePath:e.tempFilePath}):o(e)},fail:function(e){n(e)},complete:function(e){if(e&&e.errMsg&&r._timeout&&-1!==r._restrictedMethods.indexOf("download")&&"request:fail timeout"===e.errMsg){console.warn(r._timeoutMsg);try{s.abort()}catch(e){}}}})}))},t.prototype.fetch=function(e){var t=e.url,r=e.body,o=e.enableAbort,n=e.headers,i=e.method,a=e.stream,s=void 0!==a&&a,l=e.signal,c=e.timeout,f=this,d=null!=c?c:this._timeout,h=null,p=new ao({start:function(e){h=e},cancel:function(){h=null}});return new Promise((function(e,a){s&&e({data:p});var c=wx.request({url:u("https:",t),data:r,timeout:d,method:i.toUpperCase(),header:n,success:function(t){var r;null===(r=h)||void 0===r||r.close(),!s&&e(t)},fail:function(e){var t;if(null===(t=h)||void 0===t||t.close(),a(e),s)throw e},complete:function(e){if(e&&e.errMsg&&d&&-1!==f._restrictedMethods.indexOf("post")&&o&&"request:fail timeout"===e.errMsg){console.warn(f._timeoutMsg);try{c.abort()}catch(e){console.warn(Qo,e)}}},enableChunked:s});if(c.onChunkReceived((function(e){var t;null===(t=h)||void 0===t||t.enqueue(new Uint8Array(e.data))})),l){var b=function(){try{c.abort()}catch(e){console.warn(Qo,e)}};l.aborted?b():l.addEventListener("abort",(function(){return b()}))}}))},t}(l),Ko={setItem:function(e,t){wx.setStorageSync(e,t)},getItem:function(e){return wx.getStorageSync(e)},removeItem:function(e){wx.removeStorageSync(e)},clear:function(){wx.clearStorageSync()}},Jo=function(e,t){void 0===t&&(t={});var r=wx.connectSocket(Yo({url:e},t));return{set onopen(e){r.onOpen(e)},set onmessage(e){r.onMessage(e)},set onclose(e){r.onClose(e)},set onerror(e){r.onError(e)},send:function(e){return r.send({data:e})},close:function(e,t){return r.close({code:e,reason:t})},get readyState(){return r.readyState},CONNECTING:0,OPEN:1,CLOSING:2,CLOSED:3}},Xo={genAdapter:function(){return{root:{},reqClass:Go,wsClass:Jo,localStorage:Ko,primaryStorage:i.local,getAppSign:function(){var e=wx.getAccountInfoSync();return"undefined"!=typeof App||"undefined"!=typeof getApp||wx.onAppHide||wx.offAppHide||wx.onAppShow||wx.offAppShow?e&&e.miniProgram?e.miniProgram.appId:"":e&&e.plugin?e.plugin.appId:""}}},isMatch:function(){if("undefined"==typeof wx)return!1;if("undefined"==typeof Page)return!1;if(!wx.getSystemInfoSync)return!1;if(!wx.getStorageSync)return!1;if(!wx.setStorageSync)return!1;if(!wx.connectSocket)return!1;if(!wx.request)return!1;try{if(!wx.getSystemInfoSync())return!1;if("qq"===wx.getSystemInfoSync().AppPlatform)return!1}catch(e){return!1}return!0},runtime:"wx_mp"},Zo=function(e,t,r){if(r||2===arguments.length)for(var o,n=0,i=t.length;n<i;n++)!o&&n in t||(o||(o=Array.prototype.slice.call(t,0,n)),o[n]=t[n]);return e.concat(o||Array.prototype.slice.call(t))},en=s,tn={};function rn(e,t){var r=t.name,o=t.namespace,n=t.entity,i=t.injectEvents,a=t.IIFE,s=void 0!==a&&a;if(tn[r]||o&&e[o])throw new Error(JSON.stringify({code:en.INVALID_OPERATION,msg:"Duplicate component ".concat(r)}));if(s){if(!n||"function"!=typeof n)throw new Error(JSON.stringify({code:en.INVALID_PARAMS,msg:"IIFE component's entity must be a function"}));n.call(e)}if(tn[r]=t,o?e.prototype[o]=n:on(e.prototype,n),i){var l=i.bus,u=i.events;if(!l||!u||0===u.length)return;var c=e.prototype.fire||function(){};e.prototype.events||(e.prototype.events={}),(e.prototype.events||{})[r]?e.prototype.events[r].events=Zo(Zo([],e.prototype.events[r].events,!0),u,!0):e.prototype.events[r]={bus:l,events:u},e.prototype.fire=function(e,t){c(e,t);for(var r=0,o=Object.keys(this.events);r<o.length;r++){var n=o[r],i=this.events[n],a=i.bus;if(i.events.includes(e)){a.fire(e,t);break}}}}}function on(e,t){if(!(t instanceof Object))return t;switch(t.constructor){case Date:return new Date(t.getTime());case Object:void 0===e&&(e={});break;case Array:e=[];break;default:return t}for(var r=0,o=Object.keys(t);r<o.length;r++){var n=o[r];Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=on(e[n],t[n]))}return e}var nn={},an=function(){return an=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},an.apply(this,arguments)},sn=P,ln={},un={};function cn(e){var t=e.env,r=e.platformInfo,o={userInfoKey:"".concat("user_info","_").concat(t)};ln[t]=ln[t]||new sn(an(an({},e),{keys:o,platformInfo:r})),un[t]=un[t]||new sn(an(an({},e),{keys:o,platformInfo:r,persistence:"local"}))}function fn(e){return un[e]}var dn=function(){return dn=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},dn.apply(this,arguments)},hn="",pn="@cloudbase/js-sdk";function bn(){return hn}var yn,mn=[],_n=["env","endPointKey","region"],vn="https:";function gn(e){return mn.find((function(t){return _n.filter((function(t){return null!=e[t]})).every((function(r){return t[r]===e[r]}))}))}function wn(e){var t,r,o,n=gn(e);n?(null!=e.baseUrl&&(n.baseUrl=e.baseUrl),null!=e.protocol&&(n.protocol=e.protocol)):mn.push(dn(dn({},e),{protocol:null!==(t=e.protocol)&&void 0!==t?t:vn})),"CLOUD_API"===e.endPointKey&&(o=null!==(r=e.protocol)&&void 0!==r?r:vn,a=o)}function Sn(e,t,r){return gn({env:e,endPointKey:t,region:r})}function Tn(e){var t=Sn(e,"CLOUD_API"),r=t.protocol,o=t.baseUrl;return"".concat(r).concat(o).match(/(http(s)?:)?\/\/([^/?#]*)/)[0]}!function(e){e.NULL="NULL",e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.PHONE="PHONE"}(yn||(yn={}));var Pn=function(){return Pn=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},Pn.apply(this,arguments)},En=function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function a(e){try{l(o.next(e))}catch(e){i(e)}}function s(e){try{l(o.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((o=o.apply(e,t||[])).next())}))},On=function(e,t){var r,o,n,i,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,o&&(n=2&s[0]?o.return:s[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,s[1])).done)return n;switch(o=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,o=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((n=(n=a.trys).length>0&&n[n.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){a.label=s[1];break}if(6===s[0]&&a.label<n[1]){a.label=n[1],n=s;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(s);break}n[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],o=0}finally{r=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}},Rn=s,Cn=c,An=f,jn=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"];function qn(e,t,r){var o=e[t];e[t]=function(t){var n={},i={};r.forEach((function(r){var o=r.call(e,t),a=o.data,s=o.headers;Object.assign(n,a),Object.assign(i,s)}));var a=t.data;return a&&(Cn(a)?Object.keys(n).forEach((function(e){a.append(e,n[e])})):t.data=Pn(Pn({},a),n)),t.headers=Pn(Pn({},t.headers||{}),i),o.call(e,t)}}function kn(){var e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:{"X-SDK-Version":"@cloudbase/js-sdk/".concat(bn()),"x-seqid":e}}}var In=function(){function e(e){this.throwWhenRequestFail=!1,this.config=e;var t={timeout:this.config.timeout,timeoutMsg:"[@cloudbase/js-sdk] 请求在".concat(this.config.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post","put"]};this.reqClass=new nn.adapter.reqClass(t),this.throwWhenRequestFail=e.throw||!1,this.localCache=fn(this.config.env),qn(this.reqClass,"post",[kn]),qn(this.reqClass,"upload",[kn]),qn(this.reqClass,"download",[kn])}return e.prototype.post=function(e){return En(this,void 0,void 0,(function(){return On(this,(function(t){switch(t.label){case 0:return[4,this.reqClass.post(e)];case 1:return[2,t.sent()]}}))}))},e.prototype.upload=function(e){return En(this,void 0,void 0,(function(){return On(this,(function(t){switch(t.label){case 0:return[4,this.reqClass.upload(e)];case 1:return[2,t.sent()]}}))}))},e.prototype.download=function(e){return En(this,void 0,void 0,(function(){return On(this,(function(t){switch(t.label){case 0:return[4,this.reqClass.download(e)];case 1:return[2,t.sent()]}}))}))},e.prototype.getBaseEndPoint=function(){return Tn(this.config.env)},e.prototype.getOauthAccessTokenV2=function(e){return En(this,void 0,void 0,(function(){var t,r;return On(this,(function(o){switch(o.label){case 0:return[4,e.getAccessToken()];case 1:return t=o.sent(),[4,e.getCredentials()];case 2:return r=o.sent(),[2,{accessToken:t,accessTokenExpire:new Date(r.expires_at).getTime()}]}}))}))},e.prototype.request=function(e,t,r){var o,n;return En(this,void 0,void 0,(function(){var i,a,s,l,u,c,f,d,h,p,b,y,m,_,v,g,w,S,T,P;return On(this,(function(E){switch(E.label){case 0:if(i="x-tcb-trace_".concat(this.config.env),a="application/x-www-form-urlencoded",s=Pn({action:e,dataVersion:"2020-01-10",env:this.config.env},t),-1!==jn.indexOf(e))return[3,2];if(!(l=this.config._fromApp).oauthInstance)throw new Error("you can't request without auth");return u=l.oauthInstance,c=u.oauth2client,f=s,[4,this.getOauthAccessTokenV2(c)];case 1:f.access_token=E.sent().accessToken,E.label=2;case 2:return"storage.uploadFile"===e?(d=new FormData,Object.keys(d).forEach((function(e){Object.prototype.hasOwnProperty.call(d,e)&&void 0!==d[e]&&d.append(e,s[e])})),a="multipart/form-data"):(a="application/json;charset=UTF-8",d={},Object.keys(s).forEach((function(e){void 0!==s[e]&&(d[e]=s[e])}))),h={headers:{"content-type":a}},(null==r?void 0:r.onUploadProgress)&&(h.onUploadProgress=r.onUploadProgress),this.config.region&&(h.headers["X-TCB-Region"]=this.config.region),(p=this.localCache.getStore(i))&&(h.headers["X-TCB-Trace"]=p),b=void 0!==(null==r?void 0:r.parse)?r.parse:t.parse,y=void 0!==(null==r?void 0:r.inQuery)?r.inQuery:t.inQuery,m=void 0!==(null==r?void 0:r.search)?r.search:t.search,_=Pn(Pn({},(null==r?void 0:r.defaultQuery)||{}),{env:this.config.env}),b&&(_.parse=!0),y&&(_=Pn(Pn({},y),_)),v=Sn(this.config.env,"CLOUD_API"),g=v.baseUrl,w=v.protocol,S=r.pathname?An(w,"".concat(null===(o=Tn(this.config.env))||void 0===o?void 0:o.replace(/^https?:/,""),"/").concat(r.pathname),_):An(w,g,_),m&&(S+=m),[4,this.post(Pn({url:S,data:d},h))];case 3:if(T=E.sent(),(P=null===(n=T.header)||void 0===n?void 0:n["x-tcb-trace"])&&this.localCache.setStore(i,P),200!==Number(T.status)&&200!==Number(T.statusCode)||!T.data)throw new Error("network request error");return[2,T]}}))}))},e.prototype.fetch=function(e){var t,r,o,n,i,a,s,l;return En(this,void 0,void 0,(function(){var u,c,f,d,h,p,b,y=this;return On(this,(function(m){switch(m.label){case 0:u=e.token,c=e.headers,f=void 0===c?{}:c,d=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]])}return r}(e,["token","headers"]),h=function(){return En(y,void 0,void 0,(function(){var e,t,r;return On(this,(function(o){switch(o.label){case 0:if(null!=u)return[2,u];if(!(e=this.config._fromApp).oauthInstance)throw new Error("you can't request without auth");return t=e.oauthInstance,r=t.oauth2client,[4,this.getOauthAccessTokenV2(r)];case 1:return[2,o.sent().accessToken]}}))}))},p=function(){return En(y,void 0,void 0,(function(){var e,t,r,o,n;return On(this,(function(i){switch(i.label){case 0:return t=(e=this.reqClass).fetch,o={},n={"X-SDK-Version":"@cloudbase/js-sdk/".concat(bn())},r="Bearer ".concat,[4,h()];case 1:return[2,t.apply(e,[Pn.apply(void 0,[(o.headers=Pn.apply(void 0,[(n.Authorization=r.apply("Bearer ",[i.sent()]),n),f]),o),d])])]}}))}))},m.label=1;case 1:return m.trys.push([1,3,,6]),[4,p()];case 2:return[2,m.sent()];case 3:if("ACCESS_TOKEN_EXPIRED"!==(null==(b=m.sent())?void 0:b.code))return[3,5];if("function"!=typeof(null===(n=null===(o=null===(r=null===(t=this.config)||void 0===t?void 0:t._fromApp)||void 0===r?void 0:r.oauthInstance)||void 0===o?void 0:o.authApi)||void 0===n?void 0:n.refreshTokenForce))throw b;return[4,null===(l=null===(s=null===(a=null===(i=this.config)||void 0===i?void 0:i._fromApp)||void 0===a?void 0:a.oauthInstance)||void 0===s?void 0:s.authApi)||void 0===l?void 0:l.refreshTokenForce()];case 4:return m.sent(),[2,p()];case 5:throw b;case 6:return[2]}}))}))},e.prototype.send=function(e,t,r){return void 0===t&&(t={}),void 0===r&&(r={}),En(this,void 0,void 0,(function(){var o;return On(this,(function(n){switch(n.label){case 0:return[4,this.request(e,t,Pn(Pn({},r),{onUploadProgress:t.onUploadProgress}))];case 1:if((o=n.sent()).data.code&&this.throwWhenRequestFail)throw new Error(JSON.stringify({code:Rn.OPERATION_FAIL,msg:"[".concat(o.data.code,"] ").concat(o.data.message)}));return[2,o.data]}}))}))},e}(),xn={},Ln=function(){return Ln=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},Ln.apply(this,arguments)},Wn=function(e,t,r,o){var n,i=arguments.length,a=i<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,r):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,r,o);else for(var s=e.length-1;s>=0;s--)(n=e[s])&&(a=(i<3?n(a):i>3?n(t,r,a):n(t,r))||a);return i>3&&a&&Object.defineProperty(t,r,a),a},Mn=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},Nn=s,Fn="https://support.qq.com/products/148793",Bn=d,zn=function(e){return e.mode,e.customInfo,e.title,e.messages,function(e,t,r){}},Dn={timeout:15e3,persistence:"local"},Un=6e5,$n={},Vn=function(){function e(e){this.cloudbaseConfig=e||this.cloudbaseConfig,this.authInstance=null,this.oauthInstance=null,this.version=bn()}return Object.defineProperty(e.prototype,"config",{get:function(){return this.cloudbaseConfig},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"platform",{get:function(){return nn},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"cache",{get:function(){return e=this.cloudbaseConfig.env,ln[e];var e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"localCache",{get:function(){return fn(this.cloudbaseConfig.env)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"request",{get:function(){return e=this.cloudbaseConfig.env,xn[e];var e},enumerable:!1,configurable:!0}),e.prototype.init=function(t){var r;if(!t.env)throw new Error(JSON.stringify({code:Nn.INVALID_PARAMS,msg:"env must not be specified"}));nn.adapter||this.useDefaultAdapter();var o={timeout:t.timeout||5e3,timeoutMsg:"[".concat(pn,"][REQUEST TIMEOUT] request had been abort since didn't finished within").concat(t.timeout/1e3,"s")};this.requestClient=new nn.adapter.reqClass(o),this.cloudbaseConfig=Ln(Ln({},Dn),t),this.cloudbaseConfig.timeout=this.formatTimeout(this.cloudbaseConfig.timeout);var n=this.cloudbaseConfig,i=n.env,a=n.persistence,s=n.debug,l=n.timeout,u=n.oauthClient;cn({env:i,persistence:a,debug:s,platformInfo:this.platform}),function(e,t){wn({env:e,region:t,baseUrl:t?"//".concat(e,".").concat(t,".tcb-api.tencentcloudapi.com/web"):"//".concat(e,".ap-shanghai.tcb-api.tencentcloudapi.com/web"),protocol:void 0,endPointKey:"CLOUD_API"})}(i,t.region||""),function(e){wn({endPointKey:"GATEWAY",env:e,baseUrl:"//".concat(e,".api.tcloudbasegateway.com/v1"),protocol:void 0})}(i);var c=new e(this.cloudbaseConfig);return function(e){xn[e.env]=new In(Pn(Pn({},e),{throw:!0}))}({env:i,region:t.region||"",timeout:l,oauthClient:u,_fromApp:c}),c.requestClient=this.requestClient,null===(r=null==this?void 0:this.fire)||void 0===r||r.call(this,"cloudbase_init",c),c},e.prototype.updateConfig=function(e){var t=e.persistence,r=e.debug;this.cloudbaseConfig.persistence=t,this.cloudbaseConfig.debug=r,cn({env:this.cloudbaseConfig.env,persistence:t,debug:r,platformInfo:this.platform})},e.prototype.registerExtension=function(e){$n[e.name]=e},e.prototype.invokeExtension=function(e,t){return function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function a(e){try{l(o.next(e))}catch(e){i(e)}}function s(e){try{l(o.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((o=o.apply(e,t||[])).next())}))}(this,void 0,void 0,(function(){var r;return function(e,t){var r,o,n,i,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,o&&(n=2&s[0]?o.return:s[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,s[1])).done)return n;switch(o=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,o=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((n=(n=a.trys).length>0&&n[n.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){a.label=s[1];break}if(6===s[0]&&a.label<n[1]){a.label=n[1],n=s;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(s);break}n[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],o=0}finally{r=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}(this,(function(o){switch(o.label){case 0:if(!(r=$n[e]))throw new Error(JSON.stringify({code:Nn.INVALID_PARAMS,msg:"extension:".concat(e," must be registered before invoke")}));return[4,r.invoke(t,this)];case 1:return[2,o.sent()]}}))}))},e.prototype.useAdapters=function(e){var t=function(e){for(var t,r=0,o=(t=e,"[object Array]"===Object.prototype.toString.call(t)?e:[e]);r<o.length;r++){var n=o[r],i=n.isMatch,a=n.genAdapter,s=n.runtime;if(i())return{adapter:a(),runtime:s}}}(e)||{},r=t.adapter,o=t.runtime;r&&(nn.adapter=r),o&&(nn.runtime=o)},e.prototype.registerHook=function(t){!function(e,t){var r=t.entity,o=t.target;if(Object.prototype.hasOwnProperty.call(e,o))throw new Error(JSON.stringify({code:en.INVALID_OPERATION,msg:"target:".concat(o," is not exist")}));var n=e.prototype[o];if("function"!=typeof n)throw new Error(JSON.stringify({code:en.INVALID_OPERATION,msg:"target:".concat(o," is not a function which is the only type supports hook")}));e.prototype[o]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return r.call.apply(r,Zo([this],e,!1)),n.call.apply(n,Zo([this],e,!1))}}(e,t)},e.prototype.registerComponent=function(t){rn(e,t)},e.prototype.registerVersion=function(e){!function(e){hn=e}(e),this.version=e},e.prototype.registerSdkName=function(e){!function(e){pn=e,function(e){o=e}(e)}(e)},e.prototype.registerEndPoint=function(e,t){wn({baseUrl:e,protocol:t,env:this.config.env,endPointKey:"CLOUD_API"})},e.prototype.registerEndPointWithKey=function(e){wn({env:this.config.env,endPointKey:e.key,baseUrl:e.url,protocol:e.protocol})},e.prototype.getEndPointWithKey=function(e){var t=Sn(this.config.env,e);return{BASE_URL:t.baseUrl,PROTOCOL:t.protocol}},e.prototype.useDefaultAdapter=function(){var e={adapter:{root:window,reqClass:v,wsClass:WebSocket,localStorage:localStorage},runtime:p.WEB},t=e.adapter,r=e.runtime;nn.adapter=t,nn.runtime=r},e.prototype.formatTimeout=function(e){switch(!0){case e>Un:return Bn(Nn.INVALID_PARAMS,"timeout is greater than maximum value[10min]"),Un;case e<100:return Bn(Nn.INVALID_PARAMS,"timeout is less than maximum value[100ms]"),100;default:return e}},Wn([zn({mode:"sync",title:"Cloudbase 初始化失败",messages:["请确认以下各项：","  1 - 调用 cloudbase.init() 的语法或参数是否正确","  2 - 如果是非浏览器环境，是否配置了安全应用来源（https://docs.cloudbase.net/api-reference/webv3/adapter#%E7%AC%AC-2-%E6%AD%A5%E9%85%8D%E7%BD%AE%E5%AE%89%E5%85%A8%E5%BA%94%E7%94%A8%E6%9D%A5%E6%BA%90）","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(Fn)]}),Mn("design:type",Function),Mn("design:paramtypes",[Object]),Mn("design:returntype",e)],e.prototype,"init",null),Wn([zn({title:"调用扩展能力失败",messages:["请确认以下各项：","  1 - 调用 invokeExtension() 的语法或参数是否正确","  2 - 被调用的扩展能力是否已经安装并通过 registerExtension() 注册","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(Fn)]}),Mn("design:type",Function),Mn("design:paramtypes",[String,Object]),Mn("design:returntype",Promise)],e.prototype,"invokeExtension",null),e}(),Hn=new Vn;Hn.useAdapters(Xo);var Yn=Hn},47:function(e){var t=function(e){"use strict";var t,r=Object.prototype,o=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function c(e,t,r,o){var i=t&&t.prototype instanceof m?t:m,a=Object.create(i.prototype),s=new j(o||[]);return n(a,"_invoke",{value:O(e,r,s)}),a}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var d="suspendedStart",h="suspendedYield",p="executing",b="completed",y={};function m(){}function _(){}function v(){}var g={};u(g,a,(function(){return this}));var w=Object.getPrototypeOf,S=w&&w(w(q([])));S&&S!==r&&o.call(S,a)&&(g=S);var T=v.prototype=m.prototype=Object.create(g);function P(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function r(n,i,a,s){var l=f(e[n],e,i);if("throw"!==l.type){var u=l.arg,c=u.value;return c&&"object"==typeof c&&o.call(c,"__await")?t.resolve(c.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(c).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(l.arg)}var i;n(this,"_invoke",{value:function(e,o){function n(){return new t((function(t,n){r(e,o,t,n)}))}return i=i?i.then(n,n):n()}})}function O(e,r,o){var n=d;return function(i,a){if(n===p)throw new Error("Generator is already running");if(n===b){if("throw"===i)throw a;return{value:t,done:!0}}for(o.method=i,o.arg=a;;){var s=o.delegate;if(s){var l=R(s,o);if(l){if(l===y)continue;return l}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(n===d)throw n=b,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);n=p;var u=f(e,r,o);if("normal"===u.type){if(n=o.done?b:h,u.arg===y)continue;return{value:u.arg,done:o.done}}"throw"===u.type&&(n=b,o.method="throw",o.arg=u.arg)}}}function R(e,r){var o=r.method,n=e.iterator[o];if(n===t)return r.delegate=null,"throw"===o&&e.iterator.return&&(r.method="return",r.arg=t,R(e,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),y;var i=f(n,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function A(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function q(e){if(null!=e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function r(){for(;++n<e.length;)if(o.call(e,n))return r.value=e[n],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return _.prototype=v,n(T,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:_,configurable:!0}),_.displayName=u(v,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,u(e,l,"GeneratorFunction")),e.prototype=Object.create(T),e},e.awrap=function(e){return{__await:e}},P(E.prototype),u(E.prototype,s,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,o,n,i){void 0===i&&(i=Promise);var a=new E(c(t,r,o,n),i);return e.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},P(T),u(T,l,"Generator"),u(T,a,(function(){return this})),u(T,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),r=[];for(var o in t)r.push(o);return r.reverse(),function e(){for(;r.length;){var o=r.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=q,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(A),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(o,n){return s.type="throw",s.arg=e,r.next=o,n&&(r.method="next",r.arg=t),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var l=o.call(a,"catchLoc"),u=o.call(a,"finallyLoc");if(l&&u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),A(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var o=r.completion;if("throw"===o.type){var n=o.arg;A(r)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,o){return this.delegate={iterator:q(e),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=t),y}},e}(e.exports);try{regeneratorRuntime=t}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=t:Function("r","regeneratorRuntime = r")(t)}},878:function(e){"use strict";e.exports={version:"2.17.6"}}},t={};function r(o){var n=t[o];if(void 0!==n)return n.exports;var i=t[o]={exports:{}};return e[o].call(i.exports,i,i.exports,r),i.exports}return r.d=function(e,t){for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r(47),r(23)}()}));