"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Syntax = exports.ErrorType = void 0;
var consts_1 = require("../auth/consts");
Object.defineProperty(exports, "ErrorType", { enumerable: true, get: function () { return consts_1.ErrorType; } });
var Syntax;
(function (Syntax) {
    Syntax["CLIENT_ID"] = "client_id";
    Syntax["CLIENT_SECRET"] = "client_secret";
    Syntax["RESPONSE_TYPE"] = "response_type";
    Syntax["SCOPE"] = "scope";
    Syntax["STATE"] = "state";
    Syntax["REDIRECT_URI"] = "redirect_uri";
    Syntax["ERROR"] = "error";
    Syntax["ERROR_DESCRIPTION"] = "error_description";
    Syntax["ERROR_URI"] = "error_uri";
    Syntax["GRANT_TYPE"] = "grant_type";
    Syntax["CODE"] = "code";
    Syntax["ACCESS_TOKEN"] = "access_token";
    Syntax["TOKEN_TYPE"] = "token_type";
    Syntax["EXPIRES_IN"] = "expires_in";
    Syntax["USERNAME"] = "username";
    Syntax["PASSWORD"] = "password";
    Syntax["REFRESH_TOKEN"] = "refresh_token";
})(Syntax = exports.Syntax || (exports.Syntax = {}));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY29uc3RzLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vc3JjL29hdXRoMmNsaWVudC9jb25zdHMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEseUNBQTBDO0FBQWpDLG1HQUFBLFNBQVMsT0FBQTtBQUNsQixJQUFZLE1Ba0JYO0FBbEJELFdBQVksTUFBTTtJQUNoQixpQ0FBdUIsQ0FBQTtJQUN2Qix5Q0FBK0IsQ0FBQTtJQUMvQix5Q0FBK0IsQ0FBQTtJQUMvQix5QkFBZSxDQUFBO0lBQ2YseUJBQWUsQ0FBQTtJQUNmLHVDQUE2QixDQUFBO0lBQzdCLHlCQUFlLENBQUE7SUFDZixpREFBdUMsQ0FBQTtJQUN2QyxpQ0FBdUIsQ0FBQTtJQUN2QixtQ0FBeUIsQ0FBQTtJQUN6Qix1QkFBYSxDQUFBO0lBQ2IsdUNBQTZCLENBQUE7SUFDN0IsbUNBQXlCLENBQUE7SUFDekIsbUNBQXlCLENBQUE7SUFDekIsK0JBQXFCLENBQUE7SUFDckIsK0JBQXFCLENBQUE7SUFDckIseUNBQStCLENBQUE7QUFDakMsQ0FBQyxFQWxCVyxNQUFNLEdBQU4sY0FBTSxLQUFOLGNBQU0sUUFrQmpCIiwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgRXJyb3JUeXBlIH0gZnJvbSAnLi4vYXV0aC9jb25zdHMnXG5leHBvcnQgZW51bSBTeW50YXgge1xuICBDTElFTlRfSUQgPSAnY2xpZW50X2lkJyxcbiAgQ0xJRU5UX1NFQ1JFVCA9ICdjbGllbnRfc2VjcmV0JyxcbiAgUkVTUE9OU0VfVFlQRSA9ICdyZXNwb25zZV90eXBlJyxcbiAgU0NPUEUgPSAnc2NvcGUnLFxuICBTVEFURSA9ICdzdGF0ZScsXG4gIFJFRElSRUNUX1VSSSA9ICdyZWRpcmVjdF91cmknLFxuICBFUlJPUiA9ICdlcnJvcicsXG4gIEVSUk9SX0RFU0NSSVBUSU9OID0gJ2Vycm9yX2Rlc2NyaXB0aW9uJyxcbiAgRVJST1JfVVJJID0gJ2Vycm9yX3VyaScsXG4gIEdSQU5UX1RZUEUgPSAnZ3JhbnRfdHlwZScsXG4gIENPREUgPSAnY29kZScsXG4gIEFDQ0VTU19UT0tFTiA9ICdhY2Nlc3NfdG9rZW4nLFxuICBUT0tFTl9UWVBFID0gJ3Rva2VuX3R5cGUnLFxuICBFWFBJUkVTX0lOID0gJ2V4cGlyZXNfaW4nLFxuICBVU0VSTkFNRSA9ICd1c2VybmFtZScsXG4gIFBBU1NXT1JEID0gJ3Bhc3N3b3JkJyxcbiAgUkVGUkVTSF9UT0tFTiA9ICdyZWZyZXNoX3Rva2VuJyxcbn1cbiJdfQ==