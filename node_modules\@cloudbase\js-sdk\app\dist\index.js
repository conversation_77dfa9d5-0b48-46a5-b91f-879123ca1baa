"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var app_1 = __importDefault(require("@cloudbase/app"));
var package_json_1 = __importDefault(require("@cloudbase/app/package.json"));
app_1.default.registerVersion(package_json_1.default.version);
try {
    ;
    window.cloudbase = app_1.default;
}
catch (e) { }
exports.default = app_1.default;
module.exports = app_1.default;

//# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImluZGV4LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7QUFBQSx1REFBc0M7QUFFdEMsNkVBQTZDO0FBRTdDLGFBQVMsQ0FBQyxlQUFlLENBQUMsc0JBQUcsQ0FBQyxPQUFPLENBQUMsQ0FBQTtBQU90QyxJQUFJO0lBQ0YsQ0FBQztJQUFDLE1BQWlCLENBQUMsU0FBUyxHQUFHLGFBQVMsQ0FBQTtDQUMxQztBQUFDLE9BQU8sQ0FBQyxFQUFFLEdBQUU7QUFHZCxrQkFBZSxhQUFTLENBQUE7QUFEeEIsaUJBQVMsYUFBUyxDQUFBIiwiZmlsZSI6ImluZGV4LmpzIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNsb3VkYmFzZSBmcm9tICdAY2xvdWRiYXNlL2FwcCdcbmltcG9ydCB7IElDbG91ZGJhc2UgfSBmcm9tICdAY2xvdWRiYXNlL3R5cGVzJ1xuaW1wb3J0IHBrZyBmcm9tICdAY2xvdWRiYXNlL2FwcC9wYWNrYWdlLmpzb24nXG5cbmNsb3VkYmFzZS5yZWdpc3RlclZlcnNpb24ocGtnLnZlcnNpb24pXG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgaW50ZXJmYWNlIFdpbmRvdyB7XG4gICAgY2xvdWRiYXNlOiBJQ2xvdWRiYXNlXG4gIH1cbn1cbnRyeSB7XG4gIDsod2luZG93IGFzIFdpbmRvdykuY2xvdWRiYXNlID0gY2xvdWRiYXNlXG59IGNhdGNoIChlKSB7fVxuLy8gQHRzLWlnbm9yZVxuZXhwb3J0ID0gY2xvdWRiYXNlXG5leHBvcnQgZGVmYXVsdCBjbG91ZGJhc2VcbiJdfQ==

//# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImluZGV4LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7QUFBQSx1REFBc0M7QUFFdEMsNkVBQTZDO0FBRTdDLGFBQVMsQ0FBQyxlQUFlLENBQUMsc0JBQUcsQ0FBQyxPQUFPLENBQUMsQ0FBQTtBQU90QyxJQUFJO0lBQ0YsQ0FBQztJQUFDLE1BQWlCLENBQUMsU0FBUyxHQUFHLGFBQVMsQ0FBQTtDQUMxQztBQUFDLE9BQU8sQ0FBQyxFQUFFLEdBQUU7QUFHZCxrQkFBZSxhQUFTLENBQUE7QUFEeEIsaUJBQVMsYUFBUyxDQUFBIiwiZmlsZSI6ImluZGV4LmpzIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNsb3VkYmFzZSBmcm9tICdAY2xvdWRiYXNlL2FwcCdcbmltcG9ydCB7IElDbG91ZGJhc2UgfSBmcm9tICdAY2xvdWRiYXNlL3R5cGVzJ1xuaW1wb3J0IHBrZyBmcm9tICdAY2xvdWRiYXNlL2FwcC9wYWNrYWdlLmpzb24nXG5cbmNsb3VkYmFzZS5yZWdpc3RlclZlcnNpb24ocGtnLnZlcnNpb24pXG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgaW50ZXJmYWNlIFdpbmRvdyB7XG4gICAgY2xvdWRiYXNlOiBJQ2xvdWRiYXNlXG4gIH1cbn1cbnRyeSB7XG4gIDsod2luZG93IGFzIFdpbmRvdykuY2xvdWRiYXNlID0gY2xvdWRiYXNlXG59IGNhdGNoIChlKSB7fVxuLy8gQHRzLWlnbm9yZVxuZXhwb3J0ID0gY2xvdWRiYXNlXG5leHBvcnQgZGVmYXVsdCBjbG91ZGJhc2VcbiJdfQ==
