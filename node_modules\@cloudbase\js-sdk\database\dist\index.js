"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerDatabase = void 0;
var database_1 = require("@cloudbase/database");
var COMPONENT_NAME = 'database';
function database(dbConfig) {
    var _a = this.platform, adapter = _a.adapter, runtime = _a.runtime;
    database_1.Db.reqClass = this.request.constructor;
    database_1.Db.getAccessToken = this.authInstance ? this.authInstance.getAccessToken.bind(this.authInstance) : function () { return ''; };
    database_1.Db.runtime = runtime;
    if (this.wsClientClass) {
        database_1.Db.wsClass = adapter.wsClass;
        database_1.Db.wsClientClass = this.wsClientClass;
    }
    if (!database_1.Db.ws) {
        database_1.Db.ws = null;
    }
    return new database_1.Db(__assign(__assign(__assign({}, this.config), { _fromApp: this }), dbConfig));
}
var component = {
    name: COMPONENT_NAME,
    entity: {
        database: database,
    },
};
try {
    cloudbase.registerComponent(component);
}
catch (e) { }
function registerDatabase(app) {
    try {
        app.registerComponent(component);
    }
    catch (e) {
        console.warn(e);
    }
}
exports.registerDatabase = registerDatabase;

//# sourceMappingURL=data:application/json;charset=utf8;base64,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

//# sourceMappingURL=data:application/json;charset=utf8;base64,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
