"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OAuth2Client = exports.defaultStorage = exports.generateRequestId = exports.toResponseError = exports.defaultRequest = void 0;
var consts_1 = require("./consts");
var consts_2 = require("../auth/consts");
var uuid_1 = require("../utils/uuid");
var index_1 = require("../utils/index");
var single_promise_1 = require("../utils/function/single-promise");
var base64_1 = require("../utils/base64");
var cloudbase_adapter_wx_mp_1 = require("../utils/cloudbase-adapter-wx_mp");
var RequestIdHeaderName = 'x-request-id';
var DeviceIdHeaderName = 'x-device-id';
var DeviceIdSectionName = 'device_id';
var defaultRequest = function (url, options) {
    return __awaiter(this, void 0, void 0, function () {
        var result, responseError, copyOptions, responseResult, jsonResponse, error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    result = null;
                    responseError = null;
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 4, , 5]);
                    copyOptions = Object.assign({}, options);
                    if (!copyOptions.method) {
                        copyOptions.method = 'GET';
                    }
                    if (copyOptions.body && typeof copyOptions.body !== 'string') {
                        copyOptions.body = JSON.stringify(copyOptions.body);
                    }
                    return [4, fetch(url, copyOptions)];
                case 2:
                    responseResult = _a.sent();
                    return [4, responseResult.json()];
                case 3:
                    jsonResponse = _a.sent();
                    if (jsonResponse === null || jsonResponse === void 0 ? void 0 : jsonResponse.error) {
                        responseError = jsonResponse;
                        responseError.error_uri = new URL(url).pathname;
                    }
                    else {
                        result = jsonResponse;
                    }
                    return [3, 5];
                case 4:
                    error_1 = _a.sent();
                    responseError = {
                        error: consts_1.ErrorType.UNREACHABLE,
                        error_description: error_1.message,
                        error_uri: new URL(url).pathname,
                    };
                    return [3, 5];
                case 5:
                    if (responseError) {
                        throw responseError;
                    }
                    else {
                        return [2, result];
                    }
                    return [2];
            }
        });
    });
};
exports.defaultRequest = defaultRequest;
var toResponseError = function (error, options) {
    var responseError;
    var formatOptions = options || {};
    if (error instanceof Error) {
        responseError = {
            error: formatOptions.error || consts_1.ErrorType.LOCAL,
            error_description: formatOptions.error_description || error.message,
            error_uri: formatOptions.error_uri,
            details: formatOptions.details || error.stack,
        };
    }
    else {
        var formatError = error || {};
        responseError = {
            error: formatOptions.error || formatError.error || consts_1.ErrorType.LOCAL,
            error_description: formatOptions.error_description || formatError.error_description,
            error_uri: formatOptions.error_uri || formatError.error_uri,
            details: formatOptions.details || formatError.details,
        };
    }
    return responseError;
};
exports.toResponseError = toResponseError;
function generateRequestId() {
    return (0, uuid_1.uuidv4)();
}
exports.generateRequestId = generateRequestId;
var DefaultStorage = (function () {
    function DefaultStorage(opts) {
        this._env = (opts === null || opts === void 0 ? void 0 : opts.env) || '';
    }
    DefaultStorage.prototype.getItem = function (key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, window.localStorage.getItem("".concat(key).concat(this._env))];
            });
        });
    };
    DefaultStorage.prototype.removeItem = function (key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                window.localStorage.removeItem("".concat(key).concat(this._env));
                return [2];
            });
        });
    };
    DefaultStorage.prototype.setItem = function (key, value) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                window.localStorage.setItem("".concat(key).concat(this._env), value);
                return [2];
            });
        });
    };
    DefaultStorage.prototype.getItemSync = function (key) {
        return window.localStorage.getItem("".concat(key).concat(this._env));
    };
    DefaultStorage.prototype.removeItemSync = function (key) {
        window.localStorage.removeItem("".concat(key).concat(this._env));
    };
    DefaultStorage.prototype.setItemSync = function (key, value) {
        window.localStorage.setItem("".concat(key).concat(this._env), value);
    };
    return DefaultStorage;
}());
exports.defaultStorage = new DefaultStorage();
function isCredentialsExpired(credentials) {
    var isExpired = true;
    if ((credentials === null || credentials === void 0 ? void 0 : credentials.expires_at) && (credentials === null || credentials === void 0 ? void 0 : credentials.access_token)) {
        isExpired = credentials.expires_at < new Date();
    }
    return isExpired;
}
var LocalCredentials = (function () {
    function LocalCredentials(options) {
        this.credentials = null;
        this.singlePromise = null;
        this.tokenSectionName = options.tokenSectionName;
        this.storage = options.storage;
        this.clientId = options.clientId;
        this.singlePromise = new single_promise_1.SinglePromise({ clientId: this.clientId });
    }
    LocalCredentials.prototype.getStorageCredentialsSync = function () {
        var credentials = null;
        var tokenStr = this.storage.getItemSync(this.tokenSectionName);
        if (tokenStr !== undefined && tokenStr !== null) {
            try {
                credentials = JSON.parse(tokenStr);
                if (credentials === null || credentials === void 0 ? void 0 : credentials.expires_at) {
                    credentials.expires_at = new Date(credentials.expires_at);
                }
            }
            catch (error) {
                this.storage.removeItem(this.tokenSectionName);
                credentials = null;
            }
        }
        return credentials;
    };
    LocalCredentials.prototype.setCredentials = function (credentials) {
        return __awaiter(this, void 0, void 0, function () {
            var tokenStr;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!(credentials === null || credentials === void 0 ? void 0 : credentials.expires_in)) return [3, 3];
                        if (!(credentials === null || credentials === void 0 ? void 0 : credentials.expires_at)) {
                            credentials.expires_at = new Date(Date.now() + (credentials.expires_in - 30) * 1000);
                        }
                        if (!this.storage) return [3, 2];
                        tokenStr = JSON.stringify(credentials);
                        return [4, this.storage.setItem(this.tokenSectionName, tokenStr)];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        this.credentials = credentials;
                        return [3, 6];
                    case 3:
                        if (!this.storage) return [3, 5];
                        return [4, this.storage.removeItem(this.tokenSectionName)];
                    case 4:
                        _a.sent();
                        _a.label = 5;
                    case 5:
                        this.credentials = null;
                        _a.label = 6;
                    case 6: return [2];
                }
            });
        });
    };
    LocalCredentials.prototype.getCredentials = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2, this.singlePromise.run('getCredentials', function () { return __awaiter(_this, void 0, void 0, function () {
                        var _a;
                        return __generator(this, function (_b) {
                            switch (_b.label) {
                                case 0:
                                    if (!isCredentialsExpired(this.credentials)) return [3, 2];
                                    _a = this;
                                    return [4, this.getStorageCredentials()];
                                case 1:
                                    _a.credentials = _b.sent();
                                    _b.label = 2;
                                case 2: return [2, this.credentials];
                            }
                        });
                    }); })];
            });
        });
    };
    LocalCredentials.prototype.getStorageCredentials = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2, this.singlePromise.run('_getStorageCredentials', function () { return __awaiter(_this, void 0, void 0, function () {
                        var credentials, tokenStr, error_2;
                        return __generator(this, function (_a) {
                            switch (_a.label) {
                                case 0:
                                    credentials = null;
                                    return [4, this.storage.getItem(this.tokenSectionName)];
                                case 1:
                                    tokenStr = _a.sent();
                                    if (!(tokenStr !== undefined && tokenStr !== null)) return [3, 5];
                                    _a.label = 2;
                                case 2:
                                    _a.trys.push([2, 3, , 5]);
                                    credentials = JSON.parse(tokenStr);
                                    if (credentials === null || credentials === void 0 ? void 0 : credentials.expires_at) {
                                        credentials.expires_at = new Date(credentials.expires_at);
                                    }
                                    return [3, 5];
                                case 3:
                                    error_2 = _a.sent();
                                    return [4, this.storage.removeItem(this.tokenSectionName)];
                                case 4:
                                    _a.sent();
                                    credentials = null;
                                    return [3, 5];
                                case 5: return [2, credentials];
                            }
                        });
                    }); })];
            });
        });
    };
    return LocalCredentials;
}());
var OAuth2Client = exports.OAuth2Client = (function () {
    function OAuth2Client(options) {
        this.singlePromise = null;
        if (!options.clientSecret) {
            options.clientSecret = '';
        }
        if (!options.clientId && options.env) {
            options.clientId = options.env;
        }
        this.apiOrigin = options.apiOrigin;
        this.clientId = options.clientId;
        this.singlePromise = new single_promise_1.SinglePromise({ clientId: this.clientId });
        this.retry = this.formatRetry(options.retry, OAuth2Client.defaultRetry);
        if (options.baseRequest) {
            this.baseRequest = options.baseRequest;
        }
        else {
            this.baseRequest = exports.defaultRequest;
        }
        this.tokenInURL = options.tokenInURL;
        this.headers = options.headers;
        this.storage = options.storage || exports.defaultStorage;
        this.localCredentials = new LocalCredentials({
            tokenSectionName: "credentials_".concat(options.clientId),
            storage: this.storage,
            clientId: options.clientId,
        });
        this.clientSecret = options.clientSecret;
        if (options.clientId) {
            this.basicAuth = "Basic ".concat((0, base64_1.weBtoa)("".concat(options.clientId, ":").concat(this.clientSecret)));
        }
        this.wxCloud = options.wxCloud;
        try {
            if ((0, cloudbase_adapter_wx_mp_1.isMatch)() && this.wxCloud === undefined && options.env) {
                wx.cloud.init({ env: options.env });
                this.wxCloud = wx.cloud;
            }
        }
        catch (error) {
        }
        this.refreshTokenFunc = options.refreshTokenFunc || this.defaultRefreshTokenFunc;
        this.anonymousSignInFunc = options.anonymousSignInFunc;
    }
    OAuth2Client.prototype.setCredentials = function (credentials) {
        return this.localCredentials.setCredentials(credentials);
    };
    OAuth2Client.prototype.getAccessToken = function () {
        return __awaiter(this, void 0, void 0, function () {
            var credentials, respErr;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, this.getCredentials()];
                    case 1:
                        credentials = _a.sent();
                        if (credentials === null || credentials === void 0 ? void 0 : credentials.access_token) {
                            return [2, Promise.resolve(credentials.access_token)];
                        }
                        respErr = { error: consts_1.ErrorType.UNAUTHENTICATED };
                        return [2, Promise.reject(respErr)];
                }
            });
        });
    };
    OAuth2Client.prototype.request = function (url, options) {
        return __awaiter(this, void 0, void 0, function () {
            var retry, deviceId, credentials, response, maxRequestTimes, requestTime, responseError_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!options) {
                            options = {};
                        }
                        retry = this.formatRetry(options.retry, this.retry);
                        options.headers = options.headers || {};
                        if (this.headers) {
                            options.headers = __assign(__assign({}, this.headers), options.headers);
                        }
                        if (!options.headers[RequestIdHeaderName]) {
                            options.headers[RequestIdHeaderName] = generateRequestId();
                        }
                        if (!!options.headers[DeviceIdHeaderName]) return [3, 2];
                        return [4, this.getDeviceId()];
                    case 1:
                        deviceId = _a.sent();
                        options.headers[DeviceIdHeaderName] = deviceId;
                        _a.label = 2;
                    case 2:
                        if ((options === null || options === void 0 ? void 0 : options.withBasicAuth) && this.basicAuth) {
                            options.headers.Authorization = this.basicAuth;
                        }
                        if (!(options === null || options === void 0 ? void 0 : options.withCredentials)) return [3, 4];
                        return [4, this.getCredentials()];
                    case 3:
                        credentials = _a.sent();
                        if (credentials) {
                            if (this.tokenInURL) {
                                if (url.indexOf('?') < 0) {
                                    url += '?';
                                }
                                url += "access_token=".concat(credentials.access_token);
                            }
                            else {
                                options.headers.Authorization = "".concat(credentials.token_type, " ").concat(credentials.access_token);
                            }
                        }
                        return [3, 5];
                    case 4:
                        if (this.clientId && url.indexOf('client_id') < 0) {
                            url += url.indexOf('?') < 0 ? '?' : '&';
                            url += "client_id=".concat(this.clientId);
                        }
                        _a.label = 5;
                    case 5:
                        if (url.startsWith('/')) {
                            url = this.apiOrigin + url;
                        }
                        response = null;
                        maxRequestTimes = retry + 1;
                        requestTime = 0;
                        _a.label = 6;
                    case 6:
                        if (!(requestTime < maxRequestTimes)) return [3, 18];
                        _a.label = 7;
                    case 7:
                        _a.trys.push([7, 12, , 15]);
                        if (!options.useWxCloud) return [3, 9];
                        return [4, this.wxCloudCallFunction(url, options)];
                    case 8:
                        response = _a.sent();
                        return [3, 11];
                    case 9: return [4, this.baseRequest(url, options)];
                    case 10:
                        response = _a.sent();
                        _a.label = 11;
                    case 11: return [3, 18];
                    case 12:
                        responseError_1 = _a.sent();
                        if (!(options.withCredentials && responseError_1 && responseError_1.error === consts_1.ErrorType.UNAUTHENTICATED)) return [3, 14];
                        return [4, this.setCredentials(null)];
                    case 13:
                        _a.sent();
                        return [2, Promise.reject(responseError_1)];
                    case 14:
                        if (requestTime === retry || !responseError_1 || responseError_1.error !== 'unreachable') {
                            return [2, Promise.reject(responseError_1)];
                        }
                        return [3, 15];
                    case 15: return [4, this.sleep(OAuth2Client.retryInterval)];
                    case 16:
                        _a.sent();
                        _a.label = 17;
                    case 17:
                        requestTime++;
                        return [3, 6];
                    case 18: return [2, response];
                }
            });
        });
    };
    OAuth2Client.prototype.wxCloudCallFunction = function (url, options) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var result, responseError, userAgent, error_3, responseResult, error_4;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        result = null;
                        responseError = null;
                        _b.label = 1;
                    case 1:
                        _b.trys.push([1, 7, , 8]);
                        userAgent = '';
                        _b.label = 2;
                    case 2:
                        _b.trys.push([2, 4, , 5]);
                        return [4, wx.getRendererUserAgent()];
                    case 3:
                        userAgent = _b.sent();
                        return [3, 5];
                    case 4:
                        error_3 = _b.sent();
                        return [3, 5];
                    case 5: return [4, this.wxCloud.callFunction({
                            name: 'httpOverCallFunction',
                            data: {
                                url: url,
                                method: options.method,
                                headers: __assign({ origin: 'https://servicewechat.com', 'User-Agent': userAgent }, options.headers),
                                body: options.body,
                            },
                        })];
                    case 6:
                        responseResult = (_b.sent()).result;
                        if ((_a = responseResult === null || responseResult === void 0 ? void 0 : responseResult.body) === null || _a === void 0 ? void 0 : _a.error_code) {
                            responseError = responseResult === null || responseResult === void 0 ? void 0 : responseResult.body;
                            responseError.error_uri = (0, index_1.getPathName)(url);
                        }
                        else {
                            result = responseResult === null || responseResult === void 0 ? void 0 : responseResult.body;
                        }
                        return [3, 8];
                    case 7:
                        error_4 = _b.sent();
                        responseError = {
                            error: consts_1.ErrorType.UNREACHABLE,
                            error_description: error_4.message,
                            error_uri: (0, index_1.getPathName)(url),
                        };
                        return [3, 8];
                    case 8:
                        if (responseError) {
                            throw responseError;
                        }
                        else {
                            return [2, result];
                        }
                        return [2];
                }
            });
        });
    };
    OAuth2Client.prototype.getCredentials = function () {
        return __awaiter(this, void 0, void 0, function () {
            var credentials, c, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4, this.localCredentials.getCredentials()];
                    case 1:
                        credentials = _b.sent();
                        if (!credentials) {
                            return [2, this.unAuthenticatedError('credentials not found')];
                        }
                        if (!isCredentialsExpired(credentials)) return [3, 10];
                        if (!(credentials && credentials.scope === 'anonymous')) return [3, 8];
                        if (!this.anonymousSignInFunc) return [3, 5];
                        return [4, this.anonymousSignInFunc(credentials)];
                    case 2:
                        c = _b.sent();
                        _a = c;
                        if (_a) return [3, 4];
                        return [4, this.localCredentials.getCredentials()];
                    case 3:
                        _a = (_b.sent());
                        _b.label = 4;
                    case 4:
                        credentials = _a;
                        return [3, 7];
                    case 5: return [4, this.anonymousSignIn(credentials)];
                    case 6:
                        credentials = _b.sent();
                        _b.label = 7;
                    case 7: return [3, 10];
                    case 8: return [4, this.refreshToken(credentials)];
                    case 9:
                        credentials = _b.sent();
                        _b.label = 10;
                    case 10: return [2, credentials];
                }
            });
        });
    };
    OAuth2Client.prototype.getCredentialsSync = function () {
        var credentials = this.localCredentials.getStorageCredentialsSync();
        return credentials;
    };
    OAuth2Client.prototype.getCredentialsAsync = function () {
        return this.getCredentials();
    };
    OAuth2Client.prototype.getScope = function () {
        return __awaiter(this, void 0, void 0, function () {
            var credentials;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, this.localCredentials.getCredentials()];
                    case 1:
                        credentials = _a.sent();
                        if (!credentials) {
                            return [2, this.unAuthenticatedError('credentials not found')];
                        }
                        return [2, credentials.scope];
                }
            });
        });
    };
    OAuth2Client.prototype.getGroups = function () {
        return __awaiter(this, void 0, void 0, function () {
            var credentials;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, this.localCredentials.getCredentials()];
                    case 1:
                        credentials = _a.sent();
                        if (!credentials) {
                            return [2, this.unAuthenticatedError('credentials not found')];
                        }
                        return [2, credentials.groups];
                }
            });
        });
    };
    OAuth2Client.prototype.refreshToken = function (credentials) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2, this.singlePromise.run('_refreshToken', function () { return __awaiter(_this, void 0, void 0, function () {
                        var newCredentials, error_5;
                        return __generator(this, function (_a) {
                            switch (_a.label) {
                                case 0:
                                    if (!credentials || !credentials.refresh_token) {
                                        return [2, this.unAuthenticatedError('no refresh token found in credentials')];
                                    }
                                    _a.label = 1;
                                case 1:
                                    _a.trys.push([1, 4, , 7]);
                                    return [4, this.refreshTokenFunc(credentials.refresh_token, credentials)];
                                case 2:
                                    newCredentials = _a.sent();
                                    return [4, this.localCredentials.setCredentials(newCredentials)];
                                case 3:
                                    _a.sent();
                                    return [2, newCredentials];
                                case 4:
                                    error_5 = _a.sent();
                                    if (!(error_5.error === consts_1.ErrorType.INVALID_GRANT)) return [3, 6];
                                    return [4, this.localCredentials.setCredentials(null)];
                                case 5:
                                    _a.sent();
                                    return [2, this.unAuthenticatedError(error_5.error_description)];
                                case 6: return [2, Promise.reject(error_5)];
                                case 7: return [2];
                            }
                        });
                    }); })];
            });
        });
    };
    OAuth2Client.prototype.checkRetry = function (retry) {
        var responseError = null;
        if (typeof retry !== 'number' || retry < OAuth2Client.minRetry || retry > OAuth2Client.maxRetry) {
            responseError = {
                error: consts_1.ErrorType.UNREACHABLE,
                error_description: 'wrong options param: retry',
            };
        }
        if (responseError) {
            throw responseError;
        }
        return retry;
    };
    OAuth2Client.prototype.formatRetry = function (retry, defaultVale) {
        if (typeof retry === 'undefined') {
            return defaultVale;
        }
        return this.checkRetry(retry);
    };
    OAuth2Client.prototype.sleep = function (ms) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2, new Promise(function (resolve) {
                        setTimeout(function () {
                            resolve();
                        }, ms);
                    })];
            });
        });
    };
    OAuth2Client.prototype.anonymousSignIn = function (credentials) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2, this.singlePromise.run('_anonymous', function () { return __awaiter(_this, void 0, void 0, function () {
                        var newCredentials, error_6;
                        return __generator(this, function (_a) {
                            switch (_a.label) {
                                case 0:
                                    if (!credentials || credentials.scope !== 'anonymous') {
                                        return [2, this.unAuthenticatedError('no anonymous in credentials')];
                                    }
                                    _a.label = 1;
                                case 1:
                                    _a.trys.push([1, 4, , 7]);
                                    return [4, this.request(consts_2.ApiUrls.AUTH_SIGN_IN_ANONYMOUSLY_URL, {
                                            method: 'POST',
                                            withBasicAuth: true,
                                            body: {},
                                        })];
                                case 2:
                                    newCredentials = _a.sent();
                                    return [4, this.localCredentials.setCredentials(newCredentials)];
                                case 3:
                                    _a.sent();
                                    return [2, newCredentials];
                                case 4:
                                    error_6 = _a.sent();
                                    if (!(error_6.error === consts_1.ErrorType.INVALID_GRANT)) return [3, 6];
                                    return [4, this.localCredentials.setCredentials(null)];
                                case 5:
                                    _a.sent();
                                    return [2, this.unAuthenticatedError(error_6.error_description)];
                                case 6: return [2, Promise.reject(error_6)];
                                case 7: return [2];
                            }
                        });
                    }); })];
            });
        });
    };
    OAuth2Client.prototype.defaultRefreshTokenFunc = function (refreshToken, credentials) {
        return __awaiter(this, void 0, void 0, function () {
            var url, newCredentials;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (refreshToken === undefined || refreshToken === '') {
                            return [2, this.unAuthenticatedError('refresh token not found')];
                        }
                        url = consts_2.ApiUrls.AUTH_TOKEN_URL;
                        if ((credentials === null || credentials === void 0 ? void 0 : credentials.version) === 'v2') {
                            url = consts_2.ApiUrlsV2.AUTH_TOKEN_URL;
                        }
                        return [4, this.request(url, {
                                method: 'POST',
                                body: {
                                    client_id: this.clientId,
                                    client_secret: this.clientSecret,
                                    grant_type: 'refresh_token',
                                    refresh_token: refreshToken,
                                },
                            })];
                    case 1:
                        newCredentials = _a.sent();
                        return [2, __assign(__assign({}, newCredentials), { version: (credentials === null || credentials === void 0 ? void 0 : credentials.version) || 'v1' })];
                }
            });
        });
    };
    OAuth2Client.prototype.getDeviceId = function () {
        return __awaiter(this, void 0, void 0, function () {
            var deviceId;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.deviceID) {
                            return [2, this.deviceID];
                        }
                        return [4, this.storage.getItem(DeviceIdSectionName)];
                    case 1:
                        deviceId = _a.sent();
                        if (!!(typeof deviceId === 'string' && deviceId.length >= 16 && deviceId.length <= 48)) return [3, 3];
                        deviceId = (0, uuid_1.uuidv4)();
                        return [4, this.storage.setItem(DeviceIdSectionName, deviceId)];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        this.deviceID = deviceId;
                        return [2, deviceId];
                }
            });
        });
    };
    OAuth2Client.prototype.unAuthenticatedError = function (err) {
        var respErr = {
            error: consts_1.ErrorType.UNAUTHENTICATED,
            error_description: err,
        };
        return Promise.reject(respErr);
    };
    OAuth2Client.defaultRetry = 2;
    OAuth2Client.minRetry = 0;
    OAuth2Client.maxRetry = 5;
    OAuth2Client.retryInterval = 1000;
    return OAuth2Client;
}());
//# sourceMappingURL=data:application/json;base64,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