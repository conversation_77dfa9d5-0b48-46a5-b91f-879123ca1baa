export declare enum ApiUrls {
    AUTH_SIGN_UP_URL = "/auth/v1/signup",
    AUTH_TOKEN_URL = "/auth/v1/token",
    AUTH_REVOKE_URL = "/auth/v1/revoke",
    WEDA_USER_URL = "/auth/v1/plugin/weda/userinfo",
    AUTH_RESET_PASSWORD = "/auth/v1/reset",
    AUTH_GET_DEVICE_CODE = "/auth/v1/device/code",
    CHECK_USERNAME = "/auth/v1/checkUsername",
    CHECK_IF_USER_EXIST = "/auth/v1/checkIfUserExist",
    GET_PROVIDER_TYPE = "/auth/v1/mgr/provider/providerSubType",
    AUTH_SIGN_IN_URL = "/auth/v1/signin",
    AUTH_SIGN_IN_ANONYMOUSLY_URL = "/auth/v1/signin/anonymously",
    AUTH_SIGN_IN_WITH_PROVIDER_URL = "/auth/v1/signin/with/provider",
    AUTH_SIGN_IN_WITH_WECHAT_URL = "/auth/v1/signin/wechat/noauth",
    AUTH_SIGN_IN_CUSTOM = "/auth/v1/signin/custom",
    PROVIDER_TOKEN_URL = "/auth/v1/provider/token",
    PROVIDER_URI_URL = "/auth/v1/provider/uri",
    USER_ME_URL = "/auth/v1/user/me",
    AUTH_SIGNOUT_URL = "/auth/v1/user/signout",
    USER_QUERY_URL = "/auth/v1/user/query",
    USER_PRIFILE_URL = "/auth/v1/user/profile",
    USER_BASIC_EDIT_URL = "/auth/v1/user/basic/edit",
    USER_TRANS_BY_PROVIDER_URL = "/auth/v1/user/trans/by/provider",
    PROVIDER_LIST = "/auth/v1/user/provider",
    PROVIDER_BIND_URL = "/auth/v1/user/provider/bind",
    PROVIDER_UNBIND_URL = "/auth/v1/user/provider",
    CHECK_PWD_URL = "/auth/v1/user/sudo",
    SUDO_URL = "/auth/v1/user/sudo",
    BIND_CONTACT_URL = "/auth/v1/user/contact",
    AUTH_SET_PASSWORD = "/auth/v1/user/password",
    AUTHORIZE_DEVICE_URL = "/auth/v1/user/device/authorize",
    AUTHORIZE_URL = "/auth/v1/user/authorize",
    AUTHORIZE_INFO_URL = "/auth/v1/user/authorize/info",
    AUTHORIZED_DEVICES_DELETE_URL = "/auth/v1/user/authorized/devices/",
    AUTH_REVOKE_ALL_URL = "/auth/v1/user/revoke/all",
    GET_USER_BEHAVIOR_LOG = "/auth/v1/user/security/history",
    VERIFICATION_URL = "/auth/v1/verification",
    VERIFY_URL = "/auth/v1/verification/verify",
    CAPTCHA_DATA_URL = "/auth/v1/captcha/data",
    VERIFY_CAPTCHA_DATA_URL = "/auth/v1/captcha/data/verify",
    GET_CAPTCHA_URL = "/auth/v1/captcha/init",
    GET_MINIPROGRAM_QRCODE = "/auth/v1/qrcode/generate",
    GET_MINIPROGRAM_QRCODE_STATUS = "/auth/v1/qrcode/get/status"
}
export declare enum ApiUrlsV2 {
    AUTH_SIGN_IN_URL = "/auth/v2/signin/username",
    AUTH_TOKEN_URL = "/auth/v2/token",
    USER_ME_URL = "/auth/v2/user/me",
    VERIFY_URL = "/auth/v2/signin/verificationcode",
    AUTH_SIGN_IN_WITH_PROVIDER_URL = "/auth/v2/signin/with/provider",
    AUTH_PUBLIC_KEY = "/auth/v2/signin/publichkey",
    AUTH_RESET_PASSWORD = "/auth/v2/signin/password/update"
}
export declare enum VerificationUsages {
    REGISTER = "REGISTER",
    SIGN_IN = "SIGN_IN",
    PASSWORD_RESET = "PASSWORD_RESET",
    EMAIL_ADDRESS_CHANGE = "EMAIL_ADDRESS_CHANGE",
    PHONE_NUMBER_CHANGE = "PHONE_NUMBER_CHANGE"
}
export declare enum ErrorType {
    UNREACHABLE = "unreachable",
    LOCAL = "local",
    CANCELLED = "cancelled",
    UNKNOWN = "unknown",
    UNAUTHENTICATED = "unauthenticated",
    RESOURCE_EXHAUSTED = "resource_exhausted",
    FAILED_PRECONDITION = "failed_precondition",
    INVALID_ARGUMENT = "invalid_argument",
    DEADLINE_EXCEEDED = "deadline_exceeded",
    NOT_FOUND = "not_found",
    ALREADY_EXISTS = "already_exists",
    PERMISSION_DENIED = "permission_denied",
    ABORTED = "aborted",
    OUT_OF_RANGE = "out_of_range",
    UNIMPLEMENTED = "unimplemented",
    INTERNAL = "internal",
    UNAVAILABLE = "unavailable",
    DATA_LOSS = "data_loss",
    INVALID_PASSWORD = "invalid_password",
    PASSWORD_NOT_SET = "password_not_set",
    INVALID_STATUS = "invalid_status",
    USER_PENDING = "user_pending",
    USER_BLOCKED = "user_blocked",
    INVALID_VERIFICATION_CODE = "invalid_verification_code",
    TWO_FACTOR_REQUIRED = "two_factor_required",
    INVALID_TWO_FACTOR = "invalid_two_factor",
    INVALID_TWO_FACTOR_RECOVERY = "invalid_two_factor_recovery",
    UNDER_REVIEW = "under_review",
    INVALID_REQUEST = "invalid_request",
    UNAUTHORIZED_CLIENT = "unauthorized_client",
    ACCESS_DENIED = "access_denied",
    UNSUPPORTED_RESPONSE_TYPE = "unsupported_response_type",
    INVALID_SCOPE = "invalid_scope",
    INVALID_GRANT = "invalid_grant",
    SERVER_ERROR = "server_error",
    TEMPORARILY_UNAVAILABLE = "temporarily_unavailable",
    INTERACTION_REQUIRED = "interaction_required",
    LOGIN_REQUIRED = "login_required",
    ACCOUNT_SELECTION_REQUIRED = "account_selection_required",
    CONSENT_REQUIRED = "consent_required",
    INVALID_REQUEST_URI = "invalid_request_uri",
    INVALID_REQUEST_OBJECT = "invalid_request_object",
    REQUEST_NOT_SUPPORTED = "request_not_supported",
    REQUEST_URI_NOT_SUPPORTED = "request_uri_not_supported",
    REGISTRATION_NOT_SUPPORTED = "registration_not_supported",
    CAPTCHA_REQUIRED = "captcha_required",
    CAPTCHA_INVALID = "captcha_invalid"
}
