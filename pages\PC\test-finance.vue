<template>
	<view class="test-finance">
		<view class="test-header">
			<text class="test-title">PC端财务系统测试页面</text>
			<view class="test-controls">
				<button @click="testFinanceService">测试财务服务</button>
				<button @click="testUtils">测试工具函数</button>
				<button @click="clearLogs">清空日志</button>
			</view>
		</view>

		<view class="tes不应该都显示在中间区域么，你做到哪里了
		t-content">
			<view class="test-section">
				<text class="section-title">财务数据测试</text>
				<view class="test-results">
					<view class="result-item">
						<text class="result-label">仪表盘数据:</text>
						<text class="result-value">{{ JSON.stringify(dashboardData, null, 2) }}</text>
					</view>
					<view class="result-item">
						<text class="result-label">收支数据:</text>
						<text class="result-value">{{ JSON.stringify(incomeExpenseData, null, 2) }}</text>
					</view>
					<view class="result-item">
						<text class="result-label">资金流水:</text>
						<text class="result-value">{{ JSON.stringify(cashFlowData, null, 2) }}</text>
					</view>
				</view>
			</view>

			<view class="test-section">
				<text class="section-title">工具函数测试</text>
				<view class="test-results">
					<view class="result-item">
						<text class="result-label">格式化金额 (12345.67):</text>
						<text class="result-value">{{ formatMoney(12345.67) }}</text>
					</view>
					<view class="result-item">
						<text class="result-label">格式化百分比 (15.5):</text>
						<text class="result-value">{{ formatPercent(15.5) }}</text>
					</view>
					<view class="result-item">
						<text class="result-label">增长率显示 (25):</text>
						<text class="result-value">{{ JSON.stringify(getGrowthDisplay(25)) }}</text>
					</view>
					<view class="result-item">
						<text class="result-label">状态文本 (pending):</text>
						<text class="result-value">{{ getStatusText('pending') }}</text>
					</view>
				</view>
			</view>

			<view class="test-section">
				<text class="section-title">测试日志</text>
				<view class="test-logs">
					<view v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
						<text class="log-time">{{ log.time }}</text>
						<text class="log-message">{{ log.message }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import pcFinanceService from '@/utils/pcFinanceService.js'
	import { 
		formatMoney, 
		formatPercent, 
		formatDate, 
		getGrowthDisplay, 
		getStatusText, 
		getCurrentFinanceDate 
	} from '@/utils/pcUtils.js'

	export default {
		name: 'TestFinance',
		data() {
			return {
				dashboardData: null,
				incomeExpenseData: null,
				cashFlowData: null,
				logs: []
			}
		},
		
		mounted() {
			this.addLog('info', 'PC端财务系统测试页面已加载');
			this.testBasicFunctions();
		},
		
		methods: {
			addLog(type, message) {
				this.logs.unshift({
					type,
					time: new Date().toLocaleTimeString(),
					message
				});
				
				// 限制日志数量
				if (this.logs.length > 50) {
					this.logs = this.logs.slice(0, 50);
				}
			},
			
			async testFinanceService() {
				this.addLog('info', '开始测试财务服务...');
				
				try {
					const { year, month } = getCurrentFinanceDate();
					this.addLog('info', `当前财务年月: ${year}年${month}月`);
					
					// 测试仪表盘数据
					this.addLog('info', '测试仪表盘数据获取...');
					this.dashboardData = await pcFinanceService.getDashboardData(year, month);
					this.addLog('success', '仪表盘数据获取成功');
					
					// 测试收支数据
					this.addLog('info', '测试收支数据获取...');
					this.incomeExpenseData = await pcFinanceService.getIncomeExpenseData(year, month);
					this.addLog('success', '收支数据获取成功');
					
					// 测试资金流水
					this.addLog('info', '测试资金流水获取...');
					this.cashFlowData = await pcFinanceService.getCashFlowData(year, month);
					this.addLog('success', '资金流水获取成功');
					
					this.addLog('success', '所有财务服务测试完成！');
					
				} catch (error) {
					this.addLog('error', `财务服务测试失败: ${error.message}`);
					console.error('财务服务测试失败:', error);
				}
			},
			
			testUtils() {
				this.addLog('info', '开始测试工具函数...');
				
				try {
					// 测试格式化函数
					const money = formatMoney(12345.67);
					const percent = formatPercent(15.5);
					const date = formatDate(new Date());
					const growth = getGrowthDisplay(25);
					const status = getStatusText('pending');
					
					this.addLog('success', `格式化测试完成: 金额=${money}, 百分比=${percent}, 日期=${date}`);
					this.addLog('success', `状态测试完成: 增长=${JSON.stringify(growth)}, 状态=${status}`);
					
					this.addLog('success', '所有工具函数测试完成！');
					
				} catch (error) {
					this.addLog('error', `工具函数测试失败: ${error.message}`);
					console.error('工具函数测试失败:', error);
				}
			},
			
			testBasicFunctions() {
				this.addLog('info', '测试基础功能...');
				
				// 测试导入的函数是否可用
				if (typeof formatMoney === 'function') {
					this.addLog('success', 'formatMoney 函数导入成功');
				} else {
					this.addLog('error', 'formatMoney 函数导入失败');
				}
				
				if (typeof pcFinanceService === 'object') {
					this.addLog('success', 'pcFinanceService 服务导入成功');
				} else {
					this.addLog('error', 'pcFinanceService 服务导入失败');
				}
				
				// 测试uniCloud是否可用
				if (typeof uniCloud !== 'undefined') {
					this.addLog('success', 'uniCloud 服务可用');
				} else {
					this.addLog('warning', 'uniCloud 服务不可用（可能在开发环境中）');
				}
			},
			
			clearLogs() {
				this.logs = [];
				this.addLog('info', '日志已清空');
			},
			
			// 工具函数
			formatMoney(amount) {
				return formatMoney(amount);
			},
			
			formatPercent(value) {
				return formatPercent(value);
			},
			
			getGrowthDisplay(rate) {
				return getGrowthDisplay(rate);
			},
			
			getStatusText(status) {
				return getStatusText(status);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.test-finance {
		padding: 20px;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.test-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30px;
		padding: 20px;
		background-color: #fff;
		border-radius: 12px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
		
		.test-title {
			font-size: 24px;
			font-weight: bold;
			color: #333;
		}
		
		.test-controls {
			display: flex;
			gap: 10px;
			
			button {
				padding: 8px 16px;
				border: none;
				border-radius: 6px;
				background-color: #409EFF;
				color: #fff;
				cursor: pointer;
				font-size: 14px;
				
				&:hover {
					background-color: #337ecc;
				}
				
				&:nth-child(2) {
					background-color: #67C23A;
					
					&:hover {
						background-color: #5daf34;
					}
				}
				
				&:nth-child(3) {
					background-color: #E6A23C;
					
					&:hover {
						background-color: #cf9236;
					}
				}
			}
		}
	}
	
	.test-content {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20px;
	}
	
	.test-section {
		background-color: #fff;
		border-radius: 12px;
		padding: 20px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
		
		.section-title {
			display: block;
			font-size: 18px;
			font-weight: 600;
			color: #333;
			margin-bottom: 15px;
			border-bottom: 2px solid #409EFF;
			padding-bottom: 8px;
		}
		
		&:nth-child(3) {
			grid-column: 1 / -1;
		}
	}
	
	.test-results {
		.result-item {
			margin-bottom: 15px;
			
			.result-label {
				display: block;
				font-size: 14px;
				font-weight: 500;
				color: #666;
				margin-bottom: 5px;
			}
			
			.result-value {
				display: block;
				font-size: 12px;
				color: #333;
				background-color: #f8f9fa;
				padding: 8px;
				border-radius: 4px;
				white-space: pre-wrap;
				word-break: break-all;
			}
		}
	}
	
	.test-logs {
		max-height: 400px;
		overflow-y: auto;
		
		.log-item {
			display: flex;
			gap: 10px;
			padding: 8px;
			margin-bottom: 5px;
			border-radius: 4px;
			font-size: 12px;
			
			&.info {
				background-color: #e1f3ff;
				color: #409EFF;
			}
			
			&.success {
				background-color: #f0f9ff;
				color: #67C23A;
			}
			
			&.warning {
				background-color: #fdf6ec;
				color: #E6A23C;
			}
			
			&.error {
				background-color: #fef0f0;
				color: #F56C6C;
			}
			
			.log-time {
				font-weight: 500;
				min-width: 80px;
			}
			
			.log-message {
				flex: 1;
			}
		}
	}
</style>
