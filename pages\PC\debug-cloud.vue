<template>
	<view class="debug-cloud">
		<view class="debug-header">
			<text class="debug-title">云函数调试工具</text>
			<view class="debug-controls">
				<button @click="testCloudFunction">测试云函数</button>
				<button @click="testBatchFunction">测试批量云函数</button>
				<button @click="clearLogs">清空日志</button>
			</view>
		</view>

		<view class="debug-content">
			<view class="debug-section">
				<text class="section-title">云函数测试</text>
				<view class="test-form">
					<view class="form-item">
						<text class="form-label">云函数名称:</text>
						<input v-model="functionName" placeholder="输入云函数名称" class="form-input" />
					</view>
					<view class="form-item">
						<text class="form-label">年份:</text>
						<input v-model.number="testYear" type="number" placeholder="2024" class="form-input" />
					</view>
					<view class="form-item">
						<text class="form-label">月份:</text>
						<input v-model.number="testMonth" type="number" placeholder="1-12" class="form-input" />
					</view>
					<view class="form-item">
						<text class="form-label">类型:</text>
						<picker :value="typeIndex" :range="typeOptions" @change="onTypeChange">
							<view class="picker-text">{{ typeOptions[typeIndex] }}</view>
						</picker>
					</view>
				</view>
			</view>

			<view class="debug-section">
				<text class="section-title">测试结果</text>
				<view class="test-result">
					<view v-if="testResult" class="result-content">
						<text class="result-text">{{ JSON.stringify(testResult, null, 2) }}</text>
					</view>
					<view v-else class="result-empty">
						<text>暂无测试结果</text>
					</view>
				</view>
			</view>

			<view class="debug-section">
				<text class="section-title">调试日志</text>
				<view class="debug-logs">
					<view v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
						<text class="log-time">{{ log.time }}</text>
						<text class="log-message">{{ log.message }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'DebugCloud',
		data() {
			return {
				functionName: 'getFinanceBatch',
				testYear: new Date().getFullYear(),
				testMonth: new Date().getMonth() + 1,
				typeIndex: 0,
				typeOptions: ['cashFlow', 'incomeExpense', 'dashboard', 'transactions'],
				testResult: null,
				logs: []
			}
		},
		
		mounted() {
			this.addLog('info', '云函数调试工具已加载');
			this.checkEnvironment();
		},
		
		methods: {
			addLog(type, message) {
				this.logs.unshift({
					type,
					time: new Date().toLocaleTimeString(),
					message
				});
				
				// 限制日志数量
				if (this.logs.length > 100) {
					this.logs = this.logs.slice(0, 100);
				}
			},
			
			checkEnvironment() {
				this.addLog('info', '检查运行环境...');
				
				// 检查uniCloud
				if (typeof uniCloud !== 'undefined') {
					this.addLog('success', 'uniCloud 可用');
					
					// 检查callFunction方法
					if (typeof uniCloud.callFunction === 'function') {
						this.addLog('success', 'uniCloud.callFunction 方法可用');
					} else {
						this.addLog('error', 'uniCloud.callFunction 方法不可用');
					}
				} else {
					this.addLog('error', 'uniCloud 不可用');
				}
				
				// 检查当前平台
				// #ifdef H5
				this.addLog('info', '当前平台: H5');
				// #endif
				
				// #ifdef MP-WEIXIN
				this.addLog('info', '当前平台: 微信小程序');
				// #endif
				
				// #ifdef APP-PLUS
				this.addLog('info', '当前平台: APP');
				// #endif
			},
			
			async testCloudFunction() {
				this.addLog('info', '开始测试云函数...');
				
				try {
					if (typeof uniCloud === 'undefined') {
						this.addLog('error', 'uniCloud 不可用，无法测试');
						return;
					}
					
					const functionData = {
						year: this.testYear,
						month: this.testMonth,
						type: this.typeOptions[this.typeIndex]
					};
					
					this.addLog('info', `调用云函数: ${this.functionName}`);
					this.addLog('info', `参数: ${JSON.stringify(functionData)}`);
					
					const result = await uniCloud.callFunction({
						name: this.functionName,
						data: functionData
					});
					
					this.addLog('success', '云函数调用成功');
					this.addLog('info', `返回结果: ${JSON.stringify(result, null, 2)}`);
					
					this.testResult = result;
					
				} catch (error) {
					this.addLog('error', `云函数调用失败: ${error.message}`);
					this.addLog('error', `错误详情: ${JSON.stringify(error)}`);
					console.error('云函数调用失败:', error);
				}
			},
			
			async testBatchFunction() {
				this.addLog('info', '开始测试批量云函数...');
				
				try {
					if (typeof uniCloud === 'undefined') {
						this.addLog('error', 'uniCloud 不可用，无法测试');
						return;
					}
					
					const functionData = {
						year: this.testYear,
						month: this.testMonth,
						modules: ['cashFlow', 'incomeExpense'],
						fields: {
							cashFlow: ['cashIn', 'cashOut', 'cashInGrowth', 'cashOutGrowth'],
							incomeExpense: ['income', 'expense', 'incomeGrowth', 'expenseGrowth']
						}
					};
					
					this.addLog('info', '调用批量云函数: getFinanceBatch');
					this.addLog('info', `参数: ${JSON.stringify(functionData)}`);
					
					const result = await uniCloud.callFunction({
						name: 'getFinanceBatch',
						data: functionData
					});
					
					this.addLog('success', '批量云函数调用成功');
					this.addLog('info', `返回结果: ${JSON.stringify(result, null, 2)}`);
					
					this.testResult = result;
					
				} catch (error) {
					this.addLog('error', `批量云函数调用失败: ${error.message}`);
					this.addLog('error', `错误详情: ${JSON.stringify(error)}`);
					console.error('批量云函数调用失败:', error);
				}
			},
			
			onTypeChange(e) {
				this.typeIndex = e.detail.value;
			},
			
			clearLogs() {
				this.logs = [];
				this.testResult = null;
				this.addLog('info', '日志已清空');
			}
		}
	}
</script>

<style lang="scss" scoped>
	.debug-cloud {
		padding: 20px;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.debug-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30px;
		padding: 20px;
		background-color: #fff;
		border-radius: 12px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
		
		.debug-title {
			font-size: 24px;
			font-weight: bold;
			color: #333;
		}
		
		.debug-controls {
			display: flex;
			gap: 10px;
			
			button {
				padding: 8px 16px;
				border: none;
				border-radius: 6px;
				background-color: #409EFF;
				color: #fff;
				cursor: pointer;
				font-size: 14px;
				
				&:hover {
					background-color: #337ecc;
				}
				
				&:nth-child(2) {
					background-color: #67C23A;
					
					&:hover {
						background-color: #5daf34;
					}
				}
				
				&:nth-child(3) {
					background-color: #E6A23C;
					
					&:hover {
						background-color: #cf9236;
					}
				}
			}
		}
	}
	
	.debug-content {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20px;
	}
	
	.debug-section {
		background-color: #fff;
		border-radius: 12px;
		padding: 20px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
		
		.section-title {
			display: block;
			font-size: 18px;
			font-weight: 600;
			color: #333;
			margin-bottom: 15px;
			border-bottom: 2px solid #409EFF;
			padding-bottom: 8px;
		}
		
		&:nth-child(3) {
			grid-column: 1 / -1;
		}
	}
	
	.test-form {
		.form-item {
			display: flex;
			align-items: center;
			margin-bottom: 15px;
			
			.form-label {
				width: 100px;
				font-size: 14px;
				color: #666;
				font-weight: 500;
			}
			
			.form-input {
				flex: 1;
				padding: 8px 12px;
				border: 1px solid #ddd;
				border-radius: 4px;
				font-size: 14px;
			}
			
			.picker-text {
				flex: 1;
				padding: 8px 12px;
				border: 1px solid #ddd;
				border-radius: 4px;
				font-size: 14px;
				background-color: #fff;
			}
		}
	}
	
	.test-result {
		.result-content {
			.result-text {
				display: block;
				font-size: 12px;
				color: #333;
				background-color: #f8f9fa;
				padding: 15px;
				border-radius: 4px;
				white-space: pre-wrap;
				word-break: break-all;
				max-height: 300px;
				overflow-y: auto;
			}
		}
		
		.result-empty {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100px;
			color: #999;
		}
	}
	
	.debug-logs {
		max-height: 400px;
		overflow-y: auto;
		
		.log-item {
			display: flex;
			gap: 10px;
			padding: 8px;
			margin-bottom: 5px;
			border-radius: 4px;
			font-size: 12px;
			
			&.info {
				background-color: #e1f3ff;
				color: #409EFF;
			}
			
			&.success {
				background-color: #f0f9ff;
				color: #67C23A;
			}
			
			&.warning {
				background-color: #fdf6ec;
				color: #E6A23C;
			}
			
			&.error {
				background-color: #fef0f0;
				color: #F56C6C;
			}
			
			.log-time {
				font-weight: 500;
				min-width: 80px;
			}
			
			.log-message {
				flex: 1;
				word-break: break-all;
			}
		}
	}
</style>
