import cloudbase from '@cloudbase/app';
import pkg from '@cloudbase/app/package.json';
cloudbase.registerVersion(pkg.version);
try {
    ;
    window.cloudbase = cloudbase;
}
catch (e) { }
export default cloudbase;

//# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImluZGV4LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLE9BQU8sU0FBUyxNQUFNLGdCQUFnQixDQUFBO0FBRXRDLE9BQU8sR0FBRyxNQUFNLDZCQUE2QixDQUFBO0FBRTdDLFNBQVMsQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLE9BQU8sQ0FBQyxDQUFBO0FBT3RDLElBQUk7SUFDRixDQUFDO0lBQUMsTUFBaUIsQ0FBQyxTQUFTLEdBQUcsU0FBUyxDQUFBO0NBQzFDO0FBQUMsT0FBTyxDQUFDLEVBQUUsR0FBRTtBQUdkLGVBQWUsU0FBUyxDQUFBIiwiZmlsZSI6ImluZGV4LmVzbS5qcyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjbG91ZGJhc2UgZnJvbSAnQGNsb3VkYmFzZS9hcHAnXG5pbXBvcnQgeyBJQ2xvdWRiYXNlIH0gZnJvbSAnQGNsb3VkYmFzZS90eXBlcydcbmltcG9ydCBwa2cgZnJvbSAnQGNsb3VkYmFzZS9hcHAvcGFja2FnZS5qc29uJ1xuXG5jbG91ZGJhc2UucmVnaXN0ZXJWZXJzaW9uKHBrZy52ZXJzaW9uKVxuXG5kZWNsYXJlIGdsb2JhbCB7XG4gIGludGVyZmFjZSBXaW5kb3cge1xuICAgIGNsb3VkYmFzZTogSUNsb3VkYmFzZVxuICB9XG59XG50cnkge1xuICA7KHdpbmRvdyBhcyBXaW5kb3cpLmNsb3VkYmFzZSA9IGNsb3VkYmFzZVxufSBjYXRjaCAoZSkge31cbi8vIEB0cy1pZ25vcmVcbmV4cG9ydCA9IGNsb3VkYmFzZVxuZXhwb3J0IGRlZmF1bHQgY2xvdWRiYXNlXG4iXX0=
