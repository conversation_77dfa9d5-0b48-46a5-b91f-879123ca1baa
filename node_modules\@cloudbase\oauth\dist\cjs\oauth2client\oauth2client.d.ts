import { ErrorType } from './consts';
import { AuthClient, SimpleStorage } from './interface';
import { Credentials, ResponseError, RequestOptions, RequestFunction, OAuth2ClientOptions, AuthClientRequestOptions } from './models';
export interface ToResponseErrorOptions {
    error?: ErrorType;
    error_description?: string | null;
    error_uri?: string | null;
    details?: any | null;
}
export declare const defaultRequest: RequestFunction;
export declare const toResponseError: (error: ResponseError | Error, options?: ToResponseErrorOptions) => ResponseError;
export declare function generateRequestId(): string;
declare class DefaultStorage implements SimpleStorage {
    private readonly _env;
    constructor(opts?: {
        env: string;
    });
    getItem(key: string): Promise<string | null>;
    removeItem(key: string): Promise<void>;
    setItem(key: string, value: string): Promise<void>;
    getItemSync(key: string): string | null;
    removeItemSync(key: string): void;
    setItemSync(key: string, value: string): void;
}
export declare const defaultStorage: DefaultStorage;
export declare class OAuth2Client implements AuthClient {
    private static defaultRetry;
    private static minRetry;
    private static maxRetry;
    private static retryInterval;
    private apiOrigin;
    private clientId;
    private retry;
    private clientSecret?;
    private baseRequest;
    private localCredentials;
    private storage;
    private deviceID?;
    private tokenInURL?;
    private refreshTokenFunc;
    private headers?;
    private singlePromise;
    private anonymousSignInFunc;
    private wxCloud;
    private basicAuth;
    constructor(options: OAuth2ClientOptions);
    setCredentials(credentials?: Credentials): Promise<void>;
    getAccessToken(): Promise<string>;
    request<T>(url: string, options?: AuthClientRequestOptions): Promise<T>;
    wxCloudCallFunction<T>(url: string, options?: RequestOptions): Promise<T>;
    getCredentials(): Promise<Credentials | null>;
    getCredentialsSync(): Credentials | null;
    getCredentialsAsync(): Promise<Credentials | null>;
    getScope(): Promise<string>;
    getGroups(): Promise<string[]>;
    refreshToken(credentials: Credentials): Promise<Credentials>;
    private checkRetry;
    private formatRetry;
    private sleep;
    private anonymousSignIn;
    private defaultRefreshTokenFunc;
    private getDeviceId;
    private unAuthenticatedError;
}
export {};
