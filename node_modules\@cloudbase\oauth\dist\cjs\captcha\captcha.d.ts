import { SimpleStorage, RequestFunction } from '../oauth2client/interface';
import { AuthClientRequestOptions } from '../oauth2client/models';
export interface CaptchaOptions {
    clientId: string;
    request: RequestFunction;
    storage: SimpleStorage;
    openURIWithCallback?: OpenURIWithCallbackFuction;
}
type OpenURIWithCallbackFuction = (url: string) => Promise<CaptchaToken>;
export interface CaptchaToken {
    captcha_token: string;
    expires_in: number;
    expires_at?: Date | null;
}
export interface CaptchaRequestOptions extends AuthClientRequestOptions {
    withCaptcha?: boolean;
}
export interface GetCaptchaResponse {
    captcha_token?: string;
    expires_in?: number;
    url?: string;
}
export declare class Captcha {
    private config;
    private tokenSectionName;
    constructor(opts: CaptchaOptions);
    request<T>(url: string, options?: CaptchaRequestOptions): Promise<T>;
    private getDefaultOpenURIWithCallback;
    private defaultOpenURIWithCallback;
    private getCaptchaToken;
    private appendCaptchaTokenToURL;
    private saveCaptchaToken;
    private findCaptchaToken;
}
export {};
