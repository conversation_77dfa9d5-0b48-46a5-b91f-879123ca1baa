/*! For license information please see ai.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("cloudbase_ai",[],t):"object"==typeof exports?exports.cloudbase_ai=t():e.cloudbase_ai=t()}("undefined"!=typeof window?window:this,(()=>(()=>{var e={639:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),o(r(569),t)},877:function(e,t,r){var n,o;void 0===(o="function"==typeof(n=function(){"use strict";var e=void 0!==r.g?r.g:self;if(void 0!==e.TextEncoder&&void 0!==e.TextDecoder)return{TextEncoder:e.TextEncoder,TextDecoder:e.TextDecoder};var t=["utf8","utf-8","unicode-1-1-utf-8"];return{TextEncoder:function(e){if(t.indexOf(e)<0&&null!=e)throw new RangeError("Invalid encoding type. Only utf-8 is supported");this.encoding="utf-8",this.encode=function(e){if("string"!=typeof e)throw new TypeError("passed argument must be of type string");var t=unescape(encodeURIComponent(e)),r=new Uint8Array(t.length);return t.split("").forEach((function(e,t){r[t]=e.charCodeAt(0)})),r}},TextDecoder:function(e,r){if(t.indexOf(e)<0&&null!=e)throw new RangeError("Invalid encoding type. Only utf-8 is supported");if(this.encoding="utf-8",this.ignoreBOM=!1,this.fatal=void 0!==r&&"fatal"in r&&r.fatal,"boolean"!=typeof this.fatal)throw new TypeError("fatal flag must be boolean");this.decode=function(e,t){if(void 0===e)return"";if("boolean"!=typeof(void 0!==t&&"stream"in t&&t.stream))throw new TypeError("stream option must be boolean");if(ArrayBuffer.isView(e)){var r=new Uint8Array(e.buffer,e.byteOffset,e.byteLength),n=new Array(r.length);return r.forEach((function(e,t){n[t]=String.fromCharCode(e)})),decodeURIComponent(escape(n.join("")))}throw new TypeError("passed argument must be an array buffer view")}}}})?n.apply(t,[]):n)||(e.exports=o)},569:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AI:()=>yi,Bot:()=>Kn,SimpleChatModel:()=>gi,createAi:()=>Pi,models:()=>Ri,registerAi:()=>Oi,utils:()=>n});var n={};r.r(n),r.d(n,{ReadableStream:()=>Un,TextDecoderStream:()=>Ln,TransformStream:()=>zn,createAsyncIterable:()=>$n,createEventSourceParserTransformStream:()=>Mn,createPromise:()=>Yn,functionToolToModelTool:()=>Qn,intoStandardStream:()=>Dn,intoTextStream:()=>Nn,isToolCallAssistantMessage:()=>Hn,readableStream2JsonObject:()=>Gn,toPolyfillReadable:()=>Fn});var o={};function i(){}function a(e){return"object"==typeof e&&null!==e||"function"==typeof e}r.r(o),r.d(o,{ArkSimpleModel:()=>So,DSSimpleModel:()=>Oo,DefaultSimpleModel:()=>Ko,HunYuanBetaSimpleModel:()=>ho,HunYuanExpSimpleModel:()=>Lo,HunYuanOpenSimpleModel:()=>Go,HunYuanSimpleModel:()=>yo,MODELS:()=>di,MoonshotSimpleModel:()=>Uo,ReactModel:()=>ii,YiSimpleModel:()=>xo,ZhiPuSimpleModel:()=>io,toolMap:()=>li});const s=i;function l(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0})}catch(e){}}const u=Promise,c=Promise.resolve.bind(u),f=Promise.prototype.then,d=Promise.reject.bind(u),h=c;function p(e){return new u(e)}function b(e){return p((t=>t(e)))}function m(e){return d(e)}function y(e,t,r){return f.call(e,t,r)}function v(e,t,r){y(y(e,t,r),void 0,s)}function _(e,t){v(e,t)}function g(e,t){v(e,void 0,t)}function w(e,t,r){return y(e,t,r)}function S(e){y(e,void 0,s)}let R=e=>{if("function"==typeof queueMicrotask)R=queueMicrotask;else{const e=b(void 0);R=t=>y(e,t)}return R(e)};function T(e,t,r){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function P(e,t,r){try{return b(T(e,t,r))}catch(e){return m(e)}}class q{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){const t=this._back;let r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){const e=this._front;let t=e;const r=this._cursor;let n=r+1;const o=e._elements,i=o[r];return 16384===n&&(t=e._next,n=0),--this._size,this._cursor=n,e!==t&&(this._front=t),o[r]=void 0,i}forEach(e){let t=this._cursor,r=this._front,n=r._elements;for(;!(t===n.length&&void 0===r._next||t===n.length&&(r=r._next,n=r._elements,t=0,0===n.length));)e(n[t]),++t}peek(){const e=this._front,t=this._cursor;return e._elements[t]}}const O=Symbol("[[AbortSteps]]"),j=Symbol("[[ErrorSteps]]"),k=Symbol("[[CancelSteps]]"),E=Symbol("[[PullSteps]]"),C=Symbol("[[ReleaseSteps]]");function x(e,t){e._ownerReadableStream=t,t._reader=e,"readable"===t._state?B(e):"closed"===t._state?function(e){B(e),M(e)}(e):U(e,t._storedError)}function A(e,t){return Lr(e._ownerReadableStream,t)}function W(e){const t=e._ownerReadableStream;"readable"===t._state?z(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e){U(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness"))}(e),t._readableStreamController[C](),t._reader=void 0,e._ownerReadableStream=void 0}function I(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function B(e){e._closedPromise=p(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r}))}function U(e,t){B(e),z(e,t)}function z(e,t){void 0!==e._closedPromise_reject&&(S(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function M(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}const F=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},L=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function $(e,t){if(void 0!==e&&"object"!=typeof(r=e)&&"function"!=typeof r)throw new TypeError(`${t} is not an object.`);var r}function D(e,t){if("function"!=typeof e)throw new TypeError(`${t} is not a function.`)}function N(e,t){if(!function(e){return"object"==typeof e&&null!==e||"function"==typeof e}(e))throw new TypeError(`${t} is not an object.`)}function G(e,t,r){if(void 0===e)throw new TypeError(`Parameter ${t} is required in '${r}'.`)}function Y(e,t,r){if(void 0===e)throw new TypeError(`${t} is required in '${r}'.`)}function H(e){return Number(e)}function Q(e){return 0===e?0:e}function V(e,t){const r=Number.MAX_SAFE_INTEGER;let n=Number(e);if(n=Q(n),!F(n))throw new TypeError(`${t} is not a finite number`);if(n=function(e){return Q(L(e))}(n),n<0||n>r)throw new TypeError(`${t} is outside the accepted range of 0 to ${r}, inclusive`);return F(n)&&0!==n?n:0}function J(e,t){if(!Mr(e))throw new TypeError(`${t} is not a ReadableStream.`)}function X(e){return new re(e)}function Z(e,t){e._reader._readRequests.push(t)}function K(e,t,r){const n=e._reader._readRequests.shift();r?n._closeSteps():n._chunkSteps(t)}function ee(e){return e._reader._readRequests.length}function te(e){const t=e._reader;return void 0!==t&&!!ne(t)}class re{constructor(e){if(G(e,1,"ReadableStreamDefaultReader"),J(e,"First parameter"),Fr(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");x(this,e),this._readRequests=new q}get closed(){return ne(this)?this._closedPromise:m(ae("closed"))}cancel(e=void 0){return ne(this)?void 0===this._ownerReadableStream?m(I("cancel")):A(this,e):m(ae("cancel"))}read(){if(!ne(this))return m(ae("read"));if(void 0===this._ownerReadableStream)return m(I("read from"));let e,t;const r=p(((r,n)=>{e=r,t=n}));return oe(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!ne(this))throw ae("releaseLock");void 0!==this._ownerReadableStream&&function(e){W(e),ie(e,new TypeError("Reader was released"))}(this)}}function ne(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof re}function oe(e,t){const r=e._ownerReadableStream;r._disturbed=!0,"closed"===r._state?t._closeSteps():"errored"===r._state?t._errorSteps(r._storedError):r._readableStreamController[E](t)}function ie(e,t){const r=e._readRequests;e._readRequests=new q,r.forEach((e=>{e._errorSteps(t)}))}function ae(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}var se,le,ue;function ce(e){return e.slice()}function fe(e,t,r,n,o){new Uint8Array(e).set(new Uint8Array(r,n,o),t)}Object.defineProperties(re.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),l(re.prototype.cancel,"cancel"),l(re.prototype.read,"read"),l(re.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(re.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});let de=e=>(de="function"==typeof e.transfer?e=>e.transfer():"function"==typeof structuredClone?e=>structuredClone(e,{transfer:[e]}):e=>e,de(e)),he=e=>(he="boolean"==typeof e.detached?e=>e.detached:e=>0===e.byteLength,he(e));function pe(e,t,r){if(e.slice)return e.slice(t,r);const n=r-t,o=new ArrayBuffer(n);return fe(o,0,e,t,n),o}function be(e,t){const r=e[t];if(null!=r){if("function"!=typeof r)throw new TypeError(`${String(t)} is not a function`);return r}}function me(e){try{const t=e.done,r=e.value;return y(h(r),(e=>({done:t,value:e})))}catch(e){return m(e)}}const ye=null!==(ue=null!==(se=Symbol.asyncIterator)&&void 0!==se?se:null===(le=Symbol.for)||void 0===le?void 0:le.call(Symbol,"Symbol.asyncIterator"))&&void 0!==ue?ue:"@@asyncIterator";function ve(e,t="sync",r){if(void 0===r)if("async"===t){if(void 0===(r=be(e,ye)))return function(e){const t={next(){let t;try{t=_e(e)}catch(e){return m(e)}return me(t)},return(t){let r;try{const n=be(e.iterator,"return");if(void 0===n)return b({done:!0,value:t});r=T(n,e.iterator,[t])}catch(e){return m(e)}return a(r)?me(r):m(new TypeError("The iterator.return() method must return an object"))}};return{iterator:t,nextMethod:t.next,done:!1}}(ve(e,"sync",be(e,Symbol.iterator)))}else r=be(e,Symbol.iterator);if(void 0===r)throw new TypeError("The object is not iterable");const n=T(r,e,[]);if(!a(n))throw new TypeError("The iterator method must return an object");return{iterator:n,nextMethod:n.next,done:!1}}function _e(e){const t=T(e.nextMethod,e.iterator,[]);if(!a(t))throw new TypeError("The iterator.next() method must return an object");return t}class ge{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){const e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?w(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){const t=()=>this._returnSteps(e);return this._ongoingPromise?w(this._ongoingPromise,t,t):t()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});const e=this._reader;let t,r;const n=p(((e,n)=>{t=e,r=n}));return oe(e,{_chunkSteps:e=>{this._ongoingPromise=void 0,R((()=>t({value:e,done:!1})))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,W(e),t({value:void 0,done:!0})},_errorSteps:t=>{this._ongoingPromise=void 0,this._isFinished=!0,W(e),r(t)}}),n}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;const t=this._reader;if(!this._preventCancel){const r=A(t,e);return W(t),w(r,(()=>({value:e,done:!0})))}return W(t),b({value:e,done:!0})}}const we={next(){return Se(this)?this._asyncIteratorImpl.next():m(Re("next"))},return(e){return Se(this)?this._asyncIteratorImpl.return(e):m(Re("return"))},[ye](){return this}};function Se(e){if(!a(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof ge}catch(e){return!1}}function Re(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}Object.defineProperty(we,ye,{enumerable:!1});const Te=Number.isNaN||function(e){return e!=e};function Pe(e){const t=pe(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function qe(e){const t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function Oe(e,t,r){if("number"!=typeof(n=r)||Te(n)||n<0||r===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");var n;e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function je(e){e._queue=new q,e._queueTotalSize=0}function ke(e){return e===DataView}class Ee{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!Ae(this))throw ot("view");return this._view}respond(e){if(!Ae(this))throw ot("respond");if(G(e,1,"respond"),e=V(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(he(this._view.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");tt(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!Ae(this))throw ot("respondWithNewView");if(G(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(he(e.buffer))throw new TypeError("The given view's buffer has been detached and so cannot be used as a response");rt(this._associatedReadableByteStreamController,e)}}Object.defineProperties(Ee.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),l(Ee.prototype.respond,"respond"),l(Ee.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Ee.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class Ce{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!xe(this))throw it("byobRequest");return Ke(this)}get desiredSize(){if(!xe(this))throw it("desiredSize");return et(this)}close(){if(!xe(this))throw it("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");const e=this._controlledReadableByteStream._state;if("readable"!==e)throw new TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);Ve(this)}enqueue(e){if(!xe(this))throw it("enqueue");if(G(e,1,"enqueue"),!ArrayBuffer.isView(e))throw new TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");const t=this._controlledReadableByteStream._state;if("readable"!==t)throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);Je(this,e)}error(e=void 0){if(!xe(this))throw it("error");Xe(this,e)}[k](e){Ie(this),je(this);const t=this._cancelAlgorithm(e);return Qe(this),t}[E](e){const t=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void Ze(this,e);const r=this._autoAllocateChunkSize;if(void 0!==r){let n;try{n=new ArrayBuffer(r)}catch(t){return void e._errorSteps(t)}const o={buffer:n,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}Z(t,e),We(this)}[C](){if(this._pendingPullIntos.length>0){const e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new q,this._pendingPullIntos.push(e)}}}function xe(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof Ce}function Ae(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof Ee}function We(e){const t=function(e){const t=e._controlledReadableByteStream;return"readable"===t._state&&(!e._closeRequested&&(!!e._started&&(!!(te(t)&&ee(t)>0)||(!!(ct(t)&&ut(t)>0)||et(e)>0))))}(e);t&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,v(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,We(e)),null)),(t=>(Xe(e,t),null)))))}function Ie(e){Ne(e),e._pendingPullIntos=new q}function Be(e,t){let r=!1;"closed"===e._state&&(r=!0);const n=Ue(t);"default"===t.readerType?K(e,n,r):function(e,t,r){const n=e._reader._readIntoRequests.shift();r?n._closeSteps(t):n._chunkSteps(t)}(e,n,r)}function Ue(e){const t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function ze(e,t,r,n){e._queue.push({buffer:t,byteOffset:r,byteLength:n}),e._queueTotalSize+=n}function Me(e,t,r,n){let o;try{o=pe(t,r,r+n)}catch(t){throw Xe(e,t),t}ze(e,o,0,n)}function Fe(e,t){t.bytesFilled>0&&Me(e,t.buffer,t.byteOffset,t.bytesFilled),He(e)}function Le(e,t){const r=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),n=t.bytesFilled+r;let o=r,i=!1;const a=n-n%t.elementSize;a>=t.minimumFill&&(o=a-t.bytesFilled,i=!0);const s=e._queue;for(;o>0;){const r=s.peek(),n=Math.min(o,r.byteLength),i=t.byteOffset+t.bytesFilled;fe(t.buffer,i,r.buffer,r.byteOffset,n),r.byteLength===n?s.shift():(r.byteOffset+=n,r.byteLength-=n),e._queueTotalSize-=n,$e(0,n,t),o-=n}return i}function $e(e,t,r){r.bytesFilled+=t}function De(e){0===e._queueTotalSize&&e._closeRequested?(Qe(e),$r(e._controlledReadableByteStream)):We(e)}function Ne(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function Ge(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;const t=e._pendingPullIntos.peek();Le(e,t)&&(He(e),Be(e._controlledReadableByteStream,t))}}function Ye(e,t){const r=e._pendingPullIntos.peek();Ne(e),"closed"===e._controlledReadableByteStream._state?function(e,t){"none"===t.readerType&&He(e);const r=e._controlledReadableByteStream;if(ct(r))for(;ut(r)>0;)Be(r,He(e))}(e,r):function(e,t,r){if($e(0,t,r),"none"===r.readerType)return Fe(e,r),void Ge(e);if(r.bytesFilled<r.minimumFill)return;He(e);const n=r.bytesFilled%r.elementSize;if(n>0){const t=r.byteOffset+r.bytesFilled;Me(e,r.buffer,t-n,n)}r.bytesFilled-=n,Be(e._controlledReadableByteStream,r),Ge(e)}(e,t,r),We(e)}function He(e){return e._pendingPullIntos.shift()}function Qe(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function Ve(e){const t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state)if(e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();if(t.bytesFilled%t.elementSize!=0){const t=new TypeError("Insufficient bytes to fill elements in the given buffer");throw Xe(e,t),t}}Qe(e),$r(t)}}function Je(e,t){const r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;const{buffer:n,byteOffset:o,byteLength:i}=t;if(he(n))throw new TypeError("chunk's buffer is detached and so cannot be enqueued");const a=de(n);if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();if(he(t.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");Ne(e),t.buffer=de(t.buffer),"none"===t.readerType&&Fe(e,t)}te(r)?(function(e){const t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;Ze(e,t._readRequests.shift())}}(e),0===ee(r)?ze(e,a,o,i):(e._pendingPullIntos.length>0&&He(e),K(r,new Uint8Array(a,o,i),!1))):ct(r)?(ze(e,a,o,i),Ge(e)):ze(e,a,o,i),We(e)}function Xe(e,t){const r=e._controlledReadableByteStream;"readable"===r._state&&(Ie(e),je(e),Qe(e),Dr(r,t))}function Ze(e,t){const r=e._queue.shift();e._queueTotalSize-=r.byteLength,De(e);const n=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(n)}function Ke(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),n=Object.create(Ee.prototype);!function(e,t,r){e._associatedReadableByteStreamController=t,e._view=r}(n,e,r),e._byobRequest=n}return e._byobRequest}function et(e){const t=e._controlledReadableByteStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function tt(e,t){const r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===t)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw new RangeError("bytesWritten out of range")}r.buffer=de(r.buffer),Ye(e,t)}function rt(e,t){const r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===t.byteLength)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");const n=t.byteLength;r.buffer=de(t.buffer),Ye(e,n)}function nt(e,t,r,n,o,i,a){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,je(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=i,t._pullAlgorithm=n,t._cancelAlgorithm=o,t._autoAllocateChunkSize=a,t._pendingPullIntos=new q,e._readableStreamController=t,v(b(r()),(()=>(t._started=!0,We(t),null)),(e=>(Xe(t,e),null)))}function ot(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function it(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function at(e,t){if("byob"!=(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function st(e){return new ft(e)}function lt(e,t){e._reader._readIntoRequests.push(t)}function ut(e){return e._reader._readIntoRequests.length}function ct(e){const t=e._reader;return void 0!==t&&!!dt(t)}Object.defineProperties(Ce.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),l(Ce.prototype.close,"close"),l(Ce.prototype.enqueue,"enqueue"),l(Ce.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Ce.prototype,Symbol.toStringTag,{value:"ReadableByteStreamController",configurable:!0});class ft{constructor(e){if(G(e,1,"ReadableStreamBYOBReader"),J(e,"First parameter"),Fr(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!xe(e._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");x(this,e),this._readIntoRequests=new q}get closed(){return dt(this)?this._closedPromise:m(bt("closed"))}cancel(e=void 0){return dt(this)?void 0===this._ownerReadableStream?m(I("cancel")):A(this,e):m(bt("cancel"))}read(e,t={}){if(!dt(this))return m(bt("read"));if(!ArrayBuffer.isView(e))return m(new TypeError("view must be an array buffer view"));if(0===e.byteLength)return m(new TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return m(new TypeError("view's buffer must have non-zero byteLength"));if(he(e.buffer))return m(new TypeError("view's buffer has been detached"));let r;try{r=function(e,t){var r;return $(e,t),{min:V(null!==(r=null==e?void 0:e.min)&&void 0!==r?r:1,`${t} has member 'min' that`)}}(t,"options")}catch(e){return m(e)}const n=r.min;if(0===n)return m(new TypeError("options.min must be greater than 0"));if(function(e){return ke(e.constructor)}(e)){if(n>e.byteLength)return m(new RangeError("options.min must be less than or equal to view's byteLength"))}else if(n>e.length)return m(new RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return m(I("read from"));let o,i;const a=p(((e,t)=>{o=e,i=t}));return ht(this,e,n,{_chunkSteps:e=>o({value:e,done:!1}),_closeSteps:e=>o({value:e,done:!0}),_errorSteps:e=>i(e)}),a}releaseLock(){if(!dt(this))throw bt("releaseLock");void 0!==this._ownerReadableStream&&function(e){W(e),pt(e,new TypeError("Reader was released"))}(this)}}function dt(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof ft}function ht(e,t,r,n){const o=e._ownerReadableStream;o._disturbed=!0,"errored"===o._state?n._errorSteps(o._storedError):function(e,t,r,n){const o=e._controlledReadableByteStream,i=t.constructor,a=function(e){return ke(e)?1:e.BYTES_PER_ELEMENT}(i),{byteOffset:s,byteLength:l}=t,u=r*a;let c;try{c=de(t.buffer)}catch(e){return void n._errorSteps(e)}const f={buffer:c,bufferByteLength:c.byteLength,byteOffset:s,byteLength:l,bytesFilled:0,minimumFill:u,elementSize:a,viewConstructor:i,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(f),void lt(o,n);if("closed"!==o._state){if(e._queueTotalSize>0){if(Le(e,f)){const t=Ue(f);return De(e),void n._chunkSteps(t)}if(e._closeRequested){const t=new TypeError("Insufficient bytes to fill elements in the given buffer");return Xe(e,t),void n._errorSteps(t)}}e._pendingPullIntos.push(f),lt(o,n),We(e)}else{const e=new i(f.buffer,f.byteOffset,0);n._closeSteps(e)}}(o._readableStreamController,t,r,n)}function pt(e,t){const r=e._readIntoRequests;e._readIntoRequests=new q,r.forEach((e=>{e._errorSteps(t)}))}function bt(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function mt(e,t){const{highWaterMark:r}=e;if(void 0===r)return t;if(Te(r)||r<0)throw new RangeError("Invalid highWaterMark");return r}function yt(e){const{size:t}=e;return t||(()=>1)}function vt(e,t){$(e,t);const r=null==e?void 0:e.highWaterMark,n=null==e?void 0:e.size;return{highWaterMark:void 0===r?void 0:H(r),size:void 0===n?void 0:_t(n,`${t} has member 'size' that`)}}function _t(e,t){return D(e,t),t=>H(e(t))}function gt(e,t,r){return D(e,r),r=>P(e,t,[r])}function wt(e,t,r){return D(e,r),()=>P(e,t,[])}function St(e,t,r){return D(e,r),r=>T(e,t,[r])}function Rt(e,t,r){return D(e,r),(r,n)=>P(e,t,[r,n])}function Tt(e,t){if(!kt(e))throw new TypeError(`${t} is not a WritableStream.`)}Object.defineProperties(ft.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),l(ft.prototype.cancel,"cancel"),l(ft.prototype.read,"read"),l(ft.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ft.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});const Pt="function"==typeof AbortController;class qt{constructor(e={},t={}){void 0===e?e=null:N(e,"First parameter");const r=vt(t,"Second parameter"),n=function(e,t){$(e,t);const r=null==e?void 0:e.abort,n=null==e?void 0:e.close,o=null==e?void 0:e.start,i=null==e?void 0:e.type,a=null==e?void 0:e.write;return{abort:void 0===r?void 0:gt(r,e,`${t} has member 'abort' that`),close:void 0===n?void 0:wt(n,e,`${t} has member 'close' that`),start:void 0===o?void 0:St(o,e,`${t} has member 'start' that`),write:void 0===a?void 0:Rt(a,e,`${t} has member 'write' that`),type:i}}(e,"First parameter");if(jt(this),void 0!==n.type)throw new RangeError("Invalid type is specified");const o=yt(r);!function(e,t,r,n){const o=Object.create(Yt.prototype);let i,a,s,l;i=void 0!==t.start?()=>t.start(o):()=>{},a=void 0!==t.write?e=>t.write(e,o):()=>b(void 0),s=void 0!==t.close?()=>t.close():()=>b(void 0),l=void 0!==t.abort?e=>t.abort(e):()=>b(void 0),Qt(e,o,i,a,s,l,r,n)}(this,n,mt(r,1),o)}get locked(){if(!kt(this))throw tr("locked");return Et(this)}abort(e=void 0){return kt(this)?Et(this)?m(new TypeError("Cannot abort a stream that already has a writer")):Ct(this,e):m(tr("abort"))}close(){return kt(this)?Et(this)?m(new TypeError("Cannot close a stream that already has a writer")):Bt(this)?m(new TypeError("Cannot close an already-closing stream")):xt(this):m(tr("close"))}getWriter(){if(!kt(this))throw tr("getWriter");return Ot(this)}}function Ot(e){return new Mt(e)}function jt(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new q,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function kt(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof qt}function Et(e){return void 0!==e._writer}function Ct(e,t){var r;if("closed"===e._state||"errored"===e._state)return b(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort(t);const n=e._state;if("closed"===n||"errored"===n)return b(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let o=!1;"erroring"===n&&(o=!0,t=void 0);const i=p(((r,n)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:n,_reason:t,_wasAlreadyErroring:o}}));return e._pendingAbortRequest._promise=i,o||Wt(e,t),i}function xt(e){const t=e._state;if("closed"===t||"errored"===t)return m(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));const r=p(((t,r)=>{const n={_resolve:t,_reject:r};e._closeRequest=n})),n=e._writer;var o;return void 0!==n&&e._backpressure&&"writable"===t&&hr(n),Oe(o=e._writableStreamController,Gt,0),Xt(o),r}function At(e,t){"writable"!==e._state?It(e):Wt(e,t)}function Wt(e,t){const r=e._writableStreamController;e._state="erroring",e._storedError=t;const n=e._writer;void 0!==n&&$t(n,t),!function(e){return void 0!==e._inFlightWriteRequest||void 0!==e._inFlightCloseRequest}(e)&&r._started&&It(e)}function It(e){e._state="errored",e._writableStreamController[j]();const t=e._storedError;if(e._writeRequests.forEach((e=>{e._reject(t)})),e._writeRequests=new q,void 0===e._pendingAbortRequest)return void Ut(e);const r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring)return r._reject(t),void Ut(e);v(e._writableStreamController[O](r._reason),(()=>(r._resolve(),Ut(e),null)),(t=>(r._reject(t),Ut(e),null)))}function Bt(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function Ut(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);const t=e._writer;void 0!==t&&sr(t,e._storedError)}function zt(e,t){const r=e._writer;void 0!==r&&t!==e._backpressure&&(t?function(e){ur(e)}(r):hr(r)),e._backpressure=t}Object.defineProperties(qt.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),l(qt.prototype.abort,"abort"),l(qt.prototype.close,"close"),l(qt.prototype.getWriter,"getWriter"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(qt.prototype,Symbol.toStringTag,{value:"WritableStream",configurable:!0});class Mt{constructor(e){if(G(e,1,"WritableStreamDefaultWriter"),Tt(e,"First parameter"),Et(e))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;const t=e._state;if("writable"===t)!Bt(e)&&e._backpressure?ur(this):fr(this),ir(this);else if("erroring"===t)cr(this,e._storedError),ir(this);else if("closed"===t)fr(this),ir(this),lr(this);else{const t=e._storedError;cr(this,t),ar(this,t)}}get closed(){return Ft(this)?this._closedPromise:m(nr("closed"))}get desiredSize(){if(!Ft(this))throw nr("desiredSize");if(void 0===this._ownerWritableStream)throw or("desiredSize");return function(e){const t=e._ownerWritableStream,r=t._state;return"errored"===r||"erroring"===r?null:"closed"===r?0:Jt(t._writableStreamController)}(this)}get ready(){return Ft(this)?this._readyPromise:m(nr("ready"))}abort(e=void 0){return Ft(this)?void 0===this._ownerWritableStream?m(or("abort")):function(e,t){return Ct(e._ownerWritableStream,t)}(this,e):m(nr("abort"))}close(){if(!Ft(this))return m(nr("close"));const e=this._ownerWritableStream;return void 0===e?m(or("close")):Bt(e)?m(new TypeError("Cannot close an already-closing stream")):Lt(this)}releaseLock(){if(!Ft(this))throw nr("releaseLock");void 0!==this._ownerWritableStream&&Dt(this)}write(e=void 0){return Ft(this)?void 0===this._ownerWritableStream?m(or("write to")):Nt(this,e):m(nr("write"))}}function Ft(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof Mt}function Lt(e){return xt(e._ownerWritableStream)}function $t(e,t){"pending"===e._readyPromiseState?dr(e,t):function(e,t){cr(e,t)}(e,t)}function Dt(e){const t=e._ownerWritableStream,r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");$t(e,r),function(e,t){"pending"===e._closedPromiseState?sr(e,t):function(e,t){ar(e,t)}(e,t)}(e,r),t._writer=void 0,e._ownerWritableStream=void 0}function Nt(e,t){const r=e._ownerWritableStream,n=r._writableStreamController,o=function(e,t){try{return e._strategySizeAlgorithm(t)}catch(t){return Zt(e,t),1}}(n,t);if(r!==e._ownerWritableStream)return m(or("write to"));const i=r._state;if("errored"===i)return m(r._storedError);if(Bt(r)||"closed"===i)return m(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===i)return m(r._storedError);const a=function(e){return p(((t,r)=>{const n={_resolve:t,_reject:r};e._writeRequests.push(n)}))}(r);return function(e,t,r){try{Oe(e,t,r)}catch(t){return void Zt(e,t)}const n=e._controlledWritableStream;Bt(n)||"writable"!==n._state||zt(n,Kt(e)),Xt(e)}(n,t,o),a}Object.defineProperties(Mt.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),l(Mt.prototype.abort,"abort"),l(Mt.prototype.close,"close"),l(Mt.prototype.releaseLock,"releaseLock"),l(Mt.prototype.write,"write"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Mt.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});const Gt={};class Yt{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!Ht(this))throw rr("abortReason");return this._abortReason}get signal(){if(!Ht(this))throw rr("signal");if(void 0===this._abortController)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(e=void 0){if(!Ht(this))throw rr("error");"writable"===this._controlledWritableStream._state&&er(this,e)}[O](e){const t=this._abortAlgorithm(e);return Vt(this),t}[j](){je(this)}}function Ht(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof Yt}function Qt(e,t,r,n,o,i,a,s){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,je(t),t._abortReason=void 0,t._abortController=function(){if(Pt)return new AbortController}(),t._started=!1,t._strategySizeAlgorithm=s,t._strategyHWM=a,t._writeAlgorithm=n,t._closeAlgorithm=o,t._abortAlgorithm=i;const l=Kt(t);zt(e,l),v(b(r()),(()=>(t._started=!0,Xt(t),null)),(r=>(t._started=!0,At(e,r),null)))}function Vt(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Jt(e){return e._strategyHWM-e._queueTotalSize}function Xt(e){const t=e._controlledWritableStream;if(!e._started)return;if(void 0!==t._inFlightWriteRequest)return;if("erroring"===t._state)return void It(t);if(0===e._queue.length)return;const r=e._queue.peek().value;r===Gt?function(e){const t=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(t),qe(e);const r=e._closeAlgorithm();Vt(e),v(r,(()=>(function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";const t=e._writer;void 0!==t&&lr(t)}(t),null)),(e=>(function(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),At(e,t)}(t,e),null)))}(e):function(e,t){const r=e._controlledWritableStream;!function(e){e._inFlightWriteRequest=e._writeRequests.shift()}(r),v(e._writeAlgorithm(t),(()=>{!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(r);const t=r._state;if(qe(e),!Bt(r)&&"writable"===t){const t=Kt(e);zt(r,t)}return Xt(e),null}),(t=>("writable"===r._state&&Vt(e),function(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,At(e,t)}(r,t),null)))}(e,r)}function Zt(e,t){"writable"===e._controlledWritableStream._state&&er(e,t)}function Kt(e){return Jt(e)<=0}function er(e,t){const r=e._controlledWritableStream;Vt(e),Wt(r,t)}function tr(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function rr(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function nr(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function or(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function ir(e){e._closedPromise=p(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"}))}function ar(e,t){ir(e),sr(e,t)}function sr(e,t){void 0!==e._closedPromise_reject&&(S(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function lr(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function ur(e){e._readyPromise=p(((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r})),e._readyPromiseState="pending"}function cr(e,t){ur(e),dr(e,t)}function fr(e){ur(e),hr(e)}function dr(e,t){void 0!==e._readyPromise_reject&&(S(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function hr(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(Yt.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Yt.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});const pr="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0,br=function(){const e=null==pr?void 0:pr.DOMException;return function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;if("DOMException"!==e.name)return!1;try{return new e,!0}catch(e){return!1}}(e)?e:void 0}()||function(){const e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return l(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}();function mr(e,t,r,n,o,a){const s=X(e),l=Ot(t);e._disturbed=!0;let u=!1,c=b(void 0);return p(((f,d)=>{let h;if(void 0!==a){if(h=()=>{const r=void 0!==a.reason?a.reason:new br("Aborted","AbortError"),i=[];n||i.push((()=>"writable"===t._state?Ct(t,r):b(void 0))),o||i.push((()=>"readable"===e._state?Lr(e,r):b(void 0))),O((()=>Promise.all(i.map((e=>e())))),!0,r)},a.aborted)return void h();a.addEventListener("abort",h)}var w,R,T;if(q(e,s._closedPromise,(e=>(n?j(!0,e):O((()=>Ct(t,e)),!0,e),null))),q(t,l._closedPromise,(t=>(o?j(!0,t):O((()=>Lr(e,t)),!0,t),null))),w=e,R=s._closedPromise,T=()=>(r?j():O((()=>function(e){const t=e._ownerWritableStream,r=t._state;return Bt(t)||"closed"===r?b(void 0):"errored"===r?m(t._storedError):Lt(e)}(l))),null),"closed"===w._state?T():_(R,T),Bt(t)||"closed"===t._state){const t=new TypeError("the destination writable stream closed before all data could be piped to it");o?j(!0,t):O((()=>Lr(e,t)),!0,t)}function P(){const e=c;return y(c,(()=>e!==c?P():void 0))}function q(e,t,r){"errored"===e._state?r(e._storedError):g(t,r)}function O(e,r,n){function o(){return v(e(),(()=>k(r,n)),(e=>k(!0,e))),null}u||(u=!0,"writable"!==t._state||Bt(t)?o():_(P(),o))}function j(e,r){u||(u=!0,"writable"!==t._state||Bt(t)?k(e,r):_(P(),(()=>k(e,r))))}function k(e,t){return Dt(l),W(s),void 0!==a&&a.removeEventListener("abort",h),e?d(t):f(void 0),null}S(p(((e,t)=>{!function r(n){n?e():y(u?b(!0):y(l._readyPromise,(()=>p(((e,t)=>{oe(s,{_chunkSteps:t=>{c=y(Nt(l,t),void 0,i),e(!1)},_closeSteps:()=>e(!0),_errorSteps:t})})))),r,t)}(!1)})))}))}class yr{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!vr(this))throw jr("desiredSize");return Pr(this)}close(){if(!vr(this))throw jr("close");if(!qr(this))throw new TypeError("The stream is not in a state that permits close");Sr(this)}enqueue(e=void 0){if(!vr(this))throw jr("enqueue");if(!qr(this))throw new TypeError("The stream is not in a state that permits enqueue");return Rr(this,e)}error(e=void 0){if(!vr(this))throw jr("error");Tr(this,e)}[k](e){je(this);const t=this._cancelAlgorithm(e);return wr(this),t}[E](e){const t=this._controlledReadableStream;if(this._queue.length>0){const r=qe(this);this._closeRequested&&0===this._queue.length?(wr(this),$r(t)):_r(this),e._chunkSteps(r)}else Z(t,e),_r(this)}[C](){}}function vr(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof yr}function _r(e){gr(e)&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,v(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,_r(e)),null)),(t=>(Tr(e,t),null)))))}function gr(e){const t=e._controlledReadableStream;return!!qr(e)&&!!e._started&&(!!(Fr(t)&&ee(t)>0)||Pr(e)>0)}function wr(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Sr(e){if(!qr(e))return;const t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(wr(e),$r(t))}function Rr(e,t){if(!qr(e))return;const r=e._controlledReadableStream;if(Fr(r)&&ee(r)>0)K(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw Tr(e,t),t}try{Oe(e,t,r)}catch(t){throw Tr(e,t),t}}_r(e)}function Tr(e,t){const r=e._controlledReadableStream;"readable"===r._state&&(je(e),wr(e),Dr(r,t))}function Pr(e){const t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function qr(e){const t=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===t}function Or(e,t,r,n,o,i,a){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,je(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=a,t._strategyHWM=i,t._pullAlgorithm=n,t._cancelAlgorithm=o,e._readableStreamController=t,v(b(r()),(()=>(t._started=!0,_r(t),null)),(e=>(Tr(t,e),null)))}function jr(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function kr(e){return a(t=e)&&void 0!==t.getReader?function(e){let t;return t=Br(i,(function(){let r;try{r=e.read()}catch(r){return m(r)}return w(r,(e=>{if(!a(e))throw new TypeError("The promise returned by the reader.read() method must fulfill with an object");if(e.done)Sr(t._readableStreamController);else{const r=e.value;Rr(t._readableStreamController,r)}}))}),(function(t){try{return b(e.cancel(t))}catch(t){return m(t)}}),0),t}(e.getReader()):function(e){let t;const r=ve(e,"async");return t=Br(i,(function(){let e;try{e=_e(r)}catch(e){return m(e)}return w(b(e),(e=>{if(!a(e))throw new TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(e.done)Sr(t._readableStreamController);else{const r=e.value;Rr(t._readableStreamController,r)}}))}),(function(e){const t=r.iterator;let n;try{n=be(t,"return")}catch(e){return m(e)}return void 0===n?b(void 0):w(P(n,t,[e]),(e=>{if(!a(e))throw new TypeError("The promise returned by the iterator.return() method must fulfill with an object")}))}),0),t}(e);var t}function Er(e,t,r){return D(e,r),r=>P(e,t,[r])}function Cr(e,t,r){return D(e,r),r=>P(e,t,[r])}function xr(e,t,r){return D(e,r),r=>T(e,t,[r])}function Ar(e,t){if("bytes"!=(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function Wr(e,t){$(e,t);const r=null==e?void 0:e.preventAbort,n=null==e?void 0:e.preventCancel,o=null==e?void 0:e.preventClose,i=null==e?void 0:e.signal;return void 0!==i&&function(e,t){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError(`${t} is not an AbortSignal.`)}(i,`${t} has member 'signal' that`),{preventAbort:Boolean(r),preventCancel:Boolean(n),preventClose:Boolean(o),signal:i}}Object.defineProperties(yr.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),l(yr.prototype.close,"close"),l(yr.prototype.enqueue,"enqueue"),l(yr.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(yr.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class Ir{constructor(e={},t={}){void 0===e?e=null:N(e,"First parameter");const r=vt(t,"Second parameter"),n=function(e,t){$(e,t);const r=e,n=null==r?void 0:r.autoAllocateChunkSize,o=null==r?void 0:r.cancel,i=null==r?void 0:r.pull,a=null==r?void 0:r.start,s=null==r?void 0:r.type;return{autoAllocateChunkSize:void 0===n?void 0:V(n,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===o?void 0:Er(o,r,`${t} has member 'cancel' that`),pull:void 0===i?void 0:Cr(i,r,`${t} has member 'pull' that`),start:void 0===a?void 0:xr(a,r,`${t} has member 'start' that`),type:void 0===s?void 0:Ar(s,`${t} has member 'type' that`)}}(e,"First parameter");if(zr(this),"bytes"===n.type){if(void 0!==r.size)throw new RangeError("The strategy for a byte stream cannot have a size function");!function(e,t,r){const n=Object.create(Ce.prototype);let o,i,a;o=void 0!==t.start?()=>t.start(n):()=>{},i=void 0!==t.pull?()=>t.pull(n):()=>b(void 0),a=void 0!==t.cancel?e=>t.cancel(e):()=>b(void 0);const s=t.autoAllocateChunkSize;if(0===s)throw new TypeError("autoAllocateChunkSize must be greater than 0");nt(e,n,o,i,a,r,s)}(this,n,mt(r,0))}else{const e=yt(r);!function(e,t,r,n){const o=Object.create(yr.prototype);let i,a,s;i=void 0!==t.start?()=>t.start(o):()=>{},a=void 0!==t.pull?()=>t.pull(o):()=>b(void 0),s=void 0!==t.cancel?e=>t.cancel(e):()=>b(void 0),Or(e,o,i,a,s,r,n)}(this,n,mt(r,1),e)}}get locked(){if(!Mr(this))throw Nr("locked");return Fr(this)}cancel(e=void 0){return Mr(this)?Fr(this)?m(new TypeError("Cannot cancel a stream that already has a reader")):Lr(this,e):m(Nr("cancel"))}getReader(e=void 0){if(!Mr(this))throw Nr("getReader");return void 0===function(e,t){$(e,t);const r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:at(r,`${t} has member 'mode' that`)}}(e,"First parameter").mode?X(this):st(this)}pipeThrough(e,t={}){if(!Mr(this))throw Nr("pipeThrough");G(e,1,"pipeThrough");const r=function(e,t){$(e,t);const r=null==e?void 0:e.readable;Y(r,"readable","ReadableWritablePair"),J(r,`${t} has member 'readable' that`);const n=null==e?void 0:e.writable;return Y(n,"writable","ReadableWritablePair"),Tt(n,`${t} has member 'writable' that`),{readable:r,writable:n}}(e,"First parameter"),n=Wr(t,"Second parameter");if(Fr(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(Et(r.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return S(mr(this,r.writable,n.preventClose,n.preventAbort,n.preventCancel,n.signal)),r.readable}pipeTo(e,t={}){if(!Mr(this))return m(Nr("pipeTo"));if(void 0===e)return m("Parameter 1 is required in 'pipeTo'.");if(!kt(e))return m(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let r;try{r=Wr(t,"Second parameter")}catch(e){return m(e)}return Fr(this)?m(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):Et(e)?m(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):mr(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!Mr(this))throw Nr("tee");return ce(function(e){return xe(e._readableStreamController)?function(e){let t,r,n,o,i,a=X(e),s=!1,l=!1,u=!1,c=!1,f=!1;const d=p((e=>{i=e}));function h(e){g(e._closedPromise,(t=>(e!==a||(Xe(n._readableStreamController,t),Xe(o._readableStreamController,t),c&&f||i(void 0)),null)))}function m(){dt(a)&&(W(a),a=X(e),h(a)),oe(a,{_chunkSteps:t=>{R((()=>{l=!1,u=!1;const r=t;let a=t;if(!c&&!f)try{a=Pe(t)}catch(t){return Xe(n._readableStreamController,t),Xe(o._readableStreamController,t),void i(Lr(e,t))}c||Je(n._readableStreamController,r),f||Je(o._readableStreamController,a),s=!1,l?v():u&&_()}))},_closeSteps:()=>{s=!1,c||Ve(n._readableStreamController),f||Ve(o._readableStreamController),n._readableStreamController._pendingPullIntos.length>0&&tt(n._readableStreamController,0),o._readableStreamController._pendingPullIntos.length>0&&tt(o._readableStreamController,0),c&&f||i(void 0)},_errorSteps:()=>{s=!1}})}function y(t,r){ne(a)&&(W(a),a=st(e),h(a));const d=r?o:n,p=r?n:o;ht(a,t,1,{_chunkSteps:t=>{R((()=>{l=!1,u=!1;const n=r?f:c;if(r?c:f)n||rt(d._readableStreamController,t);else{let r;try{r=Pe(t)}catch(t){return Xe(d._readableStreamController,t),Xe(p._readableStreamController,t),void i(Lr(e,t))}n||rt(d._readableStreamController,t),Je(p._readableStreamController,r)}s=!1,l?v():u&&_()}))},_closeSteps:e=>{s=!1;const t=r?f:c,n=r?c:f;t||Ve(d._readableStreamController),n||Ve(p._readableStreamController),void 0!==e&&(t||rt(d._readableStreamController,e),!n&&p._readableStreamController._pendingPullIntos.length>0&&tt(p._readableStreamController,0)),t&&n||i(void 0)},_errorSteps:()=>{s=!1}})}function v(){if(s)return l=!0,b(void 0);s=!0;const e=Ke(n._readableStreamController);return null===e?m():y(e._view,!1),b(void 0)}function _(){if(s)return u=!0,b(void 0);s=!0;const e=Ke(o._readableStreamController);return null===e?m():y(e._view,!0),b(void 0)}function w(){}return n=Ur(w,v,(function(n){if(c=!0,t=n,f){const n=ce([t,r]),o=Lr(e,n);i(o)}return d})),o=Ur(w,_,(function(n){if(f=!0,r=n,c){const n=ce([t,r]),o=Lr(e,n);i(o)}return d})),h(a),[n,o]}(e):function(e){const t=X(e);let r,n,o,i,a,s=!1,l=!1,u=!1,c=!1;const f=p((e=>{a=e}));function d(){return s?(l=!0,b(void 0)):(s=!0,oe(t,{_chunkSteps:e=>{R((()=>{l=!1;const t=e,r=e;u||Rr(o._readableStreamController,t),c||Rr(i._readableStreamController,r),s=!1,l&&d()}))},_closeSteps:()=>{s=!1,u||Sr(o._readableStreamController),c||Sr(i._readableStreamController),u&&c||a(void 0)},_errorSteps:()=>{s=!1}}),b(void 0))}function h(){}return o=Br(h,d,(function(t){if(u=!0,r=t,c){const t=ce([r,n]),o=Lr(e,t);a(o)}return f})),i=Br(h,d,(function(t){if(c=!0,n=t,u){const t=ce([r,n]),o=Lr(e,t);a(o)}return f})),g(t._closedPromise,(e=>(Tr(o._readableStreamController,e),Tr(i._readableStreamController,e),u&&c||a(void 0),null))),[o,i]}(e)}(this))}values(e=void 0){if(!Mr(this))throw Nr("values");return function(e,t){const r=X(e),n=new ge(r,t),o=Object.create(we);return o._asyncIteratorImpl=n,o}(this,function(e){$(e,"First parameter");const t=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(t)}}(e).preventCancel)}[ye](e){return this.values(e)}static from(e){return kr(e)}}function Br(e,t,r,n=1,o=()=>1){const i=Object.create(Ir.prototype);return zr(i),Or(i,Object.create(yr.prototype),e,t,r,n,o),i}function Ur(e,t,r){const n=Object.create(Ir.prototype);return zr(n),nt(n,Object.create(Ce.prototype),e,t,r,0,void 0),n}function zr(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function Mr(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof Ir}function Fr(e){return void 0!==e._reader}function Lr(e,t){if(e._disturbed=!0,"closed"===e._state)return b(void 0);if("errored"===e._state)return m(e._storedError);$r(e);const r=e._reader;if(void 0!==r&&dt(r)){const e=r._readIntoRequests;r._readIntoRequests=new q,e.forEach((e=>{e._closeSteps(void 0)}))}return w(e._readableStreamController[k](t),i)}function $r(e){e._state="closed";const t=e._reader;if(void 0!==t&&(M(t),ne(t))){const e=t._readRequests;t._readRequests=new q,e.forEach((e=>{e._closeSteps()}))}}function Dr(e,t){e._state="errored",e._storedError=t;const r=e._reader;void 0!==r&&(z(r,t),ne(r)?ie(r,t):pt(r,t))}function Nr(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function Gr(e,t){$(e,t);const r=null==e?void 0:e.highWaterMark;return Y(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:H(r)}}Object.defineProperties(Ir,{from:{enumerable:!0}}),Object.defineProperties(Ir.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),l(Ir.from,"from"),l(Ir.prototype.cancel,"cancel"),l(Ir.prototype.getReader,"getReader"),l(Ir.prototype.pipeThrough,"pipeThrough"),l(Ir.prototype.pipeTo,"pipeTo"),l(Ir.prototype.tee,"tee"),l(Ir.prototype.values,"values"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Ir.prototype,Symbol.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(Ir.prototype,ye,{value:Ir.prototype.values,writable:!0,configurable:!0});const Yr=e=>e.byteLength;l(Yr,"size");class Hr{constructor(e){G(e,1,"ByteLengthQueuingStrategy"),e=Gr(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!Vr(this))throw Qr("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!Vr(this))throw Qr("size");return Yr}}function Qr(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function Vr(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof Hr}Object.defineProperties(Hr.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Hr.prototype,Symbol.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});const Jr=()=>1;l(Jr,"size");class Xr{constructor(e){G(e,1,"CountQueuingStrategy"),e=Gr(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!Kr(this))throw Zr("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!Kr(this))throw Zr("size");return Jr}}function Zr(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function Kr(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof Xr}function en(e,t,r){return D(e,r),r=>P(e,t,[r])}function tn(e,t,r){return D(e,r),r=>T(e,t,[r])}function rn(e,t,r){return D(e,r),(r,n)=>P(e,t,[r,n])}function nn(e,t,r){return D(e,r),r=>P(e,t,[r])}Object.defineProperties(Xr.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Xr.prototype,Symbol.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class on{constructor(e={},t={},r={}){void 0===e&&(e=null);const n=vt(t,"Second parameter"),o=vt(r,"Third parameter"),i=function(e,t){$(e,t);const r=null==e?void 0:e.cancel,n=null==e?void 0:e.flush,o=null==e?void 0:e.readableType,i=null==e?void 0:e.start,a=null==e?void 0:e.transform,s=null==e?void 0:e.writableType;return{cancel:void 0===r?void 0:nn(r,e,`${t} has member 'cancel' that`),flush:void 0===n?void 0:en(n,e,`${t} has member 'flush' that`),readableType:o,start:void 0===i?void 0:tn(i,e,`${t} has member 'start' that`),transform:void 0===a?void 0:rn(a,e,`${t} has member 'transform' that`),writableType:s}}(e,"First parameter");if(void 0!==i.readableType)throw new RangeError("Invalid readableType specified");if(void 0!==i.writableType)throw new RangeError("Invalid writableType specified");const a=mt(o,0),s=yt(o),l=mt(n,1),u=yt(n);let c;!function(e,t,r,n,o,i){function a(){return t}e._writable=function(e,t,r,n,o=1,i=()=>1){const a=Object.create(qt.prototype);return jt(a),Qt(a,Object.create(Yt.prototype),e,t,r,n,o,i),a}(a,(function(t){return function(e,t){const r=e._transformStreamController;return e._backpressure?w(e._backpressureChangePromise,(()=>{const n=e._writable;if("erroring"===n._state)throw n._storedError;return bn(r,t)})):bn(r,t)}(e,t)}),(function(){return function(e){const t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;const r=e._readable;t._finishPromise=p(((e,r)=>{t._finishPromise_resolve=e,t._finishPromise_reject=r}));const n=t._flushAlgorithm();return hn(t),v(n,(()=>("errored"===r._state?vn(t,r._storedError):(Sr(r._readableStreamController),yn(t)),null)),(e=>(Tr(r._readableStreamController,e),vn(t,e),null))),t._finishPromise}(e)}),(function(t){return function(e,t){const r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;const n=e._readable;r._finishPromise=p(((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t}));const o=r._cancelAlgorithm(t);return hn(r),v(o,(()=>("errored"===n._state?vn(r,n._storedError):(Tr(n._readableStreamController,t),yn(r)),null)),(e=>(Tr(n._readableStreamController,e),vn(r,e),null))),r._finishPromise}(e,t)}),r,n),e._readable=Br(a,(function(){return function(e){return cn(e,!1),e._backpressureChangePromise}(e)}),(function(t){return function(e,t){const r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;const n=e._writable;r._finishPromise=p(((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t}));const o=r._cancelAlgorithm(t);return hn(r),v(o,(()=>("errored"===n._state?vn(r,n._storedError):(Zt(n._writableStreamController,t),un(e),yn(r)),null)),(t=>(Zt(n._writableStreamController,t),un(e),vn(r,t),null))),r._finishPromise}(e,t)}),o,i),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,cn(e,!0),e._transformStreamController=void 0}(this,p((e=>{c=e})),l,u,a,s),function(e,t){const r=Object.create(fn.prototype);let n,o,i;n=void 0!==t.transform?e=>t.transform(e,r):e=>{try{return pn(r,e),b(void 0)}catch(e){return m(e)}},o=void 0!==t.flush?()=>t.flush(r):()=>b(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>b(void 0),function(e,t,r,n,o){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=r,t._flushAlgorithm=n,t._cancelAlgorithm=o,t._finishPromise=void 0,t._finishPromise_resolve=void 0,t._finishPromise_reject=void 0}(e,r,n,o,i)}(this,i),void 0!==i.start?c(i.start(this._transformStreamController)):c(void 0)}get readable(){if(!an(this))throw _n("readable");return this._readable}get writable(){if(!an(this))throw _n("writable");return this._writable}}function an(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof on}function sn(e,t){Tr(e._readable._readableStreamController,t),ln(e,t)}function ln(e,t){hn(e._transformStreamController),Zt(e._writable._writableStreamController,t),un(e)}function un(e){e._backpressure&&cn(e,!1)}function cn(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=p((t=>{e._backpressureChangePromise_resolve=t})),e._backpressure=t}Object.defineProperties(on.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(on.prototype,Symbol.toStringTag,{value:"TransformStream",configurable:!0});class fn{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!dn(this))throw mn("desiredSize");return Pr(this._controlledTransformStream._readable._readableStreamController)}enqueue(e=void 0){if(!dn(this))throw mn("enqueue");pn(this,e)}error(e=void 0){if(!dn(this))throw mn("error");var t;t=e,sn(this._controlledTransformStream,t)}terminate(){if(!dn(this))throw mn("terminate");!function(e){const t=e._controlledTransformStream;Sr(t._readable._readableStreamController),ln(t,new TypeError("TransformStream terminated"))}(this)}}function dn(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof fn}function hn(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}function pn(e,t){const r=e._controlledTransformStream,n=r._readable._readableStreamController;if(!qr(n))throw new TypeError("Readable side is not in a state that permits enqueue");try{Rr(n,t)}catch(e){throw ln(r,e),r._readable._storedError}const o=function(e){return!gr(e)}(n);o!==r._backpressure&&cn(r,!0)}function bn(e,t){return w(e._transformAlgorithm(t),void 0,(t=>{throw sn(e._controlledTransformStream,t),t}))}function mn(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function yn(e){void 0!==e._finishPromise_resolve&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function vn(e,t){void 0!==e._finishPromise_reject&&(S(e._finishPromise),e._finishPromise_reject(t),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function _n(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}Object.defineProperties(fn.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),l(fn.prototype.enqueue,"enqueue"),l(fn.prototype.error,"error"),l(fn.prototype.terminate,"terminate"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(fn.prototype,Symbol.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});var gn=function(e,t){return gn=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},gn(e,t)};function wn(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}gn(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}function Sn(e){if(!e)throw new TypeError("Assertion failed")}function Rn(){}function Tn(e){return!!function(e){return"object"==typeof e&&null!==e||"function"==typeof e}(e)&&"function"==typeof e.getReader}function Pn(e){try{return e.getReader({mode:"byob"}).releaseLock(),!0}catch(e){return!1}}function qn(e,t){var r=(void 0===t?{}:t).type;return Sn(Tn(e)),Sn(!1===e.locked),"bytes"===(r=On(r))?new Cn(e):new kn(e)}function On(e){var t=String(e);if("bytes"===t)return t;if(void 0===e)return e;throw new RangeError("Invalid type is specified")}var jn=function(){function e(e){this._underlyingReader=void 0,this._readerMode=void 0,this._readableStreamController=void 0,this._pendingRead=void 0,this._underlyingStream=e,this._attachDefaultReader()}return e.prototype.start=function(e){this._readableStreamController=e},e.prototype.cancel=function(e){return Sn(void 0!==this._underlyingReader),this._underlyingReader.cancel(e)},e.prototype._attachDefaultReader=function(){if("default"!==this._readerMode){this._detachReader();var e=this._underlyingStream.getReader();this._readerMode="default",this._attachReader(e)}},e.prototype._attachReader=function(e){var t=this;Sn(void 0===this._underlyingReader),this._underlyingReader=e;var r=this._underlyingReader.closed;r&&r.then((function(){return t._finishPendingRead()})).then((function(){e===t._underlyingReader&&t._readableStreamController.close()}),(function(r){e===t._underlyingReader&&t._readableStreamController.error(r)})).catch(Rn)},e.prototype._detachReader=function(){void 0!==this._underlyingReader&&(this._underlyingReader.releaseLock(),this._underlyingReader=void 0,this._readerMode=void 0)},e.prototype._pullWithDefaultReader=function(){var e=this;this._attachDefaultReader();var t=this._underlyingReader.read().then((function(t){var r=e._readableStreamController;t.done?e._tryClose():r.enqueue(t.value)}));return this._setPendingRead(t),t},e.prototype._tryClose=function(){try{this._readableStreamController.close()}catch(e){}},e.prototype._setPendingRead=function(e){var t,r=this,n=function(){r._pendingRead===t&&(r._pendingRead=void 0)};this._pendingRead=t=e.then(n,n)},e.prototype._finishPendingRead=function(){var e=this;if(this._pendingRead){var t=function(){return e._finishPendingRead()};return this._pendingRead.then(t,t)}},e}(),kn=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return wn(t,e),t.prototype.pull=function(){return this._pullWithDefaultReader()},t}(jn);function En(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}var Cn=function(e){function t(t){var r=this,n=Pn(t);return(r=e.call(this,t)||this)._supportsByob=n,r}return wn(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return"bytes"},enumerable:!1,configurable:!0}),t.prototype._attachByobReader=function(){if("byob"!==this._readerMode){Sn(this._supportsByob),this._detachReader();var e=this._underlyingStream.getReader({mode:"byob"});this._readerMode="byob",this._attachReader(e)}},t.prototype.pull=function(){if(this._supportsByob){var e=this._readableStreamController.byobRequest;if(e)return this._pullWithByobRequest(e)}return this._pullWithDefaultReader()},t.prototype._pullWithByobRequest=function(e){var t=this;this._attachByobReader();var r=new Uint8Array(e.view.byteLength),n=this._underlyingReader.read(r).then((function(r){t._readableStreamController,r.done?(t._tryClose(),e.respond(0)):(function(e,t){var r=En(e);En(t).set(r,0)}(r.value,e.view),e.respond(r.value.byteLength))}));return this._setPendingRead(n),n},t}(jn);!function(){function e(e){var t=this;this._writableStreamController=void 0,this._pendingWrite=void 0,this._state="writable",this._storedError=void 0,this._underlyingWriter=e,this._errorPromise=new Promise((function(e,r){t._errorPromiseReject=r})),this._errorPromise.catch(Rn)}e.prototype.start=function(e){var t=this;this._writableStreamController=e,this._underlyingWriter.closed.then((function(){t._state="closed"})).catch((function(e){return t._finishErroring(e)}))},e.prototype.write=function(e){var t=this,r=this._underlyingWriter;if(null===r.desiredSize)return r.ready;var n=r.write(e);n.catch((function(e){return t._finishErroring(e)})),r.ready.catch((function(e){return t._startErroring(e)}));var o=Promise.race([n,this._errorPromise]);return this._setPendingWrite(o),o},e.prototype.close=function(){var e=this;return void 0===this._pendingWrite?this._underlyingWriter.close():this._finishPendingWrite().then((function(){return e.close()}))},e.prototype.abort=function(e){if("errored"!==this._state)return this._underlyingWriter.abort(e)},e.prototype._setPendingWrite=function(e){var t,r=this,n=function(){r._pendingWrite===t&&(r._pendingWrite=void 0)};this._pendingWrite=t=e.then(n,n)},e.prototype._finishPendingWrite=function(){var e=this;if(void 0===this._pendingWrite)return Promise.resolve();var t=function(){return e._finishPendingWrite()};return this._pendingWrite.then(t,t)},e.prototype._startErroring=function(e){var t=this;if("writable"===this._state){this._state="erroring",this._storedError=e;var r=function(){return t._finishErroring(e)};void 0===this._pendingWrite?r():this._finishPendingWrite().then(r,r),this._writableStreamController.error(e)}},e.prototype._finishErroring=function(e){"writable"===this._state&&this._startErroring(e),"erroring"===this._state&&(this._state="errored",this._errorPromiseReject(this._storedError))}}(),function(){function e(e,t){var r=this;this._transformStreamController=void 0,this._onRead=function(e){if(!e.done)return r._transformStreamController.enqueue(e.value),r._reader.read().then(r._onRead)},this._onError=function(e){r._flushReject(e),r._transformStreamController.error(e),r._reader.cancel(e).catch(Rn),r._writer.abort(e).catch(Rn)},this._onTerminate=function(){r._flushResolve(),r._transformStreamController.terminate();var e=new TypeError("TransformStream terminated");r._writer.abort(e).catch(Rn)},this._reader=e,this._writer=t,this._flushPromise=new Promise((function(e,t){r._flushResolve=e,r._flushReject=t}))}e.prototype.start=function(e){this._transformStreamController=e,this._reader.read().then(this._onRead).then(this._onTerminate,this._onError);var t=this._reader.closed;t&&t.then(this._onTerminate,this._onError)},e.prototype.transform=function(e){return this._writer.write(e)},e.prototype.flush=function(){var e=this;return this._writer.close().then((function(){return e._flushPromise}))}}();var xn=[239,187,191],An=r(877),Wn=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function s(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))},In=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}},Bn=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e="function"==typeof __values?__values(e):e[Symbol.iterator](),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise((function(n,o){!function(e,t,r,n){Promise.resolve(n).then((function(t){e({value:t,done:r})}),t)}(n,o,(t=e[r](t)).done,t.value)}))}}},Un=Ir,zn=on,Mn=function(){var e;return new zn({start:function(t){e=function(e){var t,r,n,o,i,a,s;return l(),{feed:function(e){r=r?r+e:e,t&&function(e){return xn.every((function(t,r){return e.charCodeAt(r)===t}))}(r)&&(r=r.slice(xn.length)),t=!1;for(var i=r.length,a=0,s=!1;a<i;){s&&("\n"===r[a]&&(a+=1),s=!1);for(var l=-1,c=o,f=void 0,d=n;l<0&&d<i;d++)":"===(f=r[d])&&c<0?c=d-a:"\r"===f?(s=!0,l=d-a):"\n"===f&&(l=d-a);if(l<0){n=i-a,o=c;break}n=0,o=-1,u(r,a,c,l),a+=l+1}a===i?r="":a>0&&(r=r.slice(a))},reset:l};function l(){t=!0,r="",n=0,o=-1,i=void 0,a=void 0,s=""}function u(t,r,n,o){if(0===o)return s.length>0&&(e({type:"event",id:i,event:a||void 0,data:s.slice(0,-1)}),s="",i=void 0),void(a=void 0);var l,u=n<0,c=t.slice(r,r+(u?o:n)),f=r+(l=u?o:" "===t[r+n+1]?n+2:n+1),d=o-l,h=t.slice(f,f+d).toString();if("data"===c)s+=h?"".concat(h,"\n"):"\n";else if("event"===c)a=h;else if("id"!==c||h.includes("\0")){if("retry"===c){var p=parseInt(h,10);Number.isNaN(p)||e({type:"reconnect-interval",value:p})}}else i=h}}((function(e){"event"===e.type&&t.enqueue(e)}))},transform:function(t){e.feed(t)}})},Fn=function(e){Sn(function(e){return!!function(e){if("function"!=typeof e)return!1;var t=!1;try{new e({start:function(){t=!0}})}catch(e){}return t}(e)&&!!Tn(new e)}(e));var t=function(e){try{return new e({type:"bytes"}),!0}catch(e){return!1}}(e);return function(r,n){var o=(void 0===n?{}:n).type;if("bytes"!==(o=On(o))||t||(o=void 0),r.constructor===e&&("bytes"!==o||Pn(r)))return r;if("bytes"===o){var i=qn(r,{type:o});return new e(i)}return i=qn(r),new e(i)}}(Un),Ln=function(){function e(e,t){void 0===e&&(e="utf-8"),void 0===t&&(t={});var r=this;this.transform=new zn({transform:function(e,t){var n=r.handle.decode(new Uint8Array(e),{stream:!0});n&&t.enqueue(n)},flush:function(e){var t=r.handle.decode();t&&e.enqueue(t),e.terminate()}}),this.handle=new An.TextDecoder(e,t)}return Object.defineProperty(e.prototype,"encoding",{get:function(){return this.handle.encoding},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fatal",{get:function(){return this.handle.fatal},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ignoreBOM",{get:function(){return this.handle.ignoreBOM},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"readable",{get:function(){return this.transform.readable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"writable",{get:function(){return this.transform.writable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,Symbol.toStringTag,{get:function(){return"TextDecoderStream"},enumerable:!1,configurable:!0}),e}();function $n(e){var t=e;return t[Symbol.asyncIterator]=function(){var t=e.getReader();return{next:function(){return Wn(this,void 0,void 0,(function(){var e,r,n;return In(this,(function(o){switch(o.label){case 0:return[4,t.read()];case 1:return e=o.sent(),r=e.done,n=e.value,[2,r?{done:!0,value:void 0}:{done:!1,value:n}]}}))}))}}},t}function Dn(e){return $n(e.pipeThrough(new Ln).pipeThrough(Mn()).pipeThrough(new zn({transform:function(e,t){try{var r=JSON.parse(e.data);t.enqueue(r)}catch(r){"[DONE]"!==e.data?console.warn("Error when transforming event source data to json",r,e):t.terminate()}}})))}function Nn(e){return $n(Fn(e).pipeThrough(new Ln))}function Gn(e){var t,r,n,o;return Wn(this,void 0,void 0,(function(){var i,a,s,l,u,c;return In(this,(function(f){switch(f.label){case 0:i=Nn(e),a="",f.label=1;case 1:f.trys.push([1,6,7,12]),s=!0,l=Bn(i),f.label=2;case 2:return[4,l.next()];case 3:if(u=f.sent(),t=u.done)return[3,5];o=u.value,s=!1;try{a+=o}finally{s=!0}f.label=4;case 4:return[3,2];case 5:return[3,12];case 6:return c=f.sent(),r={error:c},[3,12];case 7:return f.trys.push([7,,10,11]),s||t||!(n=l.return)?[3,9]:[4,n.call(l)];case 8:f.sent(),f.label=9;case 9:return[3,11];case 10:if(r)throw r.error;return[7];case 11:return[7];case 12:return[2,JSON.parse(a)]}}))}))}function Yn(){var e,t;return{promise:new Promise((function(r,n){e=r,t=n})),res:e,rej:t}}function Hn(e){var t;return"assistant"===e.role&&"tool_calls"in e&&null!=(null===(t=e.tool_calls)||void 0===t?void 0:t[0])}function Qn(e){return{type:"function",function:{description:e.description,name:e.name,parameters:e.parameters}}}var Vn=function(){return Vn=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},Vn.apply(this,arguments)},Jn=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function s(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))},Xn=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}},Zn=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r},Kn=function(){function e(e,t){this.baseUrl=t;var r=arguments[2];this.req="string"==typeof r?function(t){var n=t.headers,o=void 0===n?{}:n,i=Zn(t,["headers"]);return e(Vn(Vn({},i),{headers:Vn(Vn({},o),{Authorization:"Bearer ".concat(r)})}))}:e}return e.prototype.list=function(e,t){return this.req({method:"get",url:this.join("bots"),data:e,timeout:null==t?void 0:t.timeout})},e.prototype.create=function(e,t){var r=e.botInfo;return this.req({method:"post",url:this.join("bots"),data:r,timeout:null==t?void 0:t.timeout})},e.prototype.get=function(e,t){var r=e.botId;return this.req({method:"get",url:this.join("bots/".concat(r)),timeout:null==t?void 0:t.timeout})},e.prototype.update=function(e,t){var r=e.botId,n=e.botInfo;return this.req({method:"PATCH",url:this.join("bots/".concat(r)),data:n,timeout:null==t?void 0:t.timeout})},e.prototype.delete=function(e,t){var r=e.botId;return this.req({method:"delete",url:this.join("bots/".concat(r)),timeout:null==t?void 0:t.timeout})},e.prototype.getChatRecords=function(e,t){return this.req({method:"get",url:this.join("bots/".concat(e.botId,"/records")),data:e,timeout:null==t?void 0:t.timeout})},e.prototype.sendFeedback=function(e,t){var r=e.userFeedback;return this.req({method:"post",url:this.join("bots/".concat(r.botId,"/feedback")),data:r,timeout:null==t?void 0:t.timeout})},e.prototype.getFeedback=function(e,t){return this.req({method:"get",url:this.join("bots/".concat(e.botId,"/feedback")),data:e,timeout:null==t?void 0:t.timeout})},e.prototype.uploadFiles=function(e,t){return Jn(this,void 0,void 0,(function(){return Xn(this,(function(r){return[2,this.req({method:"post",url:this.join("bots/".concat(e.botId,"/files")),data:e,timeout:null==t?void 0:t.timeout})]}))}))},e.prototype.createConversation=function(e,t){return Jn(this,void 0,void 0,(function(){return Xn(this,(function(r){return[2,this.req({method:"post",url:this.join("conversation"),data:e,timeout:null==t?void 0:t.timeout})]}))}))},e.prototype.getConversation=function(e,t){var r=e.pageSize,n=void 0===r?10:r,o=e.pageNumber,i=void 0===o?1:o,a=Zn(e,["pageSize","pageNumber"]);return Jn(this,void 0,void 0,(function(){var e,r;return Xn(this,(function(o){if(i<1)throw new Error("pageNumber must be greater than 0");return e=n*(i-1),r=n,[2,this.req({method:"get",url:this.join("conversation"),data:Vn(Vn({},a),{offset:e,limit:r}),timeout:null==t?void 0:t.timeout})]}))}))},e.prototype.deleteConversation=function(e,t){return Jn(this,void 0,void 0,(function(){return Xn(this,(function(r){return[2,this.req({method:"delete",url:this.join("conversation/".concat(e.conversationId)),data:e,timeout:null==t?void 0:t.timeout})]}))}))},e.prototype.speechToText=function(e,t){return Jn(this,void 0,void 0,(function(){return Xn(this,(function(r){return[2,this.req({method:"post",url:this.join("bots/".concat(e.botId,"/speech-to-text")),data:e,timeout:null==t?void 0:t.timeout})]}))}))},e.prototype.textToSpeech=function(e,t){return Jn(this,void 0,void 0,(function(){return Xn(this,(function(r){return[2,this.req({method:"post",url:this.join("bots/".concat(e.botId,"/text-to-speech")),data:e,timeout:null==t?void 0:t.timeout})]}))}))},e.prototype.getTextToSpeechResult=function(e,t){return Jn(this,void 0,void 0,(function(){return Xn(this,(function(r){return[2,this.req({method:"get",url:this.join("bots/".concat(e.botId,"/text-to-speech")),data:e,timeout:null==t?void 0:t.timeout})]}))}))},e.prototype.getRecommendQuestions=function(e,t){return Jn(this,void 0,void 0,(function(){var r;return Xn(this,(function(n){switch(n.label){case 0:return[4,this.req({method:"post",url:this.join("bots/".concat(e.botId,"/recommend-questions")),data:e,stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,new eo(r)]}}))}))},e.prototype.generateBot=function(e,t){return Jn(this,void 0,void 0,(function(){var r;return Xn(this,(function(n){switch(n.label){case 0:return[4,this.req({method:"post",url:this.join("generate-bot"),data:e,stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,new eo(r)]}}))}))},e.prototype.getPreview=function(e,t){return Jn(this,void 0,void 0,(function(){var r;return Xn(this,(function(n){switch(n.label){case 0:return[4,this.req({method:"post",url:this.join("preview"),data:e,stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,new eo(r)]}}))}))},e.prototype.generateImage=function(e,t){return this.req({method:"post",url:this.join("generate-image"),data:e,timeout:null==t?void 0:t.timeout})},e.prototype.sendMessage=function(e,t){return Jn(this,void 0,void 0,(function(){var r;return Xn(this,(function(n){switch(n.label){case 0:return[4,this.req({method:"post",url:this.join("bots/".concat(e.botId,"/send-message")),data:e,stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,new eo(r)]}}))}))},e.prototype.join=function(e){return"".concat(this.baseUrl,"/").concat(e)},e}(),eo=function(){function e(e){var t=Fn(e);this._eventSourceStream=t.pipeThrough(new Ln).pipeThrough(Mn())}return Object.defineProperty(e.prototype,"teeedStream",{get:function(){var e=this._eventSourceStream.tee(),t=e[0],r=e[1];return this._eventSourceStream=r,t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"eventSourceStream",{get:function(){return $n(this.teeedStream)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dataStream",{get:function(){return $n(this.eventSourceStream.pipeThrough(new zn({transform:function(e,t){try{var r=JSON.parse(e.data);t.enqueue(r)}catch(r){"[DONE]"!==e.data?console.warn("Error when transforming event source data to json",r,e):t.terminate()}}})))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"textStream",{get:function(){return $n(this.dataStream.pipeThrough(new zn({transform:function(e,t){var r;t.enqueue(null!==(r=null==e?void 0:e.content)&&void 0!==r?r:"")}})))},enumerable:!1,configurable:!0}),e}(),to=function(){return to=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},to.apply(this,arguments)},ro=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function s(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))},no=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}};function oo(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tool_choice,i=e.tools,a=e.top_p;return to(to({},e),{messages:t,model:r,temperature:n,tool_choice:(o&&"auto"!==o&&console.warn("`tool_choice` is not 'auto'"),o),tools:i,top_p:a})}var io=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="zhipu/api/paas/v4/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return ro(this,void 0,void 0,(function(){var r,n;return no(this,(function(o){switch(o.label){case 0:return r=oo(e),[4,this.req({url:this.url,data:to(to({},r),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return n=o.sent(),[2,to(to({},n),{rawResponse:n})]}}))}))},e.prototype.doStream=function(e,t){return ro(this,void 0,void 0,(function(){var r,n,o,i;return no(this,(function(a){switch(a.label){case 0:return r=oo(e),n=null,[4,this.req({url:this.url,data:to(to({},r),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return o=a.sent(),i=Fn(o),[2,$n(Dn(i).pipeThrough(new zn({transform:function(e,t){var r=e.choices.map((function(e){var t=e.delta;return null==n&&(n=Hn(t)),n?to(to({},e),{finish_reason:"tool_calls",delta:t}):e})),o=to(to({},e),{choices:r});t.enqueue(to(to({},o),{rawResponse:e}))}})))]}}))}))},e}(),ao=function(){return ao=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},ao.apply(this,arguments)};function so(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tool_choice,i=e.tools,a=e.top_p;return ao(ao({},e),{messages:(t.forEach((function(e){"tool_calls"in e&&e.tool_calls.filter((function(e){return"function"!==e.type})).forEach((function(t){return console.warn("`type` in tool_call is not 'function'",t,e)}))})),t),model:r,tools:function(){if(i)return i.forEach((function(e){"function"!==e.type&&console.warn("`type` in tool is not 'function'",e)})),i}(),top_p:a,tool_choice:o,temperature:n})}function lo(e){return"object"!=typeof e||null==e?e:Array.isArray(e)?e.map((function(e){return lo(e)})):Object.entries(e).reduce((function(e,t){var r=t[0],n=t[1],o=function(e){var t=e.replace(/[A-Z]/g,(function(e){return"_".concat(e.toLowerCase())}));return"_"===t.charAt(0)?t.slice(1):t}(r);return e[o]="object"==typeof n?lo(n):n,e}),{})}var uo=function(){return uo=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},uo.apply(this,arguments)},co=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function s(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))},fo=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}},ho=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="hunyuan-beta/openapi/v1/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return co(this,void 0,void 0,(function(){var r;return fo(this,(function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:uo(uo({},so(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,uo(uo({},r),{rawResponse:r})]}}))}))},e.prototype.doStream=function(e,t){return co(this,void 0,void 0,(function(){var r,n,o;return fo(this,(function(i){switch(i.label){case 0:return r=null,[4,this.req({url:this.url,data:uo(uo({},so(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return n=i.sent(),o=Fn(n),[2,$n(Dn(o).pipeThrough(new zn({transform:function(e,t){var n=e.choices.map((function(e){var t=e.delta;return null==r&&(r=Hn(t)),r?uo(uo({},e),{finish_reason:"tool_calls",delta:t}):e})),o=uo(uo({},e),{choices:n});t.enqueue(uo(uo({},o),{rawResponse:e}))}})))]}}))}))},e}(),po=function(){return po=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},po.apply(this,arguments)},bo=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function s(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))},mo=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}},yo=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="hunyuan",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return bo(this,void 0,void 0,(function(){var r,n;return mo(this,(function(o){switch(o.label){case 0:return[4,this.req({url:this.url,headers:{"X-Tc-Action":"ChatCompletions"},data:po(po({},so(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=o.sent(),n=lo(r.Response),[2,po(po({},n),{rawResponse:r})]}}))}))},e.prototype.doStream=function(e,t){return bo(this,void 0,void 0,(function(){var r,n,o;return mo(this,(function(i){switch(i.label){case 0:return r=null,[4,this.req({url:this.url,headers:{"X-Tc-Action":"ChatCompletions"},data:po(po({},e),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return n=i.sent(),o=Fn(n),[2,$n(Dn(o).pipeThrough(new zn({transform:function(e,t){var n=lo(e),o=n.choices.map((function(e){var t=e.delta;return null==r&&(r=Hn(t)),r?po(po({},e),{finish_reason:"tool_calls",delta:t}):e})),i=po(po({},n),{choices:o});t.enqueue(po(po({},i),{rawResponse:e}))}})))]}}))}))},e}(),vo=function(){return vo=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},vo.apply(this,arguments)},_o=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function s(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))},go=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}};function wo(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tools,i=e.top_p;return vo(vo({},e),{messages:t,model:r,tools:o,top_p:i,temperature:n})}var So=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="ark/api/v3/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return _o(this,void 0,void 0,(function(){var r;return go(this,(function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:vo(vo({},wo(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,vo(vo({},r),{rawResponse:r})]}}))}))},e.prototype.doStream=function(e,t){return _o(this,void 0,void 0,(function(){var r,n,o;return go(this,(function(i){switch(i.label){case 0:return r=null,[4,this.req({url:this.url,data:vo(vo({},wo(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return n=i.sent(),o=Fn(n),[2,$n(Dn(o).pipeThrough(new zn({transform:function(e,t){var n=e.choices.map((function(e){var t=e.delta;return null==r&&(r=Hn(t)),r?vo(vo({},e),{finish_reason:"tool_calls",delta:t}):e})),o=vo(vo({},e),{choices:n});t.enqueue(vo(vo({},o),{rawResponse:e}))}})))]}}))}))},e}(),Ro=function(){return Ro=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},Ro.apply(this,arguments)},To=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function s(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))},Po=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}};function qo(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tools,i=e.top_p;return Ro(Ro({},e),{messages:t,model:r,tools:o,top_p:i,temperature:n})}var Oo=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="dashscope/compatible-mode/v1/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return To(this,void 0,void 0,(function(){var r;return Po(this,(function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:Ro(Ro({},qo(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,Ro(Ro({},r),{rawResponse:r})]}}))}))},e.prototype.doStream=function(e,t){return To(this,void 0,void 0,(function(){var r,n,o;return Po(this,(function(i){switch(i.label){case 0:return r=null,[4,this.req({url:this.url,data:Ro(Ro({},qo(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return n=i.sent(),o=Fn(n),[2,$n(Dn(o).pipeThrough(new zn({transform:function(e,t){var n=e.choices.map((function(e){var t=Object.assign(e.delta,{role:"assistant"});return null==r&&(r=Hn(t)),Ro(Ro({},e),r?{finish_reason:"tool_calls",delta:t}:{delta:t})})),o=Ro(Ro({},e),{choices:n});t.enqueue(Ro(Ro({},o),{rawResponse:e}))}})))]}}))}))},e}(),jo=function(){return jo=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},jo.apply(this,arguments)},ko=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function s(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))},Eo=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}};function Co(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tools,i=e.top_p;return jo(jo({},e),{messages:t,model:r,tools:o,top_p:i,temperature:n})}var xo=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="01-ai/v1/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return ko(this,void 0,void 0,(function(){var r;return Eo(this,(function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:jo(jo({},Co(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,jo(jo({},r),{rawResponse:r})]}}))}))},e.prototype.doStream=function(e,t){return ko(this,void 0,void 0,(function(){var r,n,o,i,a;return Eo(this,(function(s){switch(s.label){case 0:return r=null,[4,this.req({url:this.url,data:jo(jo({},Co(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return n=s.sent(),o=Fn(n),i=Dn(o),a=i.pipeThrough(new zn({transform:function(e,t){var n,o,i,a,s,l;if((null===(i=null===(o=null===(n=null==e?void 0:e.choices)||void 0===n?void 0:n[0])||void 0===o?void 0:o.delta)||void 0===i?void 0:i.content)||(null===(l=null===(s=null===(a=null==e?void 0:e.choices)||void 0===a?void 0:a[0])||void 0===s?void 0:s.delta)||void 0===l?void 0:l.tool_calls)){var u=e.choices.map((function(e){var t=Object.assign(e.delta,{role:"assistant"});return null==r&&(r=Hn(t)),jo(jo({},e),r?{finish_reason:"tool_calls",delta:t}:{delta:t})})),c=jo(jo({},e),{choices:u});t.enqueue(jo(jo({},c),{rawResponse:e}))}}})),[2,$n(a)]}}))}))},e}(),Ao=function(){return Ao=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},Ao.apply(this,arguments)},Wo=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function s(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))},Io=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}};function Bo(e){var t=e.messages,r=e.model,n=e.temperature,o=e.tools,i=e.top_p;return Ao(Ao({},e),{messages:t,model:r,tools:o,top_p:i,temperature:n})}var Uo=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="moonshot/v1/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return Wo(this,void 0,void 0,(function(){var r;return Io(this,(function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:Ao(Ao({},Bo(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,Ao(Ao({},r),{rawResponse:r})]}}))}))},e.prototype.doStream=function(e,t){return Wo(this,void 0,void 0,(function(){var r,n,o;return Io(this,(function(i){switch(i.label){case 0:return r=null,[4,this.req({url:this.url,data:Ao(Ao({},Bo(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return n=i.sent(),o=Fn(n),[2,$n(Dn(o).pipeThrough(new zn({transform:function(e,t){var n=e.choices.map((function(e){var t=e.delta;return null==r&&(r=Hn(t)),r?Ao(Ao({},e),{finish_reason:"tool_calls",delta:t}):e})),o=Ao(Ao({},e),{choices:n});t.enqueue(Ao(Ao({},o),{rawResponse:e}))}})))]}}))}))},e}(),zo=function(){return zo=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},zo.apply(this,arguments)},Mo=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function s(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))},Fo=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}},Lo=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="hunyuan-exp/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e){return Mo(this,void 0,void 0,(function(){var t;return Fo(this,(function(r){switch(r.label){case 0:return[4,this.req({url:this.url,data:zo(zo({},so(e)),{stream:!1}),stream:!1})];case 1:return t=r.sent(),[2,zo(zo({},t),{rawResponse:t})]}}))}))},e.prototype.doStream=function(e){return Mo(this,void 0,void 0,(function(){var t,r,n;return Fo(this,(function(o){switch(o.label){case 0:return t=null,[4,this.req({url:this.url,data:zo(zo({},so(e)),{stream:!0}),stream:!0})];case 1:return r=o.sent(),n=Fn(r),[2,$n(Dn(n).pipeThrough(new zn({transform:function(e,r){var n=e.choices.map((function(e){var r=e.delta;return null==t&&(t=Hn(r)),t?zo(zo({},e),{finish_reason:"tool_calls",delta:r}):e})),o=zo(zo({},e),{choices:n});r.enqueue(zo(zo({},o),{rawResponse:e}))}})))]}}))}))},e}(),$o=function(){return $o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},$o.apply(this,arguments)},Do=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function s(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))},No=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}},Go=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="hunyuan-open/v1/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return Do(this,void 0,void 0,(function(){var r;return No(this,(function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:$o($o({},so(e)),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,$o($o({},r),{rawResponse:r})]}}))}))},e.prototype.doStream=function(e,t){return Do(this,void 0,void 0,(function(){var r,n,o;return No(this,(function(i){switch(i.label){case 0:return r=null,[4,this.req({url:this.url,data:$o($o({},so(e)),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return n=i.sent(),o=Fn(n),[2,$n(Dn(o).pipeThrough(new zn({transform:function(e,t){var n=e.choices.map((function(e){var t=e.delta;return null==r&&(r=Hn(t)),r?$o($o({},e),{finish_reason:"tool_calls",delta:t}):e})),o=$o($o({},e),{choices:n});t.enqueue($o($o({},o),{rawResponse:e}))}})))]}}))}))},e}(),Yo=function(){return Yo=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},Yo.apply(this,arguments)},Ho=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function s(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))},Qo=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}},Vo=function(){function e(e,t,r){this.req=e,this.baseUrl=t,this.subUrl="deepseek/chat/completions",null!=r&&(this.subUrl=r)}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e,t){return Ho(this,void 0,void 0,(function(){var r;return Qo(this,(function(n){switch(n.label){case 0:return[4,this.req({url:this.url,data:Yo(Yo({},e),{stream:!1}),stream:!1,timeout:null==t?void 0:t.timeout})];case 1:return r=n.sent(),[2,Yo(Yo({},r),{rawResponse:r})]}}))}))},e.prototype.doStream=function(e,t){return Ho(this,void 0,void 0,(function(){var r,n,o;return Qo(this,(function(i){switch(i.label){case 0:return r=null,[4,this.req({url:this.url,data:Yo(Yo({},e),{stream:!0}),stream:!0,timeout:null==t?void 0:t.timeout})];case 1:return n=i.sent(),o=Fn(n),[2,$n(Dn(o).pipeThrough(new zn({transform:function(e,t){var n=e.choices.map((function(e){var t=e.delta;return null==r&&(r=Hn(t)),r?Yo(Yo({},e),{finish_reason:"tool_calls",delta:t}):e})),o=Yo(Yo({},e),{choices:n});t.enqueue(Yo(Yo({},o),{rawResponse:e}))}})))]}}))}))},e}(),Jo=function(){return Jo=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},Jo.apply(this,arguments)},Xo=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function s(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))},Zo=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}},Ko=function(){function e(e,t,r){void 0===r&&(r=""),this.req=e,this.baseUrl=t,this.subUrl=r}return Object.defineProperty(e.prototype,"url",{get:function(){return"".concat(this.baseUrl,"/").concat(this.subUrl)},enumerable:!1,configurable:!0}),e.prototype.doGenerate=function(e){return Xo(this,void 0,void 0,(function(){var t;return Zo(this,(function(r){switch(r.label){case 0:return[4,this.req({url:this.url,data:Jo(Jo({},e),{stream:!1}),stream:!1})];case 1:return t=r.sent(),[2,Jo(Jo({},t),{rawResponse:t})]}}))}))},e.prototype.doStream=function(e){return Xo(this,void 0,void 0,(function(){var t,r,n;return Zo(this,(function(o){switch(o.label){case 0:return t=null,[4,this.req({url:this.url,data:Jo(Jo({},e),{stream:!0}),stream:!0})];case 1:return r=o.sent(),n=Fn(r),[2,$n(Dn(n).pipeThrough(new zn({transform:function(e,r){var n=e.choices.map((function(e){var r=e.delta;return null==t&&(t=Hn(r)),t?Jo(Jo({},e),{finish_reason:"tool_calls",delta:r}):e})),o=Jo(Jo({},e),{choices:n});r.enqueue(Jo(Jo({},o),{rawResponse:e}))}})))]}}))}))},e}(),ei=function(){return ei=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},ei.apply(this,arguments)},ti=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function s(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))},ri=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}},ni=function(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};function oi(e){var t,r=e.onStepFinish,n=e.abortSignal,o=e.maxSteps,i=e.topP,a=e.toolChoice,s=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r}(e,["onStepFinish","abortSignal","maxSteps","topP","toolChoice"]);if(null!=o&&o<1)throw new Error("`maxSteps` muse be greater than 0.");return[{onStepFinish:r,abortSignal:n,maxSteps:o},ei(ei({},s),{tools:null===(t=s.tools)||void 0===t?void 0:t.map((function(e){return"fn"in e?Qn(e):e})),top_p:null!=i?i:s.top_p,tool_choice:null!=a?a:s.tool_choice})]}var ii=function(){function e(e){this.model=e}return e.prototype.generateText=function(e,t){var r,n;return ti(this,void 0,void 0,(function(){var o,i,a,s,l,u,c,f,d,h,p,b,m,y,v,_,g,w,S,R,T,P=this;return ri(this,(function(q){switch(q.label){case 0:return o=[],i={completion_tokens:0,prompt_tokens:0,total_tokens:0},a=oi(e),s=a[0],l=s.onStepFinish,u=s.maxSteps,c=void 0===u?10:u,f=a[1],[4,(d=function(){return P.model.doGenerate(f,t)})()];case 1:h=q.sent(),p=1,h.rawResponse&&o.push(h.rawResponse),b=null,q.label=2;case 2:if(!(p<c&&null!=(b=ai(h))))return[3,9];m=ci(h.usage),fi(i,m),q.label=3;case 3:return q.trys.push([3,7,,8]),[4,ui(b)];case 4:return y=q.sent(),v=h.choices[0],[4,null==l?void 0:l({finishReason:v.finish_reason,messages:f.messages.slice(),text:v.message.content,toolCall:b,toolResult:y,stepUsage:m,totalUsage:Object.assign({},i)})];case 5:return q.sent(),si(f.messages,v.message,y),[4,d()];case 6:return(h=q.sent()).rawResponse&&o.push(h.rawResponse),p+=1,[3,8];case 7:return _=q.sent(),[2,{text:"",messages:f.messages,usage:i,error:_,rawResponses:o}];case 8:return[3,2];case 9:return g=null===(r=null==h?void 0:h.choices)||void 0===r?void 0:r[0],w=null==g?void 0:g.message,S=null!==(n=null==w?void 0:w.content)&&void 0!==n?n:"",R=w?ni(ni([],f.messages,!0),[w],!1):f.messages,T=ci(h.usage),fi(i,T),[4,null==l?void 0:l({finishReason:g.finish_reason,messages:R.slice(),text:S,toolCall:ai(h),toolResult:null,stepUsage:T,totalUsage:Object.assign({},i)})];case 10:return q.sent(),[2,{text:S,messages:R,usage:i,rawResponses:o}]}}))}))},e.prototype.streamText=function(e,t){var r;return ti(this,void 0,void 0,(function(){var n,o,i,a,s,l,u,c,f,d,h,p,b,m,y,v,_,g,w,S,R,T,P,q,O,j,k,E,C,x,A,W,I,B,U,z,M=this;return ri(this,(function(F){switch(F.label){case 0:return n={completion_tokens:0,prompt_tokens:0,total_tokens:0},o=oi(e),i=o[0],a=i.onStepFinish,s=i.maxSteps,l=void 0===s?10:s,u=o[1],[4,(c=function(){return M.model.doStream(u,t)})()];case 1:f=F.sent(),d=1,h=null,p=function(){var e=f.tee(),t=e[0],r=e[1];return f=$n(t),function(e){var t,r,n,o,i,a,s,l,u,c;return ti(this,void 0,void 0,(function(){var f,d,h,p,b,m,y,v,_,g,w,S,R;return ri(this,(function(T){switch(T.label){case 0:f={completion_tokens:0,prompt_tokens:0,total_tokens:0},d=$n(e),p={role:"assistant",content:"",tool_calls:[h={id:"",function:{name:"",arguments:""},type:""}]},T.label=1;case 1:T.trys.push([1,6,7,12]),b=!0,m=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e="function"==typeof __values?__values(e):e[Symbol.iterator](),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise((function(n,o){!function(e,t,r,n){Promise.resolve(n).then((function(t){e({value:t,done:r})}),t)}(n,o,(t=e[r](t)).done,t.value)}))}}}(d),T.label=2;case 2:return[4,m.next()];case 3:if(y=T.sent(),t=y.done)return[3,5];o=y.value,b=!1;try{if(!(_=null==(v=o)?void 0:v.choices[0]))return[2,null];if(g=_.finish_reason,w=_.delta,"tool_calls"!==g)return[2,null];if(!w)return[3,4];if(w.content&&(p.content+=w.content),!("tool_calls"in w))return[3,4];(null==(S=null===(i=null==w?void 0:w.tool_calls)||void 0===i?void 0:i[0])?void 0:S.id)&&(h.id=S.id),(null==S?void 0:S.type)&&(h.type=S.type),(null===(a=null==S?void 0:S.function)||void 0===a?void 0:a.name)&&(h.function.name=S.function.name),(null===(s=null==S?void 0:S.function)||void 0===s?void 0:s.arguments)&&(h.function.arguments+=S.function.arguments),(null===(l=null==v?void 0:v.usage)||void 0===l?void 0:l.completion_tokens)&&(f.completion_tokens=v.usage.completion_tokens),(null===(u=null==v?void 0:v.usage)||void 0===u?void 0:u.prompt_tokens)&&(f.prompt_tokens=v.usage.prompt_tokens),(null===(c=null==v?void 0:v.usage)||void 0===c?void 0:c.total_tokens)&&(f.total_tokens=v.usage.total_tokens)}finally{b=!0}T.label=4;case 4:return[3,2];case 5:return[3,12];case 6:return R=T.sent(),r={error:R},[3,12];case 7:return T.trys.push([7,,10,11]),b||t||!(n=m.return)?[3,9]:[4,n.call(m)];case 8:T.sent(),T.label=9;case 9:return[3,11];case 10:if(r)throw r.error;return[7];case 11:return[7];case 12:return[2,{message:p,usage:f}]}}))}))}(r)},F.label=2;case 2:return(b=d<l)?[4,p()]:[3,4];case 3:b=null!=(h=F.sent()),F.label=4;case 4:if(!b)return[3,11];m=h.message,y=h.usage,fi(n,y),v=null===(r=m.tool_calls)||void 0===r?void 0:r[0],F.label=5;case 5:return F.trys.push([5,9,,10]),[4,ui(v)];case 6:return _=F.sent(),[4,null==a?void 0:a({finishReason:"tool_calls",messages:u.messages.slice(),text:m.content,toolCall:v,toolResult:_,stepUsage:y,totalUsage:Object.assign({},n)})];case 7:return F.sent(),si(u.messages,m,_),[4,c()];case 8:return f=F.sent(),[3,10];case 9:return g=F.sent(),w=f.tee(),S=w[0],R=w[1],[2,{messages:Promise.resolve(u.messages),dataStream:$n(S),textStream:$n(R.pipeThrough(new zn({transform:function(e,t){var r,n,o,i=null===(o=null===(n=null===(r=null==e?void 0:e.choices)||void 0===r?void 0:r[0])||void 0===n?void 0:n.delta)||void 0===o?void 0:o.content;"string"==typeof i&&t.enqueue(i)}}))),usage:Promise.resolve(n),error:g}];case 10:return[3,2];case 11:return[4,p()];case 12:return(h=F.sent())?(T=h.message,P=h.usage,fi(n,P),q=ni(ni([],u.messages,!0),[T],!1),a({messages:q.slice(),finishReason:"tool_call",stepUsage:P,text:T.content,toolCall:T.tool_calls[0],totalUsage:Object.assign({},n)}),O=f.tee(),j=O[0],k=O[1],[2,{messages:Promise.resolve(ni(ni([],u.messages,!0),[T],!1)),dataStream:$n(j),textStream:$n(k.pipeThrough(new zn({transform:function(e,t){var r,n,o,i=null===(o=null===(n=null===(r=null==e?void 0:e.choices)||void 0===r?void 0:r[0])||void 0===n?void 0:n.delta)||void 0===o?void 0:o.content;"string"==typeof i&&t.enqueue(i)}}))),usage:Promise.resolve(n)}]):(E=Yn(),C=Yn(),x={role:"assistant",content:""},A="",W={completion_tokens:0,prompt_tokens:0,total_tokens:0},I=f.pipeThrough(new zn({transform:function(e,t){var r,n,o,i,a,s,l,u,c=null===(o=null===(n=null===(r=null==e?void 0:e.choices)||void 0===r?void 0:r[0])||void 0===n?void 0:n.delta)||void 0===o?void 0:o.content;"string"==typeof c&&(x.content+=c);var f=null===(a=null===(i=null==e?void 0:e.choices)||void 0===i?void 0:i[0])||void 0===a?void 0:a.finish_reason;f&&(A=f),(null===(s=null==e?void 0:e.usage)||void 0===s?void 0:s.completion_tokens)&&(W.completion_tokens=e.usage.completion_tokens),(null===(l=null==e?void 0:e.usage)||void 0===l?void 0:l.prompt_tokens)&&(W.prompt_tokens=e.usage.prompt_tokens),(null===(u=null==e?void 0:e.usage)||void 0===u?void 0:u.total_tokens)&&(W.total_tokens=e.usage.total_tokens),t.enqueue(e)},flush:function(){E.res(ni(ni([],u.messages,!0),[x],!1)),fi(n,W),C.res(Object.assign({},n)),null==a||a({messages:ni(ni([],u.messages,!0),[x],!1),finishReason:A,text:x.content,stepUsage:W,totalUsage:Object.assign({},n)})}})),B=I.tee(),U=B[0],z=B[1],[2,{messages:E.promise,dataStream:$n(U),textStream:$n(z.pipeThrough(new zn({transform:function(e,t){var r,n,o,i=null===(o=null===(n=null===(r=null==e?void 0:e.choices)||void 0===r?void 0:r[0])||void 0===n?void 0:n.delta)||void 0===o?void 0:o.content;"string"==typeof i&&t.enqueue(i)}}))),usage:C.promise}])}}))}))},e}();function ai(e){var t,r=null===(t=null==e?void 0:e.choices)||void 0===t?void 0:t[0];if(!r)return null;var n=r.finish_reason,o=r.message;return"tool_calls"!==n?null:o&&Hn(o)?o.tool_calls[0]:null}function si(e,t,r){e.push(t,{role:"tool",tool_call_id:t.tool_calls[0].id,content:JSON.stringify(r)})}var li=new Map;function ui(e){return li.get(e.function.name)(JSON.parse(e.function.arguments))}function ci(e){var t,r,n;return{completion_tokens:null!==(t=null==e?void 0:e.completion_tokens)&&void 0!==t?t:0,prompt_tokens:null!==(r=null==e?void 0:e.prompt_tokens)&&void 0!==r?r:0,total_tokens:null!==(n=null==e?void 0:e.total_tokens)&&void 0!==n?n:0}}function fi(e,t){e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens}var di={hunyuan:yo,"hunyuan-beta":ho,ark:So,dashscope:Oo,"01-ai":xo,moonshot:Uo,zhipu:io,"hunyuan-exp":Lo,"hunyuan-open":Go,deepseek:Vo},hi=function(){return hi=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},hi.apply(this,arguments)},pi=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function s(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))},bi=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}},mi=di,yi=function(){function e(e,t){var r=this;this.req=e,this.baseUrl=t,this.modelRequest=function(e){var t=e.url,n=e.data,o=e.headers,i=e.stream,a=e.timeout;return pi(r,void 0,void 0,(function(){var e,r,s,l;return bi(this,(function(u){switch(u.label){case 0:return e={"Content-Type":"application/json"},i&&Object.assign(e,{Accept:"text/event-stream"}),[4,this.req.fetch({method:"post",headers:hi(hi({},e),o),body:JSON.stringify(n),url:t,stream:i,timeout:a})];case 1:return r=u.sent(),s=r.data,l=r.header,[2,_i(s,l)]}}))}))},this.botRequest=function(e){var t=e.method,n=e.url,o=e.data,i=void 0===o?{}:o,a=e.headers,s=e.stream,l=e.timeout;return pi(r,void 0,void 0,(function(){var e,r,o,u,c;return bi(this,(function(f){switch(f.label){case 0:return"get"!==t?[3,2]:(e=_i,[4,this.req.fetch({url:"".concat(n,"?").concat((d=i,Object.entries(d).map((function(e){var t=e[0],r=e[1];return"".concat(t,"=").concat(r)})).join("&"))),method:t,headers:a,stream:s,timeout:l})]);case 1:return[2,e.apply(void 0,[f.sent().data])];case 2:return r={"Content-Type":"application/json"},s&&Object.assign(r,{Accept:"text/event-stream"}),[4,this.req.fetch({url:n,body:JSON.stringify(i),headers:hi(hi({},r),a),stream:s,method:t,timeout:l})];case 3:return o=f.sent(),u=o.data,c=o.header,[2,_i(u,c)]}var d}))}))},this.aiBaseUrl="".concat(t,"/ai"),this.aiBotBaseUrl="".concat(t,"/aibot"),this.bot=new Kn(this.botRequest,this.aiBotBaseUrl)}return e.prototype.createModel=function(e,t){var r,n=mi[e];if(n)r=new n(this.modelRequest,this.aiBaseUrl);else{var o="string"==typeof(null==t?void 0:t.defaultModelSubUrl)?t.defaultModelSubUrl:"/chat/completions";r=new Ko(this.modelRequest,this.aiBaseUrl,"".concat(e).concat(o))}return new ii(r)},e.prototype.registerModel=function(e,t){null==mi[e]?mi[e]=t:console.warn("AI model ".concat(e," already exists!"))},e.prototype.registerFunctionTool=function(e){li.has(e.name)&&console.warn("AI function tool ".concat(e.name," already exists and will be overwritten!")),li.set(e.name,e.fn)},e}(),vi="请检查调用方式，或前往云开发 AI+ 首页查看文档：https://tcb.cloud.tencent.com/dev#/ai";function _i(e,t){var r,n;return pi(this,void 0,void 0,(function(){var o;return bi(this,(function(i){switch(i.label){case 0:return"object"==typeof e&&e&&"then"in e?[4,e]:[3,2];case 1:if("object"==typeof(o=i.sent())&&o&&"code"in o&&"NORMAL"!==o.code)throw new Error("AI+ 请求出错，错误码：".concat(o.code,"，错误信息：").concat(o.message,"\n").concat(vi,"\n").concat(JSON.stringify(o,null,2)));return[2,e];case 2:return(null===(n=null===(r=null==t?void 0:t.get)||void 0===r?void 0:r.call(t,"content-type"))||void 0===n?void 0:n.includes("application/json"))?[4,Gn(e)]:[3,4];case 3:if("object"==typeof(o=i.sent())&&o&&"code"in o&&"NORMAL"!==o.code)throw new Error("AI+ 请求出错，错误码：".concat(o.code,"，错误信息：").concat(o.message,"\n").concat(vi,"\n").concat(JSON.stringify(o,null,2)));i.label=4;case 4:return[2,e]}}))}))}var gi=function(){},wi=function(){return wi=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},wi.apply(this,arguments)},Si=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r},Ri=Si(o,["MODELS"]),Ti=function(e){var t=e.getAccessToken,r=e.req;return{download:r.download,post:r.post,upload:r.upload,fetch:function(e){return function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function s(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))}(void 0,void 0,void 0,(function(){var n,o,i,a,s;return function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}(this,(function(l){switch(l.label){case 0:if("function"!=typeof r.fetch)throw new Error("req.fetch is not a function");return n=e.token,o=e.headers,i=void 0===o?{}:o,a=Si(e,["token","headers"]),null==n?[3,1]:(s=n,[3,3]);case 1:return[4,t()];case 2:s=l.sent().accessToken,l.label=3;case 3:return[2,r.fetch(wi({headers:wi({Authorization:"Bearer ".concat(s)},i)},a))]}}))}))}}};function Pi(e){var t=e.env,r=e.baseUrl,n=e.req,o=e.getAccessToken,i=e.handleReqInstance;return new yi(function(){if(null==i){if(null==o)throw new Error("`getAccessToken` is required when `handleReqInstance` is not provided!");return Ti({req:n,getAccessToken:o})}return i({req:n})}(),function(){if(null!=r)return r;if(null==t)throw new Error("`env` is required when `baseUrl` is not provided!");return"https://".concat(t,".api.tcloudbasegateway.com/v1")}())}var qi={name:"ai",entity:{ai:function(e){var t,r=this,n=this.request;if(null==n.fetch)throw new Error("cloudbase.request.fetch() unimplemented!");return Pi({req:n,baseUrl:null!==(t=null==e?void 0:e.baseUrl)&&void 0!==t?t:function(){var e=r.getEndPointWithKey("GATEWAY"),t=e.BASE_URL,n=e.PROTOCOL;return"".concat(n).concat(t)}(),handleReqInstance:function(e){return e.req}})}}};function Oi(e){try{e.registerComponent(qi)}catch(e){console.warn(e)}}}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n].call(i.exports,i,i.exports,r),i.exports}return r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r(639)})()));