{"name": "@cloudbase/functions", "version": "2.17.6", "description": "cloudbase javascript sdk functions componets", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "scripts": {"tsc": "rm -rf dist/ && tsc -p tsconfig.esm.json && tsc -p tsconfig.json", "lint": "eslint --fix \"./src/**/*.ts\"", "build": "npm run lint && npm run tsc", "precommit": "npm run lint"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/TencentCloudBase/cloudbase-js-sdk"}, "keywords": ["cloudbase", "tcb", "javascript", "typescript", "web", "serverless"], "author": "", "license": "Apache-2.0", "dependencies": {"@cloudbase/adapter-interface": "^0.7.0", "@cloudbase/cloudrun": "^2.17.6", "@cloudbase/types": "^2.17.6", "@cloudbase/utilities": "^2.17.6"}, "gitHead": "ab6b2e1eb2c4ccfe19e7ad1a92ac4c94e8f72332"}