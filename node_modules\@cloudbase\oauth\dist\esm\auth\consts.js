export var ApiUrls;
(function (ApiUrls) {
    ApiUrls["AUTH_SIGN_UP_URL"] = "/auth/v1/signup";
    ApiUrls["AUTH_TOKEN_URL"] = "/auth/v1/token";
    ApiUrls["AUTH_REVOKE_URL"] = "/auth/v1/revoke";
    ApiUrls["WEDA_USER_URL"] = "/auth/v1/plugin/weda/userinfo";
    ApiUrls["AUTH_RESET_PASSWORD"] = "/auth/v1/reset";
    ApiUrls["AUTH_GET_DEVICE_CODE"] = "/auth/v1/device/code";
    ApiUrls["CHECK_USERNAME"] = "/auth/v1/checkUsername";
    ApiUrls["CHECK_IF_USER_EXIST"] = "/auth/v1/checkIfUserExist";
    ApiUrls["GET_PROVIDER_TYPE"] = "/auth/v1/mgr/provider/providerSubType";
    ApiUrls["AUTH_SIGN_IN_URL"] = "/auth/v1/signin";
    ApiUrls["AUTH_SIGN_IN_ANONYMOUSLY_URL"] = "/auth/v1/signin/anonymously";
    ApiUrls["AUTH_SIGN_IN_WITH_PROVIDER_URL"] = "/auth/v1/signin/with/provider";
    ApiUrls["AUTH_SIGN_IN_WITH_WECHAT_URL"] = "/auth/v1/signin/wechat/noauth";
    ApiUrls["AUTH_SIGN_IN_CUSTOM"] = "/auth/v1/signin/custom";
    ApiUrls["PROVIDER_TOKEN_URL"] = "/auth/v1/provider/token";
    ApiUrls["PROVIDER_URI_URL"] = "/auth/v1/provider/uri";
    ApiUrls["USER_ME_URL"] = "/auth/v1/user/me";
    ApiUrls["AUTH_SIGNOUT_URL"] = "/auth/v1/user/signout";
    ApiUrls["USER_QUERY_URL"] = "/auth/v1/user/query";
    ApiUrls["USER_PRIFILE_URL"] = "/auth/v1/user/profile";
    ApiUrls["USER_BASIC_EDIT_URL"] = "/auth/v1/user/basic/edit";
    ApiUrls["USER_TRANS_BY_PROVIDER_URL"] = "/auth/v1/user/trans/by/provider";
    ApiUrls["PROVIDER_LIST"] = "/auth/v1/user/provider";
    ApiUrls["PROVIDER_BIND_URL"] = "/auth/v1/user/provider/bind";
    ApiUrls["PROVIDER_UNBIND_URL"] = "/auth/v1/user/provider";
    ApiUrls["CHECK_PWD_URL"] = "/auth/v1/user/sudo";
    ApiUrls["SUDO_URL"] = "/auth/v1/user/sudo";
    ApiUrls["BIND_CONTACT_URL"] = "/auth/v1/user/contact";
    ApiUrls["AUTH_SET_PASSWORD"] = "/auth/v1/user/password";
    ApiUrls["AUTHORIZE_DEVICE_URL"] = "/auth/v1/user/device/authorize";
    ApiUrls["AUTHORIZE_URL"] = "/auth/v1/user/authorize";
    ApiUrls["AUTHORIZE_INFO_URL"] = "/auth/v1/user/authorize/info";
    ApiUrls["AUTHORIZED_DEVICES_DELETE_URL"] = "/auth/v1/user/authorized/devices/";
    ApiUrls["AUTH_REVOKE_ALL_URL"] = "/auth/v1/user/revoke/all";
    ApiUrls["GET_USER_BEHAVIOR_LOG"] = "/auth/v1/user/security/history";
    ApiUrls["VERIFICATION_URL"] = "/auth/v1/verification";
    ApiUrls["VERIFY_URL"] = "/auth/v1/verification/verify";
    ApiUrls["CAPTCHA_DATA_URL"] = "/auth/v1/captcha/data";
    ApiUrls["VERIFY_CAPTCHA_DATA_URL"] = "/auth/v1/captcha/data/verify";
    ApiUrls["GET_CAPTCHA_URL"] = "/auth/v1/captcha/init";
    ApiUrls["GET_MINIPROGRAM_QRCODE"] = "/auth/v1/qrcode/generate";
    ApiUrls["GET_MINIPROGRAM_QRCODE_STATUS"] = "/auth/v1/qrcode/get/status";
})(ApiUrls || (ApiUrls = {}));
export var ApiUrlsV2;
(function (ApiUrlsV2) {
    ApiUrlsV2["AUTH_SIGN_IN_URL"] = "/auth/v2/signin/username";
    ApiUrlsV2["AUTH_TOKEN_URL"] = "/auth/v2/token";
    ApiUrlsV2["USER_ME_URL"] = "/auth/v2/user/me";
    ApiUrlsV2["VERIFY_URL"] = "/auth/v2/signin/verificationcode";
    ApiUrlsV2["AUTH_SIGN_IN_WITH_PROVIDER_URL"] = "/auth/v2/signin/with/provider";
    ApiUrlsV2["AUTH_PUBLIC_KEY"] = "/auth/v2/signin/publichkey";
    ApiUrlsV2["AUTH_RESET_PASSWORD"] = "/auth/v2/signin/password/update";
})(ApiUrlsV2 || (ApiUrlsV2 = {}));
export var VerificationUsages;
(function (VerificationUsages) {
    VerificationUsages["REGISTER"] = "REGISTER";
    VerificationUsages["SIGN_IN"] = "SIGN_IN";
    VerificationUsages["PASSWORD_RESET"] = "PASSWORD_RESET";
    VerificationUsages["EMAIL_ADDRESS_CHANGE"] = "EMAIL_ADDRESS_CHANGE";
    VerificationUsages["PHONE_NUMBER_CHANGE"] = "PHONE_NUMBER_CHANGE";
})(VerificationUsages || (VerificationUsages = {}));
export var ErrorType;
(function (ErrorType) {
    ErrorType["UNREACHABLE"] = "unreachable";
    ErrorType["LOCAL"] = "local";
    ErrorType["CANCELLED"] = "cancelled";
    ErrorType["UNKNOWN"] = "unknown";
    ErrorType["UNAUTHENTICATED"] = "unauthenticated";
    ErrorType["RESOURCE_EXHAUSTED"] = "resource_exhausted";
    ErrorType["FAILED_PRECONDITION"] = "failed_precondition";
    ErrorType["INVALID_ARGUMENT"] = "invalid_argument";
    ErrorType["DEADLINE_EXCEEDED"] = "deadline_exceeded";
    ErrorType["NOT_FOUND"] = "not_found";
    ErrorType["ALREADY_EXISTS"] = "already_exists";
    ErrorType["PERMISSION_DENIED"] = "permission_denied";
    ErrorType["ABORTED"] = "aborted";
    ErrorType["OUT_OF_RANGE"] = "out_of_range";
    ErrorType["UNIMPLEMENTED"] = "unimplemented";
    ErrorType["INTERNAL"] = "internal";
    ErrorType["UNAVAILABLE"] = "unavailable";
    ErrorType["DATA_LOSS"] = "data_loss";
    ErrorType["INVALID_PASSWORD"] = "invalid_password";
    ErrorType["PASSWORD_NOT_SET"] = "password_not_set";
    ErrorType["INVALID_STATUS"] = "invalid_status";
    ErrorType["USER_PENDING"] = "user_pending";
    ErrorType["USER_BLOCKED"] = "user_blocked";
    ErrorType["INVALID_VERIFICATION_CODE"] = "invalid_verification_code";
    ErrorType["TWO_FACTOR_REQUIRED"] = "two_factor_required";
    ErrorType["INVALID_TWO_FACTOR"] = "invalid_two_factor";
    ErrorType["INVALID_TWO_FACTOR_RECOVERY"] = "invalid_two_factor_recovery";
    ErrorType["UNDER_REVIEW"] = "under_review";
    ErrorType["INVALID_REQUEST"] = "invalid_request";
    ErrorType["UNAUTHORIZED_CLIENT"] = "unauthorized_client";
    ErrorType["ACCESS_DENIED"] = "access_denied";
    ErrorType["UNSUPPORTED_RESPONSE_TYPE"] = "unsupported_response_type";
    ErrorType["INVALID_SCOPE"] = "invalid_scope";
    ErrorType["INVALID_GRANT"] = "invalid_grant";
    ErrorType["SERVER_ERROR"] = "server_error";
    ErrorType["TEMPORARILY_UNAVAILABLE"] = "temporarily_unavailable";
    ErrorType["INTERACTION_REQUIRED"] = "interaction_required";
    ErrorType["LOGIN_REQUIRED"] = "login_required";
    ErrorType["ACCOUNT_SELECTION_REQUIRED"] = "account_selection_required";
    ErrorType["CONSENT_REQUIRED"] = "consent_required";
    ErrorType["INVALID_REQUEST_URI"] = "invalid_request_uri";
    ErrorType["INVALID_REQUEST_OBJECT"] = "invalid_request_object";
    ErrorType["REQUEST_NOT_SUPPORTED"] = "request_not_supported";
    ErrorType["REQUEST_URI_NOT_SUPPORTED"] = "request_uri_not_supported";
    ErrorType["REGISTRATION_NOT_SUPPORTED"] = "registration_not_supported";
    ErrorType["CAPTCHA_REQUIRED"] = "captcha_required";
    ErrorType["CAPTCHA_INVALID"] = "captcha_invalid";
})(ErrorType || (ErrorType = {}));
