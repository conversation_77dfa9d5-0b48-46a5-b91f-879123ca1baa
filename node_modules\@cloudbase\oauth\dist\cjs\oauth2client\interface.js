"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthClient = void 0;
var AuthClient = (function () {
    function AuthClient() {
    }
    return AuthClient;
}());
exports.AuthClient = AuthClient;
//# sourceMappingURL=data:application/json;base64,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