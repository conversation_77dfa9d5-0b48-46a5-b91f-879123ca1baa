/*! For license information please see database.js.LICENSE.txt */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("cloudbase_database",[],e):"object"==typeof exports?exports.cloudbase_database=e():t.cloudbase_database=e()}("undefined"!=typeof window?window:this,(()=>(()=>{"use strict";var t={258:function(t,e,n){var r=this&&this.__assign||function(){return r=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},r.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.registerDatabase=void 0;var o=n(358),i={name:"database",entity:{database:function(t){var e=this.platform,n=e.adapter,i=e.runtime;return o.Db.reqClass=this.request.constructor,o.Db.getAccessToken=this.authInstance?this.authInstance.getAccessToken.bind(this.authInstance):function(){return""},o.Db.runtime=i,this.wsClientClass&&(o.Db.wsClass=n.wsClass,o.Db.wsClientClass=this.wsClientClass),o.Db.ws||(o.Db.ws=null),new o.Db(r(r(r({},this.config),{_fromApp:this}),t))}}};try{cloudbase.registerComponent(i)}catch(t){}e.registerDatabase=function(t){try{t.registerComponent(i)}catch(t){console.warn(t)}}},358:(t,e,n)=>{n.r(e),n.d(e,{CollectionReference:()=>dn,Db:()=>Un,DocumentReference:()=>$t,Query:()=>qt});var r,o,i={};n.r(i),n.d(i,{LineString:()=>Q,MultiLineString:()=>et,MultiPoint:()=>K,MultiPolygon:()=>rt,Point:()=>W,Polygon:()=>X}),function(t){t.DocIDError="文档ID不合法",t.CollNameError="集合名称不合法",t.OpStrError="操作符不合法",t.DirectionError="排序字符不合法",t.IntergerError="must be integer",t.QueryParamTypeError="查询参数必须为对象",t.QueryParamValueError="查询参数对象值不能均为undefined"}(o||(o={}));var u,s,a="Object",f="GeoPoint",c="GeoLineString",h="GeoPolygon",p="GeoMultiPoint",l="GeoMultiLineString",d="GeoMultiPolygon",y="Date",g="ServerDate",m=["desc","asc"],w=["<","<=","==",">=",">"];!function(t){t.lt="<",t.gt=">",t.lte="<=",t.gte=">=",t.eq="=="}(u||(u={})),(r={})[u.eq]="$eq",r[u.lt]="$lt",r[u.lte]="$lte",r[u.gt]="$gt",r[u.gte]="$gte",function(t){t.WHERE="WHERE",t.DOC="DOC"}(s||(s={}));var b=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),v=[],_={},E=function(t){function e(e,n){if(n!==_)throw new TypeError("InternalSymbol cannot be constructed with new operator");return t.call(this,e)||this}return b(e,t),e.for=function(t){for(var n=0,r=v.length;n<r;n++)if(v[n].target===t)return v[n].instance;var o=new e(t,_);return v.push({target:t,instance:o}),o},e}((function(t){Object.defineProperties(this,{target:{enumerable:!1,writable:!1,configurable:!1,value:t}})}));const O=E;var S=O.for("UNSET_FIELD_NAME"),N=O.for("UPDATE_COMMAND"),x=O.for("QUERY_COMMAND"),I=O.for("LOGIC_COMMAND"),T=O.for("GEO_POINT"),A=O.for("SYMBOL_GEO_LINE_STRING"),B=O.for("SYMBOL_GEO_POLYGON"),j=O.for("SYMBOL_GEO_MULTI_POINT"),P=O.for("SYMBOL_GEO_MULTI_LINE_STRING"),L=O.for("SYMBOL_GEO_MULTI_POLYGON"),U=O.for("SERVER_DATE"),$=O.for("REGEXP"),R=function(){function t(t){var e=(void 0===t?{}:t).offset,n=void 0===e?0:e;this.offset=n}return Object.defineProperty(t.prototype,"_internalType",{get:function(){return U},enumerable:!0,configurable:!0}),t.prototype.parse=function(){return{$date:{offset:this.offset}}},t}();function M(t){return new R(t)}var D,q=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),u=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)u.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return u},C=function(){function t(){}return t.formatResDocumentData=function(e){return e.map((function(e){return t.formatField(e)}))},t.formatField=function(e){var n=Object.keys(e),r={};return Array.isArray(e)&&(r=[]),n.forEach((function(n){var o,i=e[n];switch(t.whichType(i)){case f:o=new W(i.coordinates[0],i.coordinates[1]);break;case c:o=new Q(i.coordinates.map((function(t){return new W(t[0],t[1])})));break;case h:o=new X(i.coordinates.map((function(t){return new Q(t.map((function(t){var e=q(t,2),n=e[0],r=e[1];return new W(n,r)})))})));break;case p:o=new K(i.coordinates.map((function(t){return new W(t[0],t[1])})));break;case l:o=new et(i.coordinates.map((function(t){return new Q(t.map((function(t){var e=q(t,2),n=e[0],r=e[1];return new W(n,r)})))})));break;case d:o=new rt(i.coordinates.map((function(t){return new X(t.map((function(t){return new Q(t.map((function(t){var e=q(t,2),n=e[0],r=e[1];return new W(n,r)})))})))})));break;case y:o=new Date(1e3*i.$timestamp);break;case a:case"Array":o=t.formatField(i);break;case g:o=new Date(i.$date);break;default:o=i}Array.isArray(r)?r.push(o):r[n]=o})),r},t.whichType=function(t){var e=Object.prototype.toString.call(t).slice(8,-1);if(e===y)return"BsonDate";if(e===a){if(t instanceof W)return f;if(t instanceof Date)return y;if(t instanceof R)return g;t.$timestamp?e=y:t.$date?e=g:W.validate(t)?e=f:Q.validate(t)?e=c:X.validate(t)?e=h:K.validate(t)?e=p:et.validate(t)?e=l:rt.validate(t)&&(e=d)}return e},t.generateDocId=function(){for(var t="",e=0;e<24;e++)t+="ABCDEFabcdef0123456789".charAt(Math.floor(22*Math.random()));return t},t}(),k=function(){function t(){}return t.isGeopoint=function(t,e){if("Number"!==C.whichType(e))throw new Error("Geo Point must be number type");var n=Math.abs(e);if("latitude"===t&&n>90)throw new Error("latitude should be a number ranges from -90 to 90");if("longitude"===t&&n>180)throw new Error("longitude should be a number ranges from -180 to 180");return!0},t.isInteger=function(t,e){if(!Number.isInteger(e))throw new Error(t+o.IntergerError);return!0},t.isFieldOrder=function(t){if(-1===m.indexOf(t))throw new Error(o.DirectionError);return!0},t.isFieldPath=function(t){if(!/^[a-zA-Z0-9-_\.]/.test(t))throw new Error;return!0},t.isOperator=function(t){if(-1===w.indexOf(t))throw new Error(o.OpStrError);return!0},t.isCollName=function(t){if(!/^[a-zA-Z0-9]([a-zA-Z0-9-_]){1,32}$/.test(t))throw new Error(o.CollNameError);return!0},t.isDocID=function(t){if(!/^([a-fA-F0-9]){24}$/.test(t))throw new Error(o.DocIDError);return!0},t}(),J=function(t){return Object.prototype.toString.call(t).slice(8,-1).toLowerCase()},F=function(t){return"object"===J(t)},V=function(t){return"number"===J(t)},H=function(t){return Array.isArray(t)},G=function(t){return"date"===J(t)},Z=function(t){return"regexp"===J(t)},Y=function(t){return t&&t._internalType instanceof E},W=function(){function t(t,e){k.isGeopoint("longitude",t),k.isGeopoint("latitude",e),this.longitude=t,this.latitude=e}return t.prototype.parse=function(t){var e;return(e={})[t]={type:"Point",coordinates:[this.longitude,this.latitude]},e},t.prototype.toJSON=function(){return{type:"Point",coordinates:[this.longitude,this.latitude]}},t.prototype.toReadableString=function(){return"["+this.longitude+","+this.latitude+"]"},t.validate=function(t){return"Point"===t.type&&H(t.coordinates)&&k.isGeopoint("longitude",t.coordinates[0])&&k.isGeopoint("latitude",t.coordinates[1])},Object.defineProperty(t.prototype,"_internalType",{get:function(){return T},enumerable:!0,configurable:!0}),t}(),Q=function(){function t(t){if(!H(t))throw new TypeError('"points" must be of type Point[]. Received type '+typeof t);if(t.length<2)throw new Error('"points" must contain 2 points at least');t.forEach((function(t){if(!(t instanceof W))throw new TypeError('"points" must be of type Point[]. Received type '+typeof t+"[]")})),this.points=t}return t.prototype.parse=function(t){var e;return(e={})[t]={type:"LineString",coordinates:this.points.map((function(t){return t.toJSON().coordinates}))},e},t.prototype.toJSON=function(){return{type:"LineString",coordinates:this.points.map((function(t){return t.toJSON().coordinates}))}},t.validate=function(t){var e,n;if("LineString"!==t.type||!H(t.coordinates))return!1;try{for(var r=function(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(t.coordinates),o=r.next();!o.done;o=r.next()){var i=o.value;if(!V(i[0])||!V(i[1]))return!1}}catch(t){e={error:t}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(e)throw e.error}}return!0},t.isClosed=function(t){var e=t.points[0],n=t.points[t.points.length-1];if(e.latitude===n.latitude&&e.longitude===n.longitude)return!0},Object.defineProperty(t.prototype,"_internalType",{get:function(){return A},enumerable:!0,configurable:!0}),t}(),z=function(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},X=function(){function t(t){if(!H(t))throw new TypeError('"lines" must be of type LineString[]. Received type '+typeof t);if(0===t.length)throw new Error("Polygon must contain 1 linestring at least");t.forEach((function(t){if(!(t instanceof Q))throw new TypeError('"lines" must be of type LineString[]. Received type '+typeof t+"[]");if(!Q.isClosed(t))throw new Error("LineString "+t.points.map((function(t){return t.toReadableString()}))+" is not a closed cycle")})),this.lines=t}return t.prototype.parse=function(t){var e;return(e={})[t]={type:"Polygon",coordinates:this.lines.map((function(t){return t.points.map((function(t){return[t.longitude,t.latitude]}))}))},e},t.prototype.toJSON=function(){return{type:"Polygon",coordinates:this.lines.map((function(t){return t.points.map((function(t){return[t.longitude,t.latitude]}))}))}},t.validate=function(t){var e,n,r,o;if("Polygon"!==t.type||!H(t.coordinates))return!1;try{for(var i=z(t.coordinates),u=i.next();!u.done;u=i.next()){var s=u.value;if(!this.isCloseLineString(s))return!1;try{for(var a=(r=void 0,z(s)),f=a.next();!f.done;f=a.next()){var c=f.value;if(!V(c[0])||!V(c[1]))return!1}}catch(t){r={error:t}}finally{try{f&&!f.done&&(o=a.return)&&o.call(a)}finally{if(r)throw r.error}}}}catch(t){e={error:t}}finally{try{u&&!u.done&&(n=i.return)&&n.call(i)}finally{if(e)throw e.error}}return!0},t.isCloseLineString=function(t){var e=t[0],n=t[t.length-1];return e[0]===n[0]&&e[1]===n[1]},Object.defineProperty(t.prototype,"_internalType",{get:function(){return L},enumerable:!0,configurable:!0}),t}(),K=function(){function t(t){if(!H(t))throw new TypeError('"points" must be of type Point[]. Received type '+typeof t);if(0===t.length)throw new Error('"points" must contain 1 point at least');t.forEach((function(t){if(!(t instanceof W))throw new TypeError('"points" must be of type Point[]. Received type '+typeof t+"[]")})),this.points=t}return t.prototype.parse=function(t){var e;return(e={})[t]={type:"MultiPoint",coordinates:this.points.map((function(t){return t.toJSON().coordinates}))},e},t.prototype.toJSON=function(){return{type:"MultiPoint",coordinates:this.points.map((function(t){return t.toJSON().coordinates}))}},t.validate=function(t){var e,n;if("MultiPoint"!==t.type||!H(t.coordinates))return!1;try{for(var r=function(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(t.coordinates),o=r.next();!o.done;o=r.next()){var i=o.value;if(!V(i[0])||!V(i[1]))return!1}}catch(t){e={error:t}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(e)throw e.error}}return!0},Object.defineProperty(t.prototype,"_internalType",{get:function(){return j},enumerable:!0,configurable:!0}),t}(),tt=function(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},et=function(){function t(t){if(!H(t))throw new TypeError('"lines" must be of type LineString[]. Received type '+typeof t);if(0===t.length)throw new Error("Polygon must contain 1 linestring at least");t.forEach((function(t){if(!(t instanceof Q))throw new TypeError('"lines" must be of type LineString[]. Received type '+typeof t+"[]")})),this.lines=t}return t.prototype.parse=function(t){var e;return(e={})[t]={type:"MultiLineString",coordinates:this.lines.map((function(t){return t.points.map((function(t){return[t.longitude,t.latitude]}))}))},e},t.prototype.toJSON=function(){return{type:"MultiLineString",coordinates:this.lines.map((function(t){return t.points.map((function(t){return[t.longitude,t.latitude]}))}))}},t.validate=function(t){var e,n,r,o;if("MultiLineString"!==t.type||!H(t.coordinates))return!1;try{for(var i=tt(t.coordinates),u=i.next();!u.done;u=i.next()){var s=u.value;try{for(var a=(r=void 0,tt(s)),f=a.next();!f.done;f=a.next()){var c=f.value;if(!V(c[0])||!V(c[1]))return!1}}catch(t){r={error:t}}finally{try{f&&!f.done&&(o=a.return)&&o.call(a)}finally{if(r)throw r.error}}}}catch(t){e={error:t}}finally{try{u&&!u.done&&(n=i.return)&&n.call(i)}finally{if(e)throw e.error}}return!0},Object.defineProperty(t.prototype,"_internalType",{get:function(){return P},enumerable:!0,configurable:!0}),t}(),nt=function(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},rt=function(){function t(t){var e,n;if(!H(t))throw new TypeError('"polygons" must be of type Polygon[]. Received type '+typeof t);if(0===t.length)throw new Error("MultiPolygon must contain 1 polygon at least");try{for(var r=nt(t),o=r.next();!o.done;o=r.next()){var i=o.value;if(!(i instanceof X))throw new TypeError('"polygon" must be of type Polygon[]. Received type '+typeof i+"[]")}}catch(t){e={error:t}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(e)throw e.error}}this.polygons=t}return t.prototype.parse=function(t){var e;return(e={})[t]={type:"MultiPolygon",coordinates:this.polygons.map((function(t){return t.lines.map((function(t){return t.points.map((function(t){return[t.longitude,t.latitude]}))}))}))},e},t.prototype.toJSON=function(){return{type:"MultiPolygon",coordinates:this.polygons.map((function(t){return t.lines.map((function(t){return t.points.map((function(t){return[t.longitude,t.latitude]}))}))}))}},t.validate=function(t){var e,n,r,o,i,u;if("MultiPolygon"!==t.type||!H(t.coordinates))return!1;try{for(var s=nt(t.coordinates),a=s.next();!a.done;a=s.next()){var f=a.value;try{for(var c=(r=void 0,nt(f)),h=c.next();!h.done;h=c.next()){var p=h.value;try{for(var l=(i=void 0,nt(p)),d=l.next();!d.done;d=l.next()){var y=d.value;if(!V(y[0])||!V(y[1]))return!1}}catch(t){i={error:t}}finally{try{d&&!d.done&&(u=l.return)&&u.call(l)}finally{if(i)throw i.error}}}}catch(t){r={error:t}}finally{try{h&&!h.done&&(o=c.return)&&o.call(c)}finally{if(r)throw r.error}}}}catch(t){e={error:t}}finally{try{a&&!a.done&&(n=s.return)&&n.call(s)}finally{if(e)throw e.error}}return!0},Object.defineProperty(t.prototype,"_internalType",{get:function(){return B},enumerable:!0,configurable:!0}),t}(),ot=function(){var t;if(!Promise){(t=function(){}).promise={};var e=function(){throw new Error('Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.')};return Object.defineProperty(t.promise,"then",{get:e}),Object.defineProperty(t.promise,"catch",{get:e}),t}var n=new Promise((function(e,n){t=function(t,r){return t?n(t):e(r)}}));return t.promise=n,t};!function(t){t.SET="set",t.REMOVE="remove",t.INC="inc",t.MUL="mul",t.PUSH="push",t.PULL="pull",t.PULL_ALL="pullAll",t.POP="pop",t.SHIFT="shift",t.UNSHIFT="unshift",t.ADD_TO_SET="addToSet",t.BIT="bit",t.RENAME="rename",t.MAX="max",t.MIN="min"}(D||(D={}));var it,ut=function(){function t(t,e,n){this._internalType=N,Object.defineProperties(this,{_internalType:{enumerable:!1,configurable:!1}}),this.operator=t,this.operands=e,this.fieldName=n||S}return t.prototype._setFieldName=function(e){return new t(this.operator,this.operands,e)},t}();function st(t){return t&&t instanceof ut&&t._internalType===N}!function(t){t.AND="and",t.OR="or",t.NOT="not",t.NOR="nor"}(it||(it={}));var at=function(){function t(t,e,n){if(this._internalType=I,Object.defineProperties(this,{_internalType:{enumerable:!1,configurable:!1}}),this.operator=t,this.operands=e,this.fieldName=n||S,this.fieldName!==S)if(Array.isArray(e)){e=e.slice(),this.operands=e;for(var r=0,o=e.length;r<o;r++)(ft(i=e[r])||lt(i))&&(e[r]=i._setFieldName(this.fieldName))}else{var i;(ft(i=e)||lt(i))&&(e=i._setFieldName(this.fieldName))}}return t.prototype._setFieldName=function(e){var n=this.operands.map((function(n){return n instanceof t?n._setFieldName(e):n}));return new t(this.operator,n,e)},t.prototype.and=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var r=Array.isArray(arguments[0])?arguments[0]:Array.from(arguments);return r.unshift(this),new t(it.AND,r,this.fieldName)},t.prototype.or=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var r=Array.isArray(arguments[0])?arguments[0]:Array.from(arguments);return r.unshift(this),new t(it.OR,r,this.fieldName)},t}();function ft(t){return t&&t instanceof at&&t._internalType===I}var ct,ht=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(t){t.EQ="eq",t.NEQ="neq",t.GT="gt",t.GTE="gte",t.LT="lt",t.LTE="lte",t.IN="in",t.NIN="nin",t.ALL="all",t.ELEM_MATCH="elemMatch",t.EXISTS="exists",t.SIZE="size",t.MOD="mod",t.GEO_NEAR="geoNear",t.GEO_WITHIN="geoWithin",t.GEO_INTERSECTS="geoIntersects"}(ct||(ct={}));var pt=function(t){function e(e,n,r){var o=t.call(this,e,n,r)||this;return o.operator=e,o._internalType=x,o}return ht(e,t),e.prototype.toJSON=function(){var t,e;switch(this.operator){case ct.IN:case ct.NIN:return(t={})["$"+this.operator]=this.operands,t;default:return(e={})["$"+this.operator]=this.operands[0],e}},e.prototype._setFieldName=function(t){return new e(this.operator,this.operands,t)},e.prototype.eq=function(t){var n=new e(ct.EQ,[t],this.fieldName);return this.and(n)},e.prototype.neq=function(t){var n=new e(ct.NEQ,[t],this.fieldName);return this.and(n)},e.prototype.gt=function(t){var n=new e(ct.GT,[t],this.fieldName);return this.and(n)},e.prototype.gte=function(t){var n=new e(ct.GTE,[t],this.fieldName);return this.and(n)},e.prototype.lt=function(t){var n=new e(ct.LT,[t],this.fieldName);return this.and(n)},e.prototype.lte=function(t){var n=new e(ct.LTE,[t],this.fieldName);return this.and(n)},e.prototype.in=function(t){var n=new e(ct.IN,t,this.fieldName);return this.and(n)},e.prototype.nin=function(t){var n=new e(ct.NIN,t,this.fieldName);return this.and(n)},e.prototype.geoNear=function(t){if(!(t.geometry instanceof W))throw new TypeError('"geometry" must be of type Point. Received type '+typeof t.geometry);if(void 0!==t.maxDistance&&!V(t.maxDistance))throw new TypeError('"maxDistance" must be of type Number. Received type '+typeof t.maxDistance);if(void 0!==t.minDistance&&!V(t.minDistance))throw new TypeError('"minDistance" must be of type Number. Received type '+typeof t.minDistance);var n=new e(ct.GEO_NEAR,[t],this.fieldName);return this.and(n)},e.prototype.geoWithin=function(t){if(!(t.geometry instanceof rt||t.geometry instanceof X))throw new TypeError('"geometry" must be of type Polygon or MultiPolygon. Received type '+typeof t.geometry);var n=new e(ct.GEO_WITHIN,[t],this.fieldName);return this.and(n)},e.prototype.geoIntersects=function(t){if(!(t.geometry instanceof W||t.geometry instanceof Q||t.geometry instanceof X||t.geometry instanceof K||t.geometry instanceof et||t.geometry instanceof rt))throw new TypeError('"geometry" must be of type Point, LineString, Polygon, MultiPoint, MultiLineString or MultiPolygon. Received type '+typeof t.geometry);var n=new e(ct.GEO_INTERSECTS,[t],this.fieldName);return this.and(n)},e}(at);function lt(t){return t&&t instanceof pt&&t._internalType===x}function dt(t){return lt(t)}var yt={};for(var gt in ct)yt[gt]="$"+gt;for(var gt in it)yt[gt]="$"+gt;for(var gt in D)yt[gt]="$"+gt;function mt(t){return yt[t]||"$"+t}yt[ct.NEQ]="$ne",yt[D.REMOVE]="$unset",yt[D.SHIFT]="$pop",yt[D.UNSHIFT]="$push";var wt=function(){return wt=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},wt.apply(this,arguments)},bt=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),u=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)u.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return u},vt=function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(bt(arguments[e]));return t};function _t(t){return Et(t,[t])}function Et(t,e){if(!Y(t)){if(G(t))return{$date:+t};if(Z(t))return{$regex:t.source,$options:t.flags};if(H(t))return t.map((function(t){if(e.indexOf(t)>-1)throw new Error("Cannot convert circular structure to JSON");return Et(t,vt(e,[t]))}));if(F(t)){var n=wt({},t);for(var r in n){if(e.indexOf(n[r])>-1)throw new Error("Cannot convert circular structure to JSON");n[r]=Et(n[r],vt(e,[n[r]]))}return n}return t}switch(t._internalType){case T:return t.toJSON();case U:case $:return t.parse();default:return t.toJSON?t.toJSON():t}}var Ot=function(){return Ot=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},Ot.apply(this,arguments)},St=function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),u=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)u.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return u},Nt=function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(St(arguments[e]));return t};function xt(t,e,n,r){var o=Ot({},t);for(var i in t)if(!/^\$/.test(i)){var u=t[i];if(u&&F(u)&&!e(u)){if(r.indexOf(u)>-1)throw new Error("Cannot convert circular structure to JSON");var s=xt(u,e,Nt(n,[i]),Nt(r,[u]));o[i]=s;var a=!1;for(var f in s)/^\$/.test(f)?a=!0:(o[i+"."+f]=s[f],delete o[i][f]);a||delete o[i]}}return o}function It(t){return xt(t,At,[],[t])}function Tt(t,e,n){for(var r in e[n]||delete t[n],e)t[r]?H(t[r])?t[r].push(e[r]):F(t[r])?F(e[r])?Object.assign(t[r],e[r]):(console.warn("unmergable condition, query is object but condition is "+J(e)+", can only overwrite",e,n),t[r]=e[r]):(console.warn("to-merge query is of type "+J(t)+", can only overwrite",t,e,n),t[r]=e[r]):t[r]=e[r]}function At(t){return Y(t)||G(t)||Z(t)}function Bt(t){return _t(t)}var jt=function(){function t(){}return t.encode=function(e){return(new t).encodeUpdate(e)},t.prototype.encodeUpdate=function(t){return st(t)?this.encodeUpdateCommand(t):"object"===J(t)?this.encodeUpdateObject(t):t},t.prototype.encodeUpdateCommand=function(t){if(t.fieldName===S)throw new Error("Cannot encode a comparison command with unset field name");switch(t.operator){case D.PUSH:case D.PULL:case D.PULL_ALL:case D.POP:case D.SHIFT:case D.UNSHIFT:case D.ADD_TO_SET:return this.encodeArrayUpdateCommand(t);default:return this.encodeFieldUpdateCommand(t)}},t.prototype.encodeFieldUpdateCommand=function(t){var e,n,r,o,i=mt(t.operator);return t.operator===D.REMOVE?((e={})[i]=((n={})[t.fieldName]="",n),e):((r={})[i]=((o={})[t.fieldName]=t.operands[0],o),r)},t.prototype.encodeArrayUpdateCommand=function(t){var e,n,r,o,i,u,s,a,f,c,h=mt(t.operator);switch(t.operator){case D.PUSH:var p=void 0;return p=H(t.operands)?{$each:t.operands.map(Bt)}:t.operands,(e={})[h]=((n={})[t.fieldName]=p,n),e;case D.UNSHIFT:return p={$each:t.operands.map(Bt),$position:0},(r={})[h]=((o={})[t.fieldName]=p,o),r;case D.POP:return(i={})[h]=((u={})[t.fieldName]=1,u),i;case D.SHIFT:return(s={})[h]=((a={})[t.fieldName]=-1,a),s;default:return(f={})[h]=((c={})[t.fieldName]=Bt(t.operands),c),f}},t.prototype.encodeUpdateObject=function(t){var e=It(t);for(var n in e)if(!/^\$/.test(n)){var r=e[n];if(st(r))e[n]=r._setFieldName(n),Tt(e,this.encodeUpdateCommand(e[n]),n);else{e[n]=r=Bt(r);var o=new ut(D.SET,[r],n);Tt(e,this.encodeUpdateCommand(o),n)}}return e},t}(),Pt={};function Lt(t){if(!Un.wsClientClass)throw new Error("to use realtime you must import realtime module first");var e=t.config.env;return Pt[e]||(Pt[e]=new Un.wsClientClass({context:{appConfig:{docSizeLimit:1e3,realtimePingInterval:1e4,realtimePongWaitTimeout:5e3,request:new Un.reqClass(t.config)}}})),Pt[e]}for(var Ut=function(){return Ut=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},Ut.apply(this,arguments)},$t=function(){function t(t,e,n,r){var o=this;void 0===r&&(r={}),this.watch=function(t){return Lt(o._db).watch(Ut(Ut({},t),{envId:o._db.config.env,collectionName:o._coll,query:JSON.stringify({_id:o.id})}))},this._db=t,this._coll=e,this.id=n,this.request=new Un.reqClass(this._db.config),this.projection=r}return t.prototype.create=function(t,e){e=e||ot();var n={collectionName:this._coll,data:_t(t)};return this.id&&(n._id=this.id),this.request.send("database.addDocument",n).then((function(t){t.code?e(0,t):e(0,{id:t.data._id,requestId:t.requestId})})).catch((function(t){e(t)})),e.promise},t.prototype.set=function(t,e){if(e=e||ot(),!this.id)return Promise.resolve({code:"INVALID_PARAM",message:"docId不能为空"});if(!t||"object"!=typeof t)return Promise.resolve({code:"INVALID_PARAM",message:"参数必需是非空对象"});if(t.hasOwnProperty("_id"))return Promise.resolve({code:"INVALID_PARAM",message:"不能更新_id的值"});var n=!1,r=function(t){if("object"==typeof t)for(var e in t)t[e]instanceof ut?n=!0:"object"==typeof t[e]&&r(t[e])};if(r(t),n)return Promise.resolve({code:"DATABASE_REQUEST_FAILED",message:"update operator complicit"});var o={collectionName:this._coll,queryType:s.DOC,data:_t(t),multi:!1,merge:!1,upsert:!0};return this.id&&(o.query={_id:this.id}),this.request.send("database.updateDocument",o).then((function(t){t.code?e(0,t):e(0,{updated:t.data.updated,upsertedId:t.data.upserted_id,requestId:t.requestId})})).catch((function(t){e(t)})),e.promise},t.prototype.update=function(t,e){if(e=e||ot(),!t||"object"!=typeof t)return Promise.resolve({code:"INVALID_PARAM",message:"参数必需是非空对象"});if(t.hasOwnProperty("_id"))return Promise.resolve({code:"INVALID_PARAM",message:"不能更新_id的值"});var n={_id:this.id},r={collectionName:this._coll,data:jt.encode(t),query:n,queryType:s.DOC,multi:!1,merge:!0,upsert:!1};return this.request.send("database.updateDocument",r).then((function(t){t.code?e(0,t):e(0,{updated:t.data.updated,upsertedId:t.data.upserted_id,requestId:t.requestId})})).catch((function(t){e(t)})),e.promise},t.prototype.remove=function(t){t=t||ot();var e={_id:this.id},n={collectionName:this._coll,query:e,queryType:s.DOC,multi:!1};return this.request.send("database.deleteDocument",n).then((function(e){e.code?t(0,e):t(0,{deleted:e.data.deleted,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},t.prototype.get=function(t){t=t||ot();var e={_id:this.id},n={collectionName:this._coll,query:e,queryType:s.DOC,multi:!1,projection:this.projection};return this.request.send("database.queryDocument",n).then((function(e){if(e.code)t(0,e);else{var n=C.formatResDocumentData(e.data.list);t(0,{data:n,requestId:e.requestId})}})).catch((function(e){t(e)})),t.promise},t.prototype.field=function(e){for(var n in e)e[n]?e[n]=1:e[n]=0;return new t(this._db,this._coll,this.id,e)},t}(),Rt=function(){function t(){}return t.encode=function(t){return(new Mt).encodeQuery(t)},t}(),Mt=function(){function t(){}return t.prototype.encodeQuery=function(t,e){var n;return At(t)?ft(t)?this.encodeLogicCommand(t):lt(t)?this.encodeQueryCommand(t):((n={})[e]=this.encodeQueryObject(t),n):F(t)?this.encodeQueryObject(t):t},t.prototype.encodeRegExp=function(t){return{$regex:t.source,$options:t.flags}},t.prototype.encodeLogicCommand=function(t){var e,n,r,o,i,u,s,a=this;switch(t.operator){case it.NOR:case it.AND:case it.OR:var f=mt(t.operator),c=t.operands.map((function(e){return a.encodeQuery(e,t.fieldName)}));return(e={})[f]=c,e;case it.NOT:f=mt(t.operator);var h=t.operands[0];return Z(h)?((n={})[t.fieldName]=((r={})[f]=this.encodeRegExp(h),r),n):(c=this.encodeQuery(h)[t.fieldName],(o={})[t.fieldName]=((i={})[f]=c,i),o);default:if(f=mt(t.operator),1===t.operands.length){var p=this.encodeQuery(t.operands[0]);return(u={})[f]=p,u}return c=t.operands.map(this.encodeQuery.bind(this)),(s={})[f]=c,s}},t.prototype.encodeQueryCommand=function(t){return dt(t),this.encodeComparisonCommand(t)},t.prototype.encodeComparisonCommand=function(t){var e,n,r,o,i,u,s,a,f;if(t.fieldName===S)throw new Error("Cannot encode a comparison command with unset field name");var c=mt(t.operator);switch(t.operator){case ct.EQ:case ct.NEQ:case ct.LT:case ct.LTE:case ct.GT:case ct.GTE:case ct.ELEM_MATCH:case ct.EXISTS:case ct.SIZE:case ct.MOD:return(e={})[t.fieldName]=((n={})[c]=Bt(t.operands[0]),n),e;case ct.IN:case ct.NIN:case ct.ALL:return(r={})[t.fieldName]=((o={})[c]=Bt(t.operands),o),r;case ct.GEO_NEAR:var h=t.operands[0];return(i={})[t.fieldName]={$nearSphere:{$geometry:h.geometry.toJSON(),$maxDistance:h.maxDistance,$minDistance:h.minDistance}},i;case ct.GEO_WITHIN:return h=t.operands[0],(u={})[t.fieldName]={$geoWithin:{$geometry:h.geometry.toJSON()}},u;case ct.GEO_INTERSECTS:return h=t.operands[0],(s={})[t.fieldName]={$geoIntersects:{$geometry:h.geometry.toJSON()}},s;default:return(a={})[t.fieldName]=((f={})[c]=Bt(t.operands[0]),f),a}},t.prototype.encodeQueryObject=function(t){var e=It(t);for(var n in e){var r=e[n];if(ft(r)){e[n]=r._setFieldName(n);var o=this.encodeLogicCommand(e[n]);this.mergeConditionAfterEncode(e,o,n)}else dt(r)?(e[n]=r._setFieldName(n),o=this.encodeComparisonCommand(e[n]),this.mergeConditionAfterEncode(e,o,n)):At(r)&&(e[n]=Bt(r))}return e},t.prototype.mergeConditionAfterEncode=function(t,e,n){for(var r in e[n]||delete t[n],e)t[r]?H(t[r])?t[r]=t[r].concat(e[r]):F(t[r])?F(e[r])?Object.assign(t,e):(console.warn("unmergable condition, query is object but condition is "+J(e)+", can only overwrite",e,n),t[r]=e[r]):(console.warn("to-merge query is of type "+J(t)+", can only overwrite",t,e,n),t[r]=e[r]):t[r]=e[r]},t}(),Dt=function(){return Dt=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},Dt.apply(this,arguments)},qt=function(){function t(t,e,n,r,o){var i=this;this.watch=function(t){return Lt(i._db).watch(Dt(Dt({},t),{envId:i._db.config.env,collectionName:i._coll,query:JSON.stringify(i._fieldFilters||{}),limit:i._queryOptions.limit,orderBy:i._fieldOrders?i._fieldOrders.reduce((function(t,e){return t[e.field]=e.direction,t}),{}):void 0}))},this._db=t,this._coll=e,this._fieldFilters=n,this._fieldOrders=r||[],this._queryOptions=o||{},this._request=new Un.reqClass(this._db.config)}return t.prototype.get=function(t){t=t||ot();var e=[];this._fieldOrders&&this._fieldOrders.forEach((function(t){e.push(t)}));var n={collectionName:this._coll,queryType:s.WHERE};return this._fieldFilters&&(n.query=this._fieldFilters),e.length>0&&(n.order=e),this._queryOptions.offset&&(n.offset=this._queryOptions.offset),this._queryOptions.limit?n.limit=this._queryOptions.limit<1e3?this._queryOptions.limit:1e3:n.limit=100,this._queryOptions.projection&&(n.projection=this._queryOptions.projection),this._request.send("database.queryDocument",n).then((function(e){if(e.code)t(0,e);else{var n={data:C.formatResDocumentData(e.data.list),requestId:e.requestId};e.total&&(n.total=e.total),e.limit&&(n.limit=e.limit),e.offset&&(n.offset=e.offset),t(0,n)}})).catch((function(e){t(e)})),t.promise},t.prototype.count=function(t){t=t||ot();var e={collectionName:this._coll,queryType:s.WHERE};return this._fieldFilters&&(e.query=this._fieldFilters),this._request.send("database.countDocument",e).then((function(e){e.code?t(0,e):t(0,{requestId:e.requestId,total:e.data.total})})).catch((function(e){t(e)})),t.promise},t.prototype.where=function(e){if("Object"!==Object.prototype.toString.call(e).slice(8,-1))throw Error(o.QueryParamTypeError);var n=Object.keys(e),r=n.some((function(t){return void 0!==e[t]}));if(n.length&&!r)throw Error(o.QueryParamValueError);return new t(this._db,this._coll,Rt.encode(e),this._fieldOrders,this._queryOptions)},t.prototype.orderBy=function(e,n){k.isFieldPath(e),k.isFieldOrder(n);var r={field:e,direction:n},o=this._fieldOrders.concat(r);return new t(this._db,this._coll,this._fieldFilters,o,this._queryOptions)},t.prototype.limit=function(e){k.isInteger("limit",e);var n=Dt({},this._queryOptions);return n.limit=e,new t(this._db,this._coll,this._fieldFilters,this._fieldOrders,n)},t.prototype.skip=function(e){k.isInteger("offset",e);var n=Dt({},this._queryOptions);return n.offset=e,new t(this._db,this._coll,this._fieldFilters,this._fieldOrders,n)},t.prototype.update=function(t,e){if(e=e||ot(),!t||"object"!=typeof t)return Promise.resolve({code:"INVALID_PARAM",message:"参数必需是非空对象"});if(t.hasOwnProperty("_id"))return Promise.resolve({code:"INVALID_PARAM",message:"不能更新_id的值"});var n={collectionName:this._coll,query:this._fieldFilters,queryType:s.WHERE,multi:!0,merge:!0,upsert:!1,data:jt.encode(t)};return this._request.send("database.updateDocument",n).then((function(t){t.code?e(0,t):e(0,{requestId:t.requestId,updated:t.data.updated,upsertId:t.data.upsert_id})})).catch((function(t){e(t)})),e.promise},t.prototype.field=function(e){for(var n in e)e[n]?"object"!=typeof e[n]&&(e[n]=1):e[n]=0;var r=Dt({},this._queryOptions);return r.projection=e,new t(this._db,this._coll,this._fieldFilters,this._fieldOrders,r)},t.prototype.remove=function(t){t=t||ot(),Object.keys(this._queryOptions).length>0&&console.warn("`offset`, `limit` and `projection` are not supported in remove() operation"),this._fieldOrders.length>0&&console.warn("`orderBy` is not supported in remove() operation");var e={collectionName:this._coll,query:Rt.encode(this._fieldFilters),queryType:s.WHERE,multi:!0};return this._request.send("database.deleteDocument",e).then((function(e){e.code?t(0,e):t(0,{requestId:e.requestId,deleted:e.data.deleted})})).catch((function(e){t(e)})),t.promise},t}(),Ct=[],kt=[],Jt="undefined"!=typeof Uint8Array?Uint8Array:Array,Ft="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Vt=0;Vt<64;++Vt)Ct[Vt]=Ft[Vt],kt[Ft.charCodeAt(Vt)]=Vt;function Ht(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=t.indexOf("=");return-1===n&&(n=e),[n,n===e?0:4-n%4]}function Gt(t,e,n){for(var r,o,i=[],u=e;u<n;u+=3)r=(t[u]<<16&16711680)+(t[u+1]<<8&65280)+(255&t[u+2]),i.push(Ct[(o=r)>>18&63]+Ct[o>>12&63]+Ct[o>>6&63]+Ct[63&o]);return i.join("")}kt["-".charCodeAt(0)]=62,kt["_".charCodeAt(0)]=63;var Zt=function(t){var e,n,r=Ht(t),o=r[0],i=r[1],u=new Jt(function(t,e,n){return 3*(e+n)/4-n}(0,o,i)),s=0,a=i>0?o-4:o;for(n=0;n<a;n+=4)e=kt[t.charCodeAt(n)]<<18|kt[t.charCodeAt(n+1)]<<12|kt[t.charCodeAt(n+2)]<<6|kt[t.charCodeAt(n+3)],u[s++]=e>>16&255,u[s++]=e>>8&255,u[s++]=255&e;return 2===i&&(e=kt[t.charCodeAt(n)]<<2|kt[t.charCodeAt(n+1)]>>4,u[s++]=255&e),1===i&&(e=kt[t.charCodeAt(n)]<<10|kt[t.charCodeAt(n+1)]<<4|kt[t.charCodeAt(n+2)]>>2,u[s++]=e>>8&255,u[s++]=255&e),u},Yt=function(t){for(var e,n=t.length,r=n%3,o=[],i=16383,u=0,s=n-r;u<s;u+=i)o.push(Gt(t,u,u+i>s?s:u+i));return 1===r?(e=t[n-1],o.push(Ct[e>>2]+Ct[e<<4&63]+"==")):2===r&&(e=(t[n-2]<<8)+t[n-1],o.push(Ct[e>>10]+Ct[e>>4&63]+Ct[e<<2&63]+"=")),o.join("")},Wt=function(t,e,n,r,o){var i,u,s=8*o-r-1,a=(1<<s)-1,f=a>>1,c=-7,h=n?o-1:0,p=n?-1:1,l=t[e+h];for(h+=p,i=l&(1<<-c)-1,l>>=-c,c+=s;c>0;i=256*i+t[e+h],h+=p,c-=8);for(u=i&(1<<-c)-1,i>>=-c,c+=r;c>0;u=256*u+t[e+h],h+=p,c-=8);if(0===i)i=1-f;else{if(i===a)return u?NaN:1/0*(l?-1:1);u+=Math.pow(2,r),i-=f}return(l?-1:1)*u*Math.pow(2,i-r)},Qt=function(t,e,n,r,o,i){var u,s,a,f=8*i-o-1,c=(1<<f)-1,h=c>>1,p=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,l=r?0:i-1,d=r?1:-1,y=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(s=isNaN(e)?1:0,u=c):(u=Math.floor(Math.log(e)/Math.LN2),e*(a=Math.pow(2,-u))<1&&(u--,a*=2),(e+=u+h>=1?p/a:p*Math.pow(2,1-h))*a>=2&&(u++,a/=2),u+h>=c?(s=0,u=c):u+h>=1?(s=(e*a-1)*Math.pow(2,o),u+=h):(s=e*Math.pow(2,h-1)*Math.pow(2,o),u=0));o>=8;t[n+l]=255&s,l+=d,s/=256,o-=8);for(u=u<<o|s,f+=o;f>0;t[n+l]=255&u,l+=d,u/=256,f-=8);t[n+l-d]|=128*y},zt=function(t,e){return function(t,e){var n="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;e.Buffer=i,e.SlowBuffer=function(t){return+t!=t&&(t=0),i.alloc(+t)},e.INSPECT_MAX_BYTES=50;var r=2147483647;function o(t){if(t>r)throw new RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,i.prototype),e}function i(t,e,n){if("number"==typeof t){if("string"==typeof e)throw new TypeError('The "string" argument must be of type string. Received type number');return a(t)}return u(t,e,n)}function u(t,e,n){if("string"==typeof t)return function(t,e){if("string"==typeof e&&""!==e||(e="utf8"),!i.isEncoding(e))throw new TypeError("Unknown encoding: "+e);var n=0|p(t,e),r=o(n),u=r.write(t,e);return u!==n&&(r=r.slice(0,u)),r}(t,e);if(ArrayBuffer.isView(t))return function(t){if(D(t,Uint8Array)){var e=new Uint8Array(t);return c(e.buffer,e.byteOffset,e.byteLength)}return f(t)}(t);if(null==t)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+babelHelpers.typeof(t));if(D(t,ArrayBuffer)||t&&D(t.buffer,ArrayBuffer))return c(t,e,n);if("undefined"!=typeof SharedArrayBuffer&&(D(t,SharedArrayBuffer)||t&&D(t.buffer,SharedArrayBuffer)))return c(t,e,n);if("number"==typeof t)throw new TypeError('The "value" argument must not be of type number. Received type number');var r=t.valueOf&&t.valueOf();if(null!=r&&r!==t)return i.from(r,e,n);var u=function(t){if(i.isBuffer(t)){var e=0|h(t.length),n=o(e);return 0===n.length||t.copy(n,0,0,e),n}return void 0!==t.length?"number"!=typeof t.length||q(t.length)?o(0):f(t):"Buffer"===t.type&&Array.isArray(t.data)?f(t.data):void 0}(t);if(u)return u;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return i.from(t[Symbol.toPrimitive]("string"),e,n);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+babelHelpers.typeof(t))}function s(t){if("number"!=typeof t)throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('The value "'+t+'" is invalid for option "size"')}function a(t){return s(t),o(t<0?0:0|h(t))}function f(t){for(var e=t.length<0?0:0|h(t.length),n=o(e),r=0;r<e;r+=1)n[r]=255&t[r];return n}function c(t,e,n){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(n||0))throw new RangeError('"length" is outside of buffer bounds');var r;return r=void 0===e&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,e):new Uint8Array(t,e,n),Object.setPrototypeOf(r,i.prototype),r}function h(t){if(t>=r)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+r.toString(16)+" bytes");return 0|t}function p(t,e){if(i.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||D(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+babelHelpers.typeof(t));var n=t.length,r=arguments.length>2&&!0===arguments[2];if(!r&&0===n)return 0;for(var o=!1;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":return $(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return R(t).length;default:if(o)return r?-1:$(t).length;e=(""+e).toLowerCase(),o=!0}}function l(t,e,n){var r=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return I(this,e,n);case"utf8":case"utf-8":return O(this,e,n);case"ascii":return N(this,e,n);case"latin1":case"binary":return x(this,e,n);case"base64":return E(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return T(this,e,n);default:if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}function d(t,e,n){var r=t[e];t[e]=t[n],t[n]=r}function y(t,e,n,r,o){if(0===t.length)return-1;if("string"==typeof n?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),q(n=+n)&&(n=o?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(o)return-1;n=t.length-1}else if(n<0){if(!o)return-1;n=0}if("string"==typeof e&&(e=i.from(e,r)),i.isBuffer(e))return 0===e.length?-1:g(t,e,n,r,o);if("number"==typeof e)return e&=255,"function"==typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):g(t,[e],n,r,o);throw new TypeError("val must be string, number or Buffer")}function g(t,e,n,r,o){var i,u=1,s=t.length,a=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return-1;u=2,s/=2,a/=2,n/=2}function f(t,e){return 1===u?t[e]:t.readUInt16BE(e*u)}if(o){var c=-1;for(i=n;i<s;i++)if(f(t,i)===f(e,-1===c?0:i-c)){if(-1===c&&(c=i),i-c+1===a)return c*u}else-1!==c&&(i-=i-c),c=-1}else for(n+a>s&&(n=s-a),i=n;i>=0;i--){for(var h=!0,p=0;p<a;p++)if(f(t,i+p)!==f(e,p)){h=!1;break}if(h)return i}return-1}function m(t,e,n,r){n=Number(n)||0;var o=t.length-n;r?(r=Number(r))>o&&(r=o):r=o;var i=e.length;r>i/2&&(r=i/2);for(var u=0;u<r;++u){var s=parseInt(e.substr(2*u,2),16);if(q(s))return u;t[n+u]=s}return u}function w(t,e,n,r){return M($(e,t.length-n),t,n,r)}function b(t,e,n,r){return M(function(t){for(var e=[],n=0;n<t.length;++n)e.push(255&t.charCodeAt(n));return e}(e),t,n,r)}function v(t,e,n,r){return M(R(e),t,n,r)}function _(t,e,n,r){return M(function(t,e){for(var n,r,o,i=[],u=0;u<t.length&&!((e-=2)<0);++u)r=(n=t.charCodeAt(u))>>8,o=n%256,i.push(o),i.push(r);return i}(e,t.length-n),t,n,r)}function E(t,e,n){return 0===e&&n===t.length?Yt(t):Yt(t.slice(e,n))}function O(t,e,n){n=Math.min(t.length,n);for(var r=[],o=e;o<n;){var i,u,s,a,f=t[o],c=null,h=f>239?4:f>223?3:f>191?2:1;if(o+h<=n)switch(h){case 1:f<128&&(c=f);break;case 2:128==(192&(i=t[o+1]))&&(a=(31&f)<<6|63&i)>127&&(c=a);break;case 3:i=t[o+1],u=t[o+2],128==(192&i)&&128==(192&u)&&(a=(15&f)<<12|(63&i)<<6|63&u)>2047&&(a<55296||a>57343)&&(c=a);break;case 4:i=t[o+1],u=t[o+2],s=t[o+3],128==(192&i)&&128==(192&u)&&128==(192&s)&&(a=(15&f)<<18|(63&i)<<12|(63&u)<<6|63&s)>65535&&a<1114112&&(c=a)}null===c?(c=65533,h=1):c>65535&&(c-=65536,r.push(c>>>10&1023|55296),c=56320|1023&c),r.push(c),o+=h}return function(t){var e=t.length;if(e<=S)return String.fromCharCode.apply(String,t);for(var n="",r=0;r<e;)n+=String.fromCharCode.apply(String,t.slice(r,r+=S));return n}(r)}e.kMaxLength=r,i.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),i.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(i.prototype,"parent",{enumerable:!0,get:function(){if(i.isBuffer(this))return this.buffer}}),Object.defineProperty(i.prototype,"offset",{enumerable:!0,get:function(){if(i.isBuffer(this))return this.byteOffset}}),i.poolSize=8192,i.from=function(t,e,n){return u(t,e,n)},Object.setPrototypeOf(i.prototype,Uint8Array.prototype),Object.setPrototypeOf(i,Uint8Array),i.alloc=function(t,e,n){return function(t,e,n){return s(t),t<=0?o(t):void 0!==e?"string"==typeof n?o(t).fill(e,n):o(t).fill(e):o(t)}(t,e,n)},i.allocUnsafe=function(t){return a(t)},i.allocUnsafeSlow=function(t){return a(t)},i.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==i.prototype},i.compare=function(t,e){if(D(t,Uint8Array)&&(t=i.from(t,t.offset,t.byteLength)),D(e,Uint8Array)&&(e=i.from(e,e.offset,e.byteLength)),!i.isBuffer(t)||!i.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var n=t.length,r=e.length,o=0,u=Math.min(n,r);o<u;++o)if(t[o]!==e[o]){n=t[o],r=e[o];break}return n<r?-1:r<n?1:0},i.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},i.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return i.alloc(0);var n;if(void 0===e)for(e=0,n=0;n<t.length;++n)e+=t[n].length;var r=i.allocUnsafe(e),o=0;for(n=0;n<t.length;++n){var u=t[n];if(D(u,Uint8Array))o+u.length>r.length?i.from(u).copy(r,o):Uint8Array.prototype.set.call(r,u,o);else{if(!i.isBuffer(u))throw new TypeError('"list" argument must be an Array of Buffers');u.copy(r,o)}o+=u.length}return r},i.byteLength=p,i.prototype._isBuffer=!0,i.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)d(this,e,e+1);return this},i.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)d(this,e,e+3),d(this,e+1,e+2);return this},i.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)d(this,e,e+7),d(this,e+1,e+6),d(this,e+2,e+5),d(this,e+3,e+4);return this},i.prototype.toString=function(){var t=this.length;return 0===t?"":0===arguments.length?O(this,0,t):l.apply(this,arguments)},i.prototype.toLocaleString=i.prototype.toString,i.prototype.equals=function(t){if(!i.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===i.compare(this,t)},i.prototype.inspect=function(){var t="",n=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,n).replace(/(.{2})/g,"$1 ").trim(),this.length>n&&(t+=" ... "),"<Buffer "+t+">"},n&&(i.prototype[n]=i.prototype.inspect),i.prototype.compare=function(t,e,n,r,o){if(D(t,Uint8Array)&&(t=i.from(t,t.offset,t.byteLength)),!i.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+babelHelpers.typeof(t));if(void 0===e&&(e=0),void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===o&&(o=this.length),e<0||n>t.length||r<0||o>this.length)throw new RangeError("out of range index");if(r>=o&&e>=n)return 0;if(r>=o)return-1;if(e>=n)return 1;if(this===t)return 0;for(var u=(o>>>=0)-(r>>>=0),s=(n>>>=0)-(e>>>=0),a=Math.min(u,s),f=this.slice(r,o),c=t.slice(e,n),h=0;h<a;++h)if(f[h]!==c[h]){u=f[h],s=c[h];break}return u<s?-1:s<u?1:0},i.prototype.includes=function(t,e,n){return-1!==this.indexOf(t,e,n)},i.prototype.indexOf=function(t,e,n){return y(this,t,e,n,!0)},i.prototype.lastIndexOf=function(t,e,n){return y(this,t,e,n,!1)},i.prototype.write=function(t,e,n,r){if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"==typeof e)r=e,n=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e>>>=0,isFinite(n)?(n>>>=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var o=this.length-e;if((void 0===n||n>o)&&(n=o),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var i=!1;;)switch(r){case"hex":return m(this,t,e,n);case"utf8":case"utf-8":return w(this,t,e,n);case"ascii":case"latin1":case"binary":return b(this,t,e,n);case"base64":return v(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return _(this,t,e,n);default:if(i)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),i=!0}},i.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var S=4096;function N(t,e,n){var r="";n=Math.min(t.length,n);for(var o=e;o<n;++o)r+=String.fromCharCode(127&t[o]);return r}function x(t,e,n){var r="";n=Math.min(t.length,n);for(var o=e;o<n;++o)r+=String.fromCharCode(t[o]);return r}function I(t,e,n){var r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);for(var o="",i=e;i<n;++i)o+=C[t[i]];return o}function T(t,e,n){for(var r=t.slice(e,n),o="",i=0;i<r.length-1;i+=2)o+=String.fromCharCode(r[i]+256*r[i+1]);return o}function A(t,e,n){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>n)throw new RangeError("Trying to access beyond buffer length")}function B(t,e,n,r,o,u){if(!i.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>o||e<u)throw new RangeError('"value" argument is out of bounds');if(n+r>t.length)throw new RangeError("Index out of range")}function j(t,e,n,r,o,i){if(n+r>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function P(t,e,n,r,o){return e=+e,n>>>=0,o||j(t,0,n,4),Qt(t,e,n,r,23,4),n+4}function L(t,e,n,r,o){return e=+e,n>>>=0,o||j(t,0,n,8),Qt(t,e,n,r,52,8),n+8}i.prototype.slice=function(t,e){var n=this.length;(t=~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),(e=void 0===e?n:~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),e<t&&(e=t);var r=this.subarray(t,e);return Object.setPrototypeOf(r,i.prototype),r},i.prototype.readUintLE=i.prototype.readUIntLE=function(t,e,n){t>>>=0,e>>>=0,n||A(t,e,this.length);for(var r=this[t],o=1,i=0;++i<e&&(o*=256);)r+=this[t+i]*o;return r},i.prototype.readUintBE=i.prototype.readUIntBE=function(t,e,n){t>>>=0,e>>>=0,n||A(t,e,this.length);for(var r=this[t+--e],o=1;e>0&&(o*=256);)r+=this[t+--e]*o;return r},i.prototype.readUint8=i.prototype.readUInt8=function(t,e){return t>>>=0,e||A(t,1,this.length),this[t]},i.prototype.readUint16LE=i.prototype.readUInt16LE=function(t,e){return t>>>=0,e||A(t,2,this.length),this[t]|this[t+1]<<8},i.prototype.readUint16BE=i.prototype.readUInt16BE=function(t,e){return t>>>=0,e||A(t,2,this.length),this[t]<<8|this[t+1]},i.prototype.readUint32LE=i.prototype.readUInt32LE=function(t,e){return t>>>=0,e||A(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},i.prototype.readUint32BE=i.prototype.readUInt32BE=function(t,e){return t>>>=0,e||A(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},i.prototype.readIntLE=function(t,e,n){t>>>=0,e>>>=0,n||A(t,e,this.length);for(var r=this[t],o=1,i=0;++i<e&&(o*=256);)r+=this[t+i]*o;return r>=(o*=128)&&(r-=Math.pow(2,8*e)),r},i.prototype.readIntBE=function(t,e,n){t>>>=0,e>>>=0,n||A(t,e,this.length);for(var r=e,o=1,i=this[t+--r];r>0&&(o*=256);)i+=this[t+--r]*o;return i>=(o*=128)&&(i-=Math.pow(2,8*e)),i},i.prototype.readInt8=function(t,e){return t>>>=0,e||A(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},i.prototype.readInt16LE=function(t,e){t>>>=0,e||A(t,2,this.length);var n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},i.prototype.readInt16BE=function(t,e){t>>>=0,e||A(t,2,this.length);var n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},i.prototype.readInt32LE=function(t,e){return t>>>=0,e||A(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},i.prototype.readInt32BE=function(t,e){return t>>>=0,e||A(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},i.prototype.readFloatLE=function(t,e){return t>>>=0,e||A(t,4,this.length),Wt(this,t,!0,23,4)},i.prototype.readFloatBE=function(t,e){return t>>>=0,e||A(t,4,this.length),Wt(this,t,!1,23,4)},i.prototype.readDoubleLE=function(t,e){return t>>>=0,e||A(t,8,this.length),Wt(this,t,!0,52,8)},i.prototype.readDoubleBE=function(t,e){return t>>>=0,e||A(t,8,this.length),Wt(this,t,!1,52,8)},i.prototype.writeUintLE=i.prototype.writeUIntLE=function(t,e,n,r){t=+t,e>>>=0,n>>>=0,r||B(this,t,e,n,Math.pow(2,8*n)-1,0);var o=1,i=0;for(this[e]=255&t;++i<n&&(o*=256);)this[e+i]=t/o&255;return e+n},i.prototype.writeUintBE=i.prototype.writeUIntBE=function(t,e,n,r){t=+t,e>>>=0,n>>>=0,r||B(this,t,e,n,Math.pow(2,8*n)-1,0);var o=n-1,i=1;for(this[e+o]=255&t;--o>=0&&(i*=256);)this[e+o]=t/i&255;return e+n},i.prototype.writeUint8=i.prototype.writeUInt8=function(t,e,n){return t=+t,e>>>=0,n||B(this,t,e,1,255,0),this[e]=255&t,e+1},i.prototype.writeUint16LE=i.prototype.writeUInt16LE=function(t,e,n){return t=+t,e>>>=0,n||B(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},i.prototype.writeUint16BE=i.prototype.writeUInt16BE=function(t,e,n){return t=+t,e>>>=0,n||B(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},i.prototype.writeUint32LE=i.prototype.writeUInt32LE=function(t,e,n){return t=+t,e>>>=0,n||B(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},i.prototype.writeUint32BE=i.prototype.writeUInt32BE=function(t,e,n){return t=+t,e>>>=0,n||B(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},i.prototype.writeIntLE=function(t,e,n,r){if(t=+t,e>>>=0,!r){var o=Math.pow(2,8*n-1);B(this,t,e,n,o-1,-o)}var i=0,u=1,s=0;for(this[e]=255&t;++i<n&&(u*=256);)t<0&&0===s&&0!==this[e+i-1]&&(s=1),this[e+i]=(t/u|0)-s&255;return e+n},i.prototype.writeIntBE=function(t,e,n,r){if(t=+t,e>>>=0,!r){var o=Math.pow(2,8*n-1);B(this,t,e,n,o-1,-o)}var i=n-1,u=1,s=0;for(this[e+i]=255&t;--i>=0&&(u*=256);)t<0&&0===s&&0!==this[e+i+1]&&(s=1),this[e+i]=(t/u|0)-s&255;return e+n},i.prototype.writeInt8=function(t,e,n){return t=+t,e>>>=0,n||B(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},i.prototype.writeInt16LE=function(t,e,n){return t=+t,e>>>=0,n||B(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},i.prototype.writeInt16BE=function(t,e,n){return t=+t,e>>>=0,n||B(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},i.prototype.writeInt32LE=function(t,e,n){return t=+t,e>>>=0,n||B(this,t,e,4,2147483647,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},i.prototype.writeInt32BE=function(t,e,n){return t=+t,e>>>=0,n||B(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},i.prototype.writeFloatLE=function(t,e,n){return P(this,t,e,!0,n)},i.prototype.writeFloatBE=function(t,e,n){return P(this,t,e,!1,n)},i.prototype.writeDoubleLE=function(t,e,n){return L(this,t,e,!0,n)},i.prototype.writeDoubleBE=function(t,e,n){return L(this,t,e,!1,n)},i.prototype.copy=function(t,e,n,r){if(!i.isBuffer(t))throw new TypeError("argument should be a Buffer");if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);var o=r-n;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,n,r):Uint8Array.prototype.set.call(t,this.subarray(n,r),e),o},i.prototype.fill=function(t,e,n,r){if("string"==typeof t){if("string"==typeof e?(r=e,e=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!i.isEncoding(r))throw new TypeError("Unknown encoding: "+r);if(1===t.length){var o=t.charCodeAt(0);("utf8"===r&&o<128||"latin1"===r)&&(t=o)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;var u;if(e>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0),"number"==typeof t)for(u=e;u<n;++u)this[u]=t;else{var s=i.isBuffer(t)?t:i.from(t,r),a=s.length;if(0===a)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(u=0;u<n-e;++u)this[u+e]=s[u%a]}return this};var U=/[^+/0-9A-Za-z-_]/g;function $(t,e){var n;e=e||1/0;for(var r=t.length,o=null,i=[],u=0;u<r;++u){if((n=t.charCodeAt(u))>55295&&n<57344){if(!o){if(n>56319){(e-=3)>-1&&i.push(239,191,189);continue}if(u+1===r){(e-=3)>-1&&i.push(239,191,189);continue}o=n;continue}if(n<56320){(e-=3)>-1&&i.push(239,191,189),o=n;continue}n=65536+(o-55296<<10|n-56320)}else o&&(e-=3)>-1&&i.push(239,191,189);if(o=null,n<128){if((e-=1)<0)break;i.push(n)}else if(n<2048){if((e-=2)<0)break;i.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;i.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;i.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return i}function R(t){return Zt(function(t){if((t=(t=t.split("=")[0]).trim().replace(U,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function M(t,e,n,r){for(var o=0;o<r&&!(o+n>=e.length||o>=t.length);++o)e[o+n]=t[o];return o}function D(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}function q(t){return t!=t}var C=function(){for(var t="0123456789abcdef",e=new Array(256),n=0;n<16;++n)for(var r=16*n,o=0;o<16;++o)e[r+o]=t[n]+t[o];return e}()}(e={exports:{}},e.exports),e.exports}(),Xt=zt.Buffer;zt.SlowBuffer,zt.INSPECT_MAX_BYTES,zt.kMaxLength;var Kt=function(t,e){return Kt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},Kt(t,e)};function te(t,e){function n(){this.constructor=t}Kt(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var ee=function(t){function e(n){var r=t.call(this,n)||this;return Object.setPrototypeOf(r,e.prototype),r}return te(e,t),Object.defineProperty(e.prototype,"name",{get:function(){return"BSONError"},enumerable:!1,configurable:!0}),e}(Error),ne=function(t){function e(n){var r=t.call(this,n)||this;return Object.setPrototypeOf(r,e.prototype),r}return te(e,t),Object.defineProperty(e.prototype,"name",{get:function(){return"BSONTypeError"},enumerable:!1,configurable:!0}),e}(TypeError);function re(t){return t&&t.Math==Math&&t}function oe(){return re("object"==typeof globalThis&&globalThis)||re("object"==typeof window&&window)||re("object"==typeof self&&self)||re("object"==typeof n.g&&n.g)||Function("return this")()}var ie=function(t){var e,n="object"==typeof(e=oe()).navigator&&"ReactNative"===e.navigator.product?"BSON: For React Native please polyfill crypto.getRandomValues, e.g. using: https://www.npmjs.com/package/react-native-get-random-values.":"BSON: No cryptographic implementation for random bytes present, falling back to a less secure implementation.";console.warn(n);for(var r=Xt.alloc(t),o=0;o<t;++o)r[o]=Math.floor(256*Math.random());return r},ue=function(){if("undefined"!=typeof window){var t=window.crypto||window.msCrypto;if(t&&t.getRandomValues)return function(e){return t.getRandomValues(Xt.alloc(e))}}return void 0!==n.g&&n.g.crypto&&n.g.crypto.getRandomValues?function(t){return n.g.crypto.getRandomValues(Xt.alloc(t))}:ie}();function se(t){return"[object Uint8Array]"===Object.prototype.toString.call(t)}function ae(t){return"object"==typeof t&&null!==t}function fe(t,e){var n=!1;return function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return n||(console.warn(e),n=!0),t.apply(this,r)}}function ce(t){if(ArrayBuffer.isView(t))return Xt.from(t.buffer,t.byteOffset,t.byteLength);if(e=t,["[object ArrayBuffer]","[object SharedArrayBuffer]"].includes(Object.prototype.toString.call(e)))return Xt.from(t);var e;throw new ne("Must use either Buffer or TypedArray")}var he=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|[0-9a-f]{12}4[0-9a-f]{3}[89ab][0-9a-f]{15})$/i,pe=function(t){return"string"==typeof t&&he.test(t)},le=function(t){if(!pe(t))throw new ne('UUID string representations must be a 32 or 36 character hex string (dashes excluded/included). Format: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" or "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx".');var e=t.replace(/-/g,"");return Xt.from(e,"hex")},de=function(t,e){return void 0===e&&(e=!0),e?t.toString("hex",0,4)+"-"+t.toString("hex",4,6)+"-"+t.toString("hex",6,8)+"-"+t.toString("hex",8,10)+"-"+t.toString("hex",10,16):t.toString("hex")},ye=(Math.pow(2,63),Math.pow(2,63),Math.pow(2,53)),ge=-Math.pow(2,53),me=function(){function t(e,n){if(!(this instanceof t))return new t(e,n);if(!(null==e||"string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer||Array.isArray(e)))throw new ne("Binary can only be constructed from string, Buffer, TypedArray, or Array<number>");this.sub_type=null!=n?n:t.BSON_BINARY_SUBTYPE_DEFAULT,null==e?(this.buffer=Xt.alloc(t.BUFFER_SIZE),this.position=0):("string"==typeof e?this.buffer=Xt.from(e,"binary"):Array.isArray(e)?this.buffer=Xt.from(e):this.buffer=ce(e),this.position=this.buffer.byteLength)}return t.prototype.put=function(e){if("string"==typeof e&&1!==e.length)throw new ne("only accepts single character String");if("number"!=typeof e&&1!==e.length)throw new ne("only accepts single character Uint8Array or Array");var n;if((n="string"==typeof e?e.charCodeAt(0):"number"==typeof e?e:e[0])<0||n>255)throw new ne("only accepts number in a valid unsigned byte range 0-255");if(this.buffer.length>this.position)this.buffer[this.position++]=n;else{var r=Xt.alloc(t.BUFFER_SIZE+this.buffer.length);this.buffer.copy(r,0,0,this.buffer.length),this.buffer=r,this.buffer[this.position++]=n}},t.prototype.write=function(t,e){if(e="number"==typeof e?e:this.position,this.buffer.length<e+t.length){var n=Xt.alloc(this.buffer.length+t.length);this.buffer.copy(n,0,0,this.buffer.length),this.buffer=n}ArrayBuffer.isView(t)?(this.buffer.set(ce(t),e),this.position=e+t.byteLength>this.position?e+t.length:this.position):"string"==typeof t&&(this.buffer.write(t,e,t.length,"binary"),this.position=e+t.length>this.position?e+t.length:this.position)},t.prototype.read=function(t,e){return e=e&&e>0?e:this.position,this.buffer.slice(t,t+e)},t.prototype.value=function(t){return(t=!!t)&&this.buffer.length===this.position?this.buffer:t?this.buffer.slice(0,this.position):this.buffer.toString("binary",0,this.position)},t.prototype.length=function(){return this.position},t.prototype.toJSON=function(){return this.buffer.toString("base64")},t.prototype.toString=function(t){return this.buffer.toString(t)},t.prototype.toExtendedJSON=function(t){t=t||{};var e=this.buffer.toString("base64"),n=Number(this.sub_type).toString(16);return t.legacy?{$binary:e,$type:1===n.length?"0"+n:n}:{$binary:{base64:e,subType:1===n.length?"0"+n:n}}},t.prototype.toUUID=function(){if(this.sub_type===t.SUBTYPE_UUID)return new we(this.buffer.slice(0,this.position));throw new ee('Binary sub_type "'.concat(this.sub_type,'" is not supported for converting to UUID. Only "').concat(t.SUBTYPE_UUID,'" is currently supported.'))},t.fromExtendedJSON=function(e,n){var r,o;if(n=n||{},"$binary"in e?n.legacy&&"string"==typeof e.$binary&&"$type"in e?(o=e.$type?parseInt(e.$type,16):0,r=Xt.from(e.$binary,"base64")):"string"!=typeof e.$binary&&(o=e.$binary.subType?parseInt(e.$binary.subType,16):0,r=Xt.from(e.$binary.base64,"base64")):"$uuid"in e&&(o=4,r=le(e.$uuid)),!r)throw new ne("Unexpected Binary Extended JSON format ".concat(JSON.stringify(e)));return 4===o?new we(r):new t(r,o)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){var t=this.value(!0);return'new Binary(Buffer.from("'.concat(t.toString("hex"),'", "hex"), ').concat(this.sub_type,")")},t.BSON_BINARY_SUBTYPE_DEFAULT=0,t.BUFFER_SIZE=256,t.SUBTYPE_DEFAULT=0,t.SUBTYPE_FUNCTION=1,t.SUBTYPE_BYTE_ARRAY=2,t.SUBTYPE_UUID_OLD=3,t.SUBTYPE_UUID=4,t.SUBTYPE_MD5=5,t.SUBTYPE_ENCRYPTED=6,t.SUBTYPE_COLUMN=7,t.SUBTYPE_USER_DEFINED=128,t}();Object.defineProperty(me.prototype,"_bsontype",{value:"Binary"});var we=function(t){function e(n){var r,o,i=this;if(null==n)r=e.generate();else if(n instanceof e)r=Xt.from(n.buffer),o=n.__id;else if(ArrayBuffer.isView(n)&&16===n.byteLength)r=ce(n);else{if("string"!=typeof n)throw new ne("Argument passed in UUID constructor must be a UUID, a 16 byte Buffer or a 32/36 character hex string (dashes excluded/included, format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx).");r=le(n)}return(i=t.call(this,r,4)||this).__id=o,i}return te(e,t),Object.defineProperty(e.prototype,"id",{get:function(){return this.buffer},set:function(t){this.buffer=t,e.cacheHexString&&(this.__id=de(t))},enumerable:!1,configurable:!0}),e.prototype.toHexString=function(t){if(void 0===t&&(t=!0),e.cacheHexString&&this.__id)return this.__id;var n=de(this.id,t);return e.cacheHexString&&(this.__id=n),n},e.prototype.toString=function(t){return t?this.id.toString(t):this.toHexString()},e.prototype.toJSON=function(){return this.toHexString()},e.prototype.equals=function(t){if(!t)return!1;if(t instanceof e)return t.id.equals(this.id);try{return new e(t).id.equals(this.id)}catch(t){return!1}},e.prototype.toBinary=function(){return new me(this.id,me.SUBTYPE_UUID)},e.generate=function(){var t=ue(16);return t[6]=15&t[6]|64,t[8]=63&t[8]|128,Xt.from(t)},e.isValid=function(t){return!!t&&(t instanceof e||("string"==typeof t?pe(t):!(!se(t)||16!==t.length||64!=(240&t[6])||128&~t[8])))},e.createFromHexString=function(t){return new e(le(t))},e.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},e.prototype.inspect=function(){return'new UUID("'.concat(this.toHexString(),'")')},e}(me),be=function(){function t(e,n){if(!(this instanceof t))return new t(e,n);this.code=e,this.scope=n}return t.prototype.toJSON=function(){return{code:this.code,scope:this.scope}},t.prototype.toExtendedJSON=function(){return this.scope?{$code:this.code,$scope:this.scope}:{$code:this.code}},t.fromExtendedJSON=function(e){return new t(e.$code,e.$scope)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){var t=this.toJSON();return'new Code("'.concat(String(t.code),'"').concat(t.scope?", ".concat(JSON.stringify(t.scope)):"",")")},t}();Object.defineProperty(be.prototype,"_bsontype",{value:"Code"});var ve=function(){function t(e,n,r,o){if(!(this instanceof t))return new t(e,n,r,o);var i=e.split(".");2===i.length&&(r=i.shift(),e=i.shift()),this.collection=e,this.oid=n,this.db=r,this.fields=o||{}}return Object.defineProperty(t.prototype,"namespace",{get:function(){return this.collection},set:function(t){this.collection=t},enumerable:!1,configurable:!0}),t.prototype.toJSON=function(){var t=Object.assign({$ref:this.collection,$id:this.oid},this.fields);return null!=this.db&&(t.$db=this.db),t},t.prototype.toExtendedJSON=function(t){t=t||{};var e={$ref:this.collection,$id:this.oid};return t.legacy?e:(this.db&&(e.$db=this.db),e=Object.assign(e,this.fields))},t.fromExtendedJSON=function(e){var n=Object.assign({},e);return delete n.$ref,delete n.$id,delete n.$db,new t(e.$ref,e.$id,e.$db,n)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){var t=void 0===this.oid||void 0===this.oid.toString?this.oid:this.oid.toString();return'new DBRef("'.concat(this.namespace,'", new ObjectId("').concat(String(t),'")').concat(this.db?', "'.concat(this.db,'"'):"",")")},t}();Object.defineProperty(ve.prototype,"_bsontype",{value:"DBRef"});var _e=void 0;try{_e=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(r){}var Ee=4294967296,Oe=0x10000000000000000,Se=Oe/2,Ne={},xe={},Ie=function(){function t(e,n,r){if(void 0===e&&(e=0),!(this instanceof t))return new t(e,n,r);"bigint"==typeof e?Object.assign(this,t.fromBigInt(e,!!n)):"string"==typeof e?Object.assign(this,t.fromString(e,!!n)):(this.low=0|e,this.high=0|n,this.unsigned=!!r),Object.defineProperty(this,"__isLong__",{value:!0,configurable:!1,writable:!1,enumerable:!1})}return t.fromBits=function(e,n,r){return new t(e,n,r)},t.fromInt=function(e,n){var r,o,i;return n?(i=0<=(e>>>=0)&&e<256)&&(o=xe[e])?o:(r=t.fromBits(e,(0|e)<0?-1:0,!0),i&&(xe[e]=r),r):(i=-128<=(e|=0)&&e<128)&&(o=Ne[e])?o:(r=t.fromBits(e,e<0?-1:0,!1),i&&(Ne[e]=r),r)},t.fromNumber=function(e,n){if(isNaN(e))return n?t.UZERO:t.ZERO;if(n){if(e<0)return t.UZERO;if(e>=Oe)return t.MAX_UNSIGNED_VALUE}else{if(e<=-Se)return t.MIN_VALUE;if(e+1>=Se)return t.MAX_VALUE}return e<0?t.fromNumber(-e,n).neg():t.fromBits(e%Ee|0,e/Ee|0,n)},t.fromBigInt=function(e,n){return t.fromString(e.toString(),n)},t.fromString=function(e,n,r){if(0===e.length)throw Error("empty string");if("NaN"===e||"Infinity"===e||"+Infinity"===e||"-Infinity"===e)return t.ZERO;if("number"==typeof n?(r=n,n=!1):n=!!n,(r=r||10)<2||36<r)throw RangeError("radix");var o;if((o=e.indexOf("-"))>0)throw Error("interior hyphen");if(0===o)return t.fromString(e.substring(1),n,r).neg();for(var i=t.fromNumber(Math.pow(r,8)),u=t.ZERO,s=0;s<e.length;s+=8){var a=Math.min(8,e.length-s),f=parseInt(e.substring(s,s+a),r);if(a<8){var c=t.fromNumber(Math.pow(r,a));u=u.mul(c).add(t.fromNumber(f))}else u=(u=u.mul(i)).add(t.fromNumber(f))}return u.unsigned=n,u},t.fromBytes=function(e,n,r){return r?t.fromBytesLE(e,n):t.fromBytesBE(e,n)},t.fromBytesLE=function(e,n){return new t(e[0]|e[1]<<8|e[2]<<16|e[3]<<24,e[4]|e[5]<<8|e[6]<<16|e[7]<<24,n)},t.fromBytesBE=function(e,n){return new t(e[4]<<24|e[5]<<16|e[6]<<8|e[7],e[0]<<24|e[1]<<16|e[2]<<8|e[3],n)},t.isLong=function(t){return ae(t)&&!0===t.__isLong__},t.fromValue=function(e,n){return"number"==typeof e?t.fromNumber(e,n):"string"==typeof e?t.fromString(e,n):t.fromBits(e.low,e.high,"boolean"==typeof n?n:e.unsigned)},t.prototype.add=function(e){t.isLong(e)||(e=t.fromValue(e));var n=this.high>>>16,r=65535&this.high,o=this.low>>>16,i=65535&this.low,u=e.high>>>16,s=65535&e.high,a=e.low>>>16,f=0,c=0,h=0,p=0;return h+=(p+=i+(65535&e.low))>>>16,p&=65535,c+=(h+=o+a)>>>16,h&=65535,f+=(c+=r+s)>>>16,c&=65535,f+=n+u,f&=65535,t.fromBits(h<<16|p,f<<16|c,this.unsigned)},t.prototype.and=function(e){return t.isLong(e)||(e=t.fromValue(e)),t.fromBits(this.low&e.low,this.high&e.high,this.unsigned)},t.prototype.compare=function(e){if(t.isLong(e)||(e=t.fromValue(e)),this.eq(e))return 0;var n=this.isNegative(),r=e.isNegative();return n&&!r?-1:!n&&r?1:this.unsigned?e.high>>>0>this.high>>>0||e.high===this.high&&e.low>>>0>this.low>>>0?-1:1:this.sub(e).isNegative()?-1:1},t.prototype.comp=function(t){return this.compare(t)},t.prototype.divide=function(e){if(t.isLong(e)||(e=t.fromValue(e)),e.isZero())throw Error("division by zero");if(_e){if(!this.unsigned&&-2147483648===this.high&&-1===e.low&&-1===e.high)return this;var n=(this.unsigned?_e.div_u:_e.div_s)(this.low,this.high,e.low,e.high);return t.fromBits(n,_e.get_high(),this.unsigned)}if(this.isZero())return this.unsigned?t.UZERO:t.ZERO;var r,o,i;if(this.unsigned){if(e.unsigned||(e=e.toUnsigned()),e.gt(this))return t.UZERO;if(e.gt(this.shru(1)))return t.UONE;i=t.UZERO}else{if(this.eq(t.MIN_VALUE))return e.eq(t.ONE)||e.eq(t.NEG_ONE)?t.MIN_VALUE:e.eq(t.MIN_VALUE)?t.ONE:(r=this.shr(1).div(e).shl(1)).eq(t.ZERO)?e.isNegative()?t.ONE:t.NEG_ONE:(o=this.sub(e.mul(r)),i=r.add(o.div(e)));if(e.eq(t.MIN_VALUE))return this.unsigned?t.UZERO:t.ZERO;if(this.isNegative())return e.isNegative()?this.neg().div(e.neg()):this.neg().div(e).neg();if(e.isNegative())return this.div(e.neg()).neg();i=t.ZERO}for(o=this;o.gte(e);){r=Math.max(1,Math.floor(o.toNumber()/e.toNumber()));for(var u=Math.ceil(Math.log(r)/Math.LN2),s=u<=48?1:Math.pow(2,u-48),a=t.fromNumber(r),f=a.mul(e);f.isNegative()||f.gt(o);)r-=s,f=(a=t.fromNumber(r,this.unsigned)).mul(e);a.isZero()&&(a=t.ONE),i=i.add(a),o=o.sub(f)}return i},t.prototype.div=function(t){return this.divide(t)},t.prototype.equals=function(e){return t.isLong(e)||(e=t.fromValue(e)),(this.unsigned===e.unsigned||this.high>>>31!=1||e.high>>>31!=1)&&this.high===e.high&&this.low===e.low},t.prototype.eq=function(t){return this.equals(t)},t.prototype.getHighBits=function(){return this.high},t.prototype.getHighBitsUnsigned=function(){return this.high>>>0},t.prototype.getLowBits=function(){return this.low},t.prototype.getLowBitsUnsigned=function(){return this.low>>>0},t.prototype.getNumBitsAbs=function(){if(this.isNegative())return this.eq(t.MIN_VALUE)?64:this.neg().getNumBitsAbs();var e,n=0!==this.high?this.high:this.low;for(e=31;e>0&&!(n&1<<e);e--);return 0!==this.high?e+33:e+1},t.prototype.greaterThan=function(t){return this.comp(t)>0},t.prototype.gt=function(t){return this.greaterThan(t)},t.prototype.greaterThanOrEqual=function(t){return this.comp(t)>=0},t.prototype.gte=function(t){return this.greaterThanOrEqual(t)},t.prototype.ge=function(t){return this.greaterThanOrEqual(t)},t.prototype.isEven=function(){return!(1&this.low)},t.prototype.isNegative=function(){return!this.unsigned&&this.high<0},t.prototype.isOdd=function(){return!(1&~this.low)},t.prototype.isPositive=function(){return this.unsigned||this.high>=0},t.prototype.isZero=function(){return 0===this.high&&0===this.low},t.prototype.lessThan=function(t){return this.comp(t)<0},t.prototype.lt=function(t){return this.lessThan(t)},t.prototype.lessThanOrEqual=function(t){return this.comp(t)<=0},t.prototype.lte=function(t){return this.lessThanOrEqual(t)},t.prototype.modulo=function(e){if(t.isLong(e)||(e=t.fromValue(e)),_e){var n=(this.unsigned?_e.rem_u:_e.rem_s)(this.low,this.high,e.low,e.high);return t.fromBits(n,_e.get_high(),this.unsigned)}return this.sub(this.div(e).mul(e))},t.prototype.mod=function(t){return this.modulo(t)},t.prototype.rem=function(t){return this.modulo(t)},t.prototype.multiply=function(e){if(this.isZero())return t.ZERO;if(t.isLong(e)||(e=t.fromValue(e)),_e){var n=_e.mul(this.low,this.high,e.low,e.high);return t.fromBits(n,_e.get_high(),this.unsigned)}if(e.isZero())return t.ZERO;if(this.eq(t.MIN_VALUE))return e.isOdd()?t.MIN_VALUE:t.ZERO;if(e.eq(t.MIN_VALUE))return this.isOdd()?t.MIN_VALUE:t.ZERO;if(this.isNegative())return e.isNegative()?this.neg().mul(e.neg()):this.neg().mul(e).neg();if(e.isNegative())return this.mul(e.neg()).neg();if(this.lt(t.TWO_PWR_24)&&e.lt(t.TWO_PWR_24))return t.fromNumber(this.toNumber()*e.toNumber(),this.unsigned);var r=this.high>>>16,o=65535&this.high,i=this.low>>>16,u=65535&this.low,s=e.high>>>16,a=65535&e.high,f=e.low>>>16,c=65535&e.low,h=0,p=0,l=0,d=0;return l+=(d+=u*c)>>>16,d&=65535,p+=(l+=i*c)>>>16,l&=65535,p+=(l+=u*f)>>>16,l&=65535,h+=(p+=o*c)>>>16,p&=65535,h+=(p+=i*f)>>>16,p&=65535,h+=(p+=u*a)>>>16,p&=65535,h+=r*c+o*f+i*a+u*s,h&=65535,t.fromBits(l<<16|d,h<<16|p,this.unsigned)},t.prototype.mul=function(t){return this.multiply(t)},t.prototype.negate=function(){return!this.unsigned&&this.eq(t.MIN_VALUE)?t.MIN_VALUE:this.not().add(t.ONE)},t.prototype.neg=function(){return this.negate()},t.prototype.not=function(){return t.fromBits(~this.low,~this.high,this.unsigned)},t.prototype.notEquals=function(t){return!this.equals(t)},t.prototype.neq=function(t){return this.notEquals(t)},t.prototype.ne=function(t){return this.notEquals(t)},t.prototype.or=function(e){return t.isLong(e)||(e=t.fromValue(e)),t.fromBits(this.low|e.low,this.high|e.high,this.unsigned)},t.prototype.shiftLeft=function(e){return t.isLong(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?t.fromBits(this.low<<e,this.high<<e|this.low>>>32-e,this.unsigned):t.fromBits(0,this.low<<e-32,this.unsigned)},t.prototype.shl=function(t){return this.shiftLeft(t)},t.prototype.shiftRight=function(e){return t.isLong(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?t.fromBits(this.low>>>e|this.high<<32-e,this.high>>e,this.unsigned):t.fromBits(this.high>>e-32,this.high>=0?0:-1,this.unsigned)},t.prototype.shr=function(t){return this.shiftRight(t)},t.prototype.shiftRightUnsigned=function(e){if(t.isLong(e)&&(e=e.toInt()),0==(e&=63))return this;var n=this.high;if(e<32){var r=this.low;return t.fromBits(r>>>e|n<<32-e,n>>>e,this.unsigned)}return 32===e?t.fromBits(n,0,this.unsigned):t.fromBits(n>>>e-32,0,this.unsigned)},t.prototype.shr_u=function(t){return this.shiftRightUnsigned(t)},t.prototype.shru=function(t){return this.shiftRightUnsigned(t)},t.prototype.subtract=function(e){return t.isLong(e)||(e=t.fromValue(e)),this.add(e.neg())},t.prototype.sub=function(t){return this.subtract(t)},t.prototype.toInt=function(){return this.unsigned?this.low>>>0:this.low},t.prototype.toNumber=function(){return this.unsigned?(this.high>>>0)*Ee+(this.low>>>0):this.high*Ee+(this.low>>>0)},t.prototype.toBigInt=function(){return BigInt(this.toString())},t.prototype.toBytes=function(t){return t?this.toBytesLE():this.toBytesBE()},t.prototype.toBytesLE=function(){var t=this.high,e=this.low;return[255&e,e>>>8&255,e>>>16&255,e>>>24,255&t,t>>>8&255,t>>>16&255,t>>>24]},t.prototype.toBytesBE=function(){var t=this.high,e=this.low;return[t>>>24,t>>>16&255,t>>>8&255,255&t,e>>>24,e>>>16&255,e>>>8&255,255&e]},t.prototype.toSigned=function(){return this.unsigned?t.fromBits(this.low,this.high,!1):this},t.prototype.toString=function(e){if((e=e||10)<2||36<e)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(this.eq(t.MIN_VALUE)){var n=t.fromNumber(e),r=this.div(n),o=r.mul(n).sub(this);return r.toString(e)+o.toInt().toString(e)}return"-"+this.neg().toString(e)}for(var i=t.fromNumber(Math.pow(e,6),this.unsigned),u=this,s="";;){var a=u.div(i),f=(u.sub(a.mul(i)).toInt()>>>0).toString(e);if((u=a).isZero())return f+s;for(;f.length<6;)f="0"+f;s=""+f+s}},t.prototype.toUnsigned=function(){return this.unsigned?this:t.fromBits(this.low,this.high,!0)},t.prototype.xor=function(e){return t.isLong(e)||(e=t.fromValue(e)),t.fromBits(this.low^e.low,this.high^e.high,this.unsigned)},t.prototype.eqz=function(){return this.isZero()},t.prototype.le=function(t){return this.lessThanOrEqual(t)},t.prototype.toExtendedJSON=function(t){return t&&t.relaxed?this.toNumber():{$numberLong:this.toString()}},t.fromExtendedJSON=function(e,n){var r=t.fromString(e.$numberLong);return n&&n.relaxed?r.toNumber():r},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){return'new Long("'.concat(this.toString(),'"').concat(this.unsigned?", true":"",")")},t.TWO_PWR_24=t.fromInt(16777216),t.MAX_UNSIGNED_VALUE=t.fromBits(-1,-1,!0),t.ZERO=t.fromInt(0),t.UZERO=t.fromInt(0,!0),t.ONE=t.fromInt(1),t.UONE=t.fromInt(1,!0),t.NEG_ONE=t.fromInt(-1),t.MAX_VALUE=t.fromBits(-1,2147483647,!1),t.MIN_VALUE=t.fromBits(0,-2147483648,!1),t}();Object.defineProperty(Ie.prototype,"__isLong__",{value:!0}),Object.defineProperty(Ie.prototype,"_bsontype",{value:"Long"});var Te=/^(\+|-)?(\d+|(\d*\.\d*))?(E|e)?([-+])?(\d+)?$/,Ae=/^(\+|-)?(Infinity|inf)$/i,Be=/^(\+|-)?NaN$/i,je=6111,Pe=-6176,Le=[124,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].reverse(),Ue=[248,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].reverse(),$e=[120,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].reverse(),Re=/^([-+])?(\d+)?$/;function Me(t){return!isNaN(parseInt(t,10))}function De(t){var e=Ie.fromNumber(1e9),n=Ie.fromNumber(0);if(!(t.parts[0]||t.parts[1]||t.parts[2]||t.parts[3]))return{quotient:t,rem:n};for(var r=0;r<=3;r++)n=(n=n.shiftLeft(32)).add(new Ie(t.parts[r],0)),t.parts[r]=n.div(e).low,n=n.modulo(e);return{quotient:t,rem:n}}function qe(t,e){throw new ne('"'.concat(t,'" is not a valid Decimal128 string - ').concat(e))}var Ce=function(){function t(e){if(!(this instanceof t))return new t(e);if("string"==typeof e)this.bytes=t.fromString(e).bytes;else{if(!se(e))throw new ne("Decimal128 must take a Buffer or string");if(16!==e.byteLength)throw new ne("Decimal128 must take a Buffer of 16 bytes");this.bytes=e}}return t.fromString=function(e){var n,r=!1,o=!1,i=!1,u=0,s=0,a=0,f=0,c=0,h=[0],p=0,l=0,d=0,y=0,g=0,m=0,w=new Ie(0,0),b=new Ie(0,0),v=0;if(e.length>=7e3)throw new ne(e+" not a valid Decimal128 string");var _=e.match(Te),E=e.match(Ae),O=e.match(Be);if(!_&&!E&&!O||0===e.length)throw new ne(e+" not a valid Decimal128 string");if(_){var S=_[2],N=_[4],x=_[5],I=_[6];N&&void 0===I&&qe(e,"missing exponent power"),N&&void 0===S&&qe(e,"missing exponent base"),void 0===N&&(x||I)&&qe(e,"missing e before exponent")}if("+"!==e[v]&&"-"!==e[v]||(r="-"===e[v++]),!Me(e[v])&&"."!==e[v]){if("i"===e[v]||"I"===e[v])return new t(Xt.from(r?Ue:$e));if("N"===e[v])return new t(Xt.from(Le))}for(;Me(e[v])||"."===e[v];)"."!==e[v]?(p<34&&("0"!==e[v]||i)&&(i||(c=s),i=!0,h[l++]=parseInt(e[v],10),p+=1),i&&(a+=1),o&&(f+=1),s+=1,v+=1):(o&&qe(e,"contains multiple periods"),o=!0,v+=1);if(o&&!s)throw new ne(e+" not a valid Decimal128 string");if("e"===e[v]||"E"===e[v]){var T=e.substr(++v).match(Re);if(!T||!T[2])return new t(Xt.from(Le));g=parseInt(T[0],10),v+=T[0].length}if(e[v])return new t(Xt.from(Le));if(d=0,p){if(y=p-1,1!==(u=a))for(;0===h[c+u-1];)u-=1}else d=0,y=0,h[0]=0,a=1,p=1,u=0;for(g<=f&&f-g>16384?g=Pe:g-=f;g>je;){if((y+=1)-d>34){if(h.join("").match(/^0+$/)){g=je;break}qe(e,"overflow")}g-=1}for(;g<Pe||p<a;){if(0===y&&u<p){g=Pe,u=0;break}if(p<a?a-=1:y-=1,g<je)g+=1;else{if(h.join("").match(/^0+$/)){g=je;break}qe(e,"overflow")}}if(y-d+1<u){var A=s;o&&(c+=1,A+=1),r&&(c+=1,A+=1);var B=parseInt(e[c+y+1],10),j=0;if(B>=5&&(j=1,5===B))for(j=h[y]%2==1?1:0,m=c+y+2;m<A;m++)if(parseInt(e[m],10)){j=1;break}if(j)for(var P=y;P>=0;P--)if(++h[P]>9&&(h[P]=0,0===P)){if(!(g<je))return new t(Xt.from(r?Ue:$e));g+=1,h[P]=1}}if(w=Ie.fromNumber(0),b=Ie.fromNumber(0),0===u)w=Ie.fromNumber(0),b=Ie.fromNumber(0);else if(y-d<17)for(P=d,b=Ie.fromNumber(h[P++]),w=new Ie(0,0);P<=y;P++)b=(b=b.multiply(Ie.fromNumber(10))).add(Ie.fromNumber(h[P]));else{for(P=d,w=Ie.fromNumber(h[P++]);P<=y-17;P++)w=(w=w.multiply(Ie.fromNumber(10))).add(Ie.fromNumber(h[P]));for(b=Ie.fromNumber(h[P++]);P<=y;P++)b=(b=b.multiply(Ie.fromNumber(10))).add(Ie.fromNumber(h[P]))}var L,U,$,R,M=function(t,e){if(!t&&!e)return{high:Ie.fromNumber(0),low:Ie.fromNumber(0)};var n=t.shiftRightUnsigned(32),r=new Ie(t.getLowBits(),0),o=e.shiftRightUnsigned(32),i=new Ie(e.getLowBits(),0),u=n.multiply(o),s=n.multiply(i),a=r.multiply(o),f=r.multiply(i);return u=u.add(s.shiftRightUnsigned(32)),s=new Ie(s.getLowBits(),0).add(a).add(f.shiftRightUnsigned(32)),{high:u=u.add(s.shiftRightUnsigned(32)),low:f=s.shiftLeft(32).add(new Ie(f.getLowBits(),0))}}(w,Ie.fromString("100000000000000000"));M.low=M.low.add(b),U=b,(($=(L=M.low).high>>>0)<(R=U.high>>>0)||$===R&&L.low>>>0<U.low>>>0)&&(M.high=M.high.add(Ie.fromNumber(1))),n=g+6176;var D={low:Ie.fromNumber(0),high:Ie.fromNumber(0)};M.high.shiftRightUnsigned(49).and(Ie.fromNumber(1)).equals(Ie.fromNumber(1))?(D.high=D.high.or(Ie.fromNumber(3).shiftLeft(61)),D.high=D.high.or(Ie.fromNumber(n).and(Ie.fromNumber(16383).shiftLeft(47))),D.high=D.high.or(M.high.and(Ie.fromNumber(0x7fffffffffff)))):(D.high=D.high.or(Ie.fromNumber(16383&n).shiftLeft(49)),D.high=D.high.or(M.high.and(Ie.fromNumber(562949953421311)))),D.low=M.low,r&&(D.high=D.high.or(Ie.fromString("9223372036854775808")));var q=Xt.alloc(16);return v=0,q[v++]=255&D.low.low,q[v++]=D.low.low>>8&255,q[v++]=D.low.low>>16&255,q[v++]=D.low.low>>24&255,q[v++]=255&D.low.high,q[v++]=D.low.high>>8&255,q[v++]=D.low.high>>16&255,q[v++]=D.low.high>>24&255,q[v++]=255&D.high.low,q[v++]=D.high.low>>8&255,q[v++]=D.high.low>>16&255,q[v++]=D.high.low>>24&255,q[v++]=255&D.high.high,q[v++]=D.high.high>>8&255,q[v++]=D.high.high>>16&255,q[v++]=D.high.high>>24&255,new t(q)},t.prototype.toString=function(){for(var t,e=0,n=new Array(36),r=0;r<n.length;r++)n[r]=0;var o,i,u,s=0,a=!1,f={parts:[0,0,0,0]},c=[];s=0;var h=this.bytes,p=h[s++]|h[s++]<<8|h[s++]<<16|h[s++]<<24,l=h[s++]|h[s++]<<8|h[s++]<<16|h[s++]<<24,d=h[s++]|h[s++]<<8|h[s++]<<16|h[s++]<<24,y=h[s++]|h[s++]<<8|h[s++]<<16|h[s++]<<24;s=0,(new Ie(p,l),new Ie(d,y)).lessThan(Ie.ZERO)&&c.push("-");var g=y>>26&31;if(g>>3==3){if(30===g)return c.join("")+"Infinity";if(31===g)return"NaN";t=y>>15&16383,o=8+(y>>14&1)}else o=y>>14&7,t=y>>17&16383;var m=t-6176;if(f.parts[0]=(16383&y)+((15&o)<<14),f.parts[1]=d,f.parts[2]=l,f.parts[3]=p,0===f.parts[0]&&0===f.parts[1]&&0===f.parts[2]&&0===f.parts[3])a=!0;else for(u=3;u>=0;u--){var w=0,b=De(f);if(f=b.quotient,w=b.rem.low)for(i=8;i>=0;i--)n[9*u+i]=w%10,w=Math.floor(w/10)}if(a)e=1,n[s]=0;else for(e=36;!n[s];)e-=1,s+=1;var v=e-1+m;if(v>=34||v<=-7||m>0){if(e>34)return c.push("".concat(0)),m>0?c.push("E+".concat(m)):m<0&&c.push("E".concat(m)),c.join("");for(c.push("".concat(n[s++])),(e-=1)&&c.push("."),r=0;r<e;r++)c.push("".concat(n[s++]));c.push("E"),v>0?c.push("+".concat(v)):c.push("".concat(v))}else if(m>=0)for(r=0;r<e;r++)c.push("".concat(n[s++]));else{var _=e+m;if(_>0)for(r=0;r<_;r++)c.push("".concat(n[s++]));else c.push("0");for(c.push(".");_++<0;)c.push("0");for(r=0;r<e-Math.max(_-1,0);r++)c.push("".concat(n[s++]))}return c.join("")},t.prototype.toJSON=function(){return{$numberDecimal:this.toString()}},t.prototype.toExtendedJSON=function(){return{$numberDecimal:this.toString()}},t.fromExtendedJSON=function(e){return t.fromString(e.$numberDecimal)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){return'new Decimal128("'.concat(this.toString(),'")')},t}();Object.defineProperty(Ce.prototype,"_bsontype",{value:"Decimal128"});var ke=function(){function t(e){if(!(this instanceof t))return new t(e);e instanceof Number&&(e=e.valueOf()),this.value=+e}return t.prototype.valueOf=function(){return this.value},t.prototype.toJSON=function(){return this.value},t.prototype.toString=function(t){return this.value.toString(t)},t.prototype.toExtendedJSON=function(t){return t&&(t.legacy||t.relaxed&&isFinite(this.value))?this.value:Object.is(Math.sign(this.value),-0)?{$numberDouble:"-".concat(this.value.toFixed(1))}:{$numberDouble:Number.isInteger(this.value)?this.value.toFixed(1):this.value.toString()}},t.fromExtendedJSON=function(e,n){var r=parseFloat(e.$numberDouble);return n&&n.relaxed?r:new t(r)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){var t=this.toExtendedJSON();return"new Double(".concat(t.$numberDouble,")")},t}();Object.defineProperty(ke.prototype,"_bsontype",{value:"Double"});var Je=function(){function t(e){if(!(this instanceof t))return new t(e);e instanceof Number&&(e=e.valueOf()),this.value=0|+e}return t.prototype.valueOf=function(){return this.value},t.prototype.toString=function(t){return this.value.toString(t)},t.prototype.toJSON=function(){return this.value},t.prototype.toExtendedJSON=function(t){return t&&(t.relaxed||t.legacy)?this.value:{$numberInt:this.value.toString()}},t.fromExtendedJSON=function(e,n){return n&&n.relaxed?parseInt(e.$numberInt,10):new t(e.$numberInt)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){return"new Int32(".concat(this.valueOf(),")")},t}();Object.defineProperty(Je.prototype,"_bsontype",{value:"Int32"});var Fe=function(){function t(){if(!(this instanceof t))return new t}return t.prototype.toExtendedJSON=function(){return{$maxKey:1}},t.fromExtendedJSON=function(){return new t},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){return"new MaxKey()"},t}();Object.defineProperty(Fe.prototype,"_bsontype",{value:"MaxKey"});var Ve=function(){function t(){if(!(this instanceof t))return new t}return t.prototype.toExtendedJSON=function(){return{$minKey:1}},t.fromExtendedJSON=function(){return new t},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){return"new MinKey()"},t}();Object.defineProperty(Ve.prototype,"_bsontype",{value:"MinKey"});var He=new RegExp("^[0-9a-fA-F]{24}$"),Ge=null,Ze=Symbol("id"),Ye=function(){function t(e){if(!(this instanceof t))return new t(e);var n;if("object"==typeof e&&e&&"id"in e){if("string"!=typeof e.id&&!ArrayBuffer.isView(e.id))throw new ne("Argument passed in must have an id that is of type string or Buffer");n="toHexString"in e&&"function"==typeof e.toHexString?Xt.from(e.toHexString(),"hex"):e.id}else n=e;if(null==n||"number"==typeof n)this[Ze]=t.generate("number"==typeof n?n:void 0);else if(ArrayBuffer.isView(n)&&12===n.byteLength)this[Ze]=n instanceof Xt?n:ce(n);else{if("string"!=typeof n)throw new ne("Argument passed in does not match the accepted types");if(12===n.length){var r=Xt.from(n);if(12!==r.byteLength)throw new ne("Argument passed in must be a string of 12 bytes");this[Ze]=r}else{if(24!==n.length||!He.test(n))throw new ne("Argument passed in must be a string of 12 bytes or a string of 24 hex characters or an integer");this[Ze]=Xt.from(n,"hex")}}t.cacheHexString&&(this.__id=this.id.toString("hex"))}return Object.defineProperty(t.prototype,"id",{get:function(){return this[Ze]},set:function(e){this[Ze]=e,t.cacheHexString&&(this.__id=e.toString("hex"))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"generationTime",{get:function(){return this.id.readInt32BE(0)},set:function(t){this.id.writeUInt32BE(t,0)},enumerable:!1,configurable:!0}),t.prototype.toHexString=function(){if(t.cacheHexString&&this.__id)return this.__id;var e=this.id.toString("hex");return t.cacheHexString&&!this.__id&&(this.__id=e),e},t.getInc=function(){return t.index=(t.index+1)%16777215},t.generate=function(e){"number"!=typeof e&&(e=Math.floor(Date.now()/1e3));var n=t.getInc(),r=Xt.alloc(12);return r.writeUInt32BE(e,0),null===Ge&&(Ge=ue(5)),r[4]=Ge[0],r[5]=Ge[1],r[6]=Ge[2],r[7]=Ge[3],r[8]=Ge[4],r[11]=255&n,r[10]=n>>8&255,r[9]=n>>16&255,r},t.prototype.toString=function(t){return t?this.id.toString(t):this.toHexString()},t.prototype.toJSON=function(){return this.toHexString()},t.prototype.equals=function(e){if(null==e)return!1;if(e instanceof t)return this[Ze][11]===e[Ze][11]&&this[Ze].equals(e[Ze]);if("string"==typeof e&&t.isValid(e)&&12===e.length&&se(this.id))return e===Xt.prototype.toString.call(this.id,"latin1");if("string"==typeof e&&t.isValid(e)&&24===e.length)return e.toLowerCase()===this.toHexString();if("string"==typeof e&&t.isValid(e)&&12===e.length)return Xt.from(e).equals(this.id);if("object"==typeof e&&"toHexString"in e&&"function"==typeof e.toHexString){var n=e.toHexString(),r=this.toHexString().toLowerCase();return"string"==typeof n&&n.toLowerCase()===r}return!1},t.prototype.getTimestamp=function(){var t=new Date,e=this.id.readUInt32BE(0);return t.setTime(1e3*Math.floor(e)),t},t.createPk=function(){return new t},t.createFromTime=function(e){var n=Xt.from([0,0,0,0,0,0,0,0,0,0,0,0]);return n.writeUInt32BE(e,0),new t(n)},t.createFromHexString=function(e){if(void 0===e||null!=e&&24!==e.length)throw new ne("Argument passed in must be a single String of 12 bytes or a string of 24 hex characters");return new t(Xt.from(e,"hex"))},t.isValid=function(e){if(null==e)return!1;try{return new t(e),!0}catch(t){return!1}},t.prototype.toExtendedJSON=function(){return this.toHexString?{$oid:this.toHexString()}:{$oid:this.toString("hex")}},t.fromExtendedJSON=function(e){return new t(e.$oid)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t.prototype.inspect=function(){return'new ObjectId("'.concat(this.toHexString(),'")')},t.index=Math.floor(16777215*Math.random()),t}();Object.defineProperty(Ye.prototype,"generate",{value:fe((function(t){return Ye.generate(t)}),"Please use the static `ObjectId.generate(time)` instead")}),Object.defineProperty(Ye.prototype,"getInc",{value:fe((function(){return Ye.getInc()}),"Please use the static `ObjectId.getInc()` instead")}),Object.defineProperty(Ye.prototype,"get_inc",{value:fe((function(){return Ye.getInc()}),"Please use the static `ObjectId.getInc()` instead")}),Object.defineProperty(Ye,"get_inc",{value:fe((function(){return Ye.getInc()}),"Please use the static `ObjectId.getInc()` instead")}),Object.defineProperty(Ye.prototype,"_bsontype",{value:"ObjectID"});var We=function(){function t(e,n){if(!(this instanceof t))return new t(e,n);if(this.pattern=e,this.options=(null!=n?n:"").split("").sort().join(""),-1!==this.pattern.indexOf("\0"))throw new ee("BSON Regex patterns cannot contain null bytes, found: ".concat(JSON.stringify(this.pattern)));if(-1!==this.options.indexOf("\0"))throw new ee("BSON Regex options cannot contain null bytes, found: ".concat(JSON.stringify(this.options)));for(var r=0;r<this.options.length;r++)if("i"!==this.options[r]&&"m"!==this.options[r]&&"x"!==this.options[r]&&"l"!==this.options[r]&&"s"!==this.options[r]&&"u"!==this.options[r])throw new ee("The regular expression option [".concat(this.options[r],"] is not supported"))}return t.parseOptions=function(t){return t?t.split("").sort().join(""):""},t.prototype.toExtendedJSON=function(t){return(t=t||{}).legacy?{$regex:this.pattern,$options:this.options}:{$regularExpression:{pattern:this.pattern,options:this.options}}},t.fromExtendedJSON=function(e){if("$regex"in e){if("string"==typeof e.$regex)return new t(e.$regex,t.parseOptions(e.$options));if("BSONRegExp"===e.$regex._bsontype)return e}if("$regularExpression"in e)return new t(e.$regularExpression.pattern,t.parseOptions(e.$regularExpression.options));throw new ne("Unexpected BSONRegExp EJSON object form: ".concat(JSON.stringify(e)))},t}();Object.defineProperty(We.prototype,"_bsontype",{value:"BSONRegExp"});var Qe=function(){function t(e){if(!(this instanceof t))return new t(e);this.value=e}return t.prototype.valueOf=function(){return this.value},t.prototype.toString=function(){return this.value},t.prototype.inspect=function(){return'new BSONSymbol("'.concat(this.value,'")')},t.prototype.toJSON=function(){return this.value},t.prototype.toExtendedJSON=function(){return{$symbol:this.value}},t.fromExtendedJSON=function(e){return new t(e.$symbol)},t.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},t}();Object.defineProperty(Qe.prototype,"_bsontype",{value:"Symbol"});var ze=function(t){function e(n,r){var o=this;return o instanceof e?(o=Ie.isLong(n)?t.call(this,n.low,n.high,!0)||this:ae(n)&&void 0!==n.t&&void 0!==n.i?t.call(this,n.i,n.t,!0)||this:t.call(this,n,r,!0)||this,Object.defineProperty(o,"_bsontype",{value:"Timestamp",writable:!1,configurable:!1,enumerable:!1}),o):new e(n,r)}return te(e,t),e.prototype.toJSON=function(){return{$timestamp:this.toString()}},e.fromInt=function(t){return new e(Ie.fromInt(t,!0))},e.fromNumber=function(t){return new e(Ie.fromNumber(t,!0))},e.fromBits=function(t,n){return new e(t,n)},e.fromString=function(t,n){return new e(Ie.fromString(t,!0,n))},e.prototype.toExtendedJSON=function(){return{$timestamp:{t:this.high>>>0,i:this.low>>>0}}},e.fromExtendedJSON=function(t){return new e(t.$timestamp)},e.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return this.inspect()},e.prototype.inspect=function(){return"new Timestamp({ t: ".concat(this.getHighBits(),", i: ").concat(this.getLowBits()," })")},e.MAX_VALUE=Ie.MAX_UNSIGNED_VALUE,e}(Ie);var Xe=2147483647,Ke=-2147483648,tn=0x8000000000000000,en=-0x8000000000000000,nn={$oid:Ye,$binary:me,$uuid:me,$symbol:Qe,$numberInt:Je,$numberDecimal:Ce,$numberDouble:ke,$numberLong:Ie,$minKey:Ve,$maxKey:Fe,$regex:We,$regularExpression:We,$timestamp:ze};function rn(t,e){if(void 0===e&&(e={}),"number"==typeof t){if(e.relaxed||e.legacy)return t;if(Math.floor(t)===t){if(t>=Ke&&t<=Xe)return new Je(t);if(t>=en&&t<=tn)return Ie.fromNumber(t)}return new ke(t)}if(null==t||"object"!=typeof t)return t;if(t.$undefined)return null;for(var n=Object.keys(t).filter((function(e){return e.startsWith("$")&&null!=t[e]})),r=0;r<n.length;r++){var o=nn[n[r]];if(o)return o.fromExtendedJSON(t,e)}if(null!=t.$date){var i=t.$date,u=new Date;return e.legacy?"number"==typeof i?u.setTime(i):"string"==typeof i&&u.setTime(Date.parse(i)):"string"==typeof i?u.setTime(Date.parse(i)):Ie.isLong(i)?u.setTime(i.toNumber()):"number"==typeof i&&e.relaxed&&u.setTime(i),u}if(null!=t.$code){var s=Object.assign({},t);return t.$scope&&(s.$scope=rn(t.$scope)),be.fromExtendedJSON(t)}if(function(t){return ae(t)&&null!=t.$id&&"string"==typeof t.$ref&&(null==t.$db||"string"==typeof t.$db)}(t)||t.$dbPointer){var a=t.$ref?t:t.$dbPointer;if(a instanceof ve)return a;var f=Object.keys(a).filter((function(t){return t.startsWith("$")})),c=!0;if(f.forEach((function(t){-1===["$ref","$id","$db"].indexOf(t)&&(c=!1)})),c)return ve.fromExtendedJSON(a)}return t}function on(t){var e=t.toISOString();return 0!==t.getUTCMilliseconds()?e:e.slice(0,-5)+"Z"}function un(t,e){if(("object"==typeof t||"function"==typeof t)&&null!==t){var n=e.seenObjects.findIndex((function(e){return e.obj===t}));if(-1!==n){var r=e.seenObjects.map((function(t){return t.propertyName})),o=r.slice(0,n).map((function(t){return"".concat(t," -> ")})).join(""),i=r[n],u=" -> "+r.slice(n+1,r.length-1).map((function(t){return"".concat(t," -> ")})).join(""),s=r[r.length-1],a=" ".repeat(o.length+i.length/2),f="-".repeat(u.length+(i.length+s.length)/2-1);throw new ne("Converting circular structure to EJSON:\n"+"    ".concat(o).concat(i).concat(u).concat(s,"\n")+"    ".concat(a,"\\").concat(f,"/"))}e.seenObjects[e.seenObjects.length-1].obj=t}if(Array.isArray(t))return function(t,e){return t.map((function(t,n){e.seenObjects.push({propertyName:"index ".concat(n),obj:null});try{return un(t,e)}finally{e.seenObjects.pop()}}))}(t,e);if(void 0===t)return null;if(t instanceof Date||ae(p=t)&&"[object Date]"===Object.prototype.toString.call(p)){var c=t.getTime(),h=c>-1&&c<2534023188e5;return e.legacy?e.relaxed&&h?{$date:t.getTime()}:{$date:on(t)}:e.relaxed&&h?{$date:on(t)}:{$date:{$numberLong:t.getTime().toString()}}}var p;if(!("number"!=typeof t||e.relaxed&&isFinite(t))){if(Math.floor(t)===t){var l=t>=en&&t<=tn;if(t>=Ke&&t<=Xe)return{$numberInt:t.toString()};if(l)return{$numberLong:t.toString()}}return{$numberDouble:t.toString()}}if(t instanceof RegExp||function(t){return"[object RegExp]"===Object.prototype.toString.call(t)}(t)){var d=t.flags;if(void 0===d){var y=t.toString().match(/[gimuy]*$/);y&&(d=y[0])}return new We(t.source,d).toExtendedJSON(e)}return null!=t&&"object"==typeof t?function(t,e){if(null==t||"object"!=typeof t)throw new ee("not an object instance");var n=t._bsontype;if(void 0===n){var r={};for(var o in t){e.seenObjects.push({propertyName:o,obj:null});try{var i=un(t[o],e);"__proto__"===o?Object.defineProperty(r,o,{value:i,writable:!0,enumerable:!0,configurable:!0}):r[o]=i}finally{e.seenObjects.pop()}}return r}if(function(t){return ae(t)&&Reflect.has(t,"_bsontype")&&"string"==typeof t._bsontype}(t)){var u=t;if("function"!=typeof u.toExtendedJSON){var s=an[t._bsontype];if(!s)throw new ne("Unrecognized or invalid _bsontype: "+t._bsontype);u=s(u)}return"Code"===n&&u.scope?u=new be(u.code,un(u.scope,e)):"DBRef"===n&&u.oid&&(u=new ve(un(u.collection,e),un(u.oid,e),un(u.db,e),un(u.fields,e))),u.toExtendedJSON(e)}throw new ee("_bsontype must be a string, but was: "+typeof n)}(t,e):t}var sn,an={Binary:function(t){return new me(t.value(),t.sub_type)},Code:function(t){return new be(t.code,t.scope)},DBRef:function(t){return new ve(t.collection||t.namespace,t.oid,t.db,t.fields)},Decimal128:function(t){return new Ce(t.bytes)},Double:function(t){return new ke(t.value)},Int32:function(t){return new Je(t.value)},Long:function(t){return Ie.fromBits(null!=t.low?t.low:t.low_,null!=t.low?t.high:t.high_,null!=t.low?t.unsigned:t.unsigned_)},MaxKey:function(){return new Fe},MinKey:function(){return new Ve},ObjectID:function(t){return new Ye(t)},ObjectId:function(t){return new Ye(t)},BSONRegExp:function(t){return new We(t.pattern,t.options)},Symbol:function(t){return new Qe(t.value)},Timestamp:function(t){return ze.fromBits(t.low,t.high)}};!function(t){function e(t,e){var n=Object.assign({},{relaxed:!0,legacy:!1},e);return"boolean"==typeof n.relaxed&&(n.strict=!n.relaxed),"boolean"==typeof n.strict&&(n.relaxed=!n.strict),JSON.parse(t,(function(t,e){if(-1!==t.indexOf("\0"))throw new ee("BSON Document field names cannot contain null bytes, found: ".concat(JSON.stringify(t)));return rn(e,n)}))}function n(t,e,n,r){null!=n&&"object"==typeof n&&(r=n,n=0),null==e||"object"!=typeof e||Array.isArray(e)||(r=e,e=void 0,n=0);var o=un(t,Object.assign({relaxed:!0,legacy:!1},r,{seenObjects:[{propertyName:"(root)",obj:null}]}));return JSON.stringify(o,e,n)}t.parse=e,t.stringify=n,t.serialize=function(t,e){return e=e||{},JSON.parse(n(t,e))},t.deserialize=function(t,n){return n=n||{},e(JSON.stringify(t),n)}}(sn||(sn={}));var fn=oe();fn.Map?fn.Map:function(){function t(t){void 0===t&&(t=[]),this._keys=[],this._values={};for(var e=0;e<t.length;e++)if(null!=t[e]){var n=t[e],r=n[0],o=n[1];this._keys.push(r),this._values[r]={v:o,i:this._keys.length-1}}}t.prototype.clear=function(){this._keys=[],this._values={}},t.prototype.delete=function(t){var e=this._values[t];return null!=e&&(delete this._values[t],this._keys.splice(e.i,1),!0)},t.prototype.entries=function(){var t=this,e=0;return{next:function(){var n=t._keys[e++];return{value:void 0!==n?[n,t._values[n].v]:void 0,done:void 0===n}}}},t.prototype.forEach=function(t,e){e=e||this;for(var n=0;n<this._keys.length;n++){var r=this._keys[n];t.call(e,this._values[r].v,r,e)}},t.prototype.get=function(t){return this._values[t]?this._values[t].v:void 0},t.prototype.has=function(t){return null!=this._values[t]},t.prototype.keys=function(){var t=this,e=0;return{next:function(){var n=t._keys[e++];return{value:void 0!==n?n:void 0,done:void 0===n}}}},t.prototype.set=function(t,e){return this._values[t]?(this._values[t].v=e,this):(this._keys.push(t),this._values[t]={v:e,i:this._keys.length-1},this)},t.prototype.values=function(){var t=this,e=0;return{next:function(){var n=t._keys[e++];return{value:void 0!==n?t._values[n].v:void 0,done:void 0===n}}}},Object.defineProperty(t.prototype,"size",{get:function(){return this._keys.length},enumerable:!1,configurable:!0})}(),Ie.fromNumber(ye),Ie.fromNumber(ge),new Set(["$db","$ref","$id","$clusterTime"]);var cn=new Uint8Array(8);new DataView(cn.buffer,cn.byteOffset,cn.byteLength);Xt.alloc(17825792);var hn=function(){function t(t,e){this._stages=[],t&&e&&(this._db=t,this._request=new Un.reqClass(this._db.config),this._collectionName=e)}return t.prototype.end=function(){return t=this,e=void 0,r=function(){var t;return function(t,e){var n,r,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,r=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!((o=(o=u.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=e.call(t,u)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}(this,(function(e){switch(e.label){case 0:if(!this._collectionName||!this._db)throw new Error("Aggregation pipeline cannot send request");return[4,this._request.send("database.aggregate",{collectionName:this._collectionName,stages:this._stages})];case 1:return(t=e.sent())&&t.data&&t.data.list?[2,{requestId:t.requestId,data:JSON.parse(t.data.list).map(sn.parse)}]:[2,t]}}))},new((n=void 0)||(n=Promise))((function(o,i){function u(t){try{a(r.next(t))}catch(t){i(t)}}function s(t){try{a(r.throw(t))}catch(t){i(t)}}function a(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,s)}a((r=r.apply(t,e||[])).next())}));var t,e,n,r},t.prototype.unwrap=function(){return this._stages},t.prototype.done=function(){return this._stages.map((function(t){var e,n=t.stageKey,r=t.stageValue;return(e={})[n]=JSON.parse(r),e}))},t.prototype._pipe=function(t,e){return this._stages.push({stageKey:"$"+t,stageValue:JSON.stringify(e)}),this},t.prototype.addFields=function(t){return this._pipe("addFields",t)},t.prototype.bucket=function(t){return this._pipe("bucket",t)},t.prototype.bucketAuto=function(t){return this._pipe("bucketAuto",t)},t.prototype.count=function(t){return this._pipe("count",t)},t.prototype.geoNear=function(t){return t.query&&(t.query=Rt.encode(t.query)),t.distanceMultiplier&&"number"==typeof t.distanceMultiplier&&(t.distanceMultiplier=t.distanceMultiplier),t.near&&(t.near=new W(t.near.longitude,t.near.latitude).toJSON()),this._pipe("geoNear",t)},t.prototype.group=function(t){return this._pipe("group",t)},t.prototype.limit=function(t){return this._pipe("limit",t)},t.prototype.match=function(t){return this._pipe("match",Rt.encode(t))},t.prototype.project=function(t){return this._pipe("project",t)},t.prototype.lookup=function(t){return this._pipe("lookup",t)},t.prototype.replaceRoot=function(t){return this._pipe("replaceRoot",t)},t.prototype.sample=function(t){return this._pipe("sample",t)},t.prototype.skip=function(t){return this._pipe("skip",t)},t.prototype.sort=function(t){return this._pipe("sort",t)},t.prototype.sortByCount=function(t){return this._pipe("sortByCount",t)},t.prototype.unwind=function(t){return this._pipe("unwind",t)},t}();const pn=hn;var ln=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),dn=function(t){function e(e,n){return t.call(this,e,n)||this}return ln(e,t),Object.defineProperty(e.prototype,"name",{get:function(){return this._coll},enumerable:!0,configurable:!0}),e.prototype.doc=function(t){if("string"!=typeof t&&"number"!=typeof t)throw new Error("docId必须为字符串或数字");return new $t(this._db,this._coll,t)},e.prototype.add=function(t,e){return new $t(this._db,this._coll,void 0).create(t,e)},e.prototype.aggregate=function(){return new pn(this._db,this._coll)},e}(qt),yn={eq:function(t){return new pt(ct.EQ,[t])},neq:function(t){return new pt(ct.NEQ,[t])},lt:function(t){return new pt(ct.LT,[t])},lte:function(t){return new pt(ct.LTE,[t])},gt:function(t){return new pt(ct.GT,[t])},gte:function(t){return new pt(ct.GTE,[t])},in:function(t){return new pt(ct.IN,t)},nin:function(t){return new pt(ct.NIN,t)},all:function(t){return new pt(ct.ALL,t)},elemMatch:function(t){return new pt(ct.ELEM_MATCH,[t])},exists:function(t){return new pt(ct.EXISTS,[t])},size:function(t){return new pt(ct.SIZE,[t])},mod:function(t){return new pt(ct.MOD,[t])},geoNear:function(t){return new pt(ct.GEO_NEAR,[t])},geoWithin:function(t){return new pt(ct.GEO_WITHIN,[t])},geoIntersects:function(t){return new pt(ct.GEO_INTERSECTS,[t])},and:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=H(arguments[0])?arguments[0]:Array.from(arguments);return new at(it.AND,n)},nor:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=H(arguments[0])?arguments[0]:Array.from(arguments);return new at(it.NOR,n)},or:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=H(arguments[0])?arguments[0]:Array.from(arguments);return new at(it.OR,n)},not:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=H(arguments[0])?arguments[0]:Array.from(arguments);return new at(it.NOT,n)},set:function(t){return new ut(D.SET,[t])},remove:function(){return new ut(D.REMOVE,[])},inc:function(t){return new ut(D.INC,[t])},mul:function(t){return new ut(D.MUL,[t])},push:function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(F(e[0])&&e[0].hasOwnProperty("each")){var r=e[0];t={$each:r.each,$position:r.position,$sort:r.sort,$slice:r.slice}}else t=H(e[0])?e[0]:Array.from(e);return new ut(D.PUSH,t)},pull:function(t){return new ut(D.PULL,t)},pullAll:function(t){return new ut(D.PULL_ALL,t)},pop:function(){return new ut(D.POP,[])},shift:function(){return new ut(D.SHIFT,[])},unshift:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=H(arguments[0])?arguments[0]:Array.from(arguments);return new ut(D.UNSHIFT,n)},addToSet:function(t){return new ut(D.ADD_TO_SET,t)},rename:function(t){return new ut(D.RENAME,[t])},bit:function(t){return new ut(D.BIT,[t])},max:function(t){return new ut(D.MAX,[t])},min:function(t){return new ut(D.MIN,[t])},expr:function(t){return{$expr:t}},jsonSchema:function(t){return{$jsonSchema:t}},text:function(t){return"string"===J(t)?{$search:t.search}:{$search:t.search,$language:t.language,$caseSensitive:t.caseSensitive,$diacriticSensitive:t.diacriticSensitive}},aggregate:{pipeline:function(){return new pn},abs:function(t){return new gn("abs",t)},add:function(t){return new gn("add",t)},ceil:function(t){return new gn("ceil",t)},divide:function(t){return new gn("divide",t)},exp:function(t){return new gn("exp",t)},floor:function(t){return new gn("floor",t)},ln:function(t){return new gn("ln",t)},log:function(t){return new gn("log",t)},log10:function(t){return new gn("log10",t)},mod:function(t){return new gn("mod",t)},multiply:function(t){return new gn("multiply",t)},pow:function(t){return new gn("pow",t)},sqrt:function(t){return new gn("sqrt",t)},subtract:function(t){return new gn("subtract",t)},trunc:function(t){return new gn("trunc",t)},arrayElemAt:function(t){return new gn("arrayElemAt",t)},arrayToObject:function(t){return new gn("arrayToObject",t)},concatArrays:function(t){return new gn("concatArrays",t)},filter:function(t){return new gn("filter",t)},in:function(t){return new gn("in",t)},indexOfArray:function(t){return new gn("indexOfArray",t)},isArray:function(t){return new gn("isArray",t)},map:function(t){return new gn("map",t)},range:function(t){return new gn("range",t)},reduce:function(t){return new gn("reduce",t)},reverseArray:function(t){return new gn("reverseArray",t)},size:function(t){return new gn("size",t)},slice:function(t){return new gn("slice",t)},zip:function(t){return new gn("zip",t)},and:function(t){return new gn("and",t)},not:function(t){return new gn("not",t)},or:function(t){return new gn("or",t)},cmp:function(t){return new gn("cmp",t)},eq:function(t){return new gn("eq",t)},gt:function(t){return new gn("gt",t)},gte:function(t){return new gn("gte",t)},lt:function(t){return new gn("lt",t)},lte:function(t){return new gn("lte",t)},neq:function(t){return new gn("ne",t)},cond:function(t){return new gn("cond",t)},ifNull:function(t){return new gn("ifNull",t)},switch:function(t){return new gn("switch",t)},dateFromParts:function(t){return new gn("dateFromParts",t)},dateFromString:function(t){return new gn("dateFromString",t)},dayOfMonth:function(t){return new gn("dayOfMonth",t)},dayOfWeek:function(t){return new gn("dayOfWeek",t)},dayOfYear:function(t){return new gn("dayOfYear",t)},isoDayOfWeek:function(t){return new gn("isoDayOfWeek",t)},isoWeek:function(t){return new gn("isoWeek",t)},isoWeekYear:function(t){return new gn("isoWeekYear",t)},millisecond:function(t){return new gn("millisecond",t)},minute:function(t){return new gn("minute",t)},month:function(t){return new gn("month",t)},second:function(t){return new gn("second",t)},hour:function(t){return new gn("hour",t)},week:function(t){return new gn("week",t)},year:function(t){return new gn("year",t)},literal:function(t){return new gn("literal",t)},mergeObjects:function(t){return new gn("mergeObjects",t)},objectToArray:function(t){return new gn("objectToArray",t)},allElementsTrue:function(t){return new gn("allElementsTrue",t)},anyElementTrue:function(t){return new gn("anyElementTrue",t)},setDifference:function(t){return new gn("setDifference",t)},setEquals:function(t){return new gn("setEquals",t)},setIntersection:function(t){return new gn("setIntersection",t)},setIsSubset:function(t){return new gn("setIsSubset",t)},setUnion:function(t){return new gn("setUnion",t)},concat:function(t){return new gn("concat",t)},dateToString:function(t){return new gn("dateToString",t)},indexOfBytes:function(t){return new gn("indexOfBytes",t)},indexOfCP:function(t){return new gn("indexOfCP",t)},split:function(t){return new gn("split",t)},strLenBytes:function(t){return new gn("strLenBytes",t)},strLenCP:function(t){return new gn("strLenCP",t)},strcasecmp:function(t){return new gn("strcasecmp",t)},substr:function(t){return new gn("substr",t)},substrBytes:function(t){return new gn("substrBytes",t)},substrCP:function(t){return new gn("substrCP",t)},toLower:function(t){return new gn("toLower",t)},toUpper:function(t){return new gn("toUpper",t)},meta:function(t){return new gn("meta",t)},addToSet:function(t){return new gn("addToSet",t)},avg:function(t){return new gn("avg",t)},first:function(t){return new gn("first",t)},last:function(t){return new gn("last",t)},max:function(t){return new gn("max",t)},min:function(t){return new gn("min",t)},push:function(t){return new gn("push",t)},stdDevPop:function(t){return new gn("stdDevPop",t)},stdDevSamp:function(t){return new gn("stdDevSamp",t)},sum:function(t){return new gn("sum",t)},let:function(t){return new gn("let",t)}},project:{slice:function(t){return new mn("slice",t)},elemMatch:function(t){return new mn("elemMatch",t)}}},gn=function(t,e){this["$"+t]=e},mn=function(t,e){this["$"+t]=e},wn=function(){function t(t){var e=t.regexp,n=t.options;if(!e)throw new TypeError("regexp must be a string");this.$regex=e,this.$options=n}return t.prototype.parse=function(){return{$regex:this.$regex,$options:this.$options}},Object.defineProperty(t.prototype,"_internalType",{get:function(){return $},enumerable:!0,configurable:!0}),t}();function bn(t){return new wn(t)}var vn="insert document failed",_n="DATABASE_TRANSACTION_CONFLICT",En=function(){return En=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},En.apply(this,arguments)},On=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function u(t){try{a(r.next(t))}catch(t){i(t)}}function s(t){try{a(r.throw(t))}catch(t){i(t)}}function a(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,s)}a((r=r.apply(t,e||[])).next())}))},Sn=function(t,e){var n,r,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,r=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!((o=(o=u.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=e.call(t,u)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},Nn="database.updateDocInTransaction",xn=function(){function t(t,e,n){this._coll=e,this.id=n,this._transaction=t,this._request=this._transaction.getRequestMethod(),this._transactionId=this._transaction.getTransactionId()}return t.prototype.create=function(t){return On(this,void 0,void 0,(function(){var e,n,r,o;return Sn(this,(function(i){switch(i.label){case 0:return e={collectionName:this._coll,transactionId:this._transactionId,data:sn.stringify(_t(t),{relaxed:!1})},this.id&&(e._id=this.id),[4,this._request.send("database.insertDocInTransaction",e)];case 1:if((n=i.sent()).code)throw n;if(r=sn.parse(n.inserted),1==(o=sn.parse(n.ok))&&1==r)return[2,En(En({},n),{ok:o,inserted:r})];throw new Error(vn)}}))}))},t.prototype.get=function(){return On(this,void 0,void 0,(function(){var t,e;return Sn(this,(function(n){switch(n.label){case 0:return t={collectionName:this._coll,transactionId:this._transactionId,query:{_id:{$eq:this.id}}},[4,this._request.send("database.getInTransaction",t)];case 1:if((e=n.sent()).code)throw e;return[2,{data:"null"!==e.data?C.formatField(sn.parse(e.data)):sn.parse(e.data),requestId:e.requestId}]}}))}))},t.prototype.set=function(t){return On(this,void 0,void 0,(function(){var e,n;return Sn(this,(function(r){switch(r.label){case 0:return e={collectionName:this._coll,transactionId:this._transactionId,query:{_id:{$eq:this.id}},data:sn.stringify(_t(t),{relaxed:!1}),upsert:!0},[4,this._request.send(Nn,e)];case 1:if((n=r.sent()).code)throw n;return[2,En(En({},n),{updated:sn.parse(n.updated),upserted:n.upserted?JSON.parse(n.upserted):null})]}}))}))},t.prototype.update=function(t){return On(this,void 0,void 0,(function(){var e,n;return Sn(this,(function(r){switch(r.label){case 0:return e={collectionName:this._coll,transactionId:this._transactionId,query:{_id:{$eq:this.id}},data:sn.stringify(jt.encode(t),{relaxed:!1})},[4,this._request.send(Nn,e)];case 1:if((n=r.sent()).code)throw n;return[2,En(En({},n),{updated:sn.parse(n.updated)})]}}))}))},t.prototype.delete=function(){return On(this,void 0,void 0,(function(){var t,e;return Sn(this,(function(n){switch(n.label){case 0:return t={collectionName:this._coll,transactionId:this._transactionId,query:{_id:{$eq:this.id}}},[4,this._request.send("database.deleteDocInTransaction",t)];case 1:if((e=n.sent()).code)throw e;return[2,En(En({},e),{deleted:sn.parse(e.deleted)})]}}))}))},t}(),In=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Tn=function(t){function e(e,n){return t.call(this,e,n)||this}return In(e,t),Object.defineProperty(e.prototype,"name",{get:function(){return this._coll},enumerable:!0,configurable:!0}),e.prototype.doc=function(t){if("string"!=typeof t&&"number"!=typeof t)throw new Error("docId必须为字符串或数字");return new xn(this._transaction,this._coll,t)},e.prototype.add=function(t){var e;return void 0!==t._id&&(e=t._id),new xn(this._transaction,this._coll,e).create(t)},e}((function(t,e){this._coll=e,this._transaction=t})),An=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function u(t){try{a(r.next(t))}catch(t){i(t)}}function s(t){try{a(r.throw(t))}catch(t){i(t)}}function a(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,s)}a((r=r.apply(t,e||[])).next())}))},Bn=function(t,e){var n,r,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,r=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!((o=(o=u.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=e.call(t,u)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},jn=function(){function t(t){this._db=t,this._request=new Un.reqClass(this._db.config),this.aborted=!1,this.commited=!1,this.inited=!1}return t.prototype.init=function(){return An(this,void 0,void 0,(function(){var t;return Bn(this,(function(e){switch(e.label){case 0:return[4,this._request.send("database.startTransaction")];case 1:if((t=e.sent()).code)throw t;return this.inited=!0,this._id=t.transactionId,[2]}}))}))},t.prototype.collection=function(t){if(!t)throw new Error("Collection name is required");return new Tn(this,t)},t.prototype.getTransactionId=function(){return this._id},t.prototype.getRequestMethod=function(){return this._request},t.prototype.commit=function(){return An(this,void 0,void 0,(function(){var t,e;return Bn(this,(function(n){switch(n.label){case 0:return t={transactionId:this._id},[4,this._request.send("database.commitTransaction",t)];case 1:if((e=n.sent()).code)throw e;return this.commited=!0,[2,e]}}))}))},t.prototype.rollback=function(t){return An(this,void 0,void 0,(function(){var e,n;return Bn(this,(function(r){switch(r.label){case 0:return e={transactionId:this._id},[4,this._request.send("database.abortTransaction",e)];case 1:if((n=r.sent()).code)throw n;return this.aborted=!0,this.abortReason=t,[2,n]}}))}))},t}();function Pn(){return An(this,void 0,void 0,(function(){var t;return Bn(this,(function(e){switch(e.label){case 0:return[4,(t=new jn(this)).init()];case 1:return e.sent(),[2,t]}}))}))}function Ln(t,e){return void 0===e&&(e=3),An(this,void 0,void 0,(function(){var n,r,o,i,u=this;return Bn(this,(function(s){switch(s.label){case 0:return s.trys.push([0,4,,10]),[4,(n=new jn(this)).init()];case 1:return s.sent(),[4,t(n)];case 2:if(r=s.sent(),!0===n.aborted)throw n.abortReason;return[4,n.commit()];case 3:return s.sent(),[2,r];case 4:if(o=s.sent(),!1===n.inited)throw o;return i=function(t){return An(u,void 0,void 0,(function(){return Bn(this,(function(e){switch(e.label){case 0:if(n.aborted||n.commited)return[3,5];e.label=1;case 1:return e.trys.push([1,3,,4]),[4,n.rollback()];case 2:case 3:return e.sent(),[3,4];case 4:throw t;case 5:if(!0===n.aborted)throw n.abortReason;throw t}}))}))},e<=0?[4,i(o)]:[3,6];case 5:s.sent(),s.label=6;case 6:return o&&o.code===_n?[4,Ln.bind(this)(t,--e)]:[3,8];case 7:return[2,s.sent()];case 8:return[4,i(o)];case 9:return s.sent(),[3,10];case 10:return[2]}}))}))}var Un=function(){function t(t){this.config=t,this.Geo=i,this.serverDate=M,this.command=yn,this.RegExp=bn,this.startTransaction=Pn,this.runTransaction=Ln,this.logicCommand=at,this.updateCommand=ut,this.queryCommand=pt}return t.prototype.collection=function(t){if(!t)throw new Error("Collection name is required");return new dn(this,t)},t.prototype.createCollection=function(e){var n={collectionName:e};return new t.reqClass(this.config).send("database.addCollection",n)},t}()}},e={};function n(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return t[r].call(i.exports,i,i.exports,n),i.exports}return n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n(258)})()));