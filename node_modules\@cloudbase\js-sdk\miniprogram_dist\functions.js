!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("cloudbase_functions",[],e):"object"==typeof exports?exports.cloudbase_functions=e():t.cloudbase_functions=e()}("undefined"!=typeof window?window:this,(()=>(()=>{"use strict";var t={744:(t,e,n)=>{n.r(e),n.d(e,{registerFunctions:()=>V});var r="@cloudbase/js-sdk";function o(){return r}var i,a={INVALID_PARAMS:"INVALID_PARAMS",INVALID_SYNTAX:"INVALID_SYNTAX",INVALID_OPERATION:"INVALID_OPERATION",OPERATION_FAIL:"OPERATION_FAIL",NETWORK_ERROR:"NETWORK_ERROR",UNKOWN_ERROR:"UNKOWN_ERROR"};!function(t){t.local="local",t.none="none",t.session="session"}(i||(i={}));function c(t,e){console.warn("[".concat(o(),"][").concat(t,"]:").concat(e))}var s,u,l=(s=function(t,e){return s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},s(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}s(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),f=function(){return f=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},f.apply(this,arguments)},p=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function a(t){try{s(r.next(t))}catch(t){i(t)}}function c(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,c)}s((r=r.apply(t,e||[])).next())}))},d=function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){a.label=c[1];break}if(6===c[0]&&a.label<o[1]){a.label=o[1],o=c;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(c);break}o[2]&&a.ops.pop(),a.trys.pop();continue}c=e.call(t,a)}catch(t){c=[6,t],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}};!function(t){function e(e){var n=t.call(this)||this,r=e.timeout,o=e.timeoutMsg,i=e.restrictedMethods;return n.timeout=r||0,n.timeoutMsg=o||"请求超时",n.restrictedMethods=i||["get","post","upload","download"],n}l(e,t),e.prototype.get=function(t){return this.request(f(f({},t),{method:"get"}),this.restrictedMethods.includes("get"))},e.prototype.post=function(t){return this.request(f(f({},t),{method:"post"}),this.restrictedMethods.includes("post"))},e.prototype.put=function(t){return this.request(f(f({},t),{method:"put"}))},e.prototype.upload=function(t){var e=t.data,n=t.file,r=t.name,o=t.method,i=t.headers,a=void 0===i?{}:i,c={post:"post",put:"put"}[null==o?void 0:o.toLowerCase()]||"put",s=new FormData;return"post"===c?(Object.keys(e).forEach((function(t){s.append(t,e[t])})),s.append("key",r),s.append("file",n),this.request(f(f({},t),{data:s,method:c}),this.restrictedMethods.includes("upload"))):this.request(f(f({},t),{method:"put",headers:a,body:n}),this.restrictedMethods.includes("upload"))},e.prototype.download=function(t){return p(this,void 0,void 0,(function(){var e,n,r,o;return d(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,this.get(f(f({},t),{headers:{},responseType:"blob"}))];case 1:return e=i.sent().data,n=window.URL.createObjectURL(new Blob([e])),r=decodeURIComponent(new URL(t.url).pathname.split("/").pop()||""),(o=document.createElement("a")).href=n,o.setAttribute("download",r),o.style.display="none",document.body.appendChild(o),o.click(),window.URL.revokeObjectURL(n),document.body.removeChild(o),[3,3];case 2:return i.sent(),[3,3];case 3:return[2,new Promise((function(e){e({statusCode:200,tempFilePath:t.url})}))]}}))}))},e.prototype.fetch=function(t){var e;return p(this,void 0,void 0,(function(){var n,r,o,i,a,c,s,u,l,h,y,v=this;return d(this,(function(b){switch(b.label){case 0:return n=new AbortController,r=t.url,o=t.enableAbort,i=void 0!==o&&o,a=t.stream,c=void 0!==a&&a,s=t.signal,u=t.timeout,l=null!=u?u:this.timeout,s&&(s.aborted&&n.abort(),s.addEventListener("abort",(function(){return n.abort()}))),h=null,i&&l&&(h=setTimeout((function(){console.warn(v.timeoutMsg),n.abort(new Error(v.timeoutMsg))}),l)),[4,fetch(r,f(f({},t),{signal:n.signal})).then((function(t){return p(v,void 0,void 0,(function(){var e,n,r;return d(this,(function(o){switch(o.label){case 0:return clearTimeout(h),t.ok?(e=t,[3,3]):[3,1];case 1:return r=(n=Promise).reject,[4,t.json()];case 2:e=r.apply(n,[o.sent()]),o.label=3;case 3:return[2,e]}}))}))})).catch((function(t){return clearTimeout(h),Promise.reject(t)}))];case 1:return y=b.sent(),[2,{data:c?y.body:(null===(e=y.headers.get("content-type"))||void 0===e?void 0:e.includes("application/json"))?y.json():y.text(),statusCode:y.status,header:y.headers}]}}))}))},e.prototype.request=function(t,e){var n=this;void 0===e&&(e=!1);var r=String(t.method).toLowerCase()||"get";return new Promise((function(o){var i,a,c,s=t.url,u=t.headers,l=void 0===u?{}:u,f=t.data,p=t.responseType,d=t.withCredentials,h=t.body,y=t.onUploadProgress,v=function(t,e,n){void 0===n&&(n={});var r=/\?/.test(e),o="";return Object.keys(n).forEach((function(t){""===o?!r&&(e+="?"):o+="&",o+="".concat(t,"=").concat(encodeURIComponent(n[t]))})),/^http(s)?:\/\//.test(e+=o)?e:"".concat(t).concat(e)}("https:",s,"get"===r?f:{}),b=new XMLHttpRequest;b.open(r,v),p&&(b.responseType=p),Object.keys(l).forEach((function(t){b.setRequestHeader(t,l[t])})),y&&b.upload.addEventListener("progress",y),b.onreadystatechange=function(){var t={};if(4===b.readyState){var e=b.getAllResponseHeaders().trim().split(/[\r\n]+/),n={};e.forEach((function(t){var e=t.split(": "),r=e.shift().toLowerCase(),o=e.join(": ");n[r]=o})),t.header=n,t.statusCode=b.status;try{t.data="blob"===p?b.response:JSON.parse(b.responseText)}catch(e){t.data="blob"===p?b.response:b.responseText}clearTimeout(i),o(t)}},e&&n.timeout&&(i=setTimeout((function(){console.warn(n.timeoutMsg),b.abort()}),n.timeout)),c=f,a="[object FormData]"===Object.prototype.toString.call(c)?f:"application/x-www-form-urlencoded"===l["content-type"]?function(t){void 0===t&&(t={});var e=[];return Object.keys(t).forEach((function(n){e.push("".concat(n,"=").concat(encodeURIComponent(t[n])))})),e.join("&")}(f):h||(f?JSON.stringify(f):void 0),d&&(b.withCredentials=!0),b.send(a)}))}}((function(){})),function(t){t.WEB="web",t.WX_MP="wx_mp"}(u||(u={}));var h=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},t(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),y=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function a(t){try{s(r.next(t))}catch(t){i(t)}}function c(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,c)}s((r=r.apply(t,e||[])).next())}))},v=function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){a.label=c[1];break}if(6===c[0]&&a.label<o[1]){a.label=o[1],o=c;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(c);break}o[2]&&a.ops.pop(),a.trys.pop();continue}c=e.call(t,a)}catch(t){c=[6,t],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}},b=function(t){function e(e){var n=t.call(this)||this;return n.root=e,e.tcbCacheObject||(e.tcbCacheObject={}),n}return h(e,t),e.prototype.setItem=function(t,e){this.root.tcbCacheObject[t]=e},e.prototype.getItem=function(t){return this.root.tcbCacheObject[t]},e.prototype.removeItem=function(t){delete this.root.tcbCacheObject[t]},e.prototype.clear=function(){delete this.root.tcbCacheObject},e}((function(){}));!function(){function t(t){this.keys={};var e=t.persistence,n=t.platformInfo,r=void 0===n?{}:n,o=t.keys,i=void 0===o?{}:o;this.platformInfo=r,this.storage||(this.persistenceTag=this.platformInfo.adapter.primaryStorage||e,this.storage=function(t,e){switch(t){case"local":default:return e.localStorage?e.localStorage:(c(a.INVALID_PARAMS,"localStorage is not supported on current platform"),new b(e.root));case"none":return new b(e.root)}}(this.persistenceTag,this.platformInfo.adapter),this.keys=i)}Object.defineProperty(t.prototype,"mode",{get:function(){return this.storage.mode||"sync"},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"persistence",{get:function(){return this.persistenceTag},enumerable:!1,configurable:!0}),t.prototype.setStore=function(t,e,n){if("async"!==this.mode){if(this.storage)try{var r={version:n||"localCachev1",content:e};this.storage.setItem(t,JSON.stringify(r))}catch(t){throw new Error(JSON.stringify({code:a.OPERATION_FAIL,msg:"[".concat(o(),"][").concat(a.OPERATION_FAIL,"]setStore failed"),info:t}))}}else c(a.INVALID_OPERATION,"current platform's storage is asynchronous, please use setStoreAsync insteed")},t.prototype.setStoreAsync=function(t,e,n){return y(this,void 0,void 0,(function(){var r;return v(this,(function(o){switch(o.label){case 0:if(!this.storage)return[2];o.label=1;case 1:return o.trys.push([1,3,,4]),r={version:n||"localCachev1",content:e},[4,this.storage.setItem(t,JSON.stringify(r))];case 2:return o.sent(),[3,4];case 3:return o.sent(),[2];case 4:return[2]}}))}))},t.prototype.getStore=function(t,e){var n;if("async"!==this.mode){try{if("undefined"!=typeof process&&(null===(n=process.env)||void 0===n?void 0:n.tcb_token))return process.env.tcb_token;if(!this.storage)return""}catch(t){return""}e=e||"localCachev1";var r=this.storage.getItem(t);return r&&r.indexOf(e)>=0?JSON.parse(r).content:""}c(a.INVALID_OPERATION,"current platform's storage is asynchronous, please use getStoreAsync insteed")},t.prototype.getStoreAsync=function(t,e){var n;return y(this,void 0,void 0,(function(){var r;return v(this,(function(o){switch(o.label){case 0:try{if("undefined"!=typeof process&&(null===(n=process.env)||void 0===n?void 0:n.tcb_token))return[2,process.env.tcb_token];if(!this.storage)return[2,""]}catch(t){return[2,""]}return e=e||"localCachev1",[4,this.storage.getItem(t)];case 1:return(r=o.sent())&&r.indexOf(e)>=0?[2,JSON.parse(r).content]:[2,""]}}))}))},t.prototype.removeStore=function(t){"async"!==this.mode?this.storage.removeItem(t):c(a.INVALID_OPERATION,"current platform's storage is asynchronous, please use removeStoreAsync insteed")},t.prototype.removeStoreAsync=function(t){return y(this,void 0,void 0,(function(){return v(this,(function(e){switch(e.label){case 0:return[4,this.storage.removeItem(t)];case 1:return e.sent(),[2]}}))}))}}();var m=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},t(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),w=function(t,e){this.data=e||null,this.name=t},g=function(t){function e(e,n){var r=t.call(this,"error",{error:e,data:n})||this;return r.error=e,r}return m(e,t),e}(w);function O(t){this.message=t}function x(t){this.message=t}new(function(){function t(){this.listeners={}}return t.prototype.on=function(t,e){return function(t,e,n){n[t]=n[t]||[],n[t].push(e)}(t,e,this.listeners),this},t.prototype.off=function(t,e){return function(t,e,n){if(null==n?void 0:n[t]){var r=n[t].indexOf(e);-1!==r&&n[t].splice(r,1)}}(t,e,this.listeners),this},t.prototype.fire=function(t,e){if(t instanceof g)return console.error(t.error),this;var n="string"==typeof t?new w(t,e||{}):t,r=n.name;if(this.listens(r)){n.target=this;for(var o=0,i=this.listeners[r]?function(t,e,n){if(n||2===arguments.length)for(var r,o=0,i=e.length;o<i;o++)!r&&o in e||(r||(r=Array.prototype.slice.call(e,0,o)),r[o]=e[o]);return t.concat(r||Array.prototype.slice.call(e))}([],this.listeners[r],!0):[];o<i.length;o++)i[o].call(this,n)}return this},t.prototype.listens=function(t){return this.listeners[t]&&this.listeners[t].length>0},t}()),"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.indexOf("Firefox"),O.prototype=new Error,O.prototype.name="InvalidCharacterError","undefined"!=typeof window&&window.atob&&window.atob.bind(window),x.prototype=new Error,x.prototype.name="InvalidTokenError";var I=function(){return I=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},I.apply(this,arguments)},_=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function a(t){try{s(r.next(t))}catch(t){i(t)}}function c(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,c)}s((r=r.apply(t,e||[])).next())}))},A=function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){a.label=c[1];break}if(6===c[0]&&a.label<o[1]){a.label=o[1],o=c;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(c);break}o[2]&&a.ops.pop(),a.trys.pop();continue}c=e.call(t,a)}catch(t){c=[6,t],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}},S=a,j="cloudrun";function R(t){return _(this,void 0,void 0,(function(){var e,n,r,o,i,a,c,s,u;return A(this,(function(l){switch(l.label){case 0:return e=t.name,n=t.data,r=t.path,o=void 0===r?"":r,i=t.method,a=t.header,c=void 0===a?{}:a,s="https://".concat(this.config.env,".api.tcloudbasegateway.com/v1/cloudrun/").concat(e),u=o.startsWith("/")?o:"/".concat(o),[4,this.request.fetch({method:i||"POST",headers:Object.assign({},{"Content-Type":"application/json; charset=utf-8"},c),body:n,url:"".concat(s).concat(u)})];case 1:return[4,l.sent().data];case 2:return[2,l.sent()]}}))}))}var N=new(function(){function t(){}return t.prototype.callContainer=function(t){return _(this,void 0,void 0,(function(){var e,n,r;return A(this,(function(o){switch(o.label){case 0:if(e=t.name,n=t.data,!e)throw new Error(JSON.stringify({code:S.INVALID_PARAMS,msg:"[".concat(j,".callContainer] invalid name")}));try{r=n?JSON.stringify(n):""}catch(t){throw new Error(JSON.stringify({code:S.INVALID_PARAMS,msg:"[".concat(j,".callContainer] invalid data")}))}return[4,R.call(this,I(I({},t),{data:r}))];case 1:return[2,o.sent()]}}))}))},t}()),P={name:j,entity:{callContainer:N.callContainer}};try{cloudbase.registerComponent(P)}catch(O){}var E=function(){return E=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},E.apply(this,arguments)},T=function(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)},C=o,k=a,L=function(t,e,n){if(void 0===n&&(n=null),t&&"function"==typeof t)return t(e,n);if(e)throw e;return n},M="functions",F=function(){function t(){}return t.prototype.callFunction=function(t,e){var n,r,o,i,a,c;return o=this,i=void 0,c=function(){var o,i,a,c,s,u,l,f,p,d,h,y,v,b,m,w,g,O,x;return function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){a.label=c[1];break}if(6===c[0]&&a.label<o[1]){a.label=o[1],o=c;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(c);break}o[2]&&a.ops.pop(),a.trys.pop();continue}c=e.call(t,a)}catch(t){c=[6,t],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}}(this,(function(I){switch(I.label){case 0:if(o=t.name,i=t.data,a=t.query,c=t.parse,s=t.search,u=t.type,l=t.path,f=void 0===l?"":l,p=t.method,d=t.header,h=void 0===d?{}:d,!o)throw new Error(JSON.stringify({code:k.INVALID_PARAMS,msg:"[".concat(M,".callFunction] invalid function name")}));try{y=i?JSON.stringify(i):""}catch(t){throw new Error(JSON.stringify({code:k.INVALID_PARAMS,msg:"[".concat(M,".callFunction] invalid data")}))}return"cloudrun"!==u?[3,2]:(_=(new Date).getTime(),A=(null===performance||void 0===performance?void 0:performance.now)&&1e3*performance.now()||0,v="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var e=16*Math.random();return _>0?(e=(_+e)%16|0,_=Math.floor(_/16)):(e=(A+e)%16|0,A=Math.floor(A/16)),("x"===t?e:7&e|8).toString(16)})),[4,R.call(this,{name:o,data:y,path:f,method:p,header:E(E({},h),{"X-Request-Id":v})})]);case 1:return[2,{result:O=I.sent(),requestId:v}];case 2:b="functions.invokeFunction",m={inQuery:a,parse:c,search:s,function_name:o,request_data:y},w=this.request,I.label=3;case 3:return I.trys.push([3,5,,6]),[4,w.send(b,m,{defaultQuery:(null===(n=null==i?void 0:i.params)||void 0===n?void 0:n.action)?{action:null===(r=null==i?void 0:i.params)||void 0===r?void 0:r.action}:{}})];case 4:if((g=I.sent()).code)return[2,L(e,null,g)];if(O=g.data.response_data,c)return[2,L(e,null,{result:O,requestId:g.requestId})];try{return O=JSON.parse(g.data.response_data),[2,L(e,null,{result:O,requestId:g.requestId})]}catch(t){L(e,new Error("[".concat(C(),"][").concat(k.INVALID_PARAMS,"][").concat(M,".callFunction] response data must be json")))}return[3,6];case 5:return x=I.sent(),L(e,x),[3,6];case 6:return[2]}var _,A}))},new((a=void 0)||(a=Promise))((function(t,e){function n(t){try{s(c.next(t))}catch(t){e(t)}}function r(t){try{s(c.throw(t))}catch(t){e(t)}}function s(e){var o;e.done?t(e.value):(o=e.value,o instanceof a?o:new a((function(t){t(o)}))).then(n,r)}s((c=c.apply(o,i||[])).next())}))},function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);i>3&&a&&Object.defineProperty(e,n,a)}([(e={customInfo:{className:"Cloudbase",methodName:"callFunction"},title:"函数调用失败",messages:["请确认以下各项：","  1 - 调用 callFunction() 的语法或参数是否正确","  2 - 当前环境下是否存在此函数","  3 - 函数安全规则是否限制了当前登录状态访问","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat("https://support.qq.com/products/148793")]},e.mode,e.customInfo,e.title,e.messages,function(t,e,n){}),T("design:type",Function),T("design:paramtypes",[Object,Function]),T("design:returntype",Promise)],t.prototype,"callFunction",null),t;var e}(),q=new F,D={name:M,entity:{callFunction:q.callFunction}};try{cloudbase.registerComponent(D)}catch(O){}function V(t){try{t.registerComponent(D)}catch(t){console.warn(t)}}}},e={};function n(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return t[r](i,i.exports,n),i.exports}n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var r={};return(()=>{var t=r;Object.defineProperty(t,"__esModule",{value:!0}),t.registerFunctions=void 0;var e=n(744);t.registerFunctions=e.registerFunctions})(),r})()));