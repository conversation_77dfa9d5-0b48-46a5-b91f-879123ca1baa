"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isMatch = void 0;
function isMatch() {
    if (typeof wx === 'undefined') {
        return false;
    }
    if (typeof Page === 'undefined') {
        return false;
    }
    if (!wx.getSystemInfoSync) {
        return false;
    }
    if (!wx.getStorageSync) {
        return false;
    }
    if (!wx.setStorageSync) {
        return false;
    }
    if (!wx.connectSocket) {
        return false;
    }
    if (!wx.request) {
        return false;
    }
    try {
        if (!wx.getSystemInfoSync()) {
            return false;
        }
        if (wx.getSystemInfoSync().AppPlatform === 'qq') {
            return false;
        }
    }
    catch (e) {
        return false;
    }
    return true;
}
exports.isMatch = isMatch;
//# sourceMappingURL=data:application/json;base64,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