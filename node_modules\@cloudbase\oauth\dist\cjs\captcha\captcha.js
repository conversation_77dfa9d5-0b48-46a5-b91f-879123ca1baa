"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Captcha = void 0;
var consts_1 = require("../auth/consts");
var oauth2client_1 = require("../oauth2client/oauth2client");
var mp_1 = require("../utils/mp");
var urlSearchParams_1 = __importDefault(require("../utils/urlSearchParams"));
var Captcha = (function () {
    function Captcha(opts) {
        if (!opts.openURIWithCallback) {
            opts.openURIWithCallback = this.getDefaultOpenURIWithCallback();
        }
        if (!opts.storage) {
            opts.storage = oauth2client_1.defaultStorage;
        }
        this.config = opts;
        this.tokenSectionName = "captcha_".concat(opts.clientId);
    }
    Captcha.prototype.request = function (url, options) {
        return __awaiter(this, void 0, void 0, function () {
            var state, reqURL, resp, err_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!options) {
                            options = {};
                        }
                        if (!options.method) {
                            options.method = 'GET';
                        }
                        state = "".concat(options.method, ":").concat(url);
                        reqURL = url;
                        if (!options.withCaptcha) return [3, 2];
                        return [4, this.appendCaptchaTokenToURL(url, state, false)];
                    case 1:
                        reqURL = _a.sent();
                        _a.label = 2;
                    case 2:
                        _a.trys.push([2, 4, , 7]);
                        return [4, this.config.request(reqURL, options)];
                    case 3:
                        resp = _a.sent();
                        return [3, 7];
                    case 4:
                        err_1 = _a.sent();
                        if (!(err_1.error === consts_1.ErrorType.CAPTCHA_REQUIRED || err_1.error === consts_1.ErrorType.CAPTCHA_INVALID)) return [3, 6];
                        return [4, this.appendCaptchaTokenToURL(url, state, err_1.error === consts_1.ErrorType.CAPTCHA_INVALID)];
                    case 5:
                        url = _a.sent();
                        return [2, this.config.request(url, options)];
                    case 6: return [2, Promise.reject(err_1)];
                    case 7: return [2, resp];
                }
            });
        });
    };
    Captcha.prototype.getDefaultOpenURIWithCallback = function () {
        if (!(0, mp_1.isMp)() && !(0, mp_1.isInMpWebView)()) {
            if (window.location.search.indexOf('__captcha') > 0) {
                document.body.style.display = 'none';
            }
            if (document.getElementById('captcha_panel_wrap') === null) {
                var elementDiv_1 = document.createElement('div');
                elementDiv_1.style.cssText = 'background-color: rgba(0, 0, 0, 0.7);position: fixed;left: 0px;right: 0px;top: 0px;bottom: 0px;padding: 9vw 0 0 0;display: none;z-index:100;';
                elementDiv_1.setAttribute('id', 'captcha_panel_wrap');
                setTimeout(function () {
                    document.body.appendChild(elementDiv_1);
                }, 0);
            }
        }
        return this.defaultOpenURIWithCallback;
    };
    Captcha.prototype.defaultOpenURIWithCallback = function (url, opts) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, _b, width, _c, height, matched, target, iframe;
            return __generator(this, function (_d) {
                _a = opts || {}, _b = _a.width, width = _b === void 0 ? '355px' : _b, _c = _a.height, height = _c === void 0 ? '355px' : _c;
                matched = url.match(/^(data:.*)$/);
                if (matched) {
                    return [2, Promise.reject({
                            error: consts_1.ErrorType.UNIMPLEMENTED,
                            error_description: 'need to impl captcha data',
                        })];
                }
                target = document.getElementById('captcha_panel_wrap');
                iframe = document.createElement('iframe');
                target.innerHTML = '';
                iframe.setAttribute('src', url);
                iframe.setAttribute('id', 'review-panel-iframe');
                iframe.style.cssText = "min-width:".concat(width, ";display:block;height:").concat(height, ";margin:0 auto;background-color: rgb(255, 255, 255);border: none;");
                target.appendChild(iframe);
                target.style.display = 'block';
                return [2, new Promise(function (resolve, reject) {
                        iframe.onload = function () {
                            try {
                                var windowLocation = window.location;
                                var iframeLocation = iframe.contentWindow.location;
                                if (iframeLocation.host + iframeLocation.pathname === windowLocation.host + windowLocation.pathname) {
                                    target.style.display = 'none';
                                    var iframeUrlParams = new urlSearchParams_1.default(iframeLocation.search);
                                    var captchToken = iframeUrlParams.get('captcha_token');
                                    if (captchToken) {
                                        return resolve({
                                            captcha_token: captchToken,
                                            expires_in: Number(iframeUrlParams.get('expires_in')),
                                        });
                                    }
                                    return reject({
                                        error: iframeUrlParams.get('error'),
                                        error_description: iframeUrlParams.get('error_description'),
                                    });
                                }
                                target.style.display = 'block';
                            }
                            catch (error) {
                                target.style.display = 'block';
                            }
                        };
                    })];
            });
        });
    };
    Captcha.prototype.getCaptchaToken = function (forceNewToken, state) {
        return __awaiter(this, void 0, void 0, function () {
            var captchaToken_1, captchaTokenResp, redirect_uri, captchaToken_2, captchaDataResp, captchaToken;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!!forceNewToken) return [3, 2];
                        return [4, this.findCaptchaToken()];
                    case 1:
                        captchaToken_1 = _a.sent();
                        if (captchaToken_1) {
                            return [2, captchaToken_1];
                        }
                        _a.label = 2;
                    case 2:
                        if (!(!(0, mp_1.isMp)() && !(0, mp_1.isInMpWebView)())) return [3, 4];
                        redirect_uri = "".concat(window.location.origin + window.location.pathname, "?__captcha=on");
                        return [4, this.config.request(consts_1.ApiUrls.GET_CAPTCHA_URL, {
                                method: 'POST',
                                body: {
                                    client_id: this.config.clientId,
                                    redirect_uri: redirect_uri,
                                    state: state,
                                },
                                withCredentials: false,
                            })];
                    case 3:
                        captchaTokenResp = _a.sent();
                        if (captchaTokenResp.captcha_token) {
                            captchaToken_2 = {
                                captcha_token: captchaTokenResp.captcha_token,
                                expires_in: captchaTokenResp.expires_in,
                            };
                            this.saveCaptchaToken(captchaToken_2);
                            return [2, captchaTokenResp.captcha_token];
                        }
                        return [3, 6];
                    case 4: return [4, this.config.request(consts_1.ApiUrls.CAPTCHA_DATA_URL, {
                            method: 'POST',
                            body: {
                                state: state,
                                redirect_uri: '',
                            },
                            withCredentials: false,
                        })];
                    case 5:
                        captchaDataResp = _a.sent();
                        captchaTokenResp = {
                            url: "".concat(captchaDataResp.data, "?state=").concat(encodeURIComponent(state), "&token=").concat(encodeURIComponent(captchaDataResp.token)),
                        };
                        _a.label = 6;
                    case 6: return [4, this.config.openURIWithCallback(captchaTokenResp.url)];
                    case 7:
                        captchaToken = _a.sent();
                        this.saveCaptchaToken(captchaToken);
                        return [2, captchaToken.captcha_token];
                }
            });
        });
    };
    Captcha.prototype.appendCaptchaTokenToURL = function (url, state, forceNewToken) {
        return __awaiter(this, void 0, void 0, function () {
            var captchaToken;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, this.getCaptchaToken(forceNewToken, state)];
                    case 1:
                        captchaToken = _a.sent();
                        if (url.indexOf('?') > 0) {
                            url += "&captcha_token=".concat(captchaToken);
                        }
                        else {
                            url += "?captcha_token=".concat(captchaToken);
                        }
                        return [2, url];
                }
            });
        });
    };
    Captcha.prototype.saveCaptchaToken = function (token) {
        return __awaiter(this, void 0, void 0, function () {
            var tokenStr;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        token.expires_at = new Date(Date.now() + (token.expires_in - 10) * 1000);
                        tokenStr = JSON.stringify(token);
                        return [4, this.config.storage.setItem(this.tokenSectionName, tokenStr)];
                    case 1:
                        _a.sent();
                        return [2];
                }
            });
        });
    };
    Captcha.prototype.findCaptchaToken = function () {
        return __awaiter(this, void 0, void 0, function () {
            var tokenStr, captchaToken, isExpired, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4, this.config.storage.getItem(this.tokenSectionName)];
                    case 1:
                        tokenStr = _a.sent();
                        if (!(tokenStr !== undefined && tokenStr !== null)) return [3, 5];
                        _a.label = 2;
                    case 2:
                        _a.trys.push([2, 3, , 5]);
                        captchaToken = JSON.parse(tokenStr);
                        if (captchaToken === null || captchaToken === void 0 ? void 0 : captchaToken.expires_at) {
                            captchaToken.expires_at = new Date(captchaToken.expires_at);
                        }
                        isExpired = captchaToken.expires_at < new Date();
                        if (isExpired) {
                            return [2, null];
                        }
                        return [2, captchaToken.captcha_token];
                    case 3:
                        error_1 = _a.sent();
                        return [4, this.config.storage.removeItem(this.tokenSectionName)];
                    case 4:
                        _a.sent();
                        return [2, null];
                    case 5: return [2, null];
                }
            });
        });
    };
    return Captcha;
}());
exports.Captcha = Captcha;
//# sourceMappingURL=data:application/json;base64,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