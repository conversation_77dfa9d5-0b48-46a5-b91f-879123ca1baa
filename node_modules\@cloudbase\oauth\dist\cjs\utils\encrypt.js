"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEncryptInfo = void 0;
var encryptlong_1 = __importDefault(require("./encryptlong"));
var index_1 = require("./index");
var getEncryptInfo = function (_a) {
    var _b = _a === void 0 ? {} : _a, _c = _b.publicKey, publicKey = _c === void 0 ? '' : _c, _d = _b.payload, payload = _d === void 0 ? {} : _d;
    if (!publicKey)
        return '';
    try {
        var params = (0, index_1.deepClone)(payload);
        var rsaInstance = new encryptlong_1.default();
        rsaInstance.setPublicKey(publicKey);
        var encrypted = rsaInstance.encryptLong(typeof params === 'object' ? JSON.stringify(params) : params);
        return encrypted;
    }
    catch (error) {
        console.error('encrypt error:', error);
    }
    return '';
};
exports.getEncryptInfo = getEncryptInfo;
//# sourceMappingURL=data:application/json;base64,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