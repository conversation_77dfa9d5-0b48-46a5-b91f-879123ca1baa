"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SinglePromise = void 0;
var SinglePromise = (function () {
    function SinglePromise(options) {
        this.clientId = (options === null || options === void 0 ? void 0 : options.clientId) || '';
        globalThis.jsSdkFnPromiseMap = (globalThis.jsSdkFnPromiseMap || new Map());
    }
    SinglePromise.prototype.run = function (key, fn) {
        return __awaiter(this, void 0, void 0, function () {
            var result;
            var _this = this;
            return __generator(this, function (_a) {
                key = "".concat(this.clientId, "_").concat(key);
                result = globalThis.jsSdkFnPromiseMap.get(key);
                if (!result) {
                    result = new Promise(function (resolve, reject) {
                        (function () { return __awaiter(_this, void 0, void 0, function () {
                            var fnResult, _a, error_1;
                            return __generator(this, function (_b) {
                                switch (_b.label) {
                                    case 0:
                                        _b.trys.push([0, 3, 4, 5]);
                                        return [4, this.runIdlePromise()];
                                    case 1:
                                        _b.sent();
                                        fnResult = fn();
                                        _a = resolve;
                                        return [4, fnResult];
                                    case 2:
                                        _a.apply(void 0, [_b.sent()]);
                                        return [3, 5];
                                    case 3:
                                        error_1 = _b.sent();
                                        reject(error_1);
                                        return [3, 5];
                                    case 4:
                                        globalThis.jsSdkFnPromiseMap.delete(key);
                                        return [7];
                                    case 5: return [2];
                                }
                            });
                        }); })();
                    });
                    globalThis.jsSdkFnPromiseMap.set(key, result);
                }
                return [2, result];
            });
        });
    };
    SinglePromise.prototype.runIdlePromise = function () {
        return Promise.resolve();
    };
    return SinglePromise;
}());
exports.SinglePromise = SinglePromise;
//# sourceMappingURL=data:application/json;base64,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