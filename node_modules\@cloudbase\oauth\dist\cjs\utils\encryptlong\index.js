"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var JSEncrypt;
if (!globalThis.IS_MP_BUILD) {
    var navigator_1 = typeof globalThis !== 'undefined' ? globalThis.navigator : window.globalThis;
    var BI_RM = '0123456789abcdefghijklmnopqrstuvwxyz';
    function int2char(n) {
        return BI_RM.charAt(n);
    }
    function op_and(x, y) {
        return x & y;
    }
    function op_or(x, y) {
        return x | y;
    }
    function op_xor(x, y) {
        return x ^ y;
    }
    function op_andnot(x, y) {
        return x & ~y;
    }
    function lbit(x) {
        if (x == 0) {
            return -1;
        }
        var r = 0;
        if ((x & 0xffff) == 0) {
            x >>= 16;
            r += 16;
        }
        if ((x & 0xff) == 0) {
            x >>= 8;
            r += 8;
        }
        if ((x & 0xf) == 0) {
            x >>= 4;
            r += 4;
        }
        if ((x & 3) == 0) {
            x >>= 2;
            r += 2;
        }
        if ((x & 1) == 0) {
            ++r;
        }
        return r;
    }
    function cbit(x) {
        var r = 0;
        while (x != 0) {
            x &= x - 1;
            ++r;
        }
        return r;
    }
    var b64map = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    var b64pad = '=';
    function hex2b64(h) {
        var i;
        var c;
        var ret = '';
        for (i = 0; i + 3 <= h.length; i += 3) {
            c = parseInt(h.substring(i, i + 3), 16);
            ret += b64map.charAt(c >> 6) + b64map.charAt(c & 63);
        }
        if (i + 1 == h.length) {
            c = parseInt(h.substring(i, i + 1), 16);
            ret += b64map.charAt(c << 2);
        }
        else if (i + 2 == h.length) {
            c = parseInt(h.substring(i, i + 2), 16);
            ret += b64map.charAt(c >> 2) + b64map.charAt((c & 3) << 4);
        }
        while ((ret.length & 3) > 0) {
            ret += b64pad;
        }
        return ret;
    }
    function b64tohex(s) {
        var ret = '';
        var i;
        var k = 0;
        var slop = 0;
        for (i = 0; i < s.length; ++i) {
            if (s.charAt(i) == b64pad) {
                break;
            }
            var v = b64map.indexOf(s.charAt(i));
            if (v < 0) {
                continue;
            }
            if (k == 0) {
                ret += int2char(v >> 2);
                slop = v & 3;
                k = 1;
            }
            else if (k == 1) {
                ret += int2char((slop << 2) | (v >> 4));
                slop = v & 0xf;
                k = 2;
            }
            else if (k == 2) {
                ret += int2char(slop);
                ret += int2char(v >> 2);
                slop = v & 3;
                k = 3;
            }
            else {
                ret += int2char((slop << 2) | (v >> 4));
                ret += int2char(v & 0xf);
                k = 0;
            }
        }
        if (k == 1) {
            ret += int2char(slop << 2);
        }
        return ret;
    }
    var extendStatics = function (d, b) {
        extendStatics =
            Object.setPrototypeOf ||
                ({ __proto__: [] } instanceof Array &&
                    function (d, b) {
                        d.__proto__ = b;
                    }) ||
                function (d, b) {
                    for (var p in b)
                        if (b.hasOwnProperty(p))
                            d[p] = b[p];
                };
        return extendStatics(d, b);
    };
    var decoder;
    var Hex = {
        decode: function (a) {
            var i;
            if (decoder === undefined) {
                var hex = '0123456789ABCDEF';
                var ignore = ' \f\n\r\t\u00A0\u2028\u2029';
                decoder = {};
                for (i = 0; i < 16; ++i) {
                    decoder[hex.charAt(i)] = i;
                }
                hex = hex.toLowerCase();
                for (i = 10; i < 16; ++i) {
                    decoder[hex.charAt(i)] = i;
                }
                for (i = 0; i < ignore.length; ++i) {
                    decoder[ignore.charAt(i)] = -1;
                }
            }
            var out = [];
            var bits = 0;
            var char_count = 0;
            for (i = 0; i < a.length; ++i) {
                var c = a.charAt(i);
                if (c == '=') {
                    break;
                }
                c = decoder[c];
                if (c == -1) {
                    continue;
                }
                if (c === undefined) {
                    throw new Error('Illegal character at offset ' + i);
                }
                bits |= c;
                if (++char_count >= 2) {
                    out[out.length] = bits;
                    bits = 0;
                    char_count = 0;
                }
                else {
                    bits <<= 4;
                }
            }
            if (char_count) {
                throw new Error('Hex encoding incomplete: 4 bits missing');
            }
            return out;
        },
    };
    var decoder$1;
    var Base64 = {
        decode: function (a) {
            var i;
            if (decoder$1 === undefined) {
                var b64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
                var ignore = '= \f\n\r\t\u00A0\u2028\u2029';
                decoder$1 = Object.create(null);
                for (i = 0; i < 64; ++i) {
                    decoder$1[b64.charAt(i)] = i;
                }
                for (i = 0; i < ignore.length; ++i) {
                    decoder$1[ignore.charAt(i)] = -1;
                }
            }
            var out = [];
            var bits = 0;
            var char_count = 0;
            for (i = 0; i < a.length; ++i) {
                var c = a.charAt(i);
                if (c == '=') {
                    break;
                }
                c = decoder$1[c];
                if (c == -1) {
                    continue;
                }
                if (c === undefined) {
                    throw new Error('Illegal character at offset ' + i);
                }
                bits |= c;
                if (++char_count >= 4) {
                    out[out.length] = bits >> 16;
                    out[out.length] = (bits >> 8) & 0xff;
                    out[out.length] = bits & 0xff;
                    bits = 0;
                    char_count = 0;
                }
                else {
                    bits <<= 6;
                }
            }
            switch (char_count) {
                case 1:
                    throw new Error('Base64 encoding incomplete: at least 2 bits missing');
                case 2:
                    out[out.length] = bits >> 10;
                    break;
                case 3:
                    out[out.length] = bits >> 16;
                    out[out.length] = (bits >> 8) & 0xff;
                    break;
            }
            return out;
        },
        re: /-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,
        unarmor: function (a) {
            var m = Base64.re.exec(a);
            if (m) {
                if (m[1]) {
                    a = m[1];
                }
                else if (m[2]) {
                    a = m[2];
                }
                else {
                    throw new Error('RegExp out of sync');
                }
            }
            return Base64.decode(a);
        },
    };
    var max = 10000000000000;
    var Int10_1 = (function () {
        function Int10(value) {
            this.buf = [+value || 0];
        }
        Int10.prototype.mulAdd = function (m, c) {
            var b = this.buf;
            var l = b.length;
            var i;
            var t;
            for (i = 0; i < l; ++i) {
                t = b[i] * m + c;
                if (t < max) {
                    c = 0;
                }
                else {
                    c = 0 | (t / max);
                    t -= c * max;
                }
                b[i] = t;
            }
            if (c > 0) {
                b[i] = c;
            }
        };
        Int10.prototype.sub = function (c) {
            var b = this.buf;
            var l = b.length;
            var i;
            var t;
            for (i = 0; i < l; ++i) {
                t = b[i] - c;
                if (t < 0) {
                    t += max;
                    c = 1;
                }
                else {
                    c = 0;
                }
                b[i] = t;
            }
            while (b[b.length - 1] === 0) {
                b.pop();
            }
        };
        Int10.prototype.toString = function (base) {
            if ((base || 10) != 10) {
                throw new Error('only base 10 is supported');
            }
            var b = this.buf;
            var s = b[b.length - 1].toString();
            for (var i = b.length - 2; i >= 0; --i) {
                s += (max + b[i]).toString().substring(1);
            }
            return s;
        };
        Int10.prototype.valueOf = function () {
            var b = this.buf;
            var v = 0;
            for (var i = b.length - 1; i >= 0; --i) {
                v = v * max + b[i];
            }
            return v;
        };
        Int10.prototype.simplify = function () {
            var b = this.buf;
            return b.length == 1 ? b[0] : this;
        };
        return Int10;
    }());
    var ellipsis = '\u2026';
    var reTimeS = /^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;
    var reTimeL = /^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;
    function stringCut(str, len) {
        if (str.length > len) {
            str = str.substring(0, len) + ellipsis;
        }
        return str;
    }
    var Stream_1 = (function () {
        function Stream(enc, pos) {
            this.hexDigits = '0123456789ABCDEF';
            if (enc instanceof Stream) {
                this.enc = enc.enc;
                this.pos = enc.pos;
            }
            else {
                this.enc = enc;
                this.pos = pos;
            }
        }
        Stream.prototype.get = function (pos) {
            if (pos === undefined) {
                pos = this.pos++;
            }
            if (pos >= this.enc.length) {
                throw new Error('Requesting byte offset ' + pos + ' on a stream of length ' + this.enc.length);
            }
            return 'string' === typeof this.enc ? this.enc.charCodeAt(pos) : this.enc[pos];
        };
        Stream.prototype.hexByte = function (b) {
            return this.hexDigits.charAt((b >> 4) & 0xf) + this.hexDigits.charAt(b & 0xf);
        };
        Stream.prototype.hexDump = function (start, end, raw) {
            var s = '';
            for (var i = start; i < end; ++i) {
                s += this.hexByte(this.get(i));
                if (raw !== true) {
                    switch (i & 0xf) {
                        case 0x7:
                            s += '  ';
                            break;
                        case 0xf:
                            s += '\n';
                            break;
                        default:
                            s += ' ';
                    }
                }
            }
            return s;
        };
        Stream.prototype.isASCII = function (start, end) {
            for (var i = start; i < end; ++i) {
                var c = this.get(i);
                if (c < 32 || c > 176) {
                    return false;
                }
            }
            return true;
        };
        Stream.prototype.parseStringISO = function (start, end) {
            var s = '';
            for (var i = start; i < end; ++i) {
                s += String.fromCharCode(this.get(i));
            }
            return s;
        };
        Stream.prototype.parseStringUTF = function (start, end) {
            var s = '';
            for (var i = start; i < end;) {
                var c = this.get(i++);
                if (c < 128) {
                    s += String.fromCharCode(c);
                }
                else if (c > 191 && c < 224) {
                    s += String.fromCharCode(((c & 0x1f) << 6) | (this.get(i++) & 0x3f));
                }
                else {
                    s += String.fromCharCode(((c & 0x0f) << 12) | ((this.get(i++) & 0x3f) << 6) | (this.get(i++) & 0x3f));
                }
            }
            return s;
        };
        Stream.prototype.parseStringBMP = function (start, end) {
            var str = '';
            var hi;
            var lo;
            for (var i = start; i < end;) {
                hi = this.get(i++);
                lo = this.get(i++);
                str += String.fromCharCode((hi << 8) | lo);
            }
            return str;
        };
        Stream.prototype.parseTime = function (start, end, shortYear) {
            var s = this.parseStringISO(start, end);
            var m = (shortYear ? reTimeS : reTimeL).exec(s);
            if (!m) {
                return 'Unrecognized time: ' + s;
            }
            if (shortYear) {
                m[1] = +m[1];
                m[1] += +m[1] < 70 ? 2000 : 1900;
            }
            s = m[1] + '-' + m[2] + '-' + m[3] + ' ' + m[4];
            if (m[5]) {
                s += ':' + m[5];
                if (m[6]) {
                    s += ':' + m[6];
                    if (m[7]) {
                        s += '.' + m[7];
                    }
                }
            }
            if (m[8]) {
                s += ' UTC';
                if (m[8] != 'Z') {
                    s += m[8];
                    if (m[9]) {
                        s += ':' + m[9];
                    }
                }
            }
            return s;
        };
        Stream.prototype.parseInteger = function (start, end) {
            var v = this.get(start);
            var neg = v > 127;
            var pad = neg ? 255 : 0;
            var len;
            var s = '';
            while (v == pad && ++start < end) {
                v = this.get(start);
            }
            len = end - start;
            if (len === 0) {
                return neg ? -1 : 0;
            }
            if (len > 4) {
                s = v;
                len <<= 3;
                while (((+s ^ pad) & 0x80) == 0) {
                    s = +s << 1;
                    --len;
                }
                s = '(' + len + ' bit)\n';
            }
            if (neg) {
                v = v - 256;
            }
            var n = new Int10_1(v);
            for (var i = start + 1; i < end; ++i) {
                n.mulAdd(256, this.get(i));
            }
            return s + n.toString();
        };
        Stream.prototype.parseBitString = function (start, end, maxLength) {
            var unusedBit = this.get(start);
            var lenBit = ((end - start - 1) << 3) - unusedBit;
            var intro = '(' + lenBit + ' bit)\n';
            var s = '';
            for (var i = start + 1; i < end; ++i) {
                var b = this.get(i);
                var skip = i == end - 1 ? unusedBit : 0;
                for (var j = 7; j >= skip; --j) {
                    s += (b >> j) & 1 ? '1' : '0';
                }
                if (s.length > maxLength) {
                    return intro + stringCut(s, maxLength);
                }
            }
            return intro + s;
        };
        Stream.prototype.parseOctetString = function (start, end, maxLength) {
            if (this.isASCII(start, end)) {
                return stringCut(this.parseStringISO(start, end), maxLength);
            }
            var len = end - start;
            var s = '(' + len + ' byte)\n';
            maxLength /= 2;
            if (len > maxLength) {
                end = start + maxLength;
            }
            for (var i = start; i < end; ++i) {
                s += this.hexByte(this.get(i));
            }
            if (len > maxLength) {
                s += ellipsis;
            }
            return s;
        };
        Stream.prototype.parseOID = function (start, end, maxLength) {
            var s = '';
            var n = new Int10_1();
            var bits = 0;
            for (var i = start; i < end; ++i) {
                var v = this.get(i);
                n.mulAdd(128, v & 0x7f);
                bits += 7;
                if (!(v & 0x80)) {
                    if (s === '') {
                        n = n.simplify();
                        if (n instanceof Int10_1) {
                            n.sub(80);
                            s = '2.' + n.toString();
                        }
                        else {
                            var m = n < 80 ? (n < 40 ? 0 : 1) : 2;
                            s = m + '.' + (n - m * 40);
                        }
                    }
                    else {
                        s += '.' + n.toString();
                    }
                    if (s.length > maxLength) {
                        return stringCut(s, maxLength);
                    }
                    n = new Int10_1();
                    bits = 0;
                }
            }
            if (bits > 0) {
                s += '.incomplete';
            }
            return s;
        };
        return Stream;
    }());
    var ASN1_1 = (function () {
        function ASN1(stream, header, length, tag, sub) {
            if (!(tag instanceof ASN1Tag_1)) {
                throw new Error('Invalid tag value.');
            }
            this.stream = stream;
            this.header = header;
            this.length = length;
            this.tag = tag;
            this.sub = sub;
        }
        ASN1.prototype.typeName = function () {
            switch (this.tag.tagClass) {
                case 0:
                    switch (this.tag.tagNumber) {
                        case 0x00:
                            return 'EOC';
                        case 0x01:
                            return 'BOOLEAN';
                        case 0x02:
                            return 'INTEGER';
                        case 0x03:
                            return 'BIT_STRING';
                        case 0x04:
                            return 'OCTET_STRING';
                        case 0x05:
                            return 'NULL';
                        case 0x06:
                            return 'OBJECT_IDENTIFIER';
                        case 0x07:
                            return 'ObjectDescriptor';
                        case 0x08:
                            return 'EXTERNAL';
                        case 0x09:
                            return 'REAL';
                        case 0x0a:
                            return 'ENUMERATED';
                        case 0x0b:
                            return 'EMBEDDED_PDV';
                        case 0x0c:
                            return 'UTF8String';
                        case 0x10:
                            return 'SEQUENCE';
                        case 0x11:
                            return 'SET';
                        case 0x12:
                            return 'NumericString';
                        case 0x13:
                            return 'PrintableString';
                        case 0x14:
                            return 'TeletexString';
                        case 0x15:
                            return 'VideotexString';
                        case 0x16:
                            return 'IA5String';
                        case 0x17:
                            return 'UTCTime';
                        case 0x18:
                            return 'GeneralizedTime';
                        case 0x19:
                            return 'GraphicString';
                        case 0x1a:
                            return 'VisibleString';
                        case 0x1b:
                            return 'GeneralString';
                        case 0x1c:
                            return 'UniversalString';
                        case 0x1e:
                            return 'BMPString';
                    }
                    return 'Universal_' + this.tag.tagNumber.toString();
                case 1:
                    return 'Application_' + this.tag.tagNumber.toString();
                case 2:
                    return '[' + this.tag.tagNumber.toString() + ']';
                case 3:
                    return 'Private_' + this.tag.tagNumber.toString();
            }
        };
        ASN1.prototype.content = function (maxLength) {
            if (this.tag === undefined) {
                return null;
            }
            if (maxLength === undefined) {
                maxLength = Infinity;
            }
            var content = this.posContent();
            var len = Math.abs(this.length);
            if (!this.tag.isUniversal()) {
                if (this.sub !== null) {
                    return '(' + this.sub.length + ' elem)';
                }
                return this.stream.parseOctetString(content, content + len, maxLength);
            }
            switch (this.tag.tagNumber) {
                case 0x01:
                    return this.stream.get(content) === 0 ? 'false' : 'true';
                case 0x02:
                    return this.stream.parseInteger(content, content + len);
                case 0x03:
                    return this.sub
                        ? '(' + this.sub.length + ' elem)'
                        : this.stream.parseBitString(content, content + len, maxLength);
                case 0x04:
                    return this.sub
                        ? '(' + this.sub.length + ' elem)'
                        : this.stream.parseOctetString(content, content + len, maxLength);
                case 0x06:
                    return this.stream.parseOID(content, content + len, maxLength);
                case 0x10:
                case 0x11:
                    if (this.sub !== null) {
                        return '(' + this.sub.length + ' elem)';
                    }
                    else {
                        return '(no elem)';
                    }
                case 0x0c:
                    return stringCut(this.stream.parseStringUTF(content, content + len), maxLength);
                case 0x12:
                case 0x13:
                case 0x14:
                case 0x15:
                case 0x16:
                case 0x1a:
                    return stringCut(this.stream.parseStringISO(content, content + len), maxLength);
                case 0x1e:
                    return stringCut(this.stream.parseStringBMP(content, content + len), maxLength);
                case 0x17:
                case 0x18:
                    return this.stream.parseTime(content, content + len, this.tag.tagNumber == 0x17);
            }
            return null;
        };
        ASN1.prototype.toString = function () {
            return (this.typeName() +
                '@' +
                this.stream.pos +
                '[header:' +
                this.header +
                ',length:' +
                this.length +
                ',sub:' +
                (this.sub === null ? 'null' : this.sub.length) +
                ']');
        };
        ASN1.prototype.toPrettyString = function (indent) {
            if (indent === undefined) {
                indent = '';
            }
            var s = indent + this.typeName() + ' @' + this.stream.pos;
            if (this.length >= 0) {
                s += '+';
            }
            s += this.length;
            if (this.tag.tagConstructed) {
                s += ' (constructed)';
            }
            else if (this.tag.isUniversal() &&
                (this.tag.tagNumber == 0x03 || this.tag.tagNumber == 0x04) &&
                this.sub !== null) {
                s += ' (encapsulates)';
            }
            s += '\n';
            if (this.sub !== null) {
                indent += '  ';
                for (var i = 0, max = this.sub.length; i < max; ++i) {
                    s += this.sub[i].toPrettyString(indent);
                }
            }
            return s;
        };
        ASN1.prototype.posStart = function () {
            return this.stream.pos;
        };
        ASN1.prototype.posContent = function () {
            return this.stream.pos + this.header;
        };
        ASN1.prototype.posEnd = function () {
            return this.stream.pos + this.header + Math.abs(this.length);
        };
        ASN1.prototype.toHexString = function () {
            return this.stream.hexDump(this.posStart(), this.posEnd(), true);
        };
        ASN1.decodeLength = function (stream) {
            var buf = stream.get();
            var len = buf & 0x7f;
            if (len == buf) {
                return len;
            }
            if (len > 6) {
                throw new Error('Length over 48 bits not supported at position ' + (stream.pos - 1));
            }
            if (len === 0) {
                return null;
            }
            buf = 0;
            for (var i = 0; i < len; ++i) {
                buf = buf * 256 + stream.get();
            }
            return buf;
        };
        ASN1.prototype.getHexStringValue = function () {
            var hexString = this.toHexString();
            var offset = this.header * 2;
            var length = this.length * 2;
            return hexString.substr(offset, length);
        };
        ASN1.decode = function (str) {
            var stream;
            if (!(str instanceof Stream_1)) {
                stream = new Stream_1(str, 0);
            }
            else {
                stream = str;
            }
            var streamStart = new Stream_1(stream);
            var tag = new ASN1Tag_1(stream);
            var len = ASN1.decodeLength(stream);
            var start = stream.pos;
            var header = start - streamStart.pos;
            var sub = null;
            var getSub = function () {
                var ret = [];
                if (len !== null) {
                    var end = start + len;
                    while (stream.pos < end) {
                        ret[ret.length] = ASN1.decode(stream);
                    }
                    if (stream.pos != end) {
                        throw new Error('Content size is not correct for container starting at offset ' + start);
                    }
                }
                else {
                    try {
                        for (;;) {
                            var s = ASN1.decode(stream);
                            if (s.tag.isEOC()) {
                                break;
                            }
                            ret[ret.length] = s;
                        }
                        len = start - stream.pos;
                    }
                    catch (e) {
                        throw new Error('Exception while decoding undefined length content: ' + e);
                    }
                }
                return ret;
            };
            if (tag.tagConstructed) {
                sub = getSub();
            }
            else if (tag.isUniversal() && (tag.tagNumber == 0x03 || tag.tagNumber == 0x04)) {
                try {
                    if (tag.tagNumber == 0x03) {
                        if (stream.get() != 0) {
                            throw new Error('BIT STRINGs with unused bits cannot encapsulate.');
                        }
                    }
                    sub = getSub();
                    for (var i = 0; i < sub.length; ++i) {
                        if (sub[i].tag.isEOC()) {
                            throw new Error('EOC is not supposed to be actual content.');
                        }
                    }
                }
                catch (e) {
                    sub = null;
                }
            }
            if (sub === null) {
                if (len === null) {
                    throw new Error("We can't skip over an invalid tag with undefined length at offset " + start);
                }
                stream.pos = start + Math.abs(len);
            }
            return new ASN1(streamStart, header, len, tag, sub);
        };
        return ASN1;
    }());
    var ASN1Tag_1 = (function () {
        function ASN1Tag(stream) {
            var buf = stream.get();
            this.tagClass = buf >> 6;
            this.tagConstructed = (buf & 0x20) !== 0;
            this.tagNumber = buf & 0x1f;
            if (this.tagNumber == 0x1f) {
                var n = new Int10_1();
                do {
                    buf = stream.get();
                    n.mulAdd(128, buf & 0x7f);
                } while (buf & 0x80);
                this.tagNumber = n.simplify();
            }
        }
        ASN1Tag.prototype.isUniversal = function () {
            return this.tagClass === 0x00;
        };
        ASN1Tag.prototype.isEOC = function () {
            return this.tagClass === 0x00 && this.tagNumber === 0x00;
        };
        return ASN1Tag;
    }());
    var dbits;
    var canary = 0xdeadbeefcafe;
    var j_lm = (canary & 0xffffff) == 0xefcafe;
    var lowprimes = [
        2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97, 101, 103, 107, 109,
        113, 127, 131, 137, 139, 149, 151, 157, 163, 167, 173, 179, 181, 191, 193, 197, 199, 211, 223, 227, 229, 233, 239,
        241, 251, 257, 263, 269, 271, 277, 281, 283, 293, 307, 311, 313, 317, 331, 337, 347, 349, 353, 359, 367, 373, 379,
        383, 389, 397, 401, 409, 419, 421, 431, 433, 439, 443, 449, 457, 461, 463, 467, 479, 487, 491, 499, 503, 509, 521,
        523, 541, 547, 557, 563, 569, 571, 577, 587, 593, 599, 601, 607, 613, 617, 619, 631, 641, 643, 647, 653, 659, 661,
        673, 677, 683, 691, 701, 709, 719, 727, 733, 739, 743, 751, 757, 761, 769, 773, 787, 797, 809, 811, 821, 823, 827,
        829, 839, 853, 857, 859, 863, 877, 881, 883, 887, 907, 911, 919, 929, 937, 941, 947, 953, 967, 971, 977, 983, 991,
        997,
    ];
    var lplim = (1 << 26) / lowprimes[lowprimes.length - 1];
    var BigInteger_1 = (function () {
        function BigInteger(a, b, c) {
            if (a != null) {
                if ('number' == typeof a) {
                    this.fromNumber(a, b, c);
                }
                else if (b == null && 'string' != typeof a) {
                    this.fromString(a, 256);
                }
                else {
                    this.fromString(a, b);
                }
            }
        }
        BigInteger.prototype.toString = function (b) {
            if (this.s < 0) {
                return '-' + this.negate().toString(b);
            }
            var k;
            if (b == 16) {
                k = 4;
            }
            else if (b == 8) {
                k = 3;
            }
            else if (b == 2) {
                k = 1;
            }
            else if (b == 32) {
                k = 5;
            }
            else if (b == 4) {
                k = 2;
            }
            else {
                return this.toRadix(b);
            }
            var km = (1 << k) - 1;
            var d;
            var m = false;
            var r = '';
            var i = this.t;
            var p = this.DB - ((i * this.DB) % k);
            if (i-- > 0) {
                if (p < this.DB && (d = this[i] >> p) > 0) {
                    m = true;
                    r = int2char(d);
                }
                while (i >= 0) {
                    if (p < k) {
                        d = (this[i] & ((1 << p) - 1)) << (k - p);
                        d |= this[--i] >> (p += this.DB - k);
                    }
                    else {
                        d = (this[i] >> (p -= k)) & km;
                        if (p <= 0) {
                            p += this.DB;
                            --i;
                        }
                    }
                    if (d > 0) {
                        m = true;
                    }
                    if (m) {
                        r += int2char(d);
                    }
                }
            }
            return m ? r : '0';
        };
        BigInteger.prototype.negate = function () {
            var r = nbi();
            BigInteger.ZERO.subTo(this, r);
            return r;
        };
        BigInteger.prototype.abs = function () {
            return this.s < 0 ? this.negate() : this;
        };
        BigInteger.prototype.compareTo = function (a) {
            var r = this.s - a.s;
            if (r != 0) {
                return r;
            }
            var i = this.t;
            r = i - a.t;
            if (r != 0) {
                return this.s < 0 ? -r : r;
            }
            while (--i >= 0) {
                if ((r = this[i] - a[i]) != 0) {
                    return r;
                }
            }
            return 0;
        };
        BigInteger.prototype.bitLength = function () {
            if (this.t <= 0) {
                return 0;
            }
            return this.DB * (this.t - 1) + nbits(this[this.t - 1] ^ (this.s & this.DM));
        };
        BigInteger.prototype.mod = function (a) {
            var r = nbi();
            this.abs().divRemTo(a, null, r);
            if (this.s < 0 && r.compareTo(BigInteger.ZERO) > 0) {
                a.subTo(r, r);
            }
            return r;
        };
        BigInteger.prototype.modPowInt = function (e, m) {
            var z;
            if (e < 256 || m.isEven()) {
                z = new Classic(m);
            }
            else {
                z = new Montgomery(m);
            }
            return this.exp(e, z);
        };
        BigInteger.prototype.clone = function () {
            var r = nbi();
            this.copyTo(r);
            return r;
        };
        BigInteger.prototype.intValue = function () {
            if (this.s < 0) {
                if (this.t == 1) {
                    return this[0] - this.DV;
                }
                else if (this.t == 0) {
                    return -1;
                }
            }
            else if (this.t == 1) {
                return this[0];
            }
            else if (this.t == 0) {
                return 0;
            }
            return ((this[1] & ((1 << (32 - this.DB)) - 1)) << this.DB) | this[0];
        };
        BigInteger.prototype.byteValue = function () {
            return this.t == 0 ? this.s : (this[0] << 24) >> 24;
        };
        BigInteger.prototype.shortValue = function () {
            return this.t == 0 ? this.s : (this[0] << 16) >> 16;
        };
        BigInteger.prototype.signum = function () {
            if (this.s < 0) {
                return -1;
            }
            else if (this.t <= 0 || (this.t == 1 && this[0] <= 0)) {
                return 0;
            }
            else {
                return 1;
            }
        };
        BigInteger.prototype.toByteArray = function () {
            var i = this.t;
            var r = [];
            r[0] = this.s;
            var p = this.DB - ((i * this.DB) % 8);
            var d;
            var k = 0;
            if (i-- > 0) {
                if (p < this.DB && (d = this[i] >> p) != (this.s & this.DM) >> p) {
                    r[k++] = d | (this.s << (this.DB - p));
                }
                while (i >= 0) {
                    if (p < 8) {
                        d = (this[i] & ((1 << p) - 1)) << (8 - p);
                        d |= this[--i] >> (p += this.DB - 8);
                    }
                    else {
                        d = (this[i] >> (p -= 8)) & 0xff;
                        if (p <= 0) {
                            p += this.DB;
                            --i;
                        }
                    }
                    if ((d & 0x80) != 0) {
                        d |= -256;
                    }
                    if (k == 0 && (this.s & 0x80) != (d & 0x80)) {
                        ++k;
                    }
                    if (k > 0 || d != this.s) {
                        r[k++] = d;
                    }
                }
            }
            return r;
        };
        BigInteger.prototype.equals = function (a) {
            return this.compareTo(a) == 0;
        };
        BigInteger.prototype.min = function (a) {
            return this.compareTo(a) < 0 ? this : a;
        };
        BigInteger.prototype.max = function (a) {
            return this.compareTo(a) > 0 ? this : a;
        };
        BigInteger.prototype.and = function (a) {
            var r = nbi();
            this.bitwiseTo(a, op_and, r);
            return r;
        };
        BigInteger.prototype.or = function (a) {
            var r = nbi();
            this.bitwiseTo(a, op_or, r);
            return r;
        };
        BigInteger.prototype.xor = function (a) {
            var r = nbi();
            this.bitwiseTo(a, op_xor, r);
            return r;
        };
        BigInteger.prototype.andNot = function (a) {
            var r = nbi();
            this.bitwiseTo(a, op_andnot, r);
            return r;
        };
        BigInteger.prototype.not = function () {
            var r = nbi();
            for (var i = 0; i < this.t; ++i) {
                r[i] = this.DM & ~this[i];
            }
            r.t = this.t;
            r.s = ~this.s;
            return r;
        };
        BigInteger.prototype.shiftLeft = function (n) {
            var r = nbi();
            if (n < 0) {
                this.rShiftTo(-n, r);
            }
            else {
                this.lShiftTo(n, r);
            }
            return r;
        };
        BigInteger.prototype.shiftRight = function (n) {
            var r = nbi();
            if (n < 0) {
                this.lShiftTo(-n, r);
            }
            else {
                this.rShiftTo(n, r);
            }
            return r;
        };
        BigInteger.prototype.getLowestSetBit = function () {
            for (var i = 0; i < this.t; ++i) {
                if (this[i] != 0) {
                    return i * this.DB + lbit(this[i]);
                }
            }
            if (this.s < 0) {
                return this.t * this.DB;
            }
            return -1;
        };
        BigInteger.prototype.bitCount = function () {
            var r = 0;
            var x = this.s & this.DM;
            for (var i = 0; i < this.t; ++i) {
                r += cbit(this[i] ^ x);
            }
            return r;
        };
        BigInteger.prototype.testBit = function (n) {
            var j = Math.floor(n / this.DB);
            if (j >= this.t) {
                return this.s != 0;
            }
            return (this[j] & (1 << n % this.DB)) != 0;
        };
        BigInteger.prototype.setBit = function (n) {
            return this.changeBit(n, op_or);
        };
        BigInteger.prototype.clearBit = function (n) {
            return this.changeBit(n, op_andnot);
        };
        BigInteger.prototype.flipBit = function (n) {
            return this.changeBit(n, op_xor);
        };
        BigInteger.prototype.add = function (a) {
            var r = nbi();
            this.addTo(a, r);
            return r;
        };
        BigInteger.prototype.subtract = function (a) {
            var r = nbi();
            this.subTo(a, r);
            return r;
        };
        BigInteger.prototype.multiply = function (a) {
            var r = nbi();
            this.multiplyTo(a, r);
            return r;
        };
        BigInteger.prototype.divide = function (a) {
            var r = nbi();
            this.divRemTo(a, r, null);
            return r;
        };
        BigInteger.prototype.remainder = function (a) {
            var r = nbi();
            this.divRemTo(a, null, r);
            return r;
        };
        BigInteger.prototype.divideAndRemainder = function (a) {
            var q = nbi();
            var r = nbi();
            this.divRemTo(a, q, r);
            return [q, r];
        };
        BigInteger.prototype.modPow = function (e, m) {
            var i = e.bitLength();
            var k;
            var r = nbv(1);
            var z;
            if (i <= 0) {
                return r;
            }
            else if (i < 18) {
                k = 1;
            }
            else if (i < 48) {
                k = 3;
            }
            else if (i < 144) {
                k = 4;
            }
            else if (i < 768) {
                k = 5;
            }
            else {
                k = 6;
            }
            if (i < 8) {
                z = new Classic(m);
            }
            else if (m.isEven()) {
                z = new Barrett(m);
            }
            else {
                z = new Montgomery(m);
            }
            var g = [];
            var n = 3;
            var k1 = k - 1;
            var km = (1 << k) - 1;
            g[1] = z.convert(this);
            if (k > 1) {
                var g2 = nbi();
                z.sqrTo(g[1], g2);
                while (n <= km) {
                    g[n] = nbi();
                    z.mulTo(g2, g[n - 2], g[n]);
                    n += 2;
                }
            }
            var j = e.t - 1;
            var w;
            var is1 = true;
            var r2 = nbi();
            var t;
            i = nbits(e[j]) - 1;
            while (j >= 0) {
                if (i >= k1) {
                    w = (e[j] >> (i - k1)) & km;
                }
                else {
                    w = (e[j] & ((1 << (i + 1)) - 1)) << (k1 - i);
                    if (j > 0) {
                        w |= e[j - 1] >> (this.DB + i - k1);
                    }
                }
                n = k;
                while ((w & 1) == 0) {
                    w >>= 1;
                    --n;
                }
                if ((i -= n) < 0) {
                    i += this.DB;
                    --j;
                }
                if (is1) {
                    g[w].copyTo(r);
                    is1 = false;
                }
                else {
                    while (n > 1) {
                        z.sqrTo(r, r2);
                        z.sqrTo(r2, r);
                        n -= 2;
                    }
                    if (n > 0) {
                        z.sqrTo(r, r2);
                    }
                    else {
                        t = r;
                        r = r2;
                        r2 = t;
                    }
                    z.mulTo(r2, g[w], r);
                }
                while (j >= 0 && (e[j] & (1 << i)) == 0) {
                    z.sqrTo(r, r2);
                    t = r;
                    r = r2;
                    r2 = t;
                    if (--i < 0) {
                        i = this.DB - 1;
                        --j;
                    }
                }
            }
            return z.revert(r);
        };
        BigInteger.prototype.modInverse = function (m) {
            var ac = m.isEven();
            if ((this.isEven() && ac) || m.signum() == 0) {
                return BigInteger.ZERO;
            }
            var u = m.clone();
            var v = this.clone();
            var a = nbv(1);
            var b = nbv(0);
            var c = nbv(0);
            var d = nbv(1);
            while (u.signum() != 0) {
                while (u.isEven()) {
                    u.rShiftTo(1, u);
                    if (ac) {
                        if (!a.isEven() || !b.isEven()) {
                            a.addTo(this, a);
                            b.subTo(m, b);
                        }
                        a.rShiftTo(1, a);
                    }
                    else if (!b.isEven()) {
                        b.subTo(m, b);
                    }
                    b.rShiftTo(1, b);
                }
                while (v.isEven()) {
                    v.rShiftTo(1, v);
                    if (ac) {
                        if (!c.isEven() || !d.isEven()) {
                            c.addTo(this, c);
                            d.subTo(m, d);
                        }
                        c.rShiftTo(1, c);
                    }
                    else if (!d.isEven()) {
                        d.subTo(m, d);
                    }
                    d.rShiftTo(1, d);
                }
                if (u.compareTo(v) >= 0) {
                    u.subTo(v, u);
                    if (ac) {
                        a.subTo(c, a);
                    }
                    b.subTo(d, b);
                }
                else {
                    v.subTo(u, v);
                    if (ac) {
                        c.subTo(a, c);
                    }
                    d.subTo(b, d);
                }
            }
            if (v.compareTo(BigInteger.ONE) != 0) {
                return BigInteger.ZERO;
            }
            if (d.compareTo(m) >= 0) {
                return d.subtract(m);
            }
            if (d.signum() < 0) {
                d.addTo(m, d);
            }
            else {
                return d;
            }
            if (d.signum() < 0) {
                return d.add(m);
            }
            else {
                return d;
            }
        };
        BigInteger.prototype.pow = function (e) {
            return this.exp(e, new NullExp());
        };
        BigInteger.prototype.gcd = function (a) {
            var x = this.s < 0 ? this.negate() : this.clone();
            var y = a.s < 0 ? a.negate() : a.clone();
            if (x.compareTo(y) < 0) {
                var t = x;
                x = y;
                y = t;
            }
            var i = x.getLowestSetBit();
            var g = y.getLowestSetBit();
            if (g < 0) {
                return x;
            }
            if (i < g) {
                g = i;
            }
            if (g > 0) {
                x.rShiftTo(g, x);
                y.rShiftTo(g, y);
            }
            while (x.signum() > 0) {
                if ((i = x.getLowestSetBit()) > 0) {
                    x.rShiftTo(i, x);
                }
                if ((i = y.getLowestSetBit()) > 0) {
                    y.rShiftTo(i, y);
                }
                if (x.compareTo(y) >= 0) {
                    x.subTo(y, x);
                    x.rShiftTo(1, x);
                }
                else {
                    y.subTo(x, y);
                    y.rShiftTo(1, y);
                }
            }
            if (g > 0) {
                y.lShiftTo(g, y);
            }
            return y;
        };
        BigInteger.prototype.isProbablePrime = function (t) {
            var i;
            var x = this.abs();
            if (x.t == 1 && x[0] <= lowprimes[lowprimes.length - 1]) {
                for (i = 0; i < lowprimes.length; ++i) {
                    if (x[0] == lowprimes[i]) {
                        return true;
                    }
                }
                return false;
            }
            if (x.isEven()) {
                return false;
            }
            i = 1;
            while (i < lowprimes.length) {
                var m = lowprimes[i];
                var j = i + 1;
                while (j < lowprimes.length && m < lplim) {
                    m *= lowprimes[j++];
                }
                m = x.modInt(m);
                while (i < j) {
                    if (m % lowprimes[i++] == 0) {
                        return false;
                    }
                }
            }
            return x.millerRabin(t);
        };
        BigInteger.prototype.copyTo = function (r) {
            for (var i = this.t - 1; i >= 0; --i) {
                r[i] = this[i];
            }
            r.t = this.t;
            r.s = this.s;
        };
        BigInteger.prototype.fromInt = function (x) {
            this.t = 1;
            this.s = x < 0 ? -1 : 0;
            if (x > 0) {
                this[0] = x;
            }
            else if (x < -1) {
                this[0] = x + this.DV;
            }
            else {
                this.t = 0;
            }
        };
        BigInteger.prototype.fromString = function (s, b) {
            var k;
            if (b == 16) {
                k = 4;
            }
            else if (b == 8) {
                k = 3;
            }
            else if (b == 256) {
                k = 8;
            }
            else if (b == 2) {
                k = 1;
            }
            else if (b == 32) {
                k = 5;
            }
            else if (b == 4) {
                k = 2;
            }
            else {
                this.fromRadix(s, b);
                return;
            }
            this.t = 0;
            this.s = 0;
            var i = s.length;
            var mi = false;
            var sh = 0;
            while (--i >= 0) {
                var x = k == 8 ? +s[i] & 0xff : intAt(s, i);
                if (x < 0) {
                    if (s.charAt(i) == '-') {
                        mi = true;
                    }
                    continue;
                }
                mi = false;
                if (sh == 0) {
                    this[this.t++] = x;
                }
                else if (sh + k > this.DB) {
                    this[this.t - 1] |= (x & ((1 << (this.DB - sh)) - 1)) << sh;
                    this[this.t++] = x >> (this.DB - sh);
                }
                else {
                    this[this.t - 1] |= x << sh;
                }
                sh += k;
                if (sh >= this.DB) {
                    sh -= this.DB;
                }
            }
            if (k == 8 && (+s[0] & 0x80) != 0) {
                this.s = -1;
                if (sh > 0) {
                    this[this.t - 1] |= ((1 << (this.DB - sh)) - 1) << sh;
                }
            }
            this.clamp();
            if (mi) {
                BigInteger.ZERO.subTo(this, this);
            }
        };
        BigInteger.prototype.clamp = function () {
            var c = this.s & this.DM;
            while (this.t > 0 && this[this.t - 1] == c) {
                --this.t;
            }
        };
        BigInteger.prototype.dlShiftTo = function (n, r) {
            var i;
            for (i = this.t - 1; i >= 0; --i) {
                r[i + n] = this[i];
            }
            for (i = n - 1; i >= 0; --i) {
                r[i] = 0;
            }
            r.t = this.t + n;
            r.s = this.s;
        };
        BigInteger.prototype.drShiftTo = function (n, r) {
            for (var i = n; i < this.t; ++i) {
                r[i - n] = this[i];
            }
            r.t = Math.max(this.t - n, 0);
            r.s = this.s;
        };
        BigInteger.prototype.lShiftTo = function (n, r) {
            var bs = n % this.DB;
            var cbs = this.DB - bs;
            var bm = (1 << cbs) - 1;
            var ds = Math.floor(n / this.DB);
            var c = (this.s << bs) & this.DM;
            for (var i = this.t - 1; i >= 0; --i) {
                r[i + ds + 1] = (this[i] >> cbs) | c;
                c = (this[i] & bm) << bs;
            }
            for (var i = ds - 1; i >= 0; --i) {
                r[i] = 0;
            }
            r[ds] = c;
            r.t = this.t + ds + 1;
            r.s = this.s;
            r.clamp();
        };
        BigInteger.prototype.rShiftTo = function (n, r) {
            r.s = this.s;
            var ds = Math.floor(n / this.DB);
            if (ds >= this.t) {
                r.t = 0;
                return;
            }
            var bs = n % this.DB;
            var cbs = this.DB - bs;
            var bm = (1 << bs) - 1;
            r[0] = this[ds] >> bs;
            for (var i = ds + 1; i < this.t; ++i) {
                r[i - ds - 1] |= (this[i] & bm) << cbs;
                r[i - ds] = this[i] >> bs;
            }
            if (bs > 0) {
                r[this.t - ds - 1] |= (this.s & bm) << cbs;
            }
            r.t = this.t - ds;
            r.clamp();
        };
        BigInteger.prototype.subTo = function (a, r) {
            var i = 0;
            var c = 0;
            var m = Math.min(a.t, this.t);
            while (i < m) {
                c += this[i] - a[i];
                r[i++] = c & this.DM;
                c >>= this.DB;
            }
            if (a.t < this.t) {
                c -= a.s;
                while (i < this.t) {
                    c += this[i];
                    r[i++] = c & this.DM;
                    c >>= this.DB;
                }
                c += this.s;
            }
            else {
                c += this.s;
                while (i < a.t) {
                    c -= a[i];
                    r[i++] = c & this.DM;
                    c >>= this.DB;
                }
                c -= a.s;
            }
            r.s = c < 0 ? -1 : 0;
            if (c < -1) {
                r[i++] = this.DV + c;
            }
            else if (c > 0) {
                r[i++] = c;
            }
            r.t = i;
            r.clamp();
        };
        BigInteger.prototype.multiplyTo = function (a, r) {
            var x = this.abs();
            var y = a.abs();
            var i = x.t;
            r.t = i + y.t;
            while (--i >= 0) {
                r[i] = 0;
            }
            for (i = 0; i < y.t; ++i) {
                r[i + x.t] = x.am(0, y[i], r, i, 0, x.t);
            }
            r.s = 0;
            r.clamp();
            if (this.s != a.s) {
                BigInteger.ZERO.subTo(r, r);
            }
        };
        BigInteger.prototype.squareTo = function (r) {
            var x = this.abs();
            var i = (r.t = 2 * x.t);
            while (--i >= 0) {
                r[i] = 0;
            }
            for (i = 0; i < x.t - 1; ++i) {
                var c = x.am(i, x[i], r, 2 * i, 0, 1);
                if ((r[i + x.t] += x.am(i + 1, 2 * x[i], r, 2 * i + 1, c, x.t - i - 1)) >= x.DV) {
                    r[i + x.t] -= x.DV;
                    r[i + x.t + 1] = 1;
                }
            }
            if (r.t > 0) {
                r[r.t - 1] += x.am(i, x[i], r, 2 * i, 0, 1);
            }
            r.s = 0;
            r.clamp();
        };
        BigInteger.prototype.divRemTo = function (m, q, r) {
            var pm = m.abs();
            if (pm.t <= 0) {
                return;
            }
            var pt = this.abs();
            if (pt.t < pm.t) {
                if (q != null) {
                    q.fromInt(0);
                }
                if (r != null) {
                    this.copyTo(r);
                }
                return;
            }
            if (r == null) {
                r = nbi();
            }
            var y = nbi();
            var ts = this.s;
            var ms = m.s;
            var nsh = this.DB - nbits(pm[pm.t - 1]);
            if (nsh > 0) {
                pm.lShiftTo(nsh, y);
                pt.lShiftTo(nsh, r);
            }
            else {
                pm.copyTo(y);
                pt.copyTo(r);
            }
            var ys = y.t;
            var y0 = y[ys - 1];
            if (y0 == 0) {
                return;
            }
            var yt = y0 * (1 << this.F1) + (ys > 1 ? y[ys - 2] >> this.F2 : 0);
            var d1 = this.FV / yt;
            var d2 = (1 << this.F1) / yt;
            var e = 1 << this.F2;
            var i = r.t;
            var j = i - ys;
            var t = q == null ? nbi() : q;
            y.dlShiftTo(j, t);
            if (r.compareTo(t) >= 0) {
                r[r.t++] = 1;
                r.subTo(t, r);
            }
            BigInteger.ONE.dlShiftTo(ys, t);
            t.subTo(y, y);
            while (y.t < ys) {
                y[y.t++] = 0;
            }
            while (--j >= 0) {
                var qd = r[--i] == y0 ? this.DM : Math.floor(r[i] * d1 + (r[i - 1] + e) * d2);
                if ((r[i] += y.am(0, qd, r, j, 0, ys)) < qd) {
                    y.dlShiftTo(j, t);
                    r.subTo(t, r);
                    while (r[i] < --qd) {
                        r.subTo(t, r);
                    }
                }
            }
            if (q != null) {
                r.drShiftTo(ys, q);
                if (ts != ms) {
                    BigInteger.ZERO.subTo(q, q);
                }
            }
            r.t = ys;
            r.clamp();
            if (nsh > 0) {
                r.rShiftTo(nsh, r);
            }
            if (ts < 0) {
                BigInteger.ZERO.subTo(r, r);
            }
        };
        BigInteger.prototype.invDigit = function () {
            if (this.t < 1) {
                return 0;
            }
            var x = this[0];
            if ((x & 1) == 0) {
                return 0;
            }
            var y = x & 3;
            y = (y * (2 - (x & 0xf) * y)) & 0xf;
            y = (y * (2 - (x & 0xff) * y)) & 0xff;
            y = (y * (2 - (((x & 0xffff) * y) & 0xffff))) & 0xffff;
            y = (y * (2 - ((x * y) % this.DV))) % this.DV;
            return y > 0 ? this.DV - y : -y;
        };
        BigInteger.prototype.isEven = function () {
            return (this.t > 0 ? this[0] & 1 : this.s) == 0;
        };
        BigInteger.prototype.exp = function (e, z) {
            if (e > 0xffffffff || e < 1) {
                return BigInteger.ONE;
            }
            var r = nbi();
            var r2 = nbi();
            var g = z.convert(this);
            var i = nbits(e) - 1;
            g.copyTo(r);
            while (--i >= 0) {
                z.sqrTo(r, r2);
                if ((e & (1 << i)) > 0) {
                    z.mulTo(r2, g, r);
                }
                else {
                    var t = r;
                    r = r2;
                    r2 = t;
                }
            }
            return z.revert(r);
        };
        BigInteger.prototype.chunkSize = function (r) {
            return Math.floor((Math.LN2 * this.DB) / Math.log(r));
        };
        BigInteger.prototype.toRadix = function (b) {
            if (b == null) {
                b = 10;
            }
            if (this.signum() == 0 || b < 2 || b > 36) {
                return '0';
            }
            var cs = this.chunkSize(b);
            var a = Math.pow(b, cs);
            var d = nbv(a);
            var y = nbi();
            var z = nbi();
            var r = '';
            this.divRemTo(d, y, z);
            while (y.signum() > 0) {
                r = (a + z.intValue()).toString(b).substr(1) + r;
                y.divRemTo(d, y, z);
            }
            return z.intValue().toString(b) + r;
        };
        BigInteger.prototype.fromRadix = function (s, b) {
            this.fromInt(0);
            if (b == null) {
                b = 10;
            }
            var cs = this.chunkSize(b);
            var d = Math.pow(b, cs);
            var mi = false;
            var j = 0;
            var w = 0;
            for (var i = 0; i < s.length; ++i) {
                var x = intAt(s, i);
                if (x < 0) {
                    if (s.charAt(i) == '-' && this.signum() == 0) {
                        mi = true;
                    }
                    continue;
                }
                w = b * w + x;
                if (++j >= cs) {
                    this.dMultiply(d);
                    this.dAddOffset(w, 0);
                    j = 0;
                    w = 0;
                }
            }
            if (j > 0) {
                this.dMultiply(Math.pow(b, j));
                this.dAddOffset(w, 0);
            }
            if (mi) {
                BigInteger.ZERO.subTo(this, this);
            }
        };
        BigInteger.prototype.fromNumber = function (a, b, c) {
            if ('number' == typeof b) {
                if (a < 2) {
                    this.fromInt(1);
                }
                else {
                    this.fromNumber(a, c);
                    if (!this.testBit(a - 1)) {
                        this.bitwiseTo(BigInteger.ONE.shiftLeft(a - 1), op_or, this);
                    }
                    if (this.isEven()) {
                        this.dAddOffset(1, 0);
                    }
                    while (!this.isProbablePrime(b)) {
                        this.dAddOffset(2, 0);
                        if (this.bitLength() > a) {
                            this.subTo(BigInteger.ONE.shiftLeft(a - 1), this);
                        }
                    }
                }
            }
            else {
                var x = [];
                var t = a & 7;
                x.length = (a >> 3) + 1;
                b.nextBytes(x);
                if (t > 0) {
                    x[0] &= (1 << t) - 1;
                }
                else {
                    x[0] = 0;
                }
                this.fromString(x, 256);
            }
        };
        BigInteger.prototype.bitwiseTo = function (a, op, r) {
            var i;
            var f;
            var m = Math.min(a.t, this.t);
            for (i = 0; i < m; ++i) {
                r[i] = op(this[i], a[i]);
            }
            if (a.t < this.t) {
                f = a.s & this.DM;
                for (i = m; i < this.t; ++i) {
                    r[i] = op(this[i], f);
                }
                r.t = this.t;
            }
            else {
                f = this.s & this.DM;
                for (i = m; i < a.t; ++i) {
                    r[i] = op(f, a[i]);
                }
                r.t = a.t;
            }
            r.s = op(this.s, a.s);
            r.clamp();
        };
        BigInteger.prototype.changeBit = function (n, op) {
            var r = BigInteger.ONE.shiftLeft(n);
            this.bitwiseTo(r, op, r);
            return r;
        };
        BigInteger.prototype.addTo = function (a, r) {
            var i = 0;
            var c = 0;
            var m = Math.min(a.t, this.t);
            while (i < m) {
                c += this[i] + a[i];
                r[i++] = c & this.DM;
                c >>= this.DB;
            }
            if (a.t < this.t) {
                c += a.s;
                while (i < this.t) {
                    c += this[i];
                    r[i++] = c & this.DM;
                    c >>= this.DB;
                }
                c += this.s;
            }
            else {
                c += this.s;
                while (i < a.t) {
                    c += a[i];
                    r[i++] = c & this.DM;
                    c >>= this.DB;
                }
                c += a.s;
            }
            r.s = c < 0 ? -1 : 0;
            if (c > 0) {
                r[i++] = c;
            }
            else if (c < -1) {
                r[i++] = this.DV + c;
            }
            r.t = i;
            r.clamp();
        };
        BigInteger.prototype.dMultiply = function (n) {
            this[this.t] = this.am(0, n - 1, this, 0, 0, this.t);
            ++this.t;
            this.clamp();
        };
        BigInteger.prototype.dAddOffset = function (n, w) {
            if (n == 0) {
                return;
            }
            while (this.t <= w) {
                this[this.t++] = 0;
            }
            this[w] += n;
            while (this[w] >= this.DV) {
                this[w] -= this.DV;
                if (++w >= this.t) {
                    this[this.t++] = 0;
                }
                ++this[w];
            }
        };
        BigInteger.prototype.multiplyLowerTo = function (a, n, r) {
            var i = Math.min(this.t + a.t, n);
            r.s = 0;
            r.t = i;
            while (i > 0) {
                r[--i] = 0;
            }
            for (var j = r.t - this.t; i < j; ++i) {
                r[i + this.t] = this.am(0, a[i], r, i, 0, this.t);
            }
            for (var j = Math.min(a.t, n); i < j; ++i) {
                this.am(0, a[i], r, i, 0, n - i);
            }
            r.clamp();
        };
        BigInteger.prototype.multiplyUpperTo = function (a, n, r) {
            --n;
            var i = (r.t = this.t + a.t - n);
            r.s = 0;
            while (--i >= 0) {
                r[i] = 0;
            }
            for (i = Math.max(n - this.t, 0); i < a.t; ++i) {
                r[this.t + i - n] = this.am(n - i, a[i], r, 0, 0, this.t + i - n);
            }
            r.clamp();
            r.drShiftTo(1, r);
        };
        BigInteger.prototype.modInt = function (n) {
            if (n <= 0) {
                return 0;
            }
            var d = this.DV % n;
            var r = this.s < 0 ? n - 1 : 0;
            if (this.t > 0) {
                if (d == 0) {
                    r = this[0] % n;
                }
                else {
                    for (var i = this.t - 1; i >= 0; --i) {
                        r = (d * r + this[i]) % n;
                    }
                }
            }
            return r;
        };
        BigInteger.prototype.millerRabin = function (t) {
            var n1 = this.subtract(BigInteger.ONE);
            var k = n1.getLowestSetBit();
            if (k <= 0) {
                return false;
            }
            var r = n1.shiftRight(k);
            t = (t + 1) >> 1;
            if (t > lowprimes.length) {
                t = lowprimes.length;
            }
            var a = nbi();
            for (var i = 0; i < t; ++i) {
                a.fromInt(lowprimes[Math.floor(Math.random() * lowprimes.length)]);
                var y = a.modPow(r, this);
                if (y.compareTo(BigInteger.ONE) != 0 && y.compareTo(n1) != 0) {
                    var j = 1;
                    while (j++ < k && y.compareTo(n1) != 0) {
                        y = y.modPowInt(2, this);
                        if (y.compareTo(BigInteger.ONE) == 0) {
                            return false;
                        }
                    }
                    if (y.compareTo(n1) != 0) {
                        return false;
                    }
                }
            }
            return true;
        };
        BigInteger.prototype.square = function () {
            var r = nbi();
            this.squareTo(r);
            return r;
        };
        BigInteger.prototype.gcda = function (a, callback) {
            var x = this.s < 0 ? this.negate() : this.clone();
            var y = a.s < 0 ? a.negate() : a.clone();
            if (x.compareTo(y) < 0) {
                var t = x;
                x = y;
                y = t;
            }
            var i = x.getLowestSetBit();
            var g = y.getLowestSetBit();
            if (g < 0) {
                callback(x);
                return;
            }
            if (i < g) {
                g = i;
            }
            if (g > 0) {
                x.rShiftTo(g, x);
                y.rShiftTo(g, y);
            }
            var gcda1 = function () {
                if ((i = x.getLowestSetBit()) > 0) {
                    x.rShiftTo(i, x);
                }
                if ((i = y.getLowestSetBit()) > 0) {
                    y.rShiftTo(i, y);
                }
                if (x.compareTo(y) >= 0) {
                    x.subTo(y, x);
                    x.rShiftTo(1, x);
                }
                else {
                    y.subTo(x, y);
                    y.rShiftTo(1, y);
                }
                if (!(x.signum() > 0)) {
                    if (g > 0) {
                        y.lShiftTo(g, y);
                    }
                    setTimeout(function () {
                        callback(y);
                    }, 0);
                }
                else {
                    setTimeout(gcda1, 0);
                }
            };
            setTimeout(gcda1, 10);
        };
        BigInteger.prototype.fromNumberAsync = function (a, b, c, callback) {
            if ('number' == typeof b) {
                if (a < 2) {
                    this.fromInt(1);
                }
                else {
                    this.fromNumber(a, c);
                    if (!this.testBit(a - 1)) {
                        this.bitwiseTo(BigInteger.ONE.shiftLeft(a - 1), op_or, this);
                    }
                    if (this.isEven()) {
                        this.dAddOffset(1, 0);
                    }
                    var bnp_1 = this;
                    var bnpfn1_1 = function () {
                        bnp_1.dAddOffset(2, 0);
                        if (bnp_1.bitLength() > a) {
                            bnp_1.subTo(BigInteger.ONE.shiftLeft(a - 1), bnp_1);
                        }
                        if (bnp_1.isProbablePrime(b)) {
                            setTimeout(function () {
                                callback();
                            }, 0);
                        }
                        else {
                            setTimeout(bnpfn1_1, 0);
                        }
                    };
                    setTimeout(bnpfn1_1, 0);
                }
            }
            else {
                var x = [];
                var t = a & 7;
                x.length = (a >> 3) + 1;
                b.nextBytes(x);
                if (t > 0) {
                    x[0] &= (1 << t) - 1;
                }
                else {
                    x[0] = 0;
                }
                this.fromString(x, 256);
            }
        };
        return BigInteger;
    }());
    var NullExp = (function () {
        function NullExp() { }
        NullExp.prototype.convert = function (x) {
            return x;
        };
        NullExp.prototype.revert = function (x) {
            return x;
        };
        NullExp.prototype.mulTo = function (x, y, r) {
            x.multiplyTo(y, r);
        };
        NullExp.prototype.sqrTo = function (x, r) {
            x.squareTo(r);
        };
        return NullExp;
    })();
    var Classic = (function () {
        function Classic(m) {
            this.m = m;
        }
        Classic.prototype.convert = function (x) {
            if (x.s < 0 || x.compareTo(this.m) >= 0) {
                return x.mod(this.m);
            }
            else {
                return x;
            }
        };
        Classic.prototype.revert = function (x) {
            return x;
        };
        Classic.prototype.reduce = function (x) {
            x.divRemTo(this.m, null, x);
        };
        Classic.prototype.mulTo = function (x, y, r) {
            x.multiplyTo(y, r);
            this.reduce(r);
        };
        Classic.prototype.sqrTo = function (x, r) {
            x.squareTo(r);
            this.reduce(r);
        };
        return Classic;
    })();
    var Montgomery = (function () {
        function Montgomery(m) {
            this.m = m;
            this.mp = m.invDigit();
            this.mpl = this.mp & 0x7fff;
            this.mph = this.mp >> 15;
            this.um = (1 << (m.DB - 15)) - 1;
            this.mt2 = 2 * m.t;
        }
        Montgomery.prototype.convert = function (x) {
            var r = nbi();
            x.abs().dlShiftTo(this.m.t, r);
            r.divRemTo(this.m, null, r);
            if (x.s < 0 && r.compareTo(BigInteger_1.ZERO) > 0) {
                this.m.subTo(r, r);
            }
            return r;
        };
        Montgomery.prototype.revert = function (x) {
            var r = nbi();
            x.copyTo(r);
            this.reduce(r);
            return r;
        };
        Montgomery.prototype.reduce = function (x) {
            while (x.t <= this.mt2) {
                x[x.t++] = 0;
            }
            for (var i = 0; i < this.m.t; ++i) {
                var j = x[i] & 0x7fff;
                var u0 = (j * this.mpl + (((j * this.mph + (x[i] >> 15) * this.mpl) & this.um) << 15)) & x.DM;
                j = i + this.m.t;
                x[j] += this.m.am(0, u0, x, i, 0, this.m.t);
                while (x[j] >= x.DV) {
                    x[j] -= x.DV;
                    x[++j]++;
                }
            }
            x.clamp();
            x.drShiftTo(this.m.t, x);
            if (x.compareTo(this.m) >= 0) {
                x.subTo(this.m, x);
            }
        };
        Montgomery.prototype.mulTo = function (x, y, r) {
            x.multiplyTo(y, r);
            this.reduce(r);
        };
        Montgomery.prototype.sqrTo = function (x, r) {
            x.squareTo(r);
            this.reduce(r);
        };
        return Montgomery;
    })();
    var Barrett = (function () {
        function Barrett(m) {
            this.m = m;
            this.r2 = nbi();
            this.q3 = nbi();
            BigInteger_1.ONE.dlShiftTo(2 * m.t, this.r2);
            this.mu = this.r2.divide(m);
        }
        Barrett.prototype.convert = function (x) {
            if (x.s < 0 || x.t > 2 * this.m.t) {
                return x.mod(this.m);
            }
            else if (x.compareTo(this.m) < 0) {
                return x;
            }
            else {
                var r = nbi();
                x.copyTo(r);
                this.reduce(r);
                return r;
            }
        };
        Barrett.prototype.revert = function (x) {
            return x;
        };
        Barrett.prototype.reduce = function (x) {
            x.drShiftTo(this.m.t - 1, this.r2);
            if (x.t > this.m.t + 1) {
                x.t = this.m.t + 1;
                x.clamp();
            }
            this.mu.multiplyUpperTo(this.r2, this.m.t + 1, this.q3);
            this.m.multiplyLowerTo(this.q3, this.m.t + 1, this.r2);
            while (x.compareTo(this.r2) < 0) {
                x.dAddOffset(1, this.m.t + 1);
            }
            x.subTo(this.r2, x);
            while (x.compareTo(this.m) >= 0) {
                x.subTo(this.m, x);
            }
        };
        Barrett.prototype.mulTo = function (x, y, r) {
            x.multiplyTo(y, r);
            this.reduce(r);
        };
        Barrett.prototype.sqrTo = function (x, r) {
            x.squareTo(r);
            this.reduce(r);
        };
        return Barrett;
    })();
    function nbi() {
        return new BigInteger_1(null);
    }
    function parseBigInt(str, r) {
        return new BigInteger_1(str, r);
    }
    function am1(i, x, w, j, c, n) {
        while (--n >= 0) {
            var v = x * this[i++] + w[j] + c;
            c = Math.floor(v / 0x4000000);
            w[j++] = v & 0x3ffffff;
        }
        return c;
    }
    function am2(i, x, w, j, c, n) {
        var xl = x & 0x7fff;
        var xh = x >> 15;
        while (--n >= 0) {
            var l = this[i] & 0x7fff;
            var h = this[i++] >> 15;
            var m = xh * l + h * xl;
            l = xl * l + ((m & 0x7fff) << 15) + w[j] + (c & 0x3fffffff);
            c = (l >>> 30) + (m >>> 15) + xh * h + (c >>> 30);
            w[j++] = l & 0x3fffffff;
        }
        return c;
    }
    function am3(i, x, w, j, c, n) {
        var xl = x & 0x3fff;
        var xh = x >> 14;
        while (--n >= 0) {
            var l = this[i] & 0x3fff;
            var h = this[i++] >> 14;
            var m = xh * l + h * xl;
            l = xl * l + ((m & 0x3fff) << 14) + w[j] + c;
            c = (l >> 28) + (m >> 14) + xh * h;
            w[j++] = l & 0xfffffff;
        }
        return c;
    }
    if (j_lm && typeof navigator_1 !== 'undefined' && (navigator_1 === null || navigator_1 === void 0 ? void 0 : navigator_1.appName) == 'Microsoft Internet Explorer') {
        BigInteger_1.prototype.am = am2;
        dbits = 30;
    }
    else if (j_lm && (navigator_1 === null || navigator_1 === void 0 ? void 0 : navigator_1.appName) != 'Netscape') {
        BigInteger_1.prototype.am = am1;
        dbits = 26;
    }
    else {
        BigInteger_1.prototype.am = am3;
        dbits = 28;
    }
    BigInteger_1.prototype.DB = dbits;
    BigInteger_1.prototype.DM = (1 << dbits) - 1;
    BigInteger_1.prototype.DV = 1 << dbits;
    var BI_FP = 52;
    BigInteger_1.prototype.FV = Math.pow(2, BI_FP);
    BigInteger_1.prototype.F1 = BI_FP - dbits;
    BigInteger_1.prototype.F2 = 2 * dbits - BI_FP;
    var BI_RC = [];
    var rr;
    var vv;
    rr = '0'.charCodeAt(0);
    for (vv = 0; vv <= 9; ++vv) {
        BI_RC[rr++] = vv;
    }
    rr = 'a'.charCodeAt(0);
    for (vv = 10; vv < 36; ++vv) {
        BI_RC[rr++] = vv;
    }
    rr = 'A'.charCodeAt(0);
    for (vv = 10; vv < 36; ++vv) {
        BI_RC[rr++] = vv;
    }
    function intAt(s, i) {
        var c = BI_RC[s.charCodeAt(i)];
        return c == null ? -1 : c;
    }
    function nbv(i) {
        var r = nbi();
        r.fromInt(i);
        return r;
    }
    function nbits(x) {
        var r = 1;
        var t;
        if ((t = x >>> 16) != 0) {
            x = t;
            r += 16;
        }
        if ((t = x >> 8) != 0) {
            x = t;
            r += 8;
        }
        if ((t = x >> 4) != 0) {
            x = t;
            r += 4;
        }
        if ((t = x >> 2) != 0) {
            x = t;
            r += 2;
        }
        if ((t = x >> 1) != 0) {
            x = t;
            r += 1;
        }
        return r;
    }
    BigInteger_1.ZERO = nbv(0);
    BigInteger_1.ONE = nbv(1);
    var Arcfour_1 = (function () {
        function Arcfour() {
            this.i = 0;
            this.j = 0;
            this.S = [];
        }
        Arcfour.prototype.init = function (key) {
            var i;
            var j;
            var t;
            for (i = 0; i < 256; ++i) {
                this.S[i] = i;
            }
            j = 0;
            for (i = 0; i < 256; ++i) {
                j = (j + this.S[i] + key[i % key.length]) & 255;
                t = this.S[i];
                this.S[i] = this.S[j];
                this.S[j] = t;
            }
            this.i = 0;
            this.j = 0;
        };
        Arcfour.prototype.next = function () {
            var t;
            this.i = (this.i + 1) & 255;
            this.j = (this.j + this.S[this.i]) & 255;
            t = this.S[this.i];
            this.S[this.i] = this.S[this.j];
            this.S[this.j] = t;
            return this.S[(t + this.S[this.i]) & 255];
        };
        return Arcfour;
    }());
    function prng_newstate() {
        return new Arcfour_1();
    }
    var rng_psize = 256;
    var rng_state;
    var rng_pool = null;
    var rng_pptr;
    if (rng_pool == null) {
        rng_pool = [];
        rng_pptr = 0;
        var t = void 0;
        if ((window === null || window === void 0 ? void 0 : window.crypto) && (window === null || window === void 0 ? void 0 : window.crypto.getRandomValues)) {
            var z = new Uint32Array(256);
            window === null || window === void 0 ? void 0 : window.crypto.getRandomValues(z);
            for (t = 0; t < z.length; ++t) {
                rng_pool[rng_pptr++] = z[t] & 255;
            }
        }
        var onMouseMoveListener_1 = function (ev) {
            this.count = this.count || 0;
            if (this.count >= 256 || rng_pptr >= rng_psize) {
                if (window === null || window === void 0 ? void 0 : window.removeEventListener) {
                    window === null || window === void 0 ? void 0 : window.removeEventListener('mousemove', onMouseMoveListener_1, false);
                }
                else if (window === null || window === void 0 ? void 0 : window.detachEvent) {
                    window === null || window === void 0 ? void 0 : window.detachEvent('onmousemove', onMouseMoveListener_1);
                }
                return;
            }
            try {
                var mouseCoordinates = ev.x + ev.y;
                rng_pool[rng_pptr++] = mouseCoordinates & 255;
                this.count += 1;
            }
            catch (e) {
            }
        };
        if (window === null || window === void 0 ? void 0 : window.addEventListener) {
            window === null || window === void 0 ? void 0 : window.addEventListener('mousemove', onMouseMoveListener_1, false);
        }
        else if (window === null || window === void 0 ? void 0 : window.attachEvent) {
            window === null || window === void 0 ? void 0 : window.attachEvent('onmousemove', onMouseMoveListener_1);
        }
    }
    function rng_get_byte() {
        if (rng_state == null) {
            rng_state = prng_newstate();
            while (rng_pptr < rng_psize) {
                var random = Math.floor(65536 * Math.random());
                rng_pool[rng_pptr++] = random & 255;
            }
            rng_state.init(rng_pool);
            for (rng_pptr = 0; rng_pptr < rng_pool.length; ++rng_pptr) {
                rng_pool[rng_pptr] = 0;
            }
            rng_pptr = 0;
        }
        return rng_state.next();
    }
    var SecureRandom_1 = (function () {
        function SecureRandom() {
        }
        SecureRandom.prototype.nextBytes = function (ba) {
            for (var i = 0; i < ba.length; ++i) {
                ba[i] = rng_get_byte();
            }
        };
        return SecureRandom;
    }());
    function pkcs1pad1(s, n) {
        if (n < s.length + 22) {
            console.error('Message too long for RSA');
            return null;
        }
        var len = n - s.length - 6;
        var filler = '';
        for (var f = 0; f < len; f += 2) {
            filler += 'ff';
        }
        var m = '0001' + filler + '00' + s;
        return parseBigInt(m, 16);
    }
    function pkcs1pad2(s, n) {
        if (n < s.length + 11) {
            console.error('Message too long for RSA');
            return null;
        }
        var ba = [];
        var i = s.length - 1;
        while (i >= 0 && n > 0) {
            var c = s.charCodeAt(i--);
            if (c < 128) {
                ba[--n] = c;
            }
            else if (c > 127 && c < 2048) {
                ba[--n] = (c & 63) | 128;
                ba[--n] = (c >> 6) | 192;
            }
            else {
                ba[--n] = (c & 63) | 128;
                ba[--n] = ((c >> 6) & 63) | 128;
                ba[--n] = (c >> 12) | 224;
            }
        }
        ba[--n] = 0;
        var rng = new SecureRandom_1();
        var x = [];
        while (n > 2) {
            x[0] = 0;
            while (x[0] == 0) {
                rng.nextBytes(x);
            }
            ba[--n] = x[0];
        }
        ba[--n] = 2;
        ba[--n] = 0;
        return new BigInteger_1(ba);
    }
    var RSAKey = (function () {
        function RSAKey() {
            this.n = null;
            this.e = 0;
            this.d = null;
            this.p = null;
            this.q = null;
            this.dmp1 = null;
            this.dmq1 = null;
            this.coeff = null;
        }
        RSAKey.prototype.doPublic = function (x) {
            return x.modPowInt(this.e, this.n);
        };
        RSAKey.prototype.doPrivate = function (x) {
            if (this.p == null || this.q == null) {
                return x.modPow(this.d, this.n);
            }
            var xp = x.mod(this.p).modPow(this.dmp1, this.p);
            var xq = x.mod(this.q).modPow(this.dmq1, this.q);
            while (xp.compareTo(xq) < 0) {
                xp = xp.add(this.p);
            }
            return xp.subtract(xq).multiply(this.coeff).mod(this.p).multiply(this.q).add(xq);
        };
        RSAKey.prototype.setPublic = function (N, E) {
            if (N != null && E != null && N.length > 0 && E.length > 0) {
                this.n = parseBigInt(N, 16);
                this.e = parseInt(E, 16);
            }
            else {
                console.error('Invalid RSA public key');
            }
        };
        RSAKey.prototype.encrypt = function (text) {
            var m = pkcs1pad2(text, (this.n.bitLength() + 7) >> 3);
            if (m == null) {
                return null;
            }
            var c = this.doPublic(m);
            if (c == null) {
                return null;
            }
            var h = c.toString(16);
            if ((h.length & 1) == 0) {
                return h;
            }
            else {
                return '0' + h;
            }
        };
        RSAKey.prototype.encryptLong = function (text) {
            var _this = this;
            var maxLength = ((this.n.bitLength() + 7) >> 3) - 11;
            try {
                var ct_1 = '';
                if (text.length > maxLength) {
                    var lt = text.match(/.{1,117}/g);
                    lt.forEach(function (entry) {
                        var t1 = _this.encrypt(entry);
                        ct_1 += t1;
                    });
                    return hex2b64(ct_1);
                }
                var t = this.encrypt(text);
                var y = hex2b64(t);
                return y;
            }
            catch (ex) {
                return false;
            }
        };
        RSAKey.prototype.decryptLong = function (text) {
            var _this = this;
            var maxLength = (this.n.bitLength() + 7) >> 3;
            text = b64tohex(text);
            try {
                if (text.length > maxLength) {
                    var ct_2 = '';
                    var lt = text.match(/.{1,256}/g);
                    lt.forEach(function (entry) {
                        var t1 = _this.decrypt(entry);
                        ct_2 += t1;
                    });
                    return ct_2;
                }
                var y = this.decrypt(text);
                return y;
            }
            catch (ex) {
                return false;
            }
        };
        RSAKey.prototype.setPrivate = function (N, E, D) {
            if (N != null && E != null && N.length > 0 && E.length > 0) {
                this.n = parseBigInt(N, 16);
                this.e = parseInt(E, 16);
                this.d = parseBigInt(D, 16);
            }
            else {
                console.error('Invalid RSA private key');
            }
        };
        RSAKey.prototype.setPrivateEx = function (N, E, D, P, Q, DP, DQ, C) {
            if (N != null && E != null && N.length > 0 && E.length > 0) {
                this.n = parseBigInt(N, 16);
                this.e = parseInt(E, 16);
                this.d = parseBigInt(D, 16);
                this.p = parseBigInt(P, 16);
                this.q = parseBigInt(Q, 16);
                this.dmp1 = parseBigInt(DP, 16);
                this.dmq1 = parseBigInt(DQ, 16);
                this.coeff = parseBigInt(C, 16);
            }
            else {
                console.error('Invalid RSA private key');
            }
        };
        RSAKey.prototype.generate = function (B, E) {
            var rng = new SecureRandom_1();
            var qs = B >> 1;
            this.e = parseInt(E, 16);
            var ee = new BigInteger_1(E, 16);
            for (;;) {
                for (;;) {
                    this.p = new BigInteger_1(B - qs, 1, rng);
                    if (this.p.subtract(BigInteger_1.ONE).gcd(ee).compareTo(BigInteger_1.ONE) == 0 && this.p.isProbablePrime(10)) {
                        break;
                    }
                }
                for (;;) {
                    this.q = new BigInteger_1(qs, 1, rng);
                    if (this.q.subtract(BigInteger_1.ONE).gcd(ee).compareTo(BigInteger_1.ONE) == 0 && this.q.isProbablePrime(10)) {
                        break;
                    }
                }
                if (this.p.compareTo(this.q) <= 0) {
                    var t = this.p;
                    this.p = this.q;
                    this.q = t;
                }
                var p1 = this.p.subtract(BigInteger_1.ONE);
                var q1 = this.q.subtract(BigInteger_1.ONE);
                var phi = p1.multiply(q1);
                if (phi.gcd(ee).compareTo(BigInteger_1.ONE) == 0) {
                    this.n = this.p.multiply(this.q);
                    this.d = ee.modInverse(phi);
                    this.dmp1 = this.d.mod(p1);
                    this.dmq1 = this.d.mod(q1);
                    this.coeff = this.q.modInverse(this.p);
                    break;
                }
            }
        };
        RSAKey.prototype.decrypt = function (ctext) {
            var c = parseBigInt(ctext, 16);
            var m = this.doPrivate(c);
            if (m == null) {
                return null;
            }
            return pkcs1unpad2(m, (this.n.bitLength() + 7) >> 3);
        };
        RSAKey.prototype.generateAsync = function (B, E, callback) {
            var rng = new SecureRandom_1();
            var qs = B >> 1;
            this.e = parseInt(E, 16);
            var ee = new BigInteger_1(E, 16);
            var rsa = this;
            var loop1 = function () {
                var loop4 = function () {
                    if (rsa.p.compareTo(rsa.q) <= 0) {
                        var t = rsa.p;
                        rsa.p = rsa.q;
                        rsa.q = t;
                    }
                    var p1 = rsa.p.subtract(BigInteger_1.ONE);
                    var q1 = rsa.q.subtract(BigInteger_1.ONE);
                    var phi = p1.multiply(q1);
                    if (phi.gcd(ee).compareTo(BigInteger_1.ONE) == 0) {
                        rsa.n = rsa.p.multiply(rsa.q);
                        rsa.d = ee.modInverse(phi);
                        rsa.dmp1 = rsa.d.mod(p1);
                        rsa.dmq1 = rsa.d.mod(q1);
                        rsa.coeff = rsa.q.modInverse(rsa.p);
                        setTimeout(function () {
                            callback();
                        }, 0);
                    }
                    else {
                        setTimeout(loop1, 0);
                    }
                };
                var loop3 = function () {
                    rsa.q = nbi();
                    rsa.q.fromNumberAsync(qs, 1, rng, function () {
                        rsa.q.subtract(BigInteger_1.ONE).gcda(ee, function (r) {
                            if (r.compareTo(BigInteger_1.ONE) == 0 && rsa.q.isProbablePrime(10)) {
                                setTimeout(loop4, 0);
                            }
                            else {
                                setTimeout(loop3, 0);
                            }
                        });
                    });
                };
                var loop2 = function () {
                    rsa.p = nbi();
                    rsa.p.fromNumberAsync(B - qs, 1, rng, function () {
                        rsa.p.subtract(BigInteger_1.ONE).gcda(ee, function (r) {
                            if (r.compareTo(BigInteger_1.ONE) == 0 && rsa.p.isProbablePrime(10)) {
                                setTimeout(loop3, 0);
                            }
                            else {
                                setTimeout(loop2, 0);
                            }
                        });
                    });
                };
                setTimeout(loop2, 0);
            };
            setTimeout(loop1, 0);
        };
        return RSAKey;
    }());
    function pkcs1unpad2(d, n) {
        var b = d.toByteArray();
        var i = 0;
        while (i < b.length && b[i] == 0) {
            ++i;
        }
        if (b.length - i != n - 1 || b[i] != 2) {
            return null;
        }
        ++i;
        while (b[i] != 0) {
            if (++i >= b.length) {
                return null;
            }
        }
        var ret = '';
        while (++i < b.length) {
            var c = b[i] & 255;
            if (c < 128) {
                ret += String.fromCharCode(c);
            }
            else if (c > 191 && c < 224) {
                ret += String.fromCharCode(((c & 31) << 6) | (b[i + 1] & 63));
                ++i;
            }
            else {
                ret += String.fromCharCode(((c & 15) << 12) | ((b[i + 1] & 63) << 6) | (b[i + 2] & 63));
                i += 2;
            }
        }
        return ret;
    }
    var JSEncryptRSAKey_1 = (function (_super) {
        __extends(JSEncryptRSAKey, _super);
        function JSEncryptRSAKey(key) {
            if (key === void 0) { key = ''; }
            var _this_1 = _super.call(this) || this;
            if (key) {
                if (typeof key === 'string') {
                    _this_1.parseKey(key);
                }
                else if (_this_1.hasPrivateKeyProperty(key) || _this_1.hasPublicKeyProperty(key)) {
                    _this_1.parsePropertiesFrom(key);
                }
            }
            return _this_1;
        }
        JSEncryptRSAKey.prototype.parseKey = function (pem) {
            try {
                var modulus = 0;
                var public_exponent = 0;
                var reHex = /^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/;
                var der = reHex.test(pem) ? Hex.decode(pem) : Base64.unarmor(pem);
                var asn1 = ASN1_1.decode(der);
                if (asn1.sub.length === 3) {
                    asn1 = asn1.sub[2].sub[0];
                }
                if (asn1.sub.length === 9) {
                    modulus = asn1.sub[1].getHexStringValue();
                    this.n = parseBigInt(modulus, 16);
                    public_exponent = asn1.sub[2].getHexStringValue();
                    this.e = parseInt(public_exponent, 16);
                    var private_exponent = asn1.sub[3].getHexStringValue();
                    this.d = parseBigInt(private_exponent, 16);
                    var prime1 = asn1.sub[4].getHexStringValue();
                    this.p = parseBigInt(prime1, 16);
                    var prime2 = asn1.sub[5].getHexStringValue();
                    this.q = parseBigInt(prime2, 16);
                    var exponent1 = asn1.sub[6].getHexStringValue();
                    this.dmp1 = parseBigInt(exponent1, 16);
                    var exponent2 = asn1.sub[7].getHexStringValue();
                    this.dmq1 = parseBigInt(exponent2, 16);
                    var coefficient = asn1.sub[8].getHexStringValue();
                    this.coeff = parseBigInt(coefficient, 16);
                }
                else if (asn1.sub.length === 2) {
                    var bit_string = asn1.sub[1];
                    var sequence = bit_string.sub[0];
                    modulus = sequence.sub[0].getHexStringValue();
                    this.n = parseBigInt(modulus, 16);
                    public_exponent = sequence.sub[1].getHexStringValue();
                    this.e = parseInt(public_exponent, 16);
                }
                else {
                    return false;
                }
                return true;
            }
            catch (ex) {
                return false;
            }
        };
        JSEncryptRSAKey.prototype.hasPublicKeyProperty = function (obj) {
            obj = obj || {};
            return obj.hasOwnProperty('n') && obj.hasOwnProperty('e');
        };
        JSEncryptRSAKey.prototype.hasPrivateKeyProperty = function (obj) {
            obj = obj || {};
            return (obj.hasOwnProperty('n') &&
                obj.hasOwnProperty('e') &&
                obj.hasOwnProperty('d') &&
                obj.hasOwnProperty('p') &&
                obj.hasOwnProperty('q') &&
                obj.hasOwnProperty('dmp1') &&
                obj.hasOwnProperty('dmq1') &&
                obj.hasOwnProperty('coeff'));
        };
        JSEncryptRSAKey.prototype.parsePropertiesFrom = function (obj) {
            this.n = obj.n;
            this.e = obj.e;
            if (obj.hasOwnProperty('d')) {
                this.d = obj.d;
                this.p = obj.p;
                this.q = obj.q;
                this.dmp1 = obj.dmp1;
                this.dmq1 = obj.dmq1;
                this.coeff = obj.coeff;
            }
        };
        return JSEncryptRSAKey;
    }(RSAKey));
    JSEncrypt = function (options) {
        options = options || {};
        this.default_key_size = parseInt(options.default_key_size, 10) || 1024;
        this.default_public_exponent = options.default_public_exponent || '010001';
        this.log = options.log || false;
        this.key = null;
    };
    JSEncrypt.prototype.setKey = function (key) {
        if (this.log && this.key) {
            console.warn('A key was already set, overriding existing.');
        }
        this.key = new JSEncryptRSAKey_1(key);
    };
    JSEncrypt.prototype.setPrivateKey = function (privkey) {
        this.setKey(privkey);
    };
    JSEncrypt.prototype.setPublicKey = function (pubkey) {
        this.setKey(pubkey);
    };
    JSEncrypt.prototype.decrypt = function (str) {
        try {
            return this.getKey().decrypt(b64tohex(str));
        }
        catch (ex) {
            return false;
        }
    };
    JSEncrypt.prototype.encrypt = function (str) {
        try {
            return hex2b64(this.getKey().encrypt(str));
        }
        catch (ex) {
            return false;
        }
    };
    JSEncrypt.prototype.encryptLong = function (str) {
        try {
            var encrypted = this.getKey().encryptLong(str) || '';
            var uncrypted = this.getKey().decryptLong(encrypted) || '';
            var count = 0;
            var reg = /null$/g;
            while (reg.test(uncrypted)) {
                count++;
                encrypted = this.getKey().encryptLong(str) || '';
                uncrypted = this.getKey().decryptLong(encrypted) || '';
                if (count > 10) {
                    break;
                }
            }
            return encrypted;
        }
        catch (ex) {
            return false;
        }
    };
    JSEncrypt.prototype.getKey = function (cb) {
        if (!this.key) {
            this.key = new JSEncryptRSAKey_1();
            if (cb && {}.toString.call(cb) === '[object Function]') {
                this.key.generateAsync(this.default_key_size, this.default_public_exponent, cb);
                return;
            }
            this.key.generate(this.default_key_size, this.default_public_exponent);
        }
        return this.key;
    };
    JSEncrypt.version = '3.1.4';
}
exports.default = JSEncrypt;
//# sourceMappingURL=data:application/json;base64,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