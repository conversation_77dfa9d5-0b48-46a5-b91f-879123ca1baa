!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("cloudbase_container",[],e):"object"==typeof exports?exports.cloudbase_container=e():t.cloudbase_container=e()}("undefined"!=typeof window?window:this,(()=>(()=>{"use strict";var t={63:(t,e,n)=>{n.d(e,{l:()=>r});var r="container"},428:(t,e,n)=>{n.r(e),n.d(e,{CloudbaseContainers:()=>O,registerContainers:()=>x});var r,o=n(19),i=n(883),a=n(63),c=(r=function(t,e){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},r(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),s=function(){return s=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},s.apply(this,arguments)},u=function(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)},l=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function a(t){try{s(r.next(t))}catch(t){i(t)}}function c(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,c)}s((r=r.apply(t,e||[])).next())}))},f=function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){a.label=c[1];break}if(6===c[0]&&a.label<o[1]){a.label=o[1],o=c;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(c);break}o[2]&&a.ops.pop(),a.trys.pop();continue}c=e.call(t,a)}catch(t){c=[6,t],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}},p=o._G.Sg,d=o._G.vM,h=o.P6.Gc,y=o.P6.xe,v=o.BM.D,b=function(){throw new Error("getExportFunction 未实现")},w=function(){throw new Error("loadJSExportFunction 未实现")};if(globalThis.IS_MP_BUILD)b=function(){new Error("小程序不支持 wasm 加载")},w=function(t){return t&&new Error("小程序不支持动态 js 加载"),{initContainer:globalThis.cloudbase_private_link.initContainer,callContainer:globalThis.cloudbase_private_link.callContainer}};else{var g=n(40);b=g.getExportFunction,w=g.loadJSExportFunction}var m=function(t){function e(e,n){var r=this,o=JSON.stringify({code:p.INVALID_PARAMS,msg:"[".concat(e,"] ").concat(n)});return(r=t.call(this,o)||this).code=p.INVALID_PARAMS,r}return c(e,t),e.prototype.toJSON=function(){return{code:this.code,message:this.message}},e}(Error);function _(t,e,n){void 0===e&&(e=a.l),void 0===n&&(n="call container error");var r=t instanceof Error?t:new Error("[".concat(e,"] ").concat((null==t?void 0:t.message)||n));return r.code=(null==t?void 0:t.code)||"UNKNOWN_ERROR",r}var O=function(){function t(t){var e,n=this;if(this.config=s(s({},t),{jsUrl:"string"==typeof t.jsUrl?{vm:t.jsUrl}:t.jsUrl}),!this.config.wasmUrl&&this.config.publicKey);else if(!this.config.wasmUrl&&!(null===(e=this.config.jsUrl)||void 0===e?void 0:e.vm)&&!globalThis.cloudbase_private_link)throw new m("".concat(a.l),"缺少 privatelink sdk 地址");this.config.wasmUrl?this.wasm=b(this.config.wasmUrl).catch((function(t){if(n.config.jsUrl)return console.warn("load wams error, fall back to use js",t),w(n.config.jsUrl);if(globalThis.cloudbase_private_link)return globalThis.cloudbase_private_link;throw t})):this.config.jsUrl||this.config.publicKey?this.wasm=w(this.config.jsUrl):this.wasm=Promise.resolve({initContainer:globalThis.cloudbase_private_link.initContainer,callContainer:globalThis.cloudbase_private_link.callContainer})}return t.prototype.callContainer=function(t,e){return l(this,void 0,void 0,(function(){var n,r,o,c,u,l,p,d,y,v,b,w,g,O,j,x,E,C;return f(this,(function(f){switch(f.label){case 0:return[4,this.initContainer(this.config)];case 1:return f.sent(),n="".concat(a.l,".callContainer"),[4,this.wasm];case 2:if(r=f.sent().callContainer,o=s({},t),c=o.url,O=o.method,u=void 0===O?"GET":O,j=o.header,l=void 0===j?{}:j,p=o.data,x=o.timeout,d=void 0===x?6e4:x,E=o.dataType,y=void 0===E?"json":E,C=o.responseType,v=void 0===C?"text":C,b=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(t);o<r.length;o++)e.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(n[r[o]]=t[r[o]])}return n}(o,["url","method","header","data","timeout","dataType","responseType"]),!c)throw new m(n,"invalid request url");if(!u)throw new m(n,"invalid request method");f.label=3;case 3:return f.trys.push([3,5,,6]),[4,new Promise((function(t,e){var o=s({url:c,method:u,header:l,data:(0,i.xW)(l,p,u),timeout:d,dataType:y,responseType:v},b);if(p&&((0,i.zH)(o.data)||"string"==typeof o.data)&&(null==p?void 0:p.length)>1024e4)e(new m(n,"body too large"));else{if("GET"===u){var a=o.data;if(a&&(o.data=void 0),!(0,i.zH)(a)&&"string"!=typeof a){var f=Object.entries(a||{}).reduce((function(t,e){var n=e[0],r=e[1];return t.push("".concat(encodeURIComponent(n),"=").concat(encodeURIComponent(String(r)))),t}),[]);f.length&&(a=f.join("&"))}if("string"==typeof o.data){var h=(0,i.Lk)(c);h.search?h.search+="&".concat(o.data):h.search="?".concat(o.data),o.url=h.href,o.data=void 0}}r(s(s({},o),{success:t,fail:function(t){var r=(t||{}).data;e(_(r,n,"call container error"))}}))}}))];case 4:return w=f.sent(),[2,h(e,null,w)];case 5:return g=f.sent(),h(e,g),[3,6];case 6:return[2]}}))}))},t.prototype.initContainer=function(t){return l(this,void 0,void 0,(function(){var e,n=this;return f(this,(function(r){switch(r.label){case 0:return[4,this.wasm];case 1:return e=r.sent().initContainer,this.containerInitPromise||(this.containerInitPromise=new Promise((function(r,o){e({config:t,success:function(t){"200"!==String(t.statusCode)&&o(_(t.data,"".concat(a.l,".initContainer"),"init container fail")),r(t)},fail:function(t){o(_(t.data,"".concat(a.l,".initContainer"),"init container fail")),n.containerInitPromise=null}})}))),[2,this.containerInitPromise]}}))}))},function(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,n,a):o(e,n))||a);i>3&&a&&Object.defineProperty(e,n,a)}([v({customInfo:{className:"Cloudbase",methodName:"callContainer"},title:"调用失败",messages:["请确认以下各项：","  1 - 调用 callContainer() 的语法或参数是否正确","  2 - 域名 & 路径是否存在","如果问题依然存在，建议到官方问答社区提问或寻找帮助：".concat(d)]}),u("design:type",Function),u("design:paramtypes",[Object,Function]),u("design:returntype",Promise)],t.prototype,"callContainer",null),t}(),j={name:a.l,namespace:"container",entity:function(t){if(this.containerInstance)return y(p.INVALID_OPERATION,"every cloudbase instance should has only one container object"),this.containerInstance;var e=new O(t);return this.containerInstance=e,this.callContainer=e.callContainer.bind(e),this.containerInstance}};try{cloudbase.registerComponent(j)}catch(t){}function x(t){try{t.registerComponent(j)}catch(t){console.warn(t)}}},883:(t,e,n)=>{n.d(e,{Lk:()=>a,xW:()=>u,zH:()=>c});var r=n(19),o=n(63),i=r._G.Sg;function a(t){if(document){var e=document.createElement("a");return e.href=t,e}return new URL(new Request(t).url)}function c(t){return t instanceof ArrayBuffer||ArrayBuffer.isView(t)}function s(t){var e=Object.entries(t||{}).reduce((function(t,e){var n=e[0],r=e[1];return t.push("".concat(encodeURIComponent(n),"=").concat(encodeURIComponent(r))),t}),[]);if(e.length)return e.join("&")}function u(t,e,n){void 0===n&&(n="POST");var r=e,a="Content-Type";if("object"==typeof e&&!c(e)){t[a]||t["content-type"]||(t[a]="application/json");var u=t[a]||t["content-type"];if("GET"===n)try{return s(e)||""}catch(t){throw new Error(JSON.stringify({code:i.INVALID_PARAMS,msg:"[".concat(o.l,".callContainer] invalid data in query method, ").concat(t.message)}))}switch(u){case"application/json":try{r=JSON.stringify(e)}catch(t){throw new Error(JSON.stringify({code:i.INVALID_PARAMS,msg:"[".concat(o.l,".callContainer] invalid data with content-type: application/json, ").concat(t.message)}))}break;case"application/x-www-form-urlencoded":try{var l=s(e);l&&(r=l)}catch(t){throw new Error(JSON.stringify({code:i.INVALID_PARAMS,msg:"[".concat(o.l,".callContainer] invalid data with content-type: application/x-www-form-urlencoded,").concat(t.message)}))}}}return r}},40:(t,e,n)=>{n.r(e),n.d(e,{getExportFunction:()=>h,loadJSExportFunction:()=>y});if(void 0!==n.g);else if("undefined"!=typeof window)window.global=window;else{if("undefined"==typeof self)throw new Error("cannot export Go (neither global, window nor self is defined)");self.global=self}n.g.require;var r=function(){var t=new Error("not implemented");return t.code="ENOSYS",t};if(!n.g.fs){var o="";n.g.fs={constants:{O_WRONLY:-1,O_RDWR:-1,O_CREAT:-1,O_TRUNC:-1,O_APPEND:-1,O_EXCL:-1},writeSync:function(t,e){var n=(o+=a.decode(e)).lastIndexOf("\n");return-1!=n&&(console.log(o.substr(0,n)),o=o.substr(n+1)),e.length},write:function(t,e,n,o,i,a){0===n&&o===e.length&&null===i?a(null,this.writeSync(t,e)):a(r())},chmod:function(t,e,n){n(r())},chown:function(t,e,n,o){o(r())},close:function(t,e){e(r())},fchmod:function(t,e,n){n(r())},fchown:function(t,e,n,o){o(r())},fstat:function(t,e){e(r())},fsync:function(t,e){e(null)},ftruncate:function(t,e,n){n(r())},lchown:function(t,e,n,o){o(r())},link:function(t,e,n){n(r())},lstat:function(t,e){e(r())},mkdir:function(t,e,n){n(r())},open:function(t,e,n,o){o(r())},read:function(t,e,n,o,i,a){a(r())},readdir:function(t,e){e(r())},readlink:function(t,e){e(r())},rename:function(t,e,n){n(r())},rmdir:function(t,e){e(r())},stat:function(t,e){e(r())},symlink:function(t,e,n){n(r())},truncate:function(t,e,n){n(r())},unlink:function(t,e){e(r())},utimes:function(t,e,n,o){o(r())}}}if(n.g.process||(n.g.process={getuid:function(){return-1},getgid:function(){return-1},geteuid:function(){return-1},getegid:function(){return-1},getgroups:function(){throw r()},pid:-1,ppid:-1,umask:function(){throw r()},cwd:function(){throw r()},chdir:function(){throw r()}}),!n.g.crypto)throw new Error("globalThis.crypto is not available, polyfill required (crypto.getRandomValues only)");if(!n.g.performance)throw new Error("globalThis.performance is not available, polyfill required (performance.now only)");if(!n.g.TextEncoder)throw new Error("globalThis.TextEncoder is not available, polyfill required");if(!n.g.TextDecoder)throw new Error("globalThis.TextDecoder is not available, polyfill required");var i=new TextEncoder("utf-8"),a=new TextDecoder("utf-8"),c=[];n.g.Go=function(){function t(){var t=this;this._callbackTimeouts=new Map,this._nextCallbackTimeoutID=1;var e=function(){return new DataView(t._inst.exports.memory.buffer)},r=function(t,n){e().setUint32(t+0,n,!0),e().setUint32(t+4,Math.floor(n/4294967296),!0)},o=function(n){var r=e().getFloat64(n,!0);if(0!==r){if(!isNaN(r))return r;var o=e().getUint32(n,!0);return t._values[o]}},s=function(n,r){var o=2146959360;if("number"==typeof r)return isNaN(r)?(e().setUint32(n+4,o,!0),void e().setUint32(n,0,!0)):0===r?(e().setUint32(n+4,o,!0),void e().setUint32(n,1,!0)):void e().setFloat64(n,r,!0);switch(r){case void 0:return void e().setFloat64(n,0,!0);case null:return e().setUint32(n+4,o,!0),void e().setUint32(n,2,!0);case!0:return e().setUint32(n+4,o,!0),void e().setUint32(n,3,!0);case!1:return e().setUint32(n+4,o,!0),void e().setUint32(n,4,!0)}var i=t._ids.get(r);void 0===i&&(void 0===(i=t._idPool.pop())&&(i=t._values.length),t._values[i]=r,t._goRefCounts[i]=0,t._ids.set(r,i)),t._goRefCounts[i]++;var a=1;switch(typeof r){case"string":a=2;break;case"symbol":a=3;break;case"function":a=4}e().setUint32(n+4,o|a,!0),e().setUint32(n,i,!0)},u=function(e,n,r){return new Uint8Array(t._inst.exports.memory.buffer,e,n)},l=function(t,e,n){for(var r=new Array(e),i=0;i<e;i++)r[i]=o(t+8*i);return r},f=function(e,n){return a.decode(new DataView(t._inst.exports.memory.buffer,e,n))},p=Date.now()-performance.now();this.importObject={wasi_snapshot_preview1:{fd_write:function(t,n,r,o){var i=0;if(1==t)for(var s=0;s<r;s++){var u=n+8*s,l=e().getUint32(u+0,!0),f=e().getUint32(u+4,!0);i+=f;for(var p=0;p<f;p++){var d=e().getUint8(l+p);if(13==d);else if(10==d){var h=a.decode(new Uint8Array(c));c=[],console.log(h)}else c.push(d)}}else console.error("invalid file descriptor:",t);return e().setUint32(o,i,!0),0},fd_close:function(){return 0},fd_fdstat_get:function(){return 0},fd_seek:function(){return 0},proc_exit:function(t){if(!n.g.process)throw"trying to exit with code "+t;process.exit(t)},random_get:function(t,e){return crypto.getRandomValues(u(t,e)),0}},env:{"runtime.ticks":function(){return p+performance.now()},"runtime.sleepTicks":function(e){setTimeout(t._inst.exports.go_scheduler,e)},"syscall/js.finalizeRef":function(n){var r=e().getUint32(n,!0);if(t._goRefCounts[r]--,0===t._goRefCounts[r]){var o=t._values[r];t._values[r]=null,t._ids.delete(o),t._idPool.push(r)}},"syscall/js.stringVal":function(t,e,n){var r=f(e,n);s(t,r)},"syscall/js.valueGet":function(t,e,n,r){var i=f(n,r),a=o(e),c=Reflect.get(a,i);s(t,c)},"syscall/js.valueSet":function(t,e,n,r){var i=o(t),a=f(e,n),c=o(r);Reflect.set(i,a,c)},"syscall/js.valueDelete":function(t,e,n){var r=o(t),i=f(e,n);Reflect.deleteProperty(r,i)},"syscall/js.valueIndex":function(t,e,n){s(t,Reflect.get(o(e),n))},"syscall/js.valueSetIndex":function(t,e,n){Reflect.set(o(t),e,o(n))},"syscall/js.valueCall":function(t,n,r,i,a,c,u){var p=o(n),d=f(r,i),h=l(a,c);try{var y=Reflect.get(p,d);s(t,Reflect.apply(y,p,h)),e().setUint8(t+8,1)}catch(n){s(t,n),e().setUint8(t+8,0)}},"syscall/js.valueInvoke":function(t,n,r,i,a){try{var c=o(n),u=l(r,i);s(t,Reflect.apply(c,void 0,u)),e().setUint8(t+8,1)}catch(n){s(t,n),e().setUint8(t+8,0)}},"syscall/js.valueNew":function(t,n,r,i,a){var c=o(n),u=l(r,i);try{s(t,Reflect.construct(c,u)),e().setUint8(t+8,1)}catch(n){s(t,n),e().setUint8(t+8,0)}},"syscall/js.valueLength":function(t){return o(t).length},"syscall/js.valuePrepareString":function(t,e){var n=String(o(e)),a=i.encode(n);s(t,a),r(t+8,a.length)},"syscall/js.valueLoadString":function(t,e,n,r){var i=o(t);u(e,n).set(i)},"syscall/js.valueInstanceOf":function(t,e){return o(t)instanceof o(e)},"syscall/js.copyBytesToGo":function(t,n,i,a,c){var s=t,l=t+4,f=u(n,i),p=o(c);if(p instanceof Uint8Array||p instanceof Uint8ClampedArray){var d=p.subarray(0,f.length);f.set(d),r(s,d.length),e().setUint8(l,1)}else e().setUint8(l,0)},"syscall/js.copyBytesToJS":function(t,n,i,a,c){var s=t,l=t+4,f=o(n),p=u(i,a);if(f instanceof Uint8Array||f instanceof Uint8ClampedArray){var d=p.subarray(0,f.length);f.set(d),r(s,d.length),e().setUint8(l,1)}else e().setUint8(l,0)}}}}return t.prototype.run=function(t){return e=this,r=void 0,i=function(){var e,r=this;return function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){a.label=c[1];break}if(6===c[0]&&a.label<o[1]){a.label=o[1],o=c;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(c);break}o[2]&&a.ops.pop(),a.trys.pop();continue}c=e.call(t,a)}catch(t){c=[6,t],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}}(this,(function(o){switch(o.label){case 0:this._inst=t,this._values=[NaN,0,null,!0,!1,n.g,this],this._goRefCounts=[],this._ids=new Map,this._idPool=[],this.exited=!1,new DataView(this._inst.exports.memory.buffer),o.label=1;case 1:return e=new Promise((function(t){r._resolveCallbackPromise=function(){if(r.exited)throw new Error("bad callback: Go program has already exited");setTimeout(t,0)}})),this._inst.exports._start(),this.exited?[3,3]:[4,e];case 2:return o.sent(),[3,1];case 3:return[2]}}))},new((o=void 0)||(o=Promise))((function(t,n){function a(t){try{s(i.next(t))}catch(t){n(t)}}function c(t){try{s(i.throw(t))}catch(t){n(t)}}function s(e){var n;e.done?t(e.value):(n=e.value,n instanceof o?n:new o((function(t){t(n)}))).then(a,c)}s((i=i.apply(e,r||[])).next())}));var e,r,o,i},t.prototype._resume=function(){if(this.exited)throw new Error("Go program has already exited");this._inst.exports.resume(),this.exited&&this._resolveExitPromise()},t.prototype._makeFuncWrapper=function(t){var e=this;return function(){var n={id:t,this:this,args:arguments};return e._pendingEvent=n,e._resume(),n.result}},t}();const s=n.g.Go;var u=n(883),l=function(){return l=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},l.apply(this,arguments)},f=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function a(t){try{s(r.next(t))}catch(t){i(t)}}function c(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,c)}s((r=r.apply(t,e||[])).next())}))},p=function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){a.label=c[1];break}if(6===c[0]&&a.label<o[1]){a.label=o[1],o=c;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(c);break}o[2]&&a.ops.pop(),a.trys.pop();continue}c=e.call(t,a)}catch(t){c=[6,t],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}};function d(t,e,n){return void 0===n&&(n=!1),f(this,void 0,void 0,(function(){var r,o;return p(this,(function(i){switch(i.label){case 0:return!n&&WebAssembly.instantiateStreaming?[3,3]:[4,t];case 1:return[4,i.sent().arrayBuffer()];case 2:if(r=i.sent(),n)throw new Error("do not support *.br");return o=r,[2,WebAssembly.instantiate(o,e)];case 3:return[2,WebAssembly.instantiateStreaming(t,e)]}}))}))}function h(t,e,n){return void 0===n&&(n="go"),f(this,void 0,void 0,(function(){var r,o,i,a;return p(this,(function(c){switch(c.label){case 0:return r=l({env:{memoryBase:0,tableBase:0,memory:new WebAssembly.Memory({initial:256})}},e),i=function(t){var e=!1,n="";try{n=(0,u.Lk)(t).pathname}catch(e){n=t}return/\.br$/.test(n)&&(e=!0),e}(t),"go"===n&&((o=new s)._initedModulePromise=new Promise((function(t){o._initedModuleResolve=t})),r=l(l({},o.importObject),{env:l(l({},o.importObject.env),{exportModule:function(t){return o._initedModuleResolve(t)}})})),[4,d(fetch(t),r,i)];case 1:return a=c.sent(),"go"!==n?[3,3]:[4,Promise.race([o.run(a.instance),new Promise((function(t){return setTimeout((function(){t(null)}),500)}))])];case 2:return c.sent(),[2,{callContainer:window.callContainer,initContainer:window.initContainer}];case 3:return[2,a.instance.exports]}}))}))}function y(t){return f(this,void 0,void 0,(function(){var e,n,r,o;return p(this,(function(i){switch(i.label){case 0:return n=(e=t||{}).vm,r=e.vender,o="cloudbase_private_link_vender",!r&&Object.prototype.hasOwnProperty.call(document.defaultView,o)?[3,2]:[4,v(r||"https://qbase.cdn-go.cn/lcap/lcap-resource-cdngo/-/release/_npm/@cloudbase/privatelink-vender@0.0.2/dist/cdn/cloudbase.privatelink.vender.js",o)];case 1:i.sent(),i.label=2;case 2:return[2,v(n||"https://qbase.cdn-go.cn/lcap/lcap-resource-cdngo/-/release/_npm/@cloudbase/privatelink@0.0.1/dist/cdn/cloudbase.privatelink.vm.js","cloudbase_private_link")]}}))}))}function v(t,e,n){return void 0===n&&(n=document),new Promise((function(r,o){var i=n.defaultView,a=n.createElement("script");a.setAttribute("src",t),a.setAttribute("class",e),a.addEventListener("load",(function(){if(Object.prototype.hasOwnProperty.call(i,e))return r(i[e]);var n=new Error("Fail to load UMD module ".concat(e," from [").concat(t,"]."));return o(n)})),a.addEventListener("error",(function(n){var r=new Error("main bundle [".concat(e,"] load failed from [").concat(t,"]: ").concat((null==n?void 0:n.message)||""));o(r)})),n.body?n.body.appendChild(a):n.head.appendChild(a)}))}},19:(t,e,n)=>{n.d(e,{_G:()=>r,BM:()=>i,P6:()=>o});var r={};n.r(r),n.d(r,{vM:()=>l,Sg:()=>f,Fk:()=>u,eX:()=>c});var o={};n.r(o),n.d(o,{Gc:()=>b,CN:()=>y,hp:()=>h,V9:()=>d,HD:()=>p,Jz:()=>g,xe:()=>w,UK:()=>v});var i={};n.r(i),n.d(i,{D:()=>D});var a="@cloudbase/js-sdk";function c(){return a}var s,u=!1,l="https://support.qq.com/products/148793",f={INVALID_PARAMS:"INVALID_PARAMS",INVALID_SYNTAX:"INVALID_SYNTAX",INVALID_OPERATION:"INVALID_OPERATION",OPERATION_FAIL:"OPERATION_FAIL",NETWORK_ERROR:"NETWORK_ERROR",UNKOWN_ERROR:"UNKOWN_ERROR"};!function(t){t.local="local",t.none="none",t.session="session"}(s||(s={}));function p(t){return"string"==typeof t}function d(t,e){return t instanceof e}function h(t){return"[object FormData]"===Object.prototype.toString.call(t)}function y(t,e,n){void 0===n&&(n={});var r=/\?/.test(e),o="";return Object.keys(n).forEach((function(t){""===o?!r&&(e+="?"):o+="&",o+="".concat(t,"=").concat(encodeURIComponent(n[t]))})),/^http(s)?:\/\//.test(e+=o)?e:"".concat(t).concat(e)}function v(t){void 0===t&&(t={});var e=[];return Object.keys(t).forEach((function(n){e.push("".concat(n,"=").concat(encodeURIComponent(t[n])))})),e.join("&")}function b(t,e,n){if(void 0===n&&(n=null),t&&"function"==typeof t)return t(e,n);if(e)throw e;return n}function w(t,e){console.warn("[".concat(c(),"][").concat(t,"]:").concat(e))}function g(t){var e=t.title,n=t.subtitle,r=void 0===n?"":n,o=t.content,i=void 0===o?[]:o,a=t.printTrace,c=void 0!==a&&a,s=t.collapsed;void 0!==s&&s?console.groupCollapsed(e,r):console.group(e,r);for(var u=0,l=i;u<l.length;u++){var f=l[u],p=f.type,d=f.body;switch(p){case"info":console.log(d);break;case"warn":console.warn(d);break;case"error":console.error(d)}}c&&console.trace("stack trace:"),console.groupEnd()}var m,_,O=(m=function(t,e){return m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},m(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}m(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),j=function(){return j=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},j.apply(this,arguments)},x=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function a(t){try{s(r.next(t))}catch(t){i(t)}}function c(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,c)}s((r=r.apply(t,e||[])).next())}))},E=function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){a.label=c[1];break}if(6===c[0]&&a.label<o[1]){a.label=o[1],o=c;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(c);break}o[2]&&a.ops.pop(),a.trys.pop();continue}c=e.call(t,a)}catch(t){c=[6,t],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}};!function(t){function e(e){var n=t.call(this)||this,r=e.timeout,o=e.timeoutMsg,i=e.restrictedMethods;return n.timeout=r||0,n.timeoutMsg=o||"请求超时",n.restrictedMethods=i||["get","post","upload","download"],n}O(e,t),e.prototype.get=function(t){return this.request(j(j({},t),{method:"get"}),this.restrictedMethods.includes("get"))},e.prototype.post=function(t){return this.request(j(j({},t),{method:"post"}),this.restrictedMethods.includes("post"))},e.prototype.put=function(t){return this.request(j(j({},t),{method:"put"}))},e.prototype.upload=function(t){var e=t.data,n=t.file,r=t.name,o=t.method,i=t.headers,a=void 0===i?{}:i,c={post:"post",put:"put"}[null==o?void 0:o.toLowerCase()]||"put",s=new FormData;return"post"===c?(Object.keys(e).forEach((function(t){s.append(t,e[t])})),s.append("key",r),s.append("file",n),this.request(j(j({},t),{data:s,method:c}),this.restrictedMethods.includes("upload"))):this.request(j(j({},t),{method:"put",headers:a,body:n}),this.restrictedMethods.includes("upload"))},e.prototype.download=function(t){return x(this,void 0,void 0,(function(){var e,n,r,o;return E(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,this.get(j(j({},t),{headers:{},responseType:"blob"}))];case 1:return e=i.sent().data,n=window.URL.createObjectURL(new Blob([e])),r=decodeURIComponent(new URL(t.url).pathname.split("/").pop()||""),(o=document.createElement("a")).href=n,o.setAttribute("download",r),o.style.display="none",document.body.appendChild(o),o.click(),window.URL.revokeObjectURL(n),document.body.removeChild(o),[3,3];case 2:return i.sent(),[3,3];case 3:return[2,new Promise((function(e){e({statusCode:200,tempFilePath:t.url})}))]}}))}))},e.prototype.fetch=function(t){var e;return x(this,void 0,void 0,(function(){var n,r,o,i,a,c,s,u,l,f,p,d=this;return E(this,(function(h){switch(h.label){case 0:return n=new AbortController,r=t.url,o=t.enableAbort,i=void 0!==o&&o,a=t.stream,c=void 0!==a&&a,s=t.signal,u=t.timeout,l=null!=u?u:this.timeout,s&&(s.aborted&&n.abort(),s.addEventListener("abort",(function(){return n.abort()}))),f=null,i&&l&&(f=setTimeout((function(){console.warn(d.timeoutMsg),n.abort(new Error(d.timeoutMsg))}),l)),[4,fetch(r,j(j({},t),{signal:n.signal})).then((function(t){return x(d,void 0,void 0,(function(){var e,n,r;return E(this,(function(o){switch(o.label){case 0:return clearTimeout(f),t.ok?(e=t,[3,3]):[3,1];case 1:return r=(n=Promise).reject,[4,t.json()];case 2:e=r.apply(n,[o.sent()]),o.label=3;case 3:return[2,e]}}))}))})).catch((function(t){return clearTimeout(f),Promise.reject(t)}))];case 1:return p=h.sent(),[2,{data:c?p.body:(null===(e=p.headers.get("content-type"))||void 0===e?void 0:e.includes("application/json"))?p.json():p.text(),statusCode:p.status,header:p.headers}]}}))}))},e.prototype.request=function(t,e){var n=this;void 0===e&&(e=!1);var r=String(t.method).toLowerCase()||"get";return new Promise((function(o){var i,a,c=t.url,s=t.headers,u=void 0===s?{}:s,l=t.data,f=t.responseType,p=t.withCredentials,d=t.body,b=t.onUploadProgress,w=y("https:",c,"get"===r?l:{}),g=new XMLHttpRequest;g.open(r,w),f&&(g.responseType=f),Object.keys(u).forEach((function(t){g.setRequestHeader(t,u[t])})),b&&g.upload.addEventListener("progress",b),g.onreadystatechange=function(){var t={};if(4===g.readyState){var e=g.getAllResponseHeaders().trim().split(/[\r\n]+/),n={};e.forEach((function(t){var e=t.split(": "),r=e.shift().toLowerCase(),o=e.join(": ");n[r]=o})),t.header=n,t.statusCode=g.status;try{t.data="blob"===f?g.response:JSON.parse(g.responseText)}catch(e){t.data="blob"===f?g.response:g.responseText}clearTimeout(i),o(t)}},e&&n.timeout&&(i=setTimeout((function(){console.warn(n.timeoutMsg),g.abort()}),n.timeout)),a=h(l)?l:"application/x-www-form-urlencoded"===u["content-type"]?v(l):d||(l?JSON.stringify(l):void 0),p&&(g.withCredentials=!0),g.send(a)}))}}((function(){})),function(t){t.WEB="web",t.WX_MP="wx_mp"}(_||(_={}));var C=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},t(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),I=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function a(t){try{s(r.next(t))}catch(t){i(t)}}function c(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,c)}s((r=r.apply(t,e||[])).next())}))},k=function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){a.label=c[1];break}if(6===c[0]&&a.label<o[1]){a.label=o[1],o=c;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(c);break}o[2]&&a.ops.pop(),a.trys.pop();continue}c=e.call(t,a)}catch(t){c=[6,t],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}},S=function(t){function e(e){var n=t.call(this)||this;return n.root=e,e.tcbCacheObject||(e.tcbCacheObject={}),n}return C(e,t),e.prototype.setItem=function(t,e){this.root.tcbCacheObject[t]=e},e.prototype.getItem=function(t){return this.root.tcbCacheObject[t]},e.prototype.removeItem=function(t){delete this.root.tcbCacheObject[t]},e.prototype.clear=function(){delete this.root.tcbCacheObject},e}((function(){}));!function(){function t(t){this.keys={};var e=t.persistence,n=t.platformInfo,r=void 0===n?{}:n,o=t.keys,i=void 0===o?{}:o;this.platformInfo=r,this.storage||(this.persistenceTag=this.platformInfo.adapter.primaryStorage||e,this.storage=function(t,e){switch(t){case"local":default:return e.localStorage?e.localStorage:(w(f.INVALID_PARAMS,"localStorage is not supported on current platform"),new S(e.root));case"none":return new S(e.root)}}(this.persistenceTag,this.platformInfo.adapter),this.keys=i)}Object.defineProperty(t.prototype,"mode",{get:function(){return this.storage.mode||"sync"},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"persistence",{get:function(){return this.persistenceTag},enumerable:!1,configurable:!0}),t.prototype.setStore=function(t,e,n){if("async"!==this.mode){if(this.storage)try{var r={version:n||"localCachev1",content:e};this.storage.setItem(t,JSON.stringify(r))}catch(t){throw new Error(JSON.stringify({code:f.OPERATION_FAIL,msg:"[".concat(c(),"][").concat(f.OPERATION_FAIL,"]setStore failed"),info:t}))}}else w(f.INVALID_OPERATION,"current platform's storage is asynchronous, please use setStoreAsync insteed")},t.prototype.setStoreAsync=function(t,e,n){return I(this,void 0,void 0,(function(){var r;return k(this,(function(o){switch(o.label){case 0:if(!this.storage)return[2];o.label=1;case 1:return o.trys.push([1,3,,4]),r={version:n||"localCachev1",content:e},[4,this.storage.setItem(t,JSON.stringify(r))];case 2:return o.sent(),[3,4];case 3:return o.sent(),[2];case 4:return[2]}}))}))},t.prototype.getStore=function(t,e){var n;if("async"!==this.mode){try{if("undefined"!=typeof process&&(null===(n=process.env)||void 0===n?void 0:n.tcb_token))return process.env.tcb_token;if(!this.storage)return""}catch(t){return""}e=e||"localCachev1";var r=this.storage.getItem(t);return r&&r.indexOf(e)>=0?JSON.parse(r).content:""}w(f.INVALID_OPERATION,"current platform's storage is asynchronous, please use getStoreAsync insteed")},t.prototype.getStoreAsync=function(t,e){var n;return I(this,void 0,void 0,(function(){var r;return k(this,(function(o){switch(o.label){case 0:try{if("undefined"!=typeof process&&(null===(n=process.env)||void 0===n?void 0:n.tcb_token))return[2,process.env.tcb_token];if(!this.storage)return[2,""]}catch(t){return[2,""]}return e=e||"localCachev1",[4,this.storage.getItem(t)];case 1:return(r=o.sent())&&r.indexOf(e)>=0?[2,JSON.parse(r).content]:[2,""]}}))}))},t.prototype.removeStore=function(t){"async"!==this.mode?this.storage.removeItem(t):w(f.INVALID_OPERATION,"current platform's storage is asynchronous, please use removeStoreAsync insteed")},t.prototype.removeStoreAsync=function(t){return I(this,void 0,void 0,(function(){return k(this,(function(e){switch(e.label){case 0:return[4,this.storage.removeItem(t)];case 1:return e.sent(),[2]}}))}))}}();var P=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},t(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),R=function(t,e){this.data=e||null,this.name=t},A=function(t){function e(e,n){var r=t.call(this,"error",{error:e,data:n})||this;return r.error=e,r}return P(e,t),e}(R);new(function(){function t(){this.listeners={}}return t.prototype.on=function(t,e){return function(t,e,n){n[t]=n[t]||[],n[t].push(e)}(t,e,this.listeners),this},t.prototype.off=function(t,e){return function(t,e,n){if(null==n?void 0:n[t]){var r=n[t].indexOf(e);-1!==r&&n[t].splice(r,1)}}(t,e,this.listeners),this},t.prototype.fire=function(t,e){if(d(t,A))return console.error(t.error),this;var n=p(t)?new R(t,e||{}):t,r=n.name;if(this.listens(r)){n.target=this;for(var o=0,i=this.listeners[r]?function(t,e,n){if(n||2===arguments.length)for(var r,o=0,i=e.length;o<i;o++)!r&&o in e||(r||(r=Array.prototype.slice.call(e,0,o)),r[o]=e[o]);return t.concat(r||Array.prototype.slice.call(e))}([],this.listeners[r],!0):[];o<i.length;o++)i[o].call(this,n)}return this},t.prototype.listens=function(t){return this.listeners[t]&&this.listeners[t].length>0},t}());var T=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function a(t){try{s(r.next(t))}catch(t){i(t)}}function c(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,c)}s((r=r.apply(t,e||[])).next())}))},N=function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){a.label=c[1];break}if(6===c[0]&&a.label<o[1]){a.label=o[1],o=c;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(c);break}o[2]&&a.ops.pop(),a.trys.pop();continue}c=e.call(t,a)}catch(t){c=[6,t],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}},U=!1;"undefined"!=typeof navigator&&navigator.userAgent&&(U=-1!==navigator.userAgent.indexOf("Firefox"));var L=U?/(\.js\/)?__decorate(\$\d+)?<@.*\d$/:/(\/\w+\.js\.)?__decorate(\$\d+)?\s*\(.*\)$/,M=/https?:\/\/.+:\d*\/.*\.js:\d+:\d+/;function D(t){var e=t.mode,n=void 0===e?"async":e,r=t.customInfo,o=void 0===r?{}:r,i=t.title,a=t.messages,c=void 0===a?[]:a;return function(t,e,r){if(u){var a=o.className||t.constructor.name,s=o.methodName||e,l=r.value,f=function(){var t="",e=(new Error).stack.split("\n"),n=e.findIndex((function(t){return L.test(t)}));if(-1!==n){var r=M.exec(e[n+1]||"");t=r?r[0]:""}return t}();r.value="sync"===n?function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=V({err:new Error,className:a,methodName:s,sourceLink:f});try{return l.apply(this,t)}catch(t){var r=t,o=t.message,u=t.error,p=t.error_description,d={title:i||"".concat(a,".").concat(s," failed"),content:[{type:"error",body:t}]};if(o&&/^\{.*\}$/.test(o)){var h=JSON.parse(o);d.subtitle=o,h.code&&(n?(n.code=h.code,n.msg=h.msg):(t.code=h.code,t.message=h.msg),r=n||t,d.content=c.map((function(t){return{type:"info",body:t}})))}throw u&&p&&(d.subtitle=p,n?(n.code=u,n.msg=p):(t.code=u,t.message=p),r=n||t,d.content=c.map((function(t){return{type:"info",body:t}}))),g(d),r}}:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return T(this,void 0,void 0,(function(){var e,n,r,o,u,p,d,h;return N(this,(function(y){switch(y.label){case 0:e=V({err:new Error,className:a,methodName:s,sourceLink:f}),y.label=1;case 1:return y.trys.push([1,3,,4]),[4,l.apply(this,t)];case 2:return[2,y.sent()];case 3:throw n=y.sent(),r=n,o=n.message,u=n.error,p=n.error_description,d={title:i||"".concat(a,".").concat(s," failed"),content:[{type:"error",body:n}]},o&&/^\{.*\}$/.test(o)&&(h=JSON.parse(o),d.subtitle=h,h.code&&(e?(e.code=h.code,e.message=h.msg):(n.code=h.code,n.message=h.msg),r=e||n,d.content=c.map((function(t){return{type:"info",body:t}})))),u&&p&&(d.subtitle=p,e?(e.code=u,e.msg=p):(n.code=u,n.message=p),r=e||n,d.content=c.map((function(t){return{type:"info",body:t}}))),g(d),r;case 4:return[2]}}))}))}}}}function V(t){var e=t.err,n=t.className,r=t.methodName,o=t.sourceLink;if(!o)return null;var i,a=e.stack.split("\n"),c=U?/^catchErrorsDecorator\/<\/descriptor.value@.*\d$/:new RegExp("".concat(n,"\\.descriptor.value\\s*\\[as\\s").concat(r,"\\]\\s*\\(.*\\)$")),s=U?/^catchErrorsDecorator\/<\/descriptor.value/:new RegExp("".concat(n,"\\.descriptor.value\\s*\\[as\\s").concat(r,"\\]")),u=a.findIndex((function(t){return c.test(t)}));if(-1!==u){var l=a.filter((function(t,e){return e>u}));l.unshift(a[u].replace(s,"".concat(n,".").concat(r)).replace(M,o)),(i=new Error).stack="".concat(U?"@debugger":"Error","\n").concat(l.join("\n"))}return i}function F(t){this.message=t}function G(t){this.message=t}F.prototype=new Error,F.prototype.name="InvalidCharacterError","undefined"!=typeof window&&window.atob&&window.atob.bind(window),G.prototype=new Error,G.prototype.name="InvalidTokenError"}},e={};function n(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return t[r](i,i.exports,n),i.exports}n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var r={};return(()=>{var t=r;Object.defineProperty(t,"__esModule",{value:!0}),t.registerContainers=void 0;var e=n(428);t.registerContainers=e.registerContainers})(),r})()));