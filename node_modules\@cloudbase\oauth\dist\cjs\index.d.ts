import { OAuth2Client } from './oauth2client/oauth2client';
import { AuthOptions, Auth } from './auth/apis';
export { Auth } from './auth/apis';
export * as authModels from './auth/models';
export type { ProviderProfile } from './auth/models';
export type { Credentials, OAuth2ClientOptions, ResponseError, AuthClientRequestOptions } from './oauth2client/models';
export type { AuthOptions } from './auth/apis';
export declare class CloudbaseOAuth {
    oauth2client: OAuth2Client;
    authApi: Auth;
    constructor(authOptions: AuthOptions);
}
