export function genRequestId(prefix) {
    if (prefix === void 0) { prefix = ''; }
    return "".concat(prefix ? "".concat(prefix, "_") : '').concat(+new Date(), "_").concat(Math.random());
}
export function isInitEventMessage(msg) {
    return msg.msgType === 'INIT_EVENT';
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibWVzc2FnZS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy9tZXNzYWdlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUtBLE1BQU0sVUFBVSxZQUFZLENBQUMsTUFBVztJQUFYLHVCQUFBLEVBQUEsV0FBVztJQUN0QyxPQUFPLFVBQUcsTUFBTSxDQUFDLENBQUMsQ0FBQyxVQUFHLE1BQU0sTUFBRyxDQUFDLENBQUMsQ0FBQyxFQUFFLFNBQUcsQ0FBQyxJQUFJLElBQUksRUFBRSxjQUFJLElBQUksQ0FBQyxNQUFNLEVBQUUsQ0FBRSxDQUFBO0FBQ3ZFLENBQUM7QUFFRCxNQUFNLFVBQVUsa0JBQWtCLENBQUMsR0FBcUI7SUFDdEQsT0FBTyxHQUFHLENBQUMsT0FBTyxLQUFLLFlBQVksQ0FBQTtBQUNyQyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgSVJlc3BvbnNlTWVzc2FnZSxcbiAgSVJlc3BvbnNlTWVzc2FnZUluaXRFdmVudE1zZyxcbn0gZnJvbSAnQGNsb3VkYmFzZS90eXBlcy9yZWFsdGltZSdcblxuZXhwb3J0IGZ1bmN0aW9uIGdlblJlcXVlc3RJZChwcmVmaXggPSAnJykge1xuICByZXR1cm4gYCR7cHJlZml4ID8gYCR7cHJlZml4fV9gIDogJyd9JHsrbmV3IERhdGUoKX1fJHtNYXRoLnJhbmRvbSgpfWBcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGlzSW5pdEV2ZW50TWVzc2FnZShtc2c6IElSZXNwb25zZU1lc3NhZ2UpOiBtc2cgaXMgSVJlc3BvbnNlTWVzc2FnZUluaXRFdmVudE1zZyB7XG4gIHJldHVybiBtc2cubXNnVHlwZSA9PT0gJ0lOSVRfRVZFTlQnXG59XG4iXX0=