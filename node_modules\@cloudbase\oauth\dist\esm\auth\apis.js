'use strict';
import { ApiUrls, ApiUrlsV2, ErrorType } from './consts';
import { OAuth2Client, defaultStorage } from '../oauth2client/oauth2client';
import { Captcha } from '../captcha/captcha';
import { deepClone } from '../utils';
import MyURLSearchParams from '../utils/urlSearchParams';
function getEncryptUtils(isEncrypt) {
    if (globalThis.IS_MP_BUILD) {
        return;
    }
    if (isEncrypt) {
        const utils = require('../utils/encrypt');
        return utils;
    }
}
export class Auth {
    static parseParamsToSearch(params) {
        Object.keys(params).forEach((key) => {
            if (!params[key]) {
                delete params[key];
            }
        });
        const searchParams = new MyURLSearchParams(params);
        return searchParams.toString();
    }
    constructor(opts) {
        let { request } = opts;
        let oAuth2Client = opts.credentialsClient;
        if (!oAuth2Client) {
            const initOptions = {
                apiOrigin: opts.apiOrigin,
                clientId: opts.clientId,
                storage: opts.storage,
                env: opts.env,
                baseRequest: opts.baseRequest,
                anonymousSignInFunc: opts.anonymousSignInFunc,
                wxCloud: opts.wxCloud,
            };
            oAuth2Client = new OAuth2Client(initOptions);
        }
        if (!request) {
            const baseRequest = oAuth2Client.request.bind(oAuth2Client);
            const captcha = new Captcha({
                clientId: opts.clientId,
                request: baseRequest,
                storage: opts.storage,
                ...opts.captchaOptions,
            });
            request = captcha.request.bind(captcha);
        }
        this.config = {
            env: opts.env,
            apiOrigin: opts.apiOrigin,
            clientId: opts.clientId,
            request,
            credentialsClient: oAuth2Client,
            storage: opts.storage || defaultStorage,
        };
    }
    getParamsByVersion(params, key) {
        const paramsTemp = deepClone(params);
        const url = { v2: ApiUrlsV2 }[paramsTemp?.version]?.[key] || ApiUrls[key];
        if (paramsTemp) {
            delete paramsTemp.version;
        }
        return { params: paramsTemp, url };
    }
    async signIn(params) {
        const version = params.version || 'v1';
        const res = this.getParamsByVersion(params, 'AUTH_SIGN_IN_URL');
        if (res.params.query) {
            delete res.params.query;
        }
        const body = await this.getEncryptParams(res.params);
        const credentials = await this.config.request(res.url, {
            method: 'POST',
            body,
        });
        await this.config.credentialsClient.setCredentials({
            ...credentials,
            version,
        });
        return Promise.resolve(credentials);
    }
    async signInAnonymously(data = {}, useWxCloud = false) {
        const credentials = await this.config.request(ApiUrls.AUTH_SIGN_IN_ANONYMOUSLY_URL, {
            method: 'POST',
            body: data,
            useWxCloud,
        });
        await this.config.credentialsClient.setCredentials(credentials);
        return Promise.resolve(credentials);
    }
    async signUp(params) {
        const data = await this.config.request(ApiUrls.AUTH_SIGN_UP_URL, {
            method: 'POST',
            body: params,
        });
        await this.config.credentialsClient.setCredentials(data);
        return Promise.resolve(data);
    }
    async signOut(params) {
        let resp = {};
        if (params) {
            try {
                resp = await this.config.request(ApiUrls.AUTH_SIGNOUT_URL, {
                    method: 'POST',
                    withCredentials: true,
                    body: params,
                });
            }
            catch (err) {
                if (err.error !== ErrorType.UNAUTHENTICATED) {
                    console.log('sign_out_error', err);
                }
            }
            await this.config.credentialsClient.setCredentials();
            return resp;
        }
        const accessToken = await this.config.credentialsClient.getAccessToken();
        const data = await this.config.request(ApiUrls.AUTH_REVOKE_URL, {
            method: 'POST',
            body: {
                token: accessToken,
            },
        });
        await this.config.credentialsClient.setCredentials();
        return Promise.resolve(data);
    }
    async revokeAllDevices() {
        await this.config.request(ApiUrls.AUTH_REVOKE_ALL_URL, {
            method: 'DELETE',
            withCredentials: true,
        });
    }
    async revokeDevice(params) {
        await this.config.request(ApiUrls.AUTHORIZED_DEVICES_DELETE_URL + params.device_id, {
            method: 'DELETE',
            withCredentials: true,
        });
    }
    async getVerification(params, options) {
        let withCredentials = false;
        if (params.target === 'CUR_USER') {
            withCredentials = true;
        }
        else {
            const hasLogin = await this.hasLoginState();
            if (hasLogin) {
                withCredentials = true;
            }
        }
        return this.config.request(ApiUrls.VERIFICATION_URL, {
            method: 'POST',
            body: params,
            withCaptcha: options?.withCaptcha || false,
            withCredentials,
        });
    }
    async verify(params) {
        const res = this.getParamsByVersion(params, 'VERIFY_URL');
        const data = await this.config.request(res.url, {
            method: 'POST',
            body: res.params,
        });
        if (params?.version === 'v2') {
            await this.config.credentialsClient.setCredentials({
                ...data,
                version: 'v2',
            });
        }
        return data;
    }
    async genProviderRedirectUri(params) {
        const { provider_redirect_uri: redirect_uri, other_params: otherParams = {}, ...restParams } = params;
        if (redirect_uri && !restParams.redirect_uri) {
            restParams.redirect_uri = redirect_uri;
        }
        let url = `${ApiUrls.PROVIDER_URI_URL}?${Auth.parseParamsToSearch(restParams)}`;
        Object.keys(otherParams).forEach((key) => {
            const value = otherParams[key];
            if (key === 'sign_out_uri' && !(value?.length > 0)) {
                return;
            }
            url += `&other_params[${key}]=${encodeURIComponent(value)}`;
        });
        return this.config.request(url, {
            method: 'GET',
        });
    }
    async grantProviderToken(params, useWxCloud = false) {
        return this.config.request(ApiUrls.PROVIDER_TOKEN_URL, {
            method: 'POST',
            body: params,
            useWxCloud,
        });
    }
    async patchProviderToken(params) {
        return this.config.request(ApiUrls.PROVIDER_TOKEN_URL, {
            method: 'PATCH',
            body: params,
        });
    }
    async signInWithProvider(params, useWxCloud = false) {
        const res = this.getParamsByVersion(params, 'AUTH_SIGN_IN_WITH_PROVIDER_URL');
        const credentials = await this.config.request(res.url, {
            method: 'POST',
            body: res.params,
            useWxCloud,
        });
        await this.config.credentialsClient.setCredentials({
            ...credentials,
            version: params?.version || 'v1',
        });
        return Promise.resolve(credentials);
    }
    async signInCustom(params) {
        const credentials = await this.config.request(ApiUrls.AUTH_SIGN_IN_CUSTOM, {
            method: 'POST',
            body: params,
        });
        await this.config.credentialsClient.setCredentials(credentials);
        return Promise.resolve(credentials);
    }
    async signInWithWechat(params = {}) {
        const credentials = await this.config.request(ApiUrls.AUTH_SIGN_IN_WITH_WECHAT_URL, {
            method: 'POST',
            body: params,
        });
        await this.config.credentialsClient.setCredentials(credentials);
        return Promise.resolve(credentials);
    }
    async bindWithProvider(params) {
        return this.config.request(ApiUrls.PROVIDER_BIND_URL, {
            method: 'POST',
            body: params,
            withCredentials: true,
        });
    }
    async getUserProfile(params) {
        return this.getUserInfo(params);
    }
    async getUserInfo(params = {}) {
        const res = this.getParamsByVersion(params, 'USER_ME_URL');
        if (res.params?.query) {
            const searchParams = new MyURLSearchParams(res.params.query);
            res.url += `?${searchParams.toString()}`;
        }
        const userInfo = await this.config.request(res.url, {
            method: 'GET',
            withCredentials: true,
        });
        if (userInfo.sub) {
            userInfo.uid = userInfo.sub;
        }
        return userInfo;
    }
    async getWedaUserInfo() {
        const userInfo = await this.config.request(ApiUrls.WEDA_USER_URL, {
            method: 'GET',
            withCredentials: true,
        });
        return userInfo;
    }
    async deleteMe(params) {
        const res = this.getParamsByVersion(params, 'USER_ME_URL');
        const url = `${res.url}?${Auth.parseParamsToSearch(res.params)}`;
        return this.config.request(url, {
            method: 'DELETE',
            withCredentials: true,
        });
    }
    async hasLoginState() {
        try {
            await this.config.credentialsClient.getAccessToken();
            return true;
        }
        catch (error) {
            return false;
        }
    }
    hasLoginStateSync() {
        const credentials = this.config.credentialsClient.getCredentialsSync();
        return credentials;
    }
    async getLoginState() {
        return this.config.credentialsClient.getCredentialsAsync();
    }
    async transByProvider(params) {
        return this.config.request(ApiUrls.USER_TRANS_BY_PROVIDER_URL, {
            method: 'PATCH',
            body: params,
            withCredentials: true,
        });
    }
    async grantToken(params) {
        const res = this.getParamsByVersion(params, 'AUTH_TOKEN_URL');
        return this.config.request(res.url, {
            method: 'POST',
            body: res.params,
        });
    }
    async getProviders() {
        return this.config.request(ApiUrls.PROVIDER_LIST, {
            method: 'GET',
            withCredentials: true,
        });
    }
    async unbindProvider(params) {
        return this.config.request(`${ApiUrls.PROVIDER_UNBIND_URL}/${params.provider_id}`, {
            method: 'DELETE',
            withCredentials: true,
        });
    }
    async checkPassword(params) {
        return this.config.request(`${ApiUrls.CHECK_PWD_URL}`, {
            method: 'POST',
            withCredentials: true,
            body: params,
        });
    }
    async editContact(params) {
        return this.config.request(`${ApiUrls.BIND_CONTACT_URL}`, {
            method: 'PATCH',
            withCredentials: true,
            body: params,
        });
    }
    async setPassword(params) {
        return this.config.request(`${ApiUrls.AUTH_SET_PASSWORD}`, {
            method: 'PATCH',
            withCredentials: true,
            body: params,
        });
    }
    async updatePasswordByOld(params) {
        const sudoToken = await this.sudo({ password: params.old_password });
        return this.setPassword({
            sudo_token: sudoToken.sudo_token,
            new_password: params.new_password,
        });
    }
    async sudo(params) {
        return this.config.request(`${ApiUrls.SUDO_URL}`, {
            method: 'POST',
            withCredentials: true,
            body: params,
        });
    }
    async sendVerificationCodeToCurrentUser(params) {
        params.target = 'CUR_USER';
        return this.config.request(ApiUrls.VERIFICATION_URL, {
            method: 'POST',
            body: params,
            withCredentials: true,
            withCaptcha: true,
        });
    }
    async changeBoundProvider(params) {
        return this.config.request(`${ApiUrls.PROVIDER_LIST}/${params.provider_id}/trans`, {
            method: 'POST',
            body: {
                provider_trans_token: params.trans_token,
            },
            withCredentials: true,
        });
    }
    async setUserProfile(params) {
        return this.config.request(ApiUrls.USER_PRIFILE_URL, {
            method: 'PATCH',
            body: params,
            withCredentials: true,
        });
    }
    async updateUserBasicInfo(params) {
        return this.config.request(ApiUrls.USER_BASIC_EDIT_URL, {
            method: 'POST',
            withCredentials: true,
            body: params,
        });
    }
    async queryUserProfile(params) {
        const searchParams = new MyURLSearchParams(params);
        return this.config.request(`${ApiUrls.USER_QUERY_URL}?${searchParams.toString()}`, {
            method: 'GET',
            withCredentials: true,
        });
    }
    setCustomSignFunc(getTickFn) {
        this.getCustomSignTicketFn = getTickFn;
    }
    async signInWithCustomTicket() {
        const customSignTicketFn = this.getCustomSignTicketFn;
        if (!customSignTicketFn) {
            return Promise.reject({
                error: 'failed_precondition',
                error_description: 'please use setCustomSignFunc to set custom sign function',
            });
        }
        const customTicket = await customSignTicketFn();
        return this.signInCustom({
            provider_id: 'custom',
            ticket: customTicket,
        });
    }
    async resetPassword(params) {
        return this.config.request(ApiUrls.AUTH_RESET_PASSWORD, {
            method: 'POST',
            body: params,
        });
    }
    async authorize(params) {
        return this.config.request(ApiUrls.AUTHORIZE_URL, {
            method: 'POST',
            withCredentials: true,
            body: params,
        });
    }
    async authorizeDevice(params) {
        return this.config.request(ApiUrls.AUTHORIZE_DEVICE_URL, {
            method: 'POST',
            withCredentials: true,
            body: params,
        });
    }
    async deviceAuthorize(params) {
        return this.config.request(ApiUrls.AUTH_GET_DEVICE_CODE, {
            method: 'POST',
            body: params,
            withCredentials: true,
        });
    }
    async authorizeInfo(params) {
        const url = `${ApiUrls.AUTHORIZE_INFO_URL}?${Auth.parseParamsToSearch(params)}`;
        let withBasicAuth = true;
        let withCredentials = false;
        const hasLogin = await this.hasLoginState();
        if (hasLogin) {
            withCredentials = true;
            withBasicAuth = false;
        }
        return this.config.request(url, {
            method: 'GET',
            withBasicAuth,
            withCredentials,
        });
    }
    async checkUsername(params) {
        return this.config.request(ApiUrls.CHECK_USERNAME, {
            method: 'GET',
            body: params,
            withCredentials: true,
        });
    }
    async checkIfUserExist(params) {
        const searchParams = new MyURLSearchParams(params);
        return this.config.request(`${ApiUrls.CHECK_IF_USER_EXIST}?${searchParams.toString()}`, {
            method: 'GET',
        });
    }
    async loginScope() {
        return this.config.credentialsClient.getScope();
    }
    async loginGroups() {
        return this.config.credentialsClient.getGroups();
    }
    async refreshTokenForce(params) {
        const credentials = await this.config.credentialsClient.getCredentials();
        return await this.config.credentialsClient.refreshToken({
            ...credentials,
            version: params?.version || 'v1',
        });
    }
    async getCredentials() {
        return this.config.credentialsClient.getCredentials();
    }
    async getPublicKey() {
        return this.config.request(ApiUrlsV2.AUTH_PUBLIC_KEY, {
            method: 'POST',
            body: {},
        });
    }
    async getEncryptParams(params) {
        const { isEncrypt } = params;
        delete params.isEncrypt;
        const payload = deepClone(params);
        const encryptUtils = getEncryptUtils(isEncrypt);
        if (!encryptUtils) {
            return params;
        }
        let publicKey = '';
        let public_key_thumbprint = '';
        try {
            const res = await this.getPublicKey();
            publicKey = res.public_key;
            public_key_thumbprint = res.public_key_thumbprint;
            if (!publicKey || !public_key_thumbprint) {
                throw res;
            }
        }
        catch (error) {
            throw error;
        }
        return {
            params: encryptUtils.getEncryptInfo({ publicKey, payload }),
            public_key_thumbprint,
        };
    }
    async getProviderSubType() {
        return this.config.request(ApiUrls.GET_PROVIDER_TYPE, {
            method: 'POST',
            body: {
                provider_id: 'weda',
            },
        });
    }
    async verifyCaptchaData({ token, key }) {
        return this.config.request(ApiUrls.VERIFY_CAPTCHA_DATA_URL, {
            method: 'POST',
            body: { token, key },
            withCredentials: false,
        });
    }
    async createCaptchaData({ state, redirect_uri = undefined }) {
        return this.config.request(ApiUrls.CAPTCHA_DATA_URL, {
            method: 'POST',
            body: { state, redirect_uri },
            withCredentials: false,
        });
    }
    async getMiniProgramCode(params) {
        return this.config.request(ApiUrls.GET_MINIPROGRAM_QRCODE, {
            method: 'POST',
            body: params,
        });
    }
    async getMiniProgramQrCodeStatus(params) {
        return this.config.request(ApiUrls.GET_MINIPROGRAM_QRCODE_STATUS, {
            method: 'POST',
            body: params,
        });
    }
    async getUserBehaviorLog(params) {
        const action = { LOGIN: 'query[action]=USER_LOGIN', MODIFY: 'ne_query[action]=USER_LOGIN' };
        const url = `${ApiUrls.GET_USER_BEHAVIOR_LOG}?${action[params.type]}&limit=${params.limit}${params.page_token ? `&page_token=${params.page_token}` : ''}`;
        return this.config.request(url, {
            method: 'GET',
            withCredentials: true,
        });
    }
    async modifyPassword(params) {
        let publicKey = '';
        let public_key_thumbprint = '';
        const encryptUtils = getEncryptUtils(true);
        if (!encryptUtils) {
            throw new Error('do not support encrypt, a encrypt util required.');
        }
        try {
            const res = await this.getPublicKey();
            publicKey = res.public_key;
            public_key_thumbprint = res.public_key_thumbprint;
            if (!publicKey || !public_key_thumbprint) {
                throw res;
            }
        }
        catch (error) {
            throw error;
        }
        const encrypt_password = params.password ? encryptUtils.getEncryptInfo({ publicKey, payload: params.password }) : '';
        const encrypt_new_password = encryptUtils.getEncryptInfo({ publicKey, payload: params.new_password });
        return this.config.request(ApiUrls.USER_BASIC_EDIT_URL, {
            method: 'POST',
            withCredentials: true,
            body: {
                user_id: params.user_id,
                encrypt_password,
                encrypt_new_password,
                public_key_thumbprint,
            },
        });
    }
    async modifyPasswordWithoutLogin(params) {
        let publicKey = '';
        let public_key_thumbprint = '';
        const encryptUtils = getEncryptUtils(true);
        if (!encryptUtils) {
            throw new Error('do not support encrypt, a encrypt util required.');
        }
        try {
            const res = await this.getPublicKey();
            publicKey = res.public_key;
            public_key_thumbprint = res.public_key_thumbprint;
            if (!publicKey || !public_key_thumbprint) {
                throw res;
            }
        }
        catch (error) {
            throw error;
        }
        const encrypt_password = params.password ? encryptUtils.getEncryptInfo({ publicKey, payload: params.password }) : '';
        const encrypt_new_password = encryptUtils.getEncryptInfo({ publicKey, payload: params.new_password });
        return this.config.request(ApiUrlsV2.AUTH_RESET_PASSWORD, {
            method: 'POST',
            body: {
                username: params.username,
                password: encrypt_password,
                new_password: encrypt_new_password,
                public_key_thumbprint,
            },
        });
    }
}
