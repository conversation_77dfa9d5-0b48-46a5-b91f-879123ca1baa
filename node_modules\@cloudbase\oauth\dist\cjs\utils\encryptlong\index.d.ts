export default JSEncrypt;
declare class JSEncrypt {
    private constructor();
    public setKey(key: any | string): void;
    key: {
        parseKey(pem: string): boolean;
        n: any;
        e: number;
        d: {
            toString(b: any): any;
            negate(): any;
            abs(): any;
            compareTo(a: any): number;
            bitLength(): number;
            mod(a: any): any;
            modPowInt(e: any, m: any): any;
            clone(): any;
            intValue(): any;
            byteValue(): number;
            shortValue(): number;
            signum(): 0 | 1 | -1;
            toByteArray(): number[];
            equals(a: any): boolean;
            min(a: any): any;
            max(a: any): any;
            and(a: any): any;
            or(a: any): any;
            xor(a: any): any;
            andNot(a: any): any;
            not(): any;
            shiftLeft(n: any): any;
            shiftRight(n: any): any;
            getLowestSetBit(): number;
            bitCount(): number;
            testBit(n: any): boolean;
            setBit(n: any): any;
            clearBit(n: any): any;
            flipBit(n: any): any;
            add(a: any): any;
            subtract(a: any): any;
            multiply(a: any): any;
            divide(a: any): any;
            remainder(a: any): any;
            divideAndRemainder(a: any): any[];
            modPow(e: any, m: any): any;
            modInverse(m: any): any;
            pow(e: any): any;
            gcd(a: any): any;
            isProbablePrime(t: any): boolean;
            copyTo(r: any): void;
            fromInt(x: any): void;
            t: number;
            s: number;
            0: any;
            fromString(s: any, b: any): void;
            clamp(): void;
            dlShiftTo(n: any, r: any): void;
            drShiftTo(n: any, r: any): void;
            lShiftTo(n: any, r: any): void;
            rShiftTo(n: any, r: any): void;
            subTo(a: any, r: any): void;
            multiplyTo(a: any, r: any): void;
            squareTo(r: any): void;
            divRemTo(m: any, q: any, r: any): void;
            invDigit(): number;
            isEven(): boolean;
            exp(e: any, z: any): any;
            chunkSize(r: any): number;
            toRadix(b: any): string;
            fromRadix(s: any, b: any): void;
            fromNumber(a: any, b: any, c: any): void;
            bitwiseTo(a: any, op: any, r: any): void;
            changeBit(n: any, op: any): any;
            addTo(a: any, r: any): void;
            dMultiply(n: any): void;
            dAddOffset(n: any, w: any): void;
            multiplyLowerTo(a: any, n: any, r: any): void;
            multiplyUpperTo(a: any, n: any, r: any): void;
            modInt(n: any): number;
            millerRabin(t: any): boolean;
            square(): any;
            gcda(a: any, callback: any): void;
            fromNumberAsync(a: any, b: any, c: any, callback: any): void;
            DB: number;
            DM: number;
            DV: number;
            FV: number;
            F1: number;
            F2: number;
        };
        p: any;
        q: any;
        dmp1: {
            toString(b: any): any;
            negate(): any;
            abs(): any;
            compareTo(a: any): number;
            bitLength(): number;
            mod(a: any): any;
            modPowInt(e: any, m: any): any;
            clone(): any;
            intValue(): any;
            byteValue(): number;
            shortValue(): number;
            signum(): 0 | 1 | -1;
            toByteArray(): number[];
            equals(a: any): boolean;
            min(a: any): any;
            max(a: any): any;
            and(a: any): any;
            or(a: any): any;
            xor(a: any): any;
            andNot(a: any): any;
            not(): any;
            shiftLeft(n: any): any;
            shiftRight(n: any): any;
            getLowestSetBit(): number;
            bitCount(): number;
            testBit(n: any): boolean;
            setBit(n: any): any;
            clearBit(n: any): any;
            flipBit(n: any): any;
            add(a: any): any;
            subtract(a: any): any;
            multiply(a: any): any;
            divide(a: any): any;
            remainder(a: any): any;
            divideAndRemainder(a: any): any[];
            modPow(e: any, m: any): any;
            modInverse(m: any): any;
            pow(e: any): any;
            gcd(a: any): any;
            isProbablePrime(t: any): boolean;
            copyTo(r: any): void;
            fromInt(x: any): void;
            t: number;
            s: number;
            0: any;
            fromString(s: any, b: any): void;
            clamp(): void;
            dlShiftTo(n: any, r: any): void;
            drShiftTo(n: any, r: any): void;
            lShiftTo(n: any, r: any): void;
            rShiftTo(n: any, r: any): void;
            subTo(a: any, r: any): void;
            multiplyTo(a: any, r: any): void;
            squareTo(r: any): void;
            divRemTo(m: any, q: any, r: any): void;
            invDigit(): number;
            isEven(): boolean;
            exp(e: any, z: any): any;
            chunkSize(r: any): number;
            toRadix(b: any): string;
            fromRadix(s: any, b: any): void;
            fromNumber(a: any, b: any, c: any): void;
            bitwiseTo(a: any, op: any, r: any): void;
            changeBit(n: any, op: any): any;
            addTo(a: any, r: any): void;
            dMultiply(n: any): void;
            dAddOffset(n: any, w: any): void;
            multiplyLowerTo(a: any, n: any, r: any): void;
            multiplyUpperTo(a: any, n: any, r: any): void;
            modInt(n: any): number;
            millerRabin(t: any): boolean;
            square(): any;
            gcda(a: any, callback: any): void;
            fromNumberAsync(a: any, b: any, c: any, callback: any): void;
            DB: number;
            DM: number;
            DV: number;
            FV: number;
            F1: number;
            F2: number;
        };
        dmq1: {
            toString(b: any): any;
            negate(): any;
            abs(): any;
            compareTo(a: any): number;
            bitLength(): number;
            mod(a: any): any;
            modPowInt(e: any, m: any): any;
            clone(): any;
            intValue(): any;
            byteValue(): number;
            shortValue(): number;
            signum(): 0 | 1 | -1;
            toByteArray(): number[];
            equals(a: any): boolean;
            min(a: any): any;
            max(a: any): any;
            and(a: any): any;
            or(a: any): any;
            xor(a: any): any;
            andNot(a: any): any;
            not(): any;
            shiftLeft(n: any): any;
            shiftRight(n: any): any;
            getLowestSetBit(): number;
            bitCount(): number;
            testBit(n: any): boolean;
            setBit(n: any): any;
            clearBit(n: any): any;
            flipBit(n: any): any;
            add(a: any): any;
            subtract(a: any): any;
            multiply(a: any): any;
            divide(a: any): any;
            remainder(a: any): any;
            divideAndRemainder(a: any): any[];
            modPow(e: any, m: any): any;
            modInverse(m: any): any;
            pow(e: any): any;
            gcd(a: any): any;
            isProbablePrime(t: any): boolean;
            copyTo(r: any): void;
            fromInt(x: any): void;
            t: number;
            s: number;
            0: any;
            fromString(s: any, b: any): void;
            clamp(): void;
            dlShiftTo(n: any, r: any): void;
            drShiftTo(n: any, r: any): void;
            lShiftTo(n: any, r: any): void;
            rShiftTo(n: any, r: any): void;
            subTo(a: any, r: any): void;
            multiplyTo(a: any, r: any): void;
            squareTo(r: any): void;
            divRemTo(m: any, q: any, r: any): void;
            invDigit(): number;
            isEven(): boolean;
            exp(e: any, z: any): any;
            chunkSize(r: any): number;
            toRadix(b: any): string;
            fromRadix(s: any, b: any): void;
            fromNumber(a: any, b: any, c: any): void;
            bitwiseTo(a: any, op: any, r: any): void;
            changeBit(n: any, op: any): any;
            addTo(a: any, r: any): void;
            dMultiply(n: any): void;
            dAddOffset(n: any, w: any): void;
            multiplyLowerTo(a: any, n: any, r: any): void;
            multiplyUpperTo(a: any, n: any, r: any): void;
            modInt(n: any): number;
            millerRabin(t: any): boolean;
            square(): any;
            gcda(a: any, callback: any): void;
            fromNumberAsync(a: any, b: any, c: any, callback: any): void;
            DB: number;
            DM: number;
            DV: number;
            FV: number;
            F1: number;
            F2: number;
        };
        coeff: any;
        hasPublicKeyProperty(obj?: any): boolean;
        hasPrivateKeyProperty(obj?: any): boolean;
        parsePropertiesFrom(obj: any): void;
        doPublic(x: any): any;
        doPrivate(x: any): any;
        setPublic(N: any, E: any): void;
        encrypt(text: any): any;
        encryptLong(text: any): string;
        decryptLong(text: any): string;
        setPrivate(N: any, E: any, D: any): void;
        setPrivateEx(N: any, E: any, D: any, P: any, Q: any, DP: any, DQ: any, C: any): void;
        generate(B: any, E: any): void;
        decrypt(ctext: any): string;
        generateAsync(B: any, E: any, callback: any): void;
    };
    public setPrivateKey(privkey: any): void;
    public setPublicKey(pubkey: any): void;
    public decrypt(str: string): string;
    public encrypt(str: string): string;
    public encryptLong(str: string): string;
    public getKey(cb?: callback): {
        parseKey(pem: string): boolean;
        n: any;
        e: number;
        d: {
            toString(b: any): any;
            negate(): any;
            abs(): any;
            compareTo(a: any): number;
            bitLength(): number;
            mod(a: any): any;
            modPowInt(e: any, m: any): any;
            clone(): any;
            intValue(): any;
            byteValue(): number;
            shortValue(): number;
            signum(): 0 | 1 | -1;
            toByteArray(): number[];
            equals(a: any): boolean;
            min(a: any): any;
            max(a: any): any;
            and(a: any): any;
            or(a: any): any;
            xor(a: any): any;
            andNot(a: any): any;
            not(): any;
            shiftLeft(n: any): any;
            shiftRight(n: any): any;
            getLowestSetBit(): number;
            bitCount(): number;
            testBit(n: any): boolean;
            setBit(n: any): any;
            clearBit(n: any): any;
            flipBit(n: any): any;
            add(a: any): any;
            subtract(a: any): any;
            multiply(a: any): any;
            divide(a: any): any;
            remainder(a: any): any;
            divideAndRemainder(a: any): any[];
            modPow(e: any, m: any): any;
            modInverse(m: any): any;
            pow(e: any): any;
            gcd(a: any): any;
            isProbablePrime(t: any): boolean;
            copyTo(r: any): void;
            fromInt(x: any): void;
            t: number;
            s: number;
            0: any;
            fromString(s: any, b: any): void;
            clamp(): void;
            dlShiftTo(n: any, r: any): void;
            drShiftTo(n: any, r: any): void;
            lShiftTo(n: any, r: any): void;
            rShiftTo(n: any, r: any): void;
            subTo(a: any, r: any): void;
            multiplyTo(a: any, r: any): void;
            squareTo(r: any): void;
            divRemTo(m: any, q: any, r: any): void;
            invDigit(): number;
            isEven(): boolean;
            exp(e: any, z: any): any;
            chunkSize(r: any): number;
            toRadix(b: any): string;
            fromRadix(s: any, b: any): void;
            fromNumber(a: any, b: any, c: any): void;
            bitwiseTo(a: any, op: any, r: any): void;
            changeBit(n: any, op: any): any;
            addTo(a: any, r: any): void;
            dMultiply(n: any): void;
            dAddOffset(n: any, w: any): void;
            multiplyLowerTo(a: any, n: any, r: any): void;
            multiplyUpperTo(a: any, n: any, r: any): void;
            modInt(n: any): number;
            millerRabin(t: any): boolean;
            square(): any;
            gcda(a: any, callback: any): void;
            fromNumberAsync(a: any, b: any, c: any, callback: any): void;
            DB: number;
            DM: number;
            DV: number;
            FV: number;
            F1: number;
            F2: number;
        };
        p: any;
        q: any;
        dmp1: {
            toString(b: any): any;
            negate(): any;
            abs(): any;
            compareTo(a: any): number;
            bitLength(): number;
            mod(a: any): any;
            modPowInt(e: any, m: any): any;
            clone(): any;
            intValue(): any;
            byteValue(): number;
            shortValue(): number;
            signum(): 0 | 1 | -1;
            toByteArray(): number[];
            equals(a: any): boolean;
            min(a: any): any;
            max(a: any): any;
            and(a: any): any;
            or(a: any): any;
            xor(a: any): any;
            andNot(a: any): any;
            not(): any;
            shiftLeft(n: any): any;
            shiftRight(n: any): any;
            getLowestSetBit(): number;
            bitCount(): number;
            testBit(n: any): boolean;
            setBit(n: any): any;
            clearBit(n: any): any;
            flipBit(n: any): any;
            add(a: any): any;
            subtract(a: any): any;
            multiply(a: any): any;
            divide(a: any): any;
            remainder(a: any): any;
            divideAndRemainder(a: any): any[];
            modPow(e: any, m: any): any;
            modInverse(m: any): any;
            pow(e: any): any;
            gcd(a: any): any;
            isProbablePrime(t: any): boolean;
            copyTo(r: any): void;
            fromInt(x: any): void;
            t: number;
            s: number;
            0: any;
            fromString(s: any, b: any): void;
            clamp(): void;
            dlShiftTo(n: any, r: any): void;
            drShiftTo(n: any, r: any): void;
            lShiftTo(n: any, r: any): void;
            rShiftTo(n: any, r: any): void;
            subTo(a: any, r: any): void;
            multiplyTo(a: any, r: any): void;
            squareTo(r: any): void;
            divRemTo(m: any, q: any, r: any): void;
            invDigit(): number;
            isEven(): boolean;
            exp(e: any, z: any): any;
            chunkSize(r: any): number;
            toRadix(b: any): string;
            fromRadix(s: any, b: any): void;
            fromNumber(a: any, b: any, c: any): void;
            bitwiseTo(a: any, op: any, r: any): void;
            changeBit(n: any, op: any): any;
            addTo(a: any, r: any): void;
            dMultiply(n: any): void;
            dAddOffset(n: any, w: any): void;
            multiplyLowerTo(a: any, n: any, r: any): void;
            multiplyUpperTo(a: any, n: any, r: any): void;
            modInt(n: any): number;
            millerRabin(t: any): boolean;
            square(): any;
            gcda(a: any, callback: any): void;
            fromNumberAsync(a: any, b: any, c: any, callback: any): void;
            DB: number;
            DM: number;
            DV: number;
            FV: number;
            F1: number;
            F2: number;
        };
        dmq1: {
            toString(b: any): any;
            negate(): any;
            abs(): any;
            compareTo(a: any): number;
            bitLength(): number;
            mod(a: any): any;
            modPowInt(e: any, m: any): any;
            clone(): any;
            intValue(): any;
            byteValue(): number;
            shortValue(): number;
            signum(): 0 | 1 | -1;
            toByteArray(): number[];
            equals(a: any): boolean;
            min(a: any): any;
            max(a: any): any;
            and(a: any): any;
            or(a: any): any;
            xor(a: any): any;
            andNot(a: any): any;
            not(): any;
            shiftLeft(n: any): any;
            shiftRight(n: any): any;
            getLowestSetBit(): number;
            bitCount(): number;
            testBit(n: any): boolean;
            setBit(n: any): any;
            clearBit(n: any): any;
            flipBit(n: any): any;
            add(a: any): any;
            subtract(a: any): any;
            multiply(a: any): any;
            divide(a: any): any;
            remainder(a: any): any;
            divideAndRemainder(a: any): any[];
            modPow(e: any, m: any): any;
            modInverse(m: any): any;
            pow(e: any): any;
            gcd(a: any): any;
            isProbablePrime(t: any): boolean;
            copyTo(r: any): void;
            fromInt(x: any): void;
            t: number;
            s: number;
            0: any;
            fromString(s: any, b: any): void;
            clamp(): void;
            dlShiftTo(n: any, r: any): void;
            drShiftTo(n: any, r: any): void;
            lShiftTo(n: any, r: any): void;
            rShiftTo(n: any, r: any): void;
            subTo(a: any, r: any): void;
            multiplyTo(a: any, r: any): void;
            squareTo(r: any): void;
            divRemTo(m: any, q: any, r: any): void;
            invDigit(): number;
            isEven(): boolean;
            exp(e: any, z: any): any;
            chunkSize(r: any): number;
            toRadix(b: any): string;
            fromRadix(s: any, b: any): void;
            fromNumber(a: any, b: any, c: any): void;
            bitwiseTo(a: any, op: any, r: any): void;
            changeBit(n: any, op: any): any;
            addTo(a: any, r: any): void;
            dMultiply(n: any): void;
            dAddOffset(n: any, w: any): void;
            multiplyLowerTo(a: any, n: any, r: any): void;
            multiplyUpperTo(a: any, n: any, r: any): void;
            modInt(n: any): number;
            millerRabin(t: any): boolean;
            square(): any;
            gcda(a: any, callback: any): void;
            fromNumberAsync(a: any, b: any, c: any, callback: any): void;
            DB: number;
            DM: number;
            DV: number;
            FV: number;
            F1: number;
            F2: number;
        };
        coeff: any;
        hasPublicKeyProperty(obj?: any): boolean;
        hasPrivateKeyProperty(obj?: any): boolean;
        parsePropertiesFrom(obj: any): void;
        doPublic(x: any): any;
        doPrivate(x: any): any;
        setPublic(N: any, E: any): void;
        encrypt(text: any): any;
        encryptLong(text: any): string;
        decryptLong(text: any): string;
        setPrivate(N: any, E: any, D: any): void;
        setPrivateEx(N: any, E: any, D: any, P: any, Q: any, DP: any, DQ: any, C: any): void;
        generate(B: any, E: any): void;
        decrypt(ctext: any): string;
        generateAsync(B: any, E: any, callback: any): void;
    };
}
