# PC端导航功能说明

## 导航结构

您的PC端财务系统现在具有完整的左侧导航栏，所有菜单项点击后都会在右侧主内容区域显示对应的页面内容，而不是跳转到新页面。

### 一级菜单

1. **🏠 仪表盘** (`mainView`)
   - 财务数据概览
   - 关键指标展示
   - 快捷操作面板

2. **📊 报表** (`reports` - 可展开)
   - 收支统计
   - 应收统计  
   - 应付统计
   - 项目统计
   - 客户统计
   - 供应商统计
   - 数据导出

3. **💰 财务管理** (`finance` - 可展开)
   - 资金流水
   - 应收管理
   - 应付管理
   - 账户管理

4. **✅ 审批管理** (`approval_index`)
   - 审批列表
   - 批量审批
   - 审批统计

5. **📝 收支类型** (`income-expense` - 可展开)
   - 收入类型
   - 支出类型

6. **🏗️ 项目管理** (`project`)
   - 项目信息管理

7. **👥 客户管理** (`client`)
   - 客户信息管理

8. **🏪 供应商管理** (`supplier`)
   - 供应商信息管理

9. **👤 角色管理** (`role`)
   - 系统角色配置

10. **⚙️ 个人信息设置** (`userSettings`)
    - 个人资料管理

11. **🔧 系统设置** (`systemSettings`)
    - 系统配置管理

12. **🚪 退出登录**
    - 安全退出系统

## 已实现的功能页面

### ✅ 完全实现
- **仪表盘**: 完整的财务数据展示，包含实时数据和模拟数据
- **收支统计**: 完整的收支报表组件，支持筛选和导出
- **审批管理**: 完整的审批工作流界面，支持批量操作

### 🚧 开发中（显示占位页面）
- 财务管理模块（资金流水、应收管理、应付管理、账户管理）
- 报表统计模块（项目统计、客户统计、供应商统计）
- 基础数据管理（收支类型、项目管理、客户管理、供应商管理）
- 系统管理模块（角色管理、个人设置、系统设置）

## 导航交互说明

### 单击菜单项
- 直接切换到对应的功能页面
- 页面内容在右侧主区域显示
- 左侧导航栏保持可见
- 当前选中的菜单项会高亮显示

### 可展开菜单
- 点击带有下拉箭头的菜单项可展开/收起子菜单
- 子菜单项点击后同样在右侧显示内容
- 支持多个菜单同时展开

### 视觉反馈
- 当前激活的菜单项有高亮样式
- 鼠标悬停时有hover效果
- 展开的菜单有动画效果

## 快捷操作

在仪表盘页面的右侧还提供了快捷操作面板：
- 📊 收支统计
- ✅ 审批管理  
- 💰 应收管理
- 💸 应付管理
- 📤 数据导出
- 🔄 刷新数据
- 🔧 测试连接

## 开发优先级建议

### 第一阶段（核心财务功能）
1. **资金流水管理** - 基于现有的cashFlow相关云函数
2. **应收管理详情** - 扩展现有的receivable统计功能
3. **应付管理详情** - 扩展现有的payable统计功能

### 第二阶段（数据管理）
1. **账户管理** - 银行账户的增删改查
2. **项目管理** - 项目信息的完整管理
3. **客户/供应商管理** - 基础数据管理

### 第三阶段（系统管理）
1. **角色权限管理** - 用户角色和权限配置
2. **系统设置** - 系统参数和配置管理
3. **个人设置** - 用户个人信息管理

## 技术实现说明

### 路由机制
使用Vue的响应式数据`currentView`来控制页面显示：
```javascript
// 点击菜单项时
@click="currentView = 'reports_income-expense_index'"

// 在模板中显示对应内容
<view v-if="currentView === 'reports_income-expense_index'">
  <IncomeExpenseReport />
</view>
```

### 菜单状态管理
使用`openMenus`数组管理可展开菜单的状态：
```javascript
// 切换子菜单展开状态
toggleSubMenu(menuKey) {
  const index = this.openMenus.indexOf(menuKey);
  if (index > -1) {
    this.openMenus.splice(index, 1);
  } else {
    this.openMenus.push(menuKey);
  }
}
```

### 组件化设计
- 每个功能模块都是独立的Vue组件
- 支持懒加载和按需引入
- 便于维护和扩展

## 使用建议

1. **测试导航**: 点击左侧各个菜单项，确认页面正确切换
2. **功能体验**: 已实现的功能（仪表盘、收支统计、审批管理）可以完整体验
3. **开发规划**: 根据业务优先级逐步实现其他功能模块
4. **用户反馈**: 收集用户对界面布局和交互的反馈意见

现在您的PC端系统已经具备了完整的导航结构和良好的用户体验！
