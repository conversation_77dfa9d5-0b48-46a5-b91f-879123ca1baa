export { ErrorType } from '../auth/consts';
export declare enum Syntax {
    CLIENT_ID = "client_id",
    CLIENT_SECRET = "client_secret",
    RESPONSE_TYPE = "response_type",
    SCOPE = "scope",
    STATE = "state",
    REDIRECT_URI = "redirect_uri",
    ERROR = "error",
    ERROR_DESCRIPTION = "error_description",
    ERROR_URI = "error_uri",
    GRANT_TYPE = "grant_type",
    CODE = "code",
    ACCESS_TOKEN = "access_token",
    TOKEN_TYPE = "token_type",
    EXPIRES_IN = "expires_in",
    USERNAME = "username",
    PASSWORD = "password",
    REFRESH_TOKEN = "refresh_token"
}
