interface BaseRequest {
    client_id?: string;
}
export type GetCustomSignTicketFn = () => Promise<string>;
export interface SignInRequest extends BaseRequest, EncryptParams {
    username?: string;
    password?: string;
    verification_token?: string;
    version?: string;
    query?: {
        with_department?: boolean;
        with_role?: boolean;
    };
}
export interface SignInWithProviderRequest extends BaseRequest {
    provider_token: string;
    version?: string;
}
export interface SignUpRequest extends BaseRequest {
    phone_number?: string;
    email?: string;
    verification_code?: string;
    verification_token?: string;
    provider_token?: string;
    password?: string;
    name?: string;
    gender?: string;
    picture?: string;
    locale?: string;
}
export interface GetVerificationRequest extends BaseRequest {
    phone_number?: string;
    email?: string;
    target?: string | 'ANY';
    usage?: string;
}
export interface GetVerificationResponse {
    verification_id?: string;
    is_user?: boolean | false;
}
export interface VerifyResponse {
    verification_token?: string;
}
export interface VerifyRequest extends BaseRequest {
    verification_code: string;
    verification_id?: string;
    verification_token?: string;
    version?: string;
}
export interface ProviderBindRequest {
    provider_token: string;
}
export interface GrantProviderTokenRequest extends BaseRequest {
    provider_id: string;
    provider_redirect_uri?: string;
    provider_code?: string;
    provider_access_token?: string;
    provider_id_token?: string;
}
export interface GrantProviderTokenResponse {
    provider_token: string;
    expires_in: number;
    code?: string;
    error_code?: string;
    provider_profile?: ProviderProfile;
}
export interface PatchProviderTokenRequest extends BaseRequest {
    provider_id?: string;
    provider_token: string;
    provider_params: {
        encryptedData?: string;
        iv?: string;
        code?: string;
        provider_code_type?: string;
    };
}
export interface PatchProviderTokenResponse {
    provider_token: string;
    expires_in: number;
    provider_profile: ProviderProfile;
}
export interface GenProviderRedirectUriResponse {
    uri: string;
    signout_uri?: string;
}
export interface BindWithProviderRequest extends BaseRequest {
    provider_token: string;
}
export interface BindWithProviderRequest {
    provider_token: string;
}
export interface UserProfileProvider {
    id?: string;
    provider_user_id?: string;
    name?: string;
}
export interface UserProfile {
    name?: string;
    picture?: string;
    username?: string;
    email?: string;
    email_verified?: boolean;
    phone_number?: string;
    providers?: [UserProfileProvider];
    gender?: string;
    birthdate?: string;
    zoneinfo?: string;
    locale?: string;
    created_from?: string;
    sub?: string;
    uid?: string;
    address?: {
        formatted?: string;
        street_address?: string;
        locality?: string;
        region?: string;
        postal_code?: string;
        country?: string;
    };
    nickName?: string;
    province?: string;
    country?: string;
    city?: string;
}
export type UserInfo = UserProfile;
export interface ProviderProfile {
    provider_id: string;
    phone_number?: string;
    name?: string;
    picture?: string;
    email?: string;
}
export interface TransByProviderRequest {
    provider_token: string;
}
export interface GrantTokenRequest extends BaseRequest {
    client_secret?: string;
    code?: string;
    grant_type?: string;
    redirect_uri?: string;
    nonce?: string;
    refresh_token?: string;
    scope?: string;
}
export interface UnbindProviderRequest extends BaseRequest {
    provider_id: string;
}
export interface BindPhoneRequest extends BaseRequest {
    phone_number: string;
    sudo_token: string;
    verification_token: string;
}
export interface SetPasswordRequest extends BaseRequest {
    new_password: string;
    sudo_token: string;
}
export interface ChangeBindedProviderRequest extends BaseRequest {
    trans_token: string;
    provider_id: string;
}
export interface QueryUserProfileReq extends BaseRequest {
    appended_params: string;
}
export interface SignInWithProviderRequest {
    provider_token: string;
    provider_id?: string;
}
export interface SignInCustomRequest {
    provider_id?: string;
    ticket: string;
}
export interface SignUpRequest {
    phone_number?: string;
    email?: string;
    verification_code?: string;
    verification_token?: string;
    provider_token?: string;
    username?: string;
    password?: string;
    name?: string;
    gender?: string;
    picture?: string;
    locale?: string;
    anonymous_token?: string;
}
export interface GetVerificationRequest {
    phone_number?: string;
    email?: string;
    target?: string | 'ANY';
    usage?: string;
}
export interface GetVerificationResponse {
    verification_id?: string;
    is_user?: boolean | false;
}
export interface VerifyResponse {
    verification_token?: string;
    expires_in: number;
}
export interface VerifyRequest {
    verification_code: string;
    verification_id?: string;
    verification_token?: string;
    version?: string;
}
export interface ProviderBindRequest {
    provider_token: string;
    expires_in: number;
}
export interface GrantProviderTokenRequest {
    provider_id: string;
    provider_redirect_uri?: string;
    provider_code?: string;
    provider_access_token?: string;
    provider_id_token?: string;
    provider_params?: {
        provider_code_type?: string;
        appid?: string;
    };
}
export interface PatchProviderTokenRequest {
    provider_token: string;
    provider_id?: string;
    provider_params: {
        encryptedData?: string;
        iv?: string;
        code?: string;
        provider_code_type?: string;
    };
}
export interface PatchProviderTokenResponse {
    provider_token: string;
    expires_in: number;
    provider_profile: ProviderProfile;
}
export interface GenProviderRedirectUriRequest {
    provider_id: string;
    redirect_uri: string;
    provider_redirect_uri?: string;
    state: string;
    scope?: string;
    response_type?: string;
    other_params?: {
        [key: string]: string;
    };
}
export interface GenProviderRedirectUriResponse {
    uri: string;
    signout_uri?: string;
}
export interface BindWithProviderRequest {
    provider_token: string;
}
export interface BindWithProviderRequest {
    provider_token: string;
}
export interface UserProfileProvider {
    id?: string;
    provider_user_id?: string;
    name?: string;
}
interface ProfileGroup {
    id: string;
    expires_at?: string;
}
export interface UserProfile {
    sub?: string;
    name?: string;
    picture?: string;
    username?: string;
    email?: string;
    email_verified?: boolean;
    phone_number?: string;
    groups?: [ProfileGroup];
    providers?: [UserProfileProvider];
    gender?: string;
    birthdate?: string;
    zoneinfo?: string;
    locale?: string;
    created_from?: string;
}
interface UserProvider {
    id: string;
    name: string;
    provider_user_name?: string;
    bind: boolean;
}
export interface ProvidersResponse {
    total: number;
    data: [UserProvider];
}
export interface ProviderProfile {
    provider_id: string;
    name?: string;
    picture?: string;
    phone_number?: string;
    email?: string;
}
export interface TransByProviderRequest {
    provider_token: string;
}
export interface GrantTokenRequest {
    client_secret?: string;
    code?: string;
    grant_type?: string;
    redirect_uri?: string;
    nonce?: string;
    refresh_token?: string;
    username?: string;
    password?: string;
    scope?: string;
    code_verifier?: string;
    device_code?: string;
}
export interface UnbindProviderRequest {
    provider_id: string;
}
export interface CheckPasswordRequest extends BaseRequest {
    password: string;
}
export interface EditContactRequest {
    phone_number?: string;
    email?: string;
    sudo_token: string;
    verification_token: string;
    conflict_resolution?: string;
}
export interface BindPhoneRequest {
    phone_number: string;
    sudo_token: string;
    verification_token: string;
    conflict_resolution: string;
}
export interface BindEmailRequest {
    email: string;
    sudo_token: string;
    verification_token: string;
}
export interface SetPasswordRequest {
    new_password: string;
    sudo_token: string;
}
export interface SetPasswordRequest {
    new_password: string;
    sudo_token: string;
}
export interface UpdatePasswordRequest {
    old_password: string;
    new_password: string;
}
export interface SudoRequest {
    password?: string;
    verification_token?: string;
}
export interface SudoResponse {
    sudo_token?: string;
}
export interface WithSudoRequest {
    sudo_token: string;
    version?: string;
}
export interface ChangeBoundProviderRequest {
    trans_token: string;
    provider_id: string;
}
export interface ChangeBoundProviderResponse {
    client_id: string;
}
export interface QueryUserProfileRequest {
    id?: [string];
    username?: string;
    email?: string;
    phone_number?: string;
}
export interface QueryUserProfileResponse {
    total: number;
    data?: [SimpleUserProfile];
}
export interface ResetPasswordRequest extends BaseRequest {
    email: string;
    phone_number: string;
    new_password: string;
    verification_token: string;
}
export interface DeviceAuthorizeRequest extends BaseRequest {
    scope?: string;
}
export interface AuthorizeRequest extends BaseRequest {
    response_type?: string;
    redirect_uri?: string;
    state?: string;
    scope?: string;
    code_challenge?: string;
    code_challenge_method?: string;
    sign_out_uri?: string;
}
export interface AuthorizeResponse {
    code?: string;
    access_token?: string;
    id_token?: string;
    token_type?: string;
    scope?: string;
    state?: string;
    expires_in?: number;
}
export interface AuthorizeInfoRequest extends BaseRequest {
    response_type?: string;
    redirect_uri?: string;
    state?: string;
    scope?: string;
    sign_out_uri?: string;
    locale?: string;
}
export interface Scope {
    id: string;
    name: string;
    description?: string;
    picture?: string;
    url?: string;
    children?: Scope[];
}
export interface AuthorizeInfoResponse {
    client: {
        id: string;
        name: string;
        description?: string;
        picture?: string;
        url?: string;
    };
    scopes?: Scope[];
}
export interface RevokeDeviceRequest {
    device_id: string;
}
export interface SignoutRequest {
    redirect_uri?: string;
    state?: string;
}
export interface SignoutReponse {
    redirect_uri?: string;
}
export interface AuthorizeDeviceRequest extends BaseRequest {
    user_code: string;
    scope?: string;
    state?: string;
}
export interface DeviceAuthorizeResponse {
    device_code: string;
    user_code?: string;
    expires_in: number;
    interval: number;
    verification_uri?: string;
    verification_uri_complete?: string;
}
export interface SimpleUserProfile {
    sub: string;
    name: string;
    picture?: string;
    gender?: string;
    locale?: string;
    email?: string;
    phone_number?: string;
}
export interface CheckUsernameRequest {
    username: string;
}
export interface CheckIfUserExistRequest {
    username: string;
}
export interface CheckIfUserExistResponse {
    exist: boolean;
}
export interface PublicKey {
    public_key: string;
    public_key_thumbprint: string;
}
export interface EncryptParams {
    isEncrypt?: boolean;
    public_key_thumbprint?: string;
    params?: string;
}
export interface ProviderSubType {
    id: string;
    provider_sub_type: 'NO_AUTH_LOGIN' | '';
}
export interface GetMiniProgramQrCodeRequest {
    envId: string;
    wxAppId: string;
}
export interface GetMiniProgramQrCodeResponse {
    qr_code: string;
    qr_code_id: string;
    expires_in: number;
}
export interface GetMiniProgramQrCodeStatusRequest {
    qrCodeId: string;
    envId: string;
    wxAppId: string;
}
export interface GetMiniProgramQrCodeStatusResponse {
    status: string;
    provider_token: string;
    expires_in: number;
}
export interface ModifyUserBasicInfoRequest {
    user_id?: string;
    nickname?: string;
    username?: string;
    description?: string;
    avatar_url?: string;
    gender?: 'MALE' | 'FEMALE';
    password?: string;
    new_password?: string;
}
export interface GetUserBehaviorLog {
    type: 'LOGIN' | 'MODIFY';
    limit: number;
    page_token?: string;
}
export interface GetUserBehaviorLogRes {
    id: string;
    ip: string;
    user_agent: string;
    client_id: string;
    device_id: string;
    created_at: string;
    meta: {
        from: string;
    };
}
export interface ModifyPasswordWithoutLoginRequest {
    username?: string;
    password?: string;
    new_password?: string;
}
export interface ToDefaultLoginPage {
    config_version?: 'env' | string;
    redirect_uri?: string;
    app_id?: string;
}
export {};
