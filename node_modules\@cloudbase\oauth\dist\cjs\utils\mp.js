"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isInMpWebView = exports.isMp = void 0;
function isMp() {
    var wx = globalThis.wx;
    if (typeof wx === 'undefined') {
        return false;
    }
    if (globalThis.Page === undefined) {
        return false;
    }
    if (!wx.getSystemInfoSync) {
        return false;
    }
    if (!wx.getStorageSync) {
        return false;
    }
    if (!wx.setStorageSync) {
        return false;
    }
    if (!wx.connectSocket) {
        return false;
    }
    if (!wx.request) {
        return false;
    }
    try {
        if (!wx.getSystemInfoSync()) {
            return false;
        }
        if (wx.getSystemInfoSync().AppPlatform === 'qq') {
            return false;
        }
    }
    catch (e) {
        return false;
    }
    return true;
}
exports.isMp = isMp;
var IS_IN_MP_WEBVIEW = false;
function ready() {
    IS_IN_MP_WEBVIEW = IS_IN_MP_WEBVIEW || (typeof window !== undefined && window.__wxjs_environment === 'miniprogram');
}
try {
    if (!isMp()) {
        IS_IN_MP_WEBVIEW = IS_IN_MP_WEBVIEW
            || !!navigator.userAgent.match(/miniprogram/i)
            || window.__wxjs_environment === 'miniprogram';
        if (window && window.WeixinJSBridge && window.WeixinJSBridge.invoke) {
            ready();
        }
        else if (typeof document !== 'undefined') {
            document.addEventListener('WeixinJSBridgeReady', ready, false);
        }
    }
}
catch (e) { }
function isInMpWebView() {
    return IS_IN_MP_WEBVIEW;
}
exports.isInMpWebView = isInMpWebView;
//# sourceMappingURL=data:application/json;base64,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