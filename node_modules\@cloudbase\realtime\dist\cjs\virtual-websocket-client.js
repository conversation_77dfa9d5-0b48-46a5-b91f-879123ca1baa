"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VirtualWebSocketClient = void 0;
var set_1 = __importDefault(require("lodash/set"));
var unset_1 = __importDefault(require("lodash/unset"));
var cloneDeep_1 = __importDefault(require("lodash/cloneDeep"));
var message_1 = require("./message");
var listener_1 = require("./listener");
var snapshot_1 = require("./snapshot");
var error_1 = require("./error");
var utils_1 = require("./utils");
var WatchStatus;
(function (WatchStatus) {
    WatchStatus["LOGGINGIN"] = "LOGGINGIN";
    WatchStatus["INITING"] = "INITING";
    WatchStatus["REBUILDING"] = "REBUILDING";
    WatchStatus["ACTIVE"] = "ACTIVE";
    WatchStatus["ERRORED"] = "ERRORED";
    WatchStatus["CLOSING"] = "CLOSING";
    WatchStatus["CLOSED"] = "CLOSED";
    WatchStatus["PAUSED"] = "PAUSED";
    WatchStatus["RESUMING"] = "RESUMING";
})(WatchStatus || (WatchStatus = {}));
var DEFAULT_WAIT_TIME_ON_UNKNOWN_ERROR = 100;
var DEFAULT_MAX_AUTO_RETRY_ON_ERROR = 2;
var DEFAULT_MAX_SEND_ACK_AUTO_RETRY_ON_ERROR = 2;
var DEFAULT_SEND_ACK_DEBOUNCE_TIMEOUT = 10 * 1000;
var DEFAULT_INIT_WATCH_TIMEOUT = 10 * 1000;
var DEFAULT_REBUILD_WATCH_TIMEOUT = 10 * 1000;
var VirtualWebSocketClient = (function () {
    function VirtualWebSocketClient(options) {
        var _this = this;
        this.watchStatus = WatchStatus.INITING;
        this.wsLogin = function (envId, refresh) { return __awaiter(_this, void 0, void 0, function () {
            var loginResult;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.watchStatus = WatchStatus.LOGGINGIN;
                        return [4, this.login(envId, refresh)];
                    case 1:
                        loginResult = _a.sent();
                        if (!this.envId) {
                            this.envId = loginResult.envId;
                        }
                        return [2, loginResult];
                }
            });
        }); };
        this.initWatch = function (forceRefreshLogin) { return __awaiter(_this, void 0, void 0, function () {
            var success;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.initWatchPromise !== null && this.initWatchPromise !== undefined) {
                            return [2, this.initWatchPromise];
                        }
                        this.initWatchPromise = new Promise(function (resolve, reject) {
                            void (function () { return __awaiter(_this, void 0, void 0, function () {
                                var envId, initWatchMsg, initEventMsg, _a, events, currEvent, _i, events_1, e, snapshot, e_1;
                                return __generator(this, function (_b) {
                                    switch (_b.label) {
                                        case 0:
                                            _b.trys.push([0, 3, , 4]);
                                            if (this.watchStatus === WatchStatus.PAUSED) {
                                                console.log('[realtime] initWatch cancelled on pause');
                                                return [2, resolve()];
                                            }
                                            return [4, this.wsLogin(this.envId, forceRefreshLogin)];
                                        case 1:
                                            envId = (_b.sent()).envId;
                                            if (this.watchStatus === WatchStatus.PAUSED) {
                                                console.log('[realtime] initWatch cancelled on pause');
                                                return [2, resolve()];
                                            }
                                            this.watchStatus = WatchStatus.INITING;
                                            initWatchMsg = {
                                                watchId: this.watchId,
                                                requestId: (0, message_1.genRequestId)(),
                                                msgType: 'INIT_WATCH',
                                                msgData: {
                                                    envId: envId,
                                                    collName: this.collectionName,
                                                    query: this.query,
                                                    limit: this.limit,
                                                    orderBy: this.orderBy,
                                                },
                                            };
                                            return [4, this.send({
                                                    msg: initWatchMsg,
                                                    waitResponse: true,
                                                    skipOnMessage: true,
                                                    timeout: DEFAULT_INIT_WATCH_TIMEOUT,
                                                })];
                                        case 2:
                                            initEventMsg = _b.sent();
                                            _a = initEventMsg.msgData, events = _a.events, currEvent = _a.currEvent;
                                            this.sessionInfo = {
                                                queryID: initEventMsg.msgData.queryID,
                                                currentEventId: currEvent - 1,
                                                currentDocs: [],
                                            };
                                            if (events.length > 0) {
                                                for (_i = 0, events_1 = events; _i < events_1.length; _i++) {
                                                    e = events_1[_i];
                                                    e.ID = currEvent;
                                                }
                                                this.handleServerEvents(initEventMsg);
                                            }
                                            else {
                                                this.sessionInfo.currentEventId = currEvent;
                                                snapshot = new snapshot_1.Snapshot({
                                                    id: currEvent,
                                                    docChanges: [],
                                                    docs: [],
                                                    type: 'init',
                                                });
                                                this.listener.onChange(snapshot);
                                                this.scheduleSendACK();
                                            }
                                            this.onWatchStart(this, this.sessionInfo.queryID);
                                            this.watchStatus = WatchStatus.ACTIVE;
                                            this.availableRetries.INIT_WATCH = DEFAULT_MAX_AUTO_RETRY_ON_ERROR;
                                            resolve();
                                            return [3, 4];
                                        case 3:
                                            e_1 = _b.sent();
                                            this.handleWatchEstablishmentError(e_1, {
                                                operationName: 'INIT_WATCH',
                                                resolve: resolve,
                                                reject: reject,
                                            });
                                            return [3, 4];
                                        case 4: return [2];
                                    }
                                });
                            }); })();
                        });
                        success = false;
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, , 3, 4]);
                        return [4, this.initWatchPromise];
                    case 2:
                        _a.sent();
                        success = true;
                        return [3, 4];
                    case 3:
                        this.initWatchPromise = undefined;
                        return [7];
                    case 4:
                        console.log("[realtime] initWatch ".concat(success ? 'success' : 'fail'));
                        return [2];
                }
            });
        }); };
        this.rebuildWatch = function (forceRefreshLogin) { return __awaiter(_this, void 0, void 0, function () {
            var success;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.rebuildWatchPromise !== null && this.rebuildWatchPromise !== undefined) {
                            return [2, this.rebuildWatchPromise];
                        }
                        this.rebuildWatchPromise = new Promise(function (resolve, reject) {
                            void (function () { return __awaiter(_this, void 0, void 0, function () {
                                var envId, rebuildWatchMsg, nextEventMsg, e_2;
                                return __generator(this, function (_a) {
                                    switch (_a.label) {
                                        case 0:
                                            _a.trys.push([0, 3, , 4]);
                                            if (this.watchStatus === WatchStatus.PAUSED) {
                                                console.log('[realtime] rebuildWatch cancelled on pause');
                                                return [2, resolve()];
                                            }
                                            return [4, this.wsLogin(this.envId, forceRefreshLogin)];
                                        case 1:
                                            envId = (_a.sent()).envId;
                                            if (!this.sessionInfo) {
                                                throw new Error('can not rebuildWatch without a successful initWatch (lack of sessionInfo)');
                                            }
                                            if (this.watchStatus === WatchStatus.PAUSED) {
                                                console.log('[realtime] rebuildWatch cancelled on pause');
                                                return [2, resolve()];
                                            }
                                            this.watchStatus = WatchStatus.REBUILDING;
                                            rebuildWatchMsg = {
                                                watchId: this.watchId,
                                                requestId: (0, message_1.genRequestId)(),
                                                msgType: 'REBUILD_WATCH',
                                                msgData: {
                                                    envId: envId,
                                                    collName: this.collectionName,
                                                    queryID: this.sessionInfo.queryID,
                                                    eventID: this.sessionInfo.currentEventId,
                                                },
                                            };
                                            return [4, this.send({
                                                    msg: rebuildWatchMsg,
                                                    waitResponse: true,
                                                    skipOnMessage: false,
                                                    timeout: DEFAULT_REBUILD_WATCH_TIMEOUT,
                                                })];
                                        case 2:
                                            nextEventMsg = _a.sent();
                                            this.handleServerEvents(nextEventMsg);
                                            this.watchStatus = WatchStatus.ACTIVE;
                                            this.availableRetries.REBUILD_WATCH = DEFAULT_MAX_AUTO_RETRY_ON_ERROR;
                                            resolve();
                                            return [3, 4];
                                        case 3:
                                            e_2 = _a.sent();
                                            this.handleWatchEstablishmentError(e_2, {
                                                operationName: 'REBUILD_WATCH',
                                                resolve: resolve,
                                                reject: reject,
                                            });
                                            return [3, 4];
                                        case 4: return [2];
                                    }
                                });
                            }); })();
                        });
                        success = false;
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, , 3, 4]);
                        return [4, this.rebuildWatchPromise];
                    case 2:
                        _a.sent();
                        success = true;
                        return [3, 4];
                    case 3:
                        this.rebuildWatchPromise = undefined;
                        return [7];
                    case 4:
                        console.log("[realtime] rebuildWatch ".concat(success ? 'success' : 'fail'));
                        return [2];
                }
            });
        }); };
        this.handleWatchEstablishmentError = function (e, options) { return __awaiter(_this, void 0, void 0, function () {
            var isInitWatch, abortWatch, retry;
            var _this = this;
            return __generator(this, function (_a) {
                isInitWatch = options.operationName === 'INIT_WATCH';
                abortWatch = function () {
                    _this.closeWithError(new error_1.CloudSDKError({
                        errCode: isInitWatch
                            ? error_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL
                            : error_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL,
                        errMsg: e,
                    }));
                    options.reject(e);
                };
                retry = function (refreshLogin) {
                    if (_this.useRetryTicket(options.operationName)) {
                        if (isInitWatch) {
                            _this.initWatchPromise = undefined;
                            options.resolve(_this.initWatch(refreshLogin));
                        }
                        else {
                            _this.rebuildWatchPromise = undefined;
                            options.resolve(_this.rebuildWatch(refreshLogin));
                        }
                    }
                    else {
                        abortWatch();
                    }
                };
                this.handleCommonError(e, {
                    onSignError: function () { return retry(true); },
                    onTimeoutError: function () { return retry(false); },
                    onNotRetryableError: abortWatch,
                    onCancelledError: options.reject,
                    onUnknownError: function () {
                        (function () { return __awaiter(_this, void 0, void 0, function () {
                            var onWSDisconnected, e_3;
                            var _this = this;
                            return __generator(this, function (_a) {
                                switch (_a.label) {
                                    case 0:
                                        _a.trys.push([0, 8, , 9]);
                                        onWSDisconnected = function () { return __awaiter(_this, void 0, void 0, function () {
                                            return __generator(this, function (_a) {
                                                switch (_a.label) {
                                                    case 0:
                                                        this.pause();
                                                        return [4, this.onceWSConnected()];
                                                    case 1:
                                                        _a.sent();
                                                        retry(true);
                                                        return [2];
                                                }
                                            });
                                        }); };
                                        if (!!this.isWSConnected()) return [3, 2];
                                        return [4, onWSDisconnected()];
                                    case 1:
                                        _a.sent();
                                        return [3, 7];
                                    case 2: return [4, (0, utils_1.sleep)(DEFAULT_WAIT_TIME_ON_UNKNOWN_ERROR)];
                                    case 3:
                                        _a.sent();
                                        if (!(this.watchStatus === WatchStatus.PAUSED)) return [3, 4];
                                        options.reject(new error_1.CancelledError("".concat(options.operationName, " cancelled due to pause after unknownError")));
                                        return [3, 7];
                                    case 4:
                                        if (!!this.isWSConnected()) return [3, 6];
                                        return [4, onWSDisconnected()];
                                    case 5:
                                        _a.sent();
                                        return [3, 7];
                                    case 6:
                                        retry(false);
                                        _a.label = 7;
                                    case 7: return [3, 9];
                                    case 8:
                                        e_3 = _a.sent();
                                        retry(true);
                                        return [3, 9];
                                    case 9: return [2];
                                }
                            });
                        }); })();
                    },
                });
                return [2];
            });
        }); };
        this.closeWatch = function () { return __awaiter(_this, void 0, void 0, function () {
            var queryId, closeWatchMsg, e_4;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        queryId = this.sessionInfo ? this.sessionInfo.queryID : '';
                        if (this.watchStatus !== WatchStatus.ACTIVE) {
                            this.watchStatus = WatchStatus.CLOSED;
                            this.onWatchClose(this, queryId);
                            return [2];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, 4, 5]);
                        this.watchStatus = WatchStatus.CLOSING;
                        closeWatchMsg = {
                            watchId: this.watchId,
                            requestId: (0, message_1.genRequestId)(),
                            msgType: 'CLOSE_WATCH',
                            msgData: null,
                        };
                        return [4, this.send({
                                msg: closeWatchMsg,
                            })];
                    case 2:
                        _a.sent();
                        this.sessionInfo = undefined;
                        this.watchStatus = WatchStatus.CLOSED;
                        return [3, 5];
                    case 3:
                        e_4 = _a.sent();
                        this.closeWithError(new error_1.CloudSDKError({
                            errCode: error_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL,
                            errMsg: e_4,
                        }));
                        return [3, 5];
                    case 4:
                        this.onWatchClose(this, queryId);
                        return [7];
                    case 5: return [2];
                }
            });
        }); };
        this.scheduleSendACK = function () {
            _this.clearACKSchedule();
            _this.ackTimeoutId = setTimeout(function () {
                if (_this.waitExpectedTimeoutId) {
                    _this.scheduleSendACK();
                }
                else {
                    _this.sendACK();
                }
            }, DEFAULT_SEND_ACK_DEBOUNCE_TIMEOUT);
        };
        this.clearACKSchedule = function () {
            if (_this.ackTimeoutId) {
                clearTimeout(_this.ackTimeoutId);
            }
        };
        this.sendACK = function () { return __awaiter(_this, void 0, void 0, function () {
            var ackMsg, e_5, msg;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        if (this.watchStatus !== WatchStatus.ACTIVE) {
                            this.scheduleSendACK();
                            return [2];
                        }
                        if (!this.sessionInfo) {
                            console.warn('[realtime listener] can not send ack without a successful initWatch (lack of sessionInfo)');
                            return [2];
                        }
                        ackMsg = {
                            watchId: this.watchId,
                            requestId: (0, message_1.genRequestId)(),
                            msgType: 'CHECK_LAST',
                            msgData: {
                                queryID: this.sessionInfo.queryID,
                                eventID: this.sessionInfo.currentEventId,
                            },
                        };
                        return [4, this.send({
                                msg: ackMsg,
                            })];
                    case 1:
                        _a.sent();
                        this.scheduleSendACK();
                        return [3, 3];
                    case 2:
                        e_5 = _a.sent();
                        if ((0, error_1.isRealtimeErrorMessageError)(e_5)) {
                            msg = e_5.payload;
                            switch (msg.msgData.code) {
                                case 'CHECK_LOGIN_FAILED':
                                case 'SIGN_EXPIRED_ERROR':
                                case 'SIGN_INVALID_ERROR':
                                case 'SIGN_PARAM_INVALID': {
                                    this.rebuildWatch();
                                    return [2];
                                }
                                case 'QUERYID_INVALID_ERROR':
                                case 'SYS_ERR':
                                case 'INVALIID_ENV':
                                case 'COLLECTION_PERMISSION_DENIED': {
                                    this.closeWithError(new error_1.CloudSDKError({
                                        errCode: error_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL,
                                        errMsg: msg.msgData.code,
                                    }));
                                    return [2];
                                }
                                default: {
                                    break;
                                }
                            }
                        }
                        if (this.availableRetries.CHECK_LAST
                            && this.availableRetries.CHECK_LAST > 0) {
                            this.availableRetries.CHECK_LAST -= 1;
                            this.scheduleSendACK();
                        }
                        else {
                            this.closeWithError(new error_1.CloudSDKError({
                                errCode: error_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL,
                                errMsg: e_5,
                            }));
                        }
                        return [3, 3];
                    case 3: return [2];
                }
            });
        }); };
        this.handleCommonError = function (e, options) {
            if ((0, error_1.isRealtimeErrorMessageError)(e)) {
                var msg = e.payload;
                switch (msg.msgData.code) {
                    case 'CHECK_LOGIN_FAILED':
                    case 'SIGN_EXPIRED_ERROR':
                    case 'SIGN_INVALID_ERROR':
                    case 'SIGN_PARAM_INVALID': {
                        options.onSignError(e);
                        return;
                    }
                    case 'QUERYID_INVALID_ERROR':
                    case 'SYS_ERR':
                    case 'INVALIID_ENV':
                    case 'COLLECTION_PERMISSION_DENIED': {
                        options.onNotRetryableError(e);
                        return;
                    }
                    default: {
                        options.onNotRetryableError(e);
                        return;
                    }
                }
            }
            else if ((0, error_1.isTimeoutError)(e)) {
                options.onTimeoutError(e);
                return;
            }
            else if ((0, error_1.isCancelledError)(e)) {
                options.onCancelledError(e);
                return;
            }
            options.onUnknownError(e);
        };
        this.watchId = "watchid_".concat(+new Date(), "_").concat(Math.random());
        this.envId = options.envId;
        this.collectionName = options.collectionName;
        this.query = options.query;
        this.limit = options.limit;
        this.orderBy = options.orderBy;
        this.send = options.send;
        this.login = options.login;
        this.isWSConnected = options.isWSConnected;
        this.onceWSConnected = options.onceWSConnected;
        this.getWaitExpectedTimeoutLength = options.getWaitExpectedTimeoutLength;
        this.onWatchStart = options.onWatchStart;
        this.onWatchClose = options.onWatchClose;
        this.debug = options.debug;
        this.availableRetries = {
            INIT_WATCH: DEFAULT_MAX_AUTO_RETRY_ON_ERROR,
            REBUILD_WATCH: DEFAULT_MAX_AUTO_RETRY_ON_ERROR,
            CHECK_LAST: DEFAULT_MAX_SEND_ACK_AUTO_RETRY_ON_ERROR,
        };
        this.listener = new listener_1.RealtimeListener({
            close: function () {
                _this.closeWatch();
            },
            onChange: options.onChange,
            onError: options.onError,
            debug: this.debug,
            virtualClient: this,
        });
        this.initWatch();
    }
    VirtualWebSocketClient.prototype.onMessage = function (msg) {
        var _this = this;
        switch (this.watchStatus) {
            case WatchStatus.PAUSED: {
                if (msg.msgType !== 'ERROR') {
                    return;
                }
                break;
            }
            case WatchStatus.LOGGINGIN:
            case WatchStatus.INITING:
            case WatchStatus.REBUILDING: {
                console.warn("[realtime listener] internal non-fatal error: unexpected message received while ".concat(this.watchStatus));
                return;
            }
            case WatchStatus.CLOSED: {
                console.warn('[realtime listener] internal non-fatal error: unexpected message received when the watch has closed');
                return;
            }
            case WatchStatus.ERRORED: {
                console.warn('[realtime listener] internal non-fatal error: unexpected message received when the watch has ended with error');
                return;
            }
        }
        if (!this.sessionInfo) {
            console.warn('[realtime listener] internal non-fatal error: sessionInfo not found while message is received.');
            return;
        }
        this.scheduleSendACK();
        switch (msg.msgType) {
            case 'NEXT_EVENT': {
                console.warn("nextevent ".concat(msg.msgData.currEvent, " ignored"), msg);
                this.handleServerEvents(msg);
                break;
            }
            case 'CHECK_EVENT': {
                if (this.sessionInfo.currentEventId < msg.msgData.currEvent) {
                    this.sessionInfo.expectEventId = msg.msgData.currEvent;
                    this.clearWaitExpectedEvent();
                    this.waitExpectedTimeoutId = setTimeout(function () {
                        _this.rebuildWatch();
                    }, this.getWaitExpectedTimeoutLength());
                    console.log("[realtime] waitExpectedTimeoutLength ".concat(this.getWaitExpectedTimeoutLength()));
                }
                break;
            }
            case 'ERROR': {
                this.closeWithError(new error_1.CloudSDKError({
                    errCode: error_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG,
                    errMsg: "".concat(msg.msgData.code, " - ").concat(msg.msgData.message),
                }));
                break;
            }
            default: {
                console.warn("[realtime listener] virtual client receive unexpected msg ".concat(msg.msgType, ": "), msg);
                break;
            }
        }
    };
    VirtualWebSocketClient.prototype.closeWithError = function (error) {
        var _a;
        this.watchStatus = WatchStatus.ERRORED;
        this.clearACKSchedule();
        this.listener.onError(error);
        this.onWatchClose(this, ((_a = this.sessionInfo) === null || _a === void 0 ? void 0 : _a.queryID) || '');
        console.log("[realtime] client closed (".concat(this.collectionName, " ").concat(this.query, ") (watchId ").concat(this.watchId, ")"));
    };
    VirtualWebSocketClient.prototype.pause = function () {
        this.watchStatus = WatchStatus.PAUSED;
        console.log("[realtime] client paused (".concat(this.collectionName, " ").concat(this.query, ") (watchId ").concat(this.watchId, ")"));
    };
    VirtualWebSocketClient.prototype.resume = function () {
        return __awaiter(this, void 0, void 0, function () {
            var e_6;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.watchStatus = WatchStatus.RESUMING;
                        console.log("[realtime] client resuming with ".concat(this.sessionInfo ? 'REBUILD_WATCH' : 'INIT_WATCH', " (").concat(this.collectionName, " ").concat(this.query, ") (").concat(this.watchId, ")"));
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4, (this.sessionInfo ? this.rebuildWatch() : this.initWatch())];
                    case 2:
                        _a.sent();
                        console.log("[realtime] client successfully resumed (".concat(this.collectionName, " ").concat(this.query, ") (").concat(this.watchId, ")"));
                        return [3, 4];
                    case 3:
                        e_6 = _a.sent();
                        console.error("[realtime] client resume failed (".concat(this.collectionName, " ").concat(this.query, ") (").concat(this.watchId, ")"), e_6);
                        return [3, 4];
                    case 4: return [2];
                }
            });
        });
    };
    VirtualWebSocketClient.prototype.useRetryTicket = function (operationName) {
        if (this.availableRetries[operationName]
            && this.availableRetries[operationName] > 0) {
            this.availableRetries[operationName] -= 1;
            console.log("[realtime] ".concat(operationName, " use a retry ticket, now only ").concat(this.availableRetries[operationName], " retry left"));
            return true;
        }
        return false;
    };
    VirtualWebSocketClient.prototype.handleServerEvents = function (msg) {
        return __awaiter(this, void 0, void 0, function () {
            var e_7;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        this.scheduleSendACK();
                        return [4, this.handleServerEventsInternel(msg)];
                    case 1:
                        _a.sent();
                        this.postHandleServerEventsValidityCheck(msg);
                        return [3, 3];
                    case 2:
                        e_7 = _a.sent();
                        console.error('[realtime listener] internal non-fatal error: handle server events failed with error: ', e_7);
                        throw e_7;
                    case 3: return [2];
                }
            });
        });
    };
    VirtualWebSocketClient.prototype.handleServerEventsInternel = function (msg) {
        return __awaiter(this, void 0, void 0, function () {
            var requestId, events, msgType, sessionInfo, allChangeEvents, docs, initEncountered, _loop_1, this_1, i, len, state_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        requestId = msg.requestId;
                        events = msg.msgData.events;
                        msgType = msg.msgType;
                        if (!events.length || !this.sessionInfo) {
                            return [2];
                        }
                        sessionInfo = this.sessionInfo;
                        try {
                            allChangeEvents = events.map(getPublicEvent);
                        }
                        catch (e) {
                            this.closeWithError(new error_1.CloudSDKError({
                                errCode: error_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA,
                                errMsg: e,
                            }));
                            return [2];
                        }
                        docs = __spreadArray([], sessionInfo.currentDocs, true);
                        initEncountered = false;
                        _loop_1 = function (i, len) {
                            var change, localDoc, doc_1, _i, _b, fieldPath, err, err, doc, doc, err, ind, ind, docsSnapshot, docChanges, snapshot;
                            return __generator(this, function (_c) {
                                switch (_c.label) {
                                    case 0:
                                        change = allChangeEvents[i];
                                        if (!(sessionInfo.currentEventId >= change.id)) return [3, 1];
                                        if (!allChangeEvents[i - 1] || change.id > allChangeEvents[i - 1].id) {
                                            console.warn("[realtime] duplicate event received, cur ".concat(sessionInfo.currentEventId, " but got ").concat(change.id));
                                        }
                                        else {
                                            console.error("[realtime listener] server non-fatal error: events out of order (the latter event's id is smaller than that of the former) (requestId ".concat(requestId, ")"));
                                        }
                                        return [2, "continue"];
                                    case 1:
                                        if (!(sessionInfo.currentEventId === change.id - 1)) return [3, 2];
                                        switch (change.dataType) {
                                            case 'update': {
                                                if (!change.doc) {
                                                    switch (change.queueType) {
                                                        case 'update':
                                                        case 'dequeue': {
                                                            localDoc = docs.find(function (doc) { return doc._id === change.docId; });
                                                            if (localDoc) {
                                                                doc_1 = (0, cloneDeep_1.default)(localDoc);
                                                                if (change.updatedFields) {
                                                                    Object.keys(change.updatedFields).forEach(function (fieldPath) {
                                                                        (0, set_1.default)(doc_1, fieldPath, change.updatedFields[fieldPath]);
                                                                    });
                                                                }
                                                                if (change.removedFields) {
                                                                    for (_i = 0, _b = change.removedFields; _i < _b.length; _i++) {
                                                                        fieldPath = _b[_i];
                                                                        (0, unset_1.default)(doc_1, fieldPath);
                                                                    }
                                                                }
                                                                change.doc = doc_1;
                                                            }
                                                            else {
                                                                console.error('[realtime listener] internal non-fatal server error: unexpected update dataType event where no doc is associated.');
                                                            }
                                                            break;
                                                        }
                                                        case 'enqueue': {
                                                            err = new error_1.CloudSDKError({
                                                                errCode: error_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,
                                                                errMsg: "HandleServerEvents: full doc is not provided with dataType=\"update\" and queueType=\"enqueue\" (requestId ".concat(msg.requestId, ")"),
                                                            });
                                                            this_1.closeWithError(err);
                                                            throw err;
                                                        }
                                                        default: {
                                                            break;
                                                        }
                                                    }
                                                }
                                                break;
                                            }
                                            case 'replace': {
                                                if (!change.doc) {
                                                    err = new error_1.CloudSDKError({
                                                        errCode: error_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,
                                                        errMsg: "HandleServerEvents: full doc is not provided with dataType=\"replace\" (requestId ".concat(msg.requestId, ")"),
                                                    });
                                                    this_1.closeWithError(err);
                                                    throw err;
                                                }
                                                break;
                                            }
                                            case 'remove': {
                                                doc = docs.find(function (doc) { return doc._id === change.docId; });
                                                if (doc) {
                                                    change.doc = doc;
                                                }
                                                else {
                                                    console.error('[realtime listener] internal non-fatal server error: unexpected remove event where no doc is associated.');
                                                }
                                                break;
                                            }
                                            case 'limit': {
                                                if (!change.doc) {
                                                    switch (change.queueType) {
                                                        case 'dequeue': {
                                                            doc = docs.find(function (doc) { return doc._id === change.docId; });
                                                            if (doc) {
                                                                change.doc = doc;
                                                            }
                                                            else {
                                                                console.error('[realtime listener] internal non-fatal server error: unexpected limit dataType event where no doc is associated.');
                                                            }
                                                            break;
                                                        }
                                                        case 'enqueue': {
                                                            err = new error_1.CloudSDKError({
                                                                errCode: error_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,
                                                                errMsg: "HandleServerEvents: full doc is not provided with dataType=\"limit\" and queueType=\"enqueue\" (requestId ".concat(msg.requestId, ")"),
                                                            });
                                                            this_1.closeWithError(err);
                                                            throw err;
                                                        }
                                                        default: {
                                                            break;
                                                        }
                                                    }
                                                }
                                                break;
                                            }
                                        }
                                        switch (change.queueType) {
                                            case 'init': {
                                                if (!initEncountered) {
                                                    initEncountered = true;
                                                    docs = [change.doc];
                                                }
                                                else {
                                                    docs.push(change.doc);
                                                }
                                                break;
                                            }
                                            case 'enqueue': {
                                                docs.push(change.doc);
                                                break;
                                            }
                                            case 'dequeue': {
                                                ind = docs.findIndex(function (doc) { return doc._id === change.docId; });
                                                if (ind > -1) {
                                                    docs.splice(ind, 1);
                                                }
                                                else {
                                                    console.error('[realtime listener] internal non-fatal server error: unexpected dequeue event where no doc is associated.');
                                                }
                                                break;
                                            }
                                            case 'update': {
                                                ind = docs.findIndex(function (doc) { return doc._id === change.docId; });
                                                if (ind > -1) {
                                                    docs[ind] = change.doc;
                                                }
                                                else {
                                                    console.error('[realtime listener] internal non-fatal server error: unexpected queueType update event where no doc is associated.');
                                                }
                                                break;
                                            }
                                        }
                                        if (i === len - 1
                                            || (allChangeEvents[i + 1] && allChangeEvents[i + 1].id !== change.id)) {
                                            docsSnapshot = __spreadArray([], docs, true);
                                            docChanges = allChangeEvents
                                                .slice(0, i + 1)
                                                .filter(function (c) { return c.id === change.id; });
                                            this_1.sessionInfo.currentEventId = change.id;
                                            this_1.sessionInfo.currentDocs = docs;
                                            snapshot = new snapshot_1.Snapshot({
                                                id: change.id,
                                                docChanges: docChanges,
                                                docs: docsSnapshot,
                                                msgType: msgType,
                                            });
                                            this_1.listener.onChange(snapshot);
                                        }
                                        return [3, 4];
                                    case 2:
                                        console.warn("[realtime listener] event received is out of order, cur ".concat(this_1.sessionInfo.currentEventId, " but got ").concat(change.id));
                                        return [4, this_1.rebuildWatch()];
                                    case 3:
                                        _c.sent();
                                        return [2, { value: void 0 }];
                                    case 4: return [2];
                                }
                            });
                        };
                        this_1 = this;
                        i = 0, len = allChangeEvents.length;
                        _a.label = 1;
                    case 1:
                        if (!(i < len)) return [3, 4];
                        return [5, _loop_1(i, len)];
                    case 2:
                        state_1 = _a.sent();
                        if (typeof state_1 === "object")
                            return [2, state_1.value];
                        _a.label = 3;
                    case 3:
                        i++;
                        return [3, 1];
                    case 4: return [2];
                }
            });
        });
    };
    VirtualWebSocketClient.prototype.postHandleServerEventsValidityCheck = function (msg) {
        if (!this.sessionInfo) {
            console.error('[realtime listener] internal non-fatal error: sessionInfo lost after server event handling, this should never occur');
            return;
        }
        if (this.sessionInfo.expectEventId
            && this.sessionInfo.currentEventId >= this.sessionInfo.expectEventId) {
            this.clearWaitExpectedEvent();
        }
        if (this.sessionInfo.currentEventId < msg.msgData.currEvent) {
            console.warn('[realtime listener] internal non-fatal error: client eventId does not match with server event id after server event handling');
            return;
        }
    };
    VirtualWebSocketClient.prototype.clearWaitExpectedEvent = function () {
        if (this.waitExpectedTimeoutId) {
            clearTimeout(this.waitExpectedTimeoutId);
            this.waitExpectedTimeoutId = undefined;
        }
    };
    return VirtualWebSocketClient;
}());
exports.VirtualWebSocketClient = VirtualWebSocketClient;
function getPublicEvent(event) {
    var e = {
        id: event.ID,
        dataType: event.DataType,
        queueType: event.QueueType,
        docId: event.DocID,
        doc: event.Doc && event.Doc !== '{}' ? JSON.parse(event.Doc) : undefined,
    };
    if (event.DataType === 'update') {
        if (event.UpdatedFields) {
            e.updatedFields = JSON.parse(event.UpdatedFields);
        }
        if (event.removedFields || event.RemovedFields) {
            e.removedFields = JSON.parse(event.removedFields);
        }
    }
    return e;
}
//# sourceMappingURL=data:application/json;base64,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