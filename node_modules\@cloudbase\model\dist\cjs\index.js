"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerModel = exports.lazyGetEntity = void 0;
var wx_cloud_client_sdk_1 = require("@cloudbase/wx-cloud-client-sdk");
var utilities_1 = require("@cloudbase/utilities");
var CloudbaseEventEmitter = utilities_1.events.CloudbaseEventEmitter;
var COMPONENT_NAME = 'models';
function getEntity(cloudbase) {
    var _this = this;
    var callFunction = cloudbase.callFunction.bind(cloudbase);
    var cloudbaseFetch = cloudbase.request.fetch.bind(cloudbase.request);
    var fetch = function (fetchOptions) { return __awaiter(_this, void 0, void 0, function () {
        var res;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0: return [4, cloudbaseFetch(__assign(__assign({}, fetchOptions), { headers: __assign({ 'Content-Type': 'application/json' }, fetchOptions.headers) }))];
                case 1:
                    res = _a.sent();
                    return [4, res.data];
                case 2: return [2, _a.sent()];
            }
        });
    }); };
    var _a = cloudbase.getEndPointWithKey('GATEWAY'), BASE_URL = _a.BASE_URL, PROTOCOL = _a.PROTOCOL;
    var baseUrl = "".concat(PROTOCOL).concat(BASE_URL, "/model");
    var sqlBaseUrl = "".concat(PROTOCOL).concat(BASE_URL, "/sql");
    var entity = (0, wx_cloud_client_sdk_1.generateHTTPClient)(callFunction, fetch, baseUrl, { sqlBaseUrl: sqlBaseUrl });
    return entity;
}
var cloudbaseModelMap = new WeakMap();
function lazyGetEntity(cloudbase) {
    return new Proxy({}, {
        get: function (_, prop) {
            var entity = cloudbaseModelMap.get(cloudbase);
            if (!entity) {
                entity = getEntity(cloudbase);
                cloudbaseModelMap.set(cloudbase, entity);
            }
            return entity[prop];
        },
    });
}
exports.lazyGetEntity = lazyGetEntity;
var CLOUDBASE_INIT_EVENT = 'cloudbase_init';
var bus = new CloudbaseEventEmitter();
bus.on(CLOUDBASE_INIT_EVENT, function (_a) {
    var cloudbase = _a.data;
    Object.assign(cloudbase, { models: lazyGetEntity(cloudbase) });
});
var component = {
    name: COMPONENT_NAME,
    namespace: COMPONENT_NAME,
    entity: new Proxy({}, {
        get: function (_, prop) {
            console.warn("\u3010deprecated\u3011Accessing Cloudbase.prototype.models.".concat(prop, "."));
        },
    }),
    injectEvents: {
        bus: bus,
        events: [CLOUDBASE_INIT_EVENT],
    },
};
try {
    cloudbase.registerComponent(component);
}
catch (e) { }
function registerModel(app) {
    try {
        app.registerComponent(component);
    }
    catch (e) {
        console.warn(e);
    }
}
exports.registerModel = registerModel;
__exportStar(require("@cloudbase/wx-cloud-client-sdk"), exports);
//# sourceMappingURL=data:application/json;base64,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