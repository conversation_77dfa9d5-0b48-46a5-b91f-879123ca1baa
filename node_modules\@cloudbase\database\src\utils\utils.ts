import { Db } from '../';

export const sleep = (ms: number = 0) => new Promise(r => setTimeout(r, ms))

const counters: Record<string, number> = {}

export const autoCount = (domain: string = 'any'): number => {
  if (!counters[domain]) {
    counters[domain] = 0
  }
  return counters[domain]++
}

/**
 * 工厂模式创建 ws 实例
 */
const wsList = {}
export function getWsInstance(db: Db) {
  if(!Db.wsClientClass){
    throw new Error('to use realtime you must import realtime module first')
  }
  const { env } = db.config

  if (!wsList[env]) {
    wsList[env] = new Db.wsClientClass({
      context: {
        appConfig: {
          docSizeLimit: 1000,
          realtimePingInterval: 10000,
          realtimePongWaitTimeout: 5000,
          request: new Db.reqClass(db.config)
        }
      }
    })
  }

  return wsList[env]
}
