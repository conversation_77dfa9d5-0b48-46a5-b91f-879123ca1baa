{"name": "@cloudbase/database", "version": "0.9.22", "description": "database for (node.)js sdk ", "main": "dist/commonjs/index.js", "module": "dist/esm/index.js", "publishConfig": {"tag": "next"}, "scripts": {"eslint": "eslint \"./**/*.ts\"", "eslint-fix": "eslint --fix \"./**/*.ts\"", "test": "echo \"Error: no test specified\" && exit 1", "build": "tsc -p tsconfig.json && tsc -p tsconfig.esm.json", "prepublishOnly": "npm run build"}, "author": "", "license": "ISC", "devDependencies": {"eslint": "^5.15.3", "eslint-config-prettier": "^4.1.0", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-typescript": "^0.14.0", "typescript": "^3.6.2", "typescript-eslint-parser": "^22.0.0"}, "sideEffects": false, "dependencies": {"bson": "^4.0.2"}}