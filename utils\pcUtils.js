/**
 * PC端工具函数
 * 包含格式化、计算、验证等通用功能
 */

/**
 * 格式化金额显示
 * @param {number} amount 金额
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的金额字符串
 */
export function formatMoney(amount, decimals = 2) {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '0.00';
  }
  
  const num = parseFloat(amount);
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  });
}

/**
 * 格式化百分比
 * @param {number} value 数值
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的百分比字符串
 */
export function formatPercent(value, decimals = 2) {
  if (value === null || value === undefined || isNaN(value)) {
    return '0.00%';
  }
  
  const num = parseFloat(value);
  return `${num.toFixed(decimals)}%`;
}

/**
 * 格式化日期
 * @param {string|Date} date 日期
 * @param {string} format 格式 'YYYY-MM-DD' | 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  if (format === 'YYYY-MM-DD HH:mm:ss') {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }
  
  return `${year}-${month}-${day}`;
}

/**
 * 计算增长率
 * @param {number} current 当前值
 * @param {number} previous 上期值
 * @returns {number} 增长率百分比
 */
export function calculateGrowthRate(current, previous) {
  if (!previous || previous === 0) {
    return current > 0 ? 100 : 0;
  }
  
  return ((current - previous) / Math.abs(previous)) * 100;
}

/**
 * 获取增长率显示文本
 * @param {number} growthRate 增长率
 * @returns {object} 包含文本、颜色、图标的对象
 */
export function getGrowthDisplay(growthRate) {
  const rate = parseFloat(growthRate) || 0;
  const absRate = Math.abs(rate);
  
  if (rate > 0) {
    return {
      text: `↗ ${absRate.toFixed(2)}%`,
      color: '#67C23A',
      icon: 'el-icon-top',
      type: 'increase'
    };
  } else if (rate < 0) {
    return {
      text: `↘ ${absRate.toFixed(2)}%`,
      color: '#F56C6C',
      icon: 'el-icon-bottom',
      type: 'decrease'
    };
  } else {
    return {
      text: '- 0.00%',
      color: '#909399',
      icon: 'el-icon-minus',
      type: 'stable'
    };
  }
}

/**
 * 获取状态标签类型
 * @param {string} status 状态值
 * @returns {string} Element Plus标签类型
 */
export function getStatusTagType(status) {
  const statusMap = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'withdrawn': 'info',
    'completed': 'success',
    'unpaid': 'warning',
    'partially_paid': 'primary',
    'paid': 'success'
  };
  
  return statusMap[status] || 'info';
}

/**
 * 获取状态显示文本
 * @param {string} status 状态值
 * @returns {string} 显示文本
 */
export function getStatusText(status) {
  const statusMap = {
    'pending': '待审批',
    'approved': '已通过',
    'rejected': '已拒绝',
    'withdrawn': '已撤销',
    'completed': '已完成',
    'unpaid': '未收款',
    'partially_paid': '部分收款',
    'paid': '已收款'
  };
  
  return statusMap[status] || status;
}

/**
 * 获取审批类型显示文本
 * @param {string} type 审批类型
 * @returns {string} 显示文本
 */
export function getApprovalTypeText(type) {
  const typeMap = {
    'expense': '日常报销',
    'driver': '司机运费',
    'worker': '工人费用'
  };
  
  return typeMap[type] || type;
}

/**
 * 获取审批类型标签类型
 * @param {string} type 审批类型
 * @returns {string} Element Plus标签类型
 */
export function getApprovalTypeTagType(type) {
  const typeMap = {
    'expense': 'primary',
    'driver': 'success',
    'worker': 'warning'
  };
  
  return typeMap[type] || 'info';
}

/**
 * 生成图表颜色
 * @param {number} index 索引
 * @returns {string} 颜色值
 */
export function getChartColor(index) {
  const colors = [
    '#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE',
    '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC', '#FF9F7F'
  ];
  
  return colors[index % colors.length];
}

/**
 * 生成随机ID
 * @param {number} length 长度
 * @returns {string} 随机ID
 */
export function generateId(length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 时间限制
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle;
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 深拷贝对象
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item));
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
}

/**
 * 获取月份日期范围
 * @param {number} year 年份
 * @param {number} month 月份
 * @returns {object} 包含开始和结束日期的对象
 */
export function getMonthDateRange(year, month) {
  const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
  const endDay = new Date(year, month, 0).getDate();
  const endDate = `${year}-${month.toString().padStart(2, '0')}-${endDay.toString().padStart(2, '0')}`;
  
  return { startDate, endDate };
}

/**
 * 验证金额格式
 * @param {string} amount 金额字符串
 * @returns {boolean} 是否有效
 */
export function validateAmount(amount) {
  const regex = /^\d+(\.\d{1,2})?$/;
  return regex.test(amount) && parseFloat(amount) >= 0;
}

/**
 * 获取当前财务年月
 * @returns {object} 包含年份和月份的对象
 */
export function getCurrentFinanceDate() {
  const now = new Date();
  return {
    year: now.getFullYear(),
    month: now.getMonth() + 1
  };
}

/**
 * 计算两个日期之间的天数
 * @param {string|Date} startDate 开始日期
 * @param {string|Date} endDate 结束日期
 * @returns {number} 天数
 */
export function getDaysBetween(startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end - start);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}
